import type { Meta, StoryObj } from '@storybook/react';
import { MeasurementRow, MeasurementRowProps } from './measurement-row';

const meta = {
  title: 'Molecules/Measurement-Row',
  component: MeasurementRow,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: { control: 'text' },
    value: { control: 'text' },
  },
} satisfies Meta<typeof MeasurementRow>;

export default meta;

type Story = StoryObj<typeof meta>;

const Template = ({ label, value }: MeasurementRowProps) => (
  <div className="w-3xs">
    <MeasurementRow label={label} value={value} />
  </div>
);
export const Default: Story = {
  args: {
    label: 'Chest',
    value: '28,5 cm',
  },

  render: (args) => <Template {...args} />,
};
