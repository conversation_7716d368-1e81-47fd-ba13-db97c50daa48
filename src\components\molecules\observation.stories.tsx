import type { Meta, StoryObj } from '@storybook/react';
import { Observation, ObservationProps } from './observation';

const meta = {
  title: 'Molecules/Observation',
  component: Observation,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    observationText: { control: 'text' },
    observationTranslations: { control: 'object' },
  },
} satisfies Meta<typeof Observation>;

export default meta;

type Story = StoryObj<typeof meta>;

const ObservationContainer = (args: ObservationProps) => (
  <div className="w-96">
    <Observation {...args} />
  </div>
);

export const Default: Story = {
  args: {
    observationText: 'This is a simple observation text without translations.',
  },
  render: ObservationContainer,
};

export const WithMultiLanguageText: Story = {
  args: {
    observationText: '[en]This product runs small, consider sizing up.[pt-BR]Este produto veste pequeno, considere um tamanho maior.[es]Este producto es pequeño, considera una talla más grande.',
  },
  render: ObservationContainer,
};

export const WithObservationTranslations: Story = {
  args: {
    observationText: '',
    observationTranslations: [
      {
        id: '1',
        modelingId: '123',
        language: 'en',
        observation: 'This garment fits true to size. Choose your usual size for the best fit.',
      },
      {
        id: '2',
        modelingId: '123',
        language: 'pt-BR',
        observation: 'Esta peça veste no tamanho. Escolha seu tamanho habitual para o melhor caimento.',
      },
      {
        id: '3',
        modelingId: '123',
        language: 'es',
        observation: 'Esta prenda se ajusta a la talla. Elige tu talla habitual para el mejor ajuste.',
      },
    ],
  },
  render: ObservationContainer,
};

export const WithHTMLContent: Story = {
  args: {
    observationText: '',
    observationTranslations: [
      {
        id: '1',
        modelingId: '123',
        language: 'en',
        observation: '<strong>Important:</strong> This item runs <em>large</em>. We recommend ordering one size <u>smaller</u> than usual.',
      },
    ],
  },
  render: ObservationContainer,
};

export const WithTitle: Story = {
  args: {
    title: 'Size Recommendation',
    observationText: 'This product fits true to size. Choose your usual size for the best fit.',
  },
  render: ObservationContainer,
};

export const Empty: Story = {
  args: {
    observationText: '',
    observationTranslations: [],
  },
  render: ObservationContainer,
};
