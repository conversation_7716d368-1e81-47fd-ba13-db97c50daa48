import getQueryParams from './get-query-params';

const qp = getQueryParams<{ tenantId: string }>();

const setCustomTheme = (themeURL: string) => {
  const customCssUrl = themeURL || `${import.meta.env.VITE_S3_URL}/${qp.tenantId}/theme_v4.css`;

  try {
    const customTheme = document.createElement('link');

    customTheme.id = 'szb-vfr-custom-theme';
    customTheme.setAttribute('rel', 'preload');
    customTheme.setAttribute('rel', 'stylesheet');
    customTheme.setAttribute('as', 'style');
    customTheme.setAttribute('href', customCssUrl);

    document.querySelector('head')!.appendChild(customTheme);
  } catch (error) {
    console.error('Error setting custom theme:', error);
  }
};

export default setCustomTheme;
