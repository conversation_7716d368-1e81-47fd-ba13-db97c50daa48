import { useDevice } from '@/hooks/use-device';
import { BodyMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface UpperBodyProps {
  measure: BodyMeasure;
  className?: string;
}

export function UpperBody({ measure, className }: UpperBodyProps) {
  const { measures } = measure;
  const mappedMeasures = measures.map((item) => item.measure);
  const { isMobile } = useDevice();

  const findMeasure = (measure: string) => {
    const foundMeasure = mappedMeasures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full flex justify-center', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_upper_body">
            <mask id="mask0_128_1730" maskUnits="userSpaceOnUse" x="0" y="0" width="331" height="193">
              <rect id="rect" width="331" height="193" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_1730)">
              <g id="group_upper_body_mask">
                <g id="child">
                  <g id="Group 38">
                    <g id="Group 37">
                      <path
                        id="Vector"
                        d="M144.339 114.006C147.055 105.86 147.301 94.3823 147.301 94.3823H148.782H181.365H182.847C182.847 94.3823 183.093 105.86 185.809 114.006C187.783 119.93 195.806 121.041 208.395 125.114C220.699 129.095 228.759 127.706 233.943 141.776C239.126 155.846 253.566 199.907 255.788 209.534C257.565 217.235 259.984 228.294 260.972 232.86C262.946 236.069 267.785 248.633 271.339 273.219C274.894 297.804 275.535 327.4 275.412 339.125C276.029 346.284 276.141 363.197 276.141 363.197L275.412 370.019C275.412 370.019 273.561 376.077 269.118 381.705C263.564 388.74 254.307 397.997 253.196 395.405C252.308 393.332 253.567 390.592 254.307 389.481C253.073 390.592 250.53 392.369 250.234 390.592C249.864 388.37 247.642 388.74 253.196 383.557C258.75 378.373 260.972 373.93 260.972 368.006C260.972 363.266 256.529 355.417 254.307 352.084C253.073 352.825 249.864 355.269 246.902 359.119C243.199 363.933 241.718 368.376 239.497 366.154C237.275 363.933 237.275 362.452 241.348 355.787C245.421 349.122 250.975 337.274 254.307 335.052C256.973 333.275 258.627 332.09 259.12 331.72C258.503 327.03 256.973 316.317 255.788 310.985C254.307 304.321 248.383 295.434 244.31 280.994C241.052 269.442 237.522 247.3 236.164 237.674C231.598 225.578 222.316 200.647 221.724 197.685C221.132 194.723 218.268 183.369 216.911 178.061L214.689 183.615V246.93C215.43 251.25 216.688 262.407 215.8 272.478C214.911 282.549 215.43 286.795 215.8 287.659C217.528 294.2 220.984 309.282 220.984 317.28V337.644C223.575 347.148 228.759 368.598 228.759 378.373C228.759 390.592 228.389 413.918 227.648 424.285C227.056 432.579 225.673 442.552 225.056 446.501H182.476C179.884 428.975 174.479 391.851 173.59 383.557C172.479 373.189 172.849 368.376 171.739 366.154C170.628 363.933 168.777 361.711 166.555 361.711H163.593C161.371 361.711 159.52 363.933 158.409 366.154C157.298 368.376 157.669 373.189 156.558 383.557C155.669 391.851 150.263 428.975 147.672 446.501H105.092C104.474 442.552 103.092 432.579 102.5 424.285C101.759 413.918 101.389 390.592 101.389 378.373C101.389 368.598 106.573 347.148 109.164 337.644V317.28C109.164 309.282 112.62 294.2 114.348 287.659C114.718 286.795 115.237 282.549 114.348 272.478C113.459 262.407 114.718 251.25 115.459 246.93V183.615L113.237 178.061C111.88 183.369 109.016 194.723 108.424 197.685C107.832 200.647 98.5503 225.578 93.9837 237.674C92.6261 247.3 89.0963 269.442 85.8379 280.994C81.7651 295.434 75.8409 304.321 74.3599 310.985C73.175 316.317 71.6446 327.03 71.0275 331.72C71.5212 332.09 73.175 333.275 75.8409 335.052C79.1732 337.274 84.7272 349.122 88.8 355.787C92.8729 362.452 92.8729 363.933 90.6514 366.154C88.4298 368.376 86.9487 363.933 83.2461 359.119C80.284 355.269 77.0751 352.825 75.8409 352.084C73.6193 355.417 69.1762 363.266 69.1762 368.006C69.1762 373.93 71.3977 378.373 76.9517 383.557C82.5056 388.74 80.284 388.37 79.9138 390.592C79.6176 392.369 77.0751 390.592 75.8409 389.481C76.5814 390.592 77.8403 393.332 76.9517 395.405C75.8409 397.997 66.5843 388.74 61.0304 381.705C56.5873 376.077 54.4119 369.98 55.029 368.746C54.7822 367.512 53.8411 367.265 54.2886 363.933C54.6944 360.91 53.6715 346.284 54.2886 339.125C54.1652 327.4 55.2544 297.804 58.8089 273.219C62.3634 248.633 67.2015 236.069 69.1762 232.86C70.1636 228.294 72.5826 217.235 74.3599 209.534C76.5814 199.907 91.0216 155.846 96.2053 141.776C101.389 127.706 109.449 129.095 121.753 125.114C134.342 121.041 142.365 119.93 144.339 114.006Z"
                        fill="white"
                      />
                      <g id="Group 36">
                        <g id="Group 35">
                          <path
                            id="Vector 21"
                            d="M167.958 112.154C178.783 107.563 184.25 97.7141 186.101 94.752V92.1602C181.658 89.1981 172.624 83.2739 172.031 83.2739C171.439 83.2739 152.778 88.4576 143.521 91.0494L147.082 101.737L145.219 109.19C152.624 114.003 161.086 115.069 167.958 112.154Z"
                            fill="url(#paint0_linear_128_1730)"
                          />
                        </g>
                        <g id="Group 33">
                          <path
                            id="Vector 17"
                            d="M163.005 184.726C163.005 184.726 162.632 187.097 162.625 188.92C162.618 190.514 162.703 192.292 162.703 192.292"
                            stroke="black"
                            stroke-width="0.740522"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <g id="Group 27">
                            <path
                              id="Vector 15"
                              d="M161.293 137.332C161.293 137.332 160.503 135.083 158.331 133.767C156.159 132.451 149.198 132.396 145.742 129.927"
                              stroke="black"
                              stroke-width="0.740522"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              id="Vector 14"
                              d="M167.217 137.332C167.217 137.332 168.007 135.083 170.179 133.767C172.351 132.451 179.312 132.396 182.768 129.927"
                              stroke="black"
                              stroke-width="0.740522"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                  </g>
                  <path
                    id="Vector_2"
                    d="M166.554 361.711H163.592C161.371 361.711 159.519 363.933 158.408 366.154C157.298 368.376 157.668 373.189 156.557 383.557C155.668 391.851 150.263 428.975 147.671 446.501H105.091C104.474 442.552 103.091 432.579 102.499 424.285C101.758 413.918 101.388 390.592 101.388 378.373C101.388 368.598 106.572 347.148 109.164 337.644V317.28C109.164 309.282 112.619 294.2 114.347 287.659C114.718 286.795 115.236 282.549 114.347 272.478C113.459 262.407 114.718 251.25 115.458 246.93V183.615L113.237 178.061C111.879 183.369 109.016 194.723 108.423 197.685C107.831 200.647 98.5495 225.578 93.9829 237.674C92.6253 247.3 89.0955 269.442 85.8371 280.994C81.7643 295.434 75.8401 304.321 74.3591 310.985C73.1742 316.317 71.6438 327.03 71.0267 331.72C71.5204 332.09 73.1742 333.275 75.8401 335.052C79.1724 337.274 84.7264 349.122 88.7992 355.787C92.8721 362.452 92.8721 363.933 90.6506 366.154C88.429 368.376 86.9479 363.933 83.2453 359.119C80.2832 355.269 77.0743 352.825 75.8401 352.084C73.6185 355.417 69.1754 363.266 69.1754 368.006C69.1754 373.93 71.3969 378.373 76.9509 383.557C82.5048 388.74 80.2832 388.37 79.913 390.592C79.6168 392.369 77.0743 390.592 75.8401 389.481C76.5806 390.592 77.8395 393.332 76.9509 395.405C75.8401 397.997 66.5835 388.74 61.0296 381.705C56.5865 376.077 55.3984 370.968 55.3984 370.968C55.3984 370.968 54.5806 368.006 54.2876 363.933C54.0688 360.891 54.1181 346.284 54.7352 339.125C54.6118 327.4 55.2536 297.804 58.8081 273.219C62.3626 248.633 67.2007 236.069 69.1754 232.86C70.1628 228.294 72.5818 217.235 74.3591 209.534C76.5806 199.907 91.0208 155.846 96.2045 141.776C101.388 127.706 109.448 129.095 121.753 125.114C134.341 121.041 142.364 119.93 144.338 114.006C147.054 105.86 147.301 94.3823 147.301 94.3823H181.365M166.554 361.711C168.776 361.711 170.627 363.933 171.738 366.154C172.849 368.376 172.478 373.189 173.589 383.557C174.478 391.851 179.621 428.257 182.213 445.783L224.943 446.142C225.56 442.193 227.055 432.579 227.647 424.285C228.388 413.918 228.758 390.592 228.758 378.373C228.758 368.598 223.575 347.148 220.983 337.644V317.28C220.983 309.282 217.527 294.2 215.799 287.659C215.429 286.795 214.91 282.549 215.799 272.478C216.688 262.407 215.429 251.25 214.688 246.93V183.615L216.91 178.061C218.267 183.369 221.131 194.723 221.723 197.685C222.316 200.647 231.597 225.578 236.163 237.674C237.521 247.3 241.051 269.442 244.309 280.994C248.382 295.434 254.306 304.321 255.787 310.985C256.972 316.317 258.503 327.03 259.12 331.72C258.626 332.09 256.972 333.275 254.306 335.052C250.974 337.274 245.42 349.122 241.347 355.787C237.274 362.452 237.274 363.933 239.496 366.154C241.717 368.376 243.198 363.933 246.901 359.119C249.863 355.269 253.072 352.825 254.306 352.084C256.528 355.417 260.971 363.266 260.971 368.006C260.971 373.93 258.749 378.373 253.195 383.557C247.642 388.74 249.863 388.37 250.233 390.592C250.53 392.369 253.072 390.592 254.306 389.481C253.566 390.592 252.307 393.332 253.195 395.405C254.306 397.997 263.563 388.74 269.117 381.705C273.56 376.077 275.041 370.968 275.411 369.116C276.815 364.303 276.028 346.284 275.411 339.125C275.535 327.4 274.893 297.804 271.338 273.219C267.784 248.633 262.946 236.069 260.971 232.86C259.984 228.294 257.565 217.235 255.787 209.534C253.566 199.907 239.125 155.846 233.942 141.776C228.758 127.706 220.698 129.095 208.394 125.114C195.805 121.041 187.783 119.93 185.808 114.006C183.093 105.86 182.846 94.3823 182.846 94.3823H148.782M166.554 361.711H163.222"
                    stroke="black"
                    stroke-width="0.740522"
                    stroke-linecap="round"
                  />
                  <g id="Group 31">
                    <g id="Group 2">
                      <path
                        id="Vector 4"
                        d="M125.381 54.5047C126.763 49.6831 129.411 53.4411 130.563 55.9228L132.29 61.9499L133.154 74.3585C133.01 74.4767 132.29 75.7767 130.563 74.7131C128.351 73.351 123.654 60.5318 125.381 54.5047Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.740522"
                      />
                      <path
                        id="Vector 5"
                        d="M207.572 54.5047C206.191 49.6831 203.542 53.4411 202.391 55.9228L200.664 61.9499L199.8 74.3585C199.944 74.4767 200.664 75.7767 202.391 74.7131C204.603 73.351 209.3 60.5318 207.572 54.5047Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.740522"
                      />
                    </g>
                    <g id="Vector_3">
                      <path
                        d="M135.121 23.5495C142.292 7.89679 162.739 7.04243 165.737 7.00187C168.735 7.04243 189.182 7.89679 196.353 23.5495C202.161 36.2283 205.483 62.9956 196.353 86.1473C191.714 97.9102 184.18 103.056 179.016 105.574C173.852 108.092 166.106 108.452 166.106 108.452C166.106 108.452 157.253 108.452 152.458 105.574C147.662 102.697 139.759 97.9102 135.121 86.1473C125.991 62.9956 129.312 36.2283 135.121 23.5495Z"
                        fill="white"
                      />
                      <path
                        d="M166.106 7.0006C166.106 7.0006 142.867 6.64077 135.121 23.5495C129.312 36.2283 125.991 62.9956 135.121 86.1473C139.759 97.9102 147.662 102.697 152.458 105.574C157.253 108.452 166.106 108.452 166.106 108.452C166.106 108.452 173.852 108.092 179.016 105.574C184.18 103.056 191.714 97.9102 196.353 86.1473C205.483 62.9956 202.161 36.2283 196.353 23.5495C188.607 6.64077 165.368 7.0006 165.368 7.0006"
                        stroke="black"
                        stroke-width="0.740522"
                        stroke-linecap="round"
                      />
                    </g>
                  </g>
                </g>
                {findMeasure('collar') && (
                  <g id="collar">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M185 112C185 112 175.265 113.706 162.938 114.224M145 112C145 112 147 112.626 158.014 114.224"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                          <path
                            id="Vector 27"
                            d="M165.072 112.024L161.978 114.104L164.636 116.1"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                          <path
                            id="Vector 28"
                            d="M155.324 112.024L158.417 114.104L155.76 116.1"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('neck') && (
                  <g id="neck">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M185 112C185 112 175.265 113.706 162.938 114.224M145 112C145 112 147 112.626 158.014 114.224"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                          <path
                            id="Vector 27_2"
                            d="M165.072 112.024L161.978 114.104L164.636 116.1"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                          <path
                            id="Vector 28_2"
                            d="M155.324 112.024L158.417 114.104L155.76 116.1"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('headCircumference') && (
                  <g id="head_circumference">
                    <g id="Group 221_3">
                      <g id="Group 217_3">
                        <g id="Group 220_3">
                          <path
                            id="Ellipse 23_3"
                            d="M198.5 29.5C198.5 29.5 181.606 34.5632 161.421 33.6936M133 29.5C133 29.5 148.241 33.6936 156.931 33.6936"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                          <path
                            id="Vector 27_3"
                            d="M163.365 31.6881L160.545 33.5838L162.968 35.4037"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                          <path
                            id="Vector 28_3"
                            d="M154.478 31.6881L157.298 33.5838L154.876 35.4037"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_1730"
              x1="157.961"
              y1="83.2739"
              x2="157.961"
              y2="127.017"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className=" h-full m-auto "
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_upper_body">
            <mask id="mask0_128_2420" maskUnits="userSpaceOnUse" x="0" y="0" width="342" height="291">
              <rect id="rect" width="342" height="291" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_2420)">
              <g id="group_upper_body_mask">
                <g id="child">
                  <g id="Group 38">
                    <g id="Group 37">
                      <path
                        id="Vector"
                        d="M140.355 176.18C144.419 163.986 144.789 146.804 144.789 146.804H147.006H195.78H197.997C197.997 146.804 198.367 163.986 202.431 176.18C205.387 185.048 217.396 186.711 236.241 192.807C254.66 198.767 266.724 196.687 274.484 217.749C282.244 238.81 303.859 304.767 307.185 319.177C309.845 330.706 313.466 347.259 314.944 354.095C317.9 358.899 325.143 377.706 330.464 414.509C335.784 451.311 336.745 495.614 336.56 513.166C337.484 523.881 337.651 549.2 337.651 549.2L336.56 559.412C336.56 559.412 333.789 568.48 327.138 576.905C318.824 587.436 304.968 601.292 303.305 597.412C301.975 594.309 303.859 590.207 304.968 588.544C303.12 590.207 299.315 592.867 298.871 590.207C298.317 586.882 294.991 587.436 303.305 579.676C311.619 571.917 314.944 565.266 314.944 556.398C314.944 549.303 308.293 537.553 304.968 532.565C303.12 533.673 298.317 537.331 293.883 543.096C288.34 550.301 286.123 556.952 282.798 553.626C279.472 550.301 279.472 548.084 285.569 538.107C291.666 528.131 299.98 510.395 304.968 507.069C308.959 504.409 311.434 502.635 312.173 502.081C311.249 495.06 308.958 479.024 307.185 471.043C304.968 461.066 296.1 447.764 290.003 426.148C285.126 408.855 279.842 375.711 277.81 361.3C270.974 343.195 257.08 305.875 256.194 301.441C255.307 297.007 251.021 280.01 248.988 272.066L245.663 280.379V375.157C246.771 381.623 248.656 398.325 247.326 413.4C245.995 428.476 246.771 434.831 247.326 436.125C249.912 445.916 255.085 468.493 255.085 480.465V510.949C258.965 525.175 266.724 557.284 266.724 571.917C266.724 590.207 266.17 625.125 265.062 640.644C264.175 653.059 262.106 667.987 261.182 673.899H197.443C193.563 647.665 185.471 592.092 184.141 579.676C182.478 564.157 183.032 556.952 181.369 553.626C179.707 550.301 176.935 546.975 173.61 546.975H169.176C165.85 546.975 163.079 550.301 161.416 553.626C159.754 556.952 160.308 564.157 158.645 579.676C157.315 592.092 149.223 647.665 145.343 673.899H81.6038C80.6801 667.987 78.6108 653.059 77.724 640.644C76.6155 625.125 76.0613 590.207 76.0613 571.917C76.0613 557.284 83.8208 525.175 87.7006 510.949V480.465C87.7006 468.493 92.8736 445.916 95.4602 436.125C96.0144 434.831 96.7904 428.476 95.4602 413.4C94.13 398.325 96.0144 381.623 97.1229 375.157V280.379L93.7974 272.066C91.7651 280.01 87.4789 297.007 86.5921 301.441C85.7053 305.875 71.812 343.195 64.9762 361.3C62.944 375.711 57.6601 408.855 52.7826 426.148C46.6859 447.764 37.8178 461.066 35.6008 471.043C33.8272 479.024 31.5363 495.06 30.6125 502.081C31.3515 502.635 33.8272 504.409 37.8178 507.069C42.8061 510.395 51.1199 528.131 57.2167 538.107C63.3135 548.084 63.3135 550.301 59.9879 553.626C56.6624 556.952 54.4454 550.301 48.9029 543.096C44.4688 537.331 39.6653 533.673 37.8178 532.565C34.4923 537.553 27.8413 549.303 27.8413 556.398C27.8413 565.266 31.1668 571.917 39.4806 579.676C47.7944 587.436 44.4688 586.882 43.9146 590.207C43.4712 592.867 39.6653 590.207 37.8178 588.544C38.9263 590.207 40.8108 594.309 39.4806 597.412C37.8178 601.292 23.9615 587.436 15.6477 576.905C8.99663 568.48 5.7403 559.354 6.66405 557.506C6.29454 555.659 4.88587 555.289 5.55565 550.301C6.16315 545.776 4.63191 523.881 5.55565 513.166C5.3709 495.614 7.00135 451.311 12.3222 414.509C17.643 377.706 24.8853 358.899 27.8413 354.095C29.3193 347.259 32.9404 330.706 35.6008 319.177C38.9263 304.767 60.5422 238.81 68.3018 217.749C76.0613 196.687 88.126 198.767 106.545 192.807C125.39 186.711 137.399 185.048 140.355 176.18Z"
                        fill="white"
                      />
                      <g id="Group 36">
                        <g id="Group 35">
                          <path
                            id="Vector 21"
                            d="M175.71 173.408C191.915 166.535 200.097 151.792 202.869 147.358V143.478C196.218 139.044 182.694 130.176 181.807 130.176C180.92 130.176 152.986 137.936 139.13 141.815L144.46 157.813L141.671 168.97C152.756 176.176 165.423 177.771 175.71 173.408Z"
                            fill="url(#paint0_linear_128_2420)"
                          />
                        </g>
                        <g id="Group 33">
                          <path
                            id="Vector 17"
                            d="M168.296 282.043C168.296 282.043 167.737 285.592 167.726 288.32C167.717 290.706 167.843 293.368 167.843 293.368"
                            stroke="black"
                            stroke-width="1.1085"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <g id="Group 27">
                            <path
                              id="Vector 15"
                              d="M165.733 211.098C165.733 211.098 164.551 207.731 161.299 205.761C158.047 203.79 147.628 203.708 142.455 200.013"
                              stroke="black"
                              stroke-width="1.1085"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              id="Vector 14"
                              d="M174.601 211.098C174.601 211.098 175.783 207.731 179.035 205.761C182.286 203.79 192.706 203.708 197.879 200.013"
                              stroke="black"
                              stroke-width="1.1085"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                  </g>
                  <path
                    id="Vector_2"
                    d="M173.609 546.975H169.175C165.849 546.975 163.078 550.301 161.415 553.626C159.752 556.952 160.307 564.157 158.644 579.676C157.314 592.092 149.222 647.665 145.342 673.899H81.6026C80.6789 667.987 78.6097 653.059 77.7228 640.644C76.6143 625.125 76.0601 590.207 76.0601 571.917C76.0601 557.284 83.8196 525.175 87.6994 510.949V480.465C87.6994 468.493 92.8724 445.916 95.459 436.125C96.0132 434.831 96.7892 428.476 95.459 413.4C94.1288 398.325 96.0132 381.623 97.1217 375.157V280.379L93.7962 272.066C91.7639 280.01 87.4777 297.007 86.5909 301.441C85.7041 305.875 71.8108 343.195 64.975 361.3C62.9428 375.711 57.6589 408.855 52.7815 426.148C46.6847 447.764 37.8166 461.066 35.5996 471.043C33.8261 479.024 31.5351 495.06 30.6113 502.081C31.3503 502.635 33.826 504.409 37.8166 507.069C42.8049 510.395 51.1187 528.131 57.2155 538.107C63.3123 548.084 63.3123 550.301 59.9867 553.626C56.6612 556.952 54.4442 550.301 48.9017 543.096C44.4677 537.331 39.6641 533.673 37.8166 532.565C34.4911 537.553 27.8401 549.303 27.8401 556.398C27.8401 565.266 31.1656 571.917 39.4794 579.676C47.7932 587.436 44.4677 586.882 43.9134 590.207C43.47 592.867 39.6641 590.207 37.8166 588.544C38.9251 590.207 40.8096 594.309 39.4794 597.412C37.8166 601.292 23.9603 587.436 15.6465 576.905C8.99545 568.48 7.21702 560.832 7.21702 560.832C7.21702 560.832 5.99286 556.398 5.55426 550.301C5.22669 545.747 5.30048 523.881 6.22422 513.166C6.03947 495.614 7.00018 451.311 12.321 414.509C17.6418 377.706 24.8841 358.899 27.8401 354.095C29.3181 347.259 32.9392 330.706 35.5996 319.177C38.9252 304.767 60.541 238.81 68.3006 217.749C76.0601 196.687 88.1248 198.767 106.544 192.807C125.389 186.711 137.397 185.048 140.353 176.18C144.418 163.986 144.787 146.804 144.787 146.804H195.779M173.609 546.975C176.934 546.975 179.705 550.301 181.368 553.626C183.031 556.952 182.477 564.157 184.139 579.676C185.47 592.092 193.169 646.59 197.049 672.824L261.011 673.362C261.935 667.45 264.174 653.059 265.06 640.644C266.169 625.125 266.723 590.207 266.723 571.917C266.723 557.284 258.964 525.175 255.084 510.949V480.465C255.084 468.493 249.911 445.916 247.324 436.125C246.77 434.831 245.994 428.476 247.324 413.4C248.655 398.325 246.77 381.623 245.662 375.157V280.379L248.987 272.066C251.019 280.01 255.306 297.007 256.192 301.441C257.079 305.875 270.972 343.195 277.808 361.3C279.841 375.711 285.124 408.855 290.002 426.148C296.099 447.764 304.967 461.066 307.184 471.043C308.957 479.024 311.248 495.06 312.172 502.081C311.433 502.635 308.957 504.409 304.967 507.069C299.978 510.395 291.665 528.131 285.568 538.107C279.471 548.084 279.471 550.301 282.797 553.626C286.122 556.952 288.339 550.301 293.882 543.096C298.316 537.331 303.119 533.673 304.967 532.565C308.292 537.553 314.943 549.303 314.943 556.398C314.943 565.266 311.618 571.917 303.304 579.676C294.99 587.436 298.316 586.882 298.87 590.207C299.313 592.867 303.119 590.207 304.967 588.544C303.858 590.207 301.974 594.309 303.304 597.412C304.967 601.292 318.823 587.436 327.137 576.905C333.788 568.48 336.005 560.832 336.559 558.06C338.66 550.855 337.483 523.881 336.559 513.166C336.744 495.614 335.783 451.311 330.462 414.509C325.142 377.706 317.899 358.899 314.943 354.095C313.465 347.259 309.844 330.706 307.184 319.177C303.858 304.767 282.242 238.81 274.483 217.749C266.723 196.687 254.658 198.767 236.239 192.807C217.395 186.711 205.386 185.048 202.43 176.18C198.365 163.986 197.996 146.804 197.996 146.804H147.005M173.609 546.975H168.62"
                    stroke="black"
                    stroke-width="1.1085"
                    stroke-linecap="round"
                  />
                  <g id="Group 31">
                    <g id="Group 2">
                      <path
                        id="Vector 4"
                        d="M111.976 87.1107C114.044 79.8931 118.008 85.5186 119.732 89.2336L122.317 98.2556L123.61 116.83C123.395 117.007 122.317 118.953 119.732 117.361C116.421 115.322 109.39 96.1328 111.976 87.1107Z"
                        fill="white"
                        stroke="black"
                        stroke-width="1.1085"
                      />
                      <path
                        id="Vector 5"
                        d="M235.01 87.1107C232.941 79.8931 228.977 85.5186 227.254 89.2336L224.668 98.2556L223.376 116.83C223.591 117.007 224.668 118.953 227.254 117.361C230.564 115.322 237.595 96.1328 235.01 87.1107Z"
                        fill="white"
                        stroke="black"
                        stroke-width="1.1085"
                      />
                    </g>
                    <g id="Vector_3">
                      <path
                        d="M126.555 40.7733C137.289 17.3424 167.897 16.0635 172.385 16.0028C176.873 16.0635 207.48 17.3424 218.215 40.7733C226.91 59.7525 231.882 99.8212 218.215 134.477C211.271 152.086 199.993 159.788 192.263 163.558C184.532 167.328 172.937 167.865 172.937 167.865C172.937 167.865 159.685 167.865 152.507 163.558C145.328 159.251 133.499 152.086 126.555 134.477C112.888 99.8212 117.86 59.7525 126.555 40.7733Z"
                        fill="white"
                      />
                      <path
                        d="M172.937 16.0009C172.937 16.0009 138.15 15.4623 126.555 40.7733C117.86 59.7525 112.888 99.8212 126.555 134.477C133.499 152.086 145.328 159.251 152.507 163.558C159.685 167.865 172.937 167.865 172.937 167.865C172.937 167.865 184.532 167.328 192.263 163.558C199.993 159.788 211.271 152.086 218.215 134.477C231.882 99.8212 226.91 59.7525 218.215 40.7733C206.619 15.4623 171.833 16.0009 171.833 16.0009"
                        stroke="black"
                        stroke-width="1.1085"
                        stroke-linecap="round"
                      />
                    </g>
                  </g>
                </g>
                {findMeasure('collar') && (
                  <g id="collar">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M201.22 173.177C201.22 173.177 186.648 175.73 168.196 176.506M141.344 173.177C141.344 173.177 144.337 174.114 160.824 176.506"
                            stroke="#E55959"
                            stroke-width="2.24538"
                          />
                          <path
                            id="Vector 27"
                            d="M171.389 173.213L166.759 176.326L170.737 179.314"
                            stroke="#E55959"
                            stroke-width="2.24538"
                          />
                          <path
                            id="Vector 28"
                            d="M156.798 173.213L161.428 176.326L157.45 179.314"
                            stroke="#E55959"
                            stroke-width="2.24538"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('neck') && (
                  <g id="neck">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M201.22 173.177C201.22 173.177 186.648 175.73 168.196 176.506M141.344 173.177C141.344 173.177 144.337 174.114 160.824 176.506"
                            stroke="#E55959"
                            stroke-width="2.24538"
                          />
                          <path
                            id="Vector 27_2"
                            d="M171.389 173.213L166.759 176.326L170.737 179.314"
                            stroke="#E55959"
                            stroke-width="2.24538"
                          />
                          <path
                            id="Vector 28_2"
                            d="M156.798 173.213L161.428 176.326L157.45 179.314"
                            stroke="#E55959"
                            stroke-width="2.24538"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('headCircumference') && (
                  <g id="head_circumference">
                    <g id="Group 221_3">
                      <g id="Group 217_3">
                        <g id="Group 220_3">
                          <path
                            id="Ellipse 23_3"
                            d="M221.429 49.6807C221.429 49.6807 196.139 57.2598 165.924 55.9581M123.38 49.6807C123.38 49.6807 146.194 55.9581 159.203 55.9581"
                            stroke="#E55959"
                            stroke-width="2.24538"
                          />
                          <path
                            id="Vector 27_3"
                            d="M168.835 52.956L164.613 55.7937L168.24 58.5179"
                            stroke="#E55959"
                            stroke-width="2.24538"
                          />
                          <path
                            id="Vector 28_3"
                            d="M155.531 52.956L159.753 55.7937L156.126 58.5179"
                            stroke="#E55959"
                            stroke-width="2.24538"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_2420"
              x1="160.745"
              y1="130.176"
              x2="160.745"
              y2="195.656"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
