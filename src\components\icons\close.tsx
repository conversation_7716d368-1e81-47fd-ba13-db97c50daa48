interface CloseProps {
  className?: string;
}

function Close({ className }: CloseProps) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M14.1666 5.83334L5.83331 14.1667M5.83331 5.83334L14.1666 14.1667"
        stroke="#454545"
        stroke-width="1.66667"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
}

export default Close;
