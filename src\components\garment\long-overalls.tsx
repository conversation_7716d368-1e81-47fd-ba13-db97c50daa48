import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface LongOverallsProps {
  measure: GarmentMeasure;
  className?: string;
}

export function LongOveralls({ measure, className }: LongOverallsProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_long_overalls">
            <g id="long_overalls">
              <path
                id="Vector"
                d="M160.426 26.1138L163.165 22.5754L168.594 22.4367L173.537 22.7192L176.296 26.1138L186.155 28.9718C186.474 29.5162 188.818 31.9206 189.783 38.4983C190.731 44.9541 192.437 53.2092 192.802 56.793C193.715 61.2841 195.219 71.2695 195.876 73.8825C196.534 76.4955 196.88 82.411 196.972 85.0421L189.496 86.1224C189.496 86.1224 188.777 82.9594 187.483 77.1487C186.457 72.5416 185.018 64.3106 184.744 60.6815L182.458 49.1136V64.0838C183.142 68.8017 184.959 74.7025 185.333 81.6398C187.483 121.49 187.483 138.599 188.202 170.804C182.163 170.085 177.85 170.372 172.387 170.804L168.793 90.4355H167.93L164.336 170.804C158.872 170.372 154.559 170.085 148.521 170.804C148.521 138.024 149.24 117.752 151.389 81.6398C151.802 74.7047 153.58 68.8017 154.265 64.0838V49.1136L151.978 60.6815C151.704 64.3106 150.266 72.5416 149.24 77.1487C147.946 82.9594 147.227 86.1224 147.227 86.1224L139.751 85.0421C139.842 82.411 140.189 76.4955 140.846 73.8825C141.503 71.2695 143.007 61.2841 143.92 56.793C144.285 53.2092 145.992 44.9541 146.939 38.4983C147.905 31.9206 150.248 29.5162 150.568 28.9718L160.426 26.1138Z"
                fill="white"
              />
              <path
                id="Vector 44"
                d="M156.97 127.48C158.343 126.642 160.987 123.658 161.14 123.502C161.148 123.864 160.79 125.803 160.79 125.803C160.724 125.914 159.436 127.079 157.403 127.906C155.777 128.567 153.237 129.043 152.522 129.011C153.307 128.89 155.723 128.241 156.97 127.48Z"
                fill="url(#paint0_linear_208_4970)"
                fill-opacity="0.87"
              />
              <path
                id="Vector 45"
                d="M178.833 127.48C177.46 126.642 174.816 123.658 174.663 123.502C174.655 123.864 175.013 125.803 175.013 125.803C175.079 125.914 176.367 127.079 178.4 127.906C180.026 128.567 182.566 129.043 183.281 129.011C182.496 128.89 180.08 128.241 178.833 127.48Z"
                fill="url(#paint1_linear_208_4970)"
                fill-opacity="0.87"
              />
              <path
                id="Vector 43"
                d="M169.909 60.1951C171.282 59.3571 173.927 56.3733 174.079 56.2178C174.088 56.5794 173.73 58.5183 173.73 58.5183C173.664 58.6297 172.375 59.7939 170.343 60.6209C168.716 61.2826 166.176 61.7587 165.462 61.7264C166.247 61.605 168.663 60.956 169.909 60.1951Z"
                fill="url(#paint2_linear_208_4970)"
                fill-opacity="0.87"
              />
              <path
                id="Vector_2"
                d="M168.793 22.4316L163.165 22.5754L160.426 26.1138L150.568 28.9718C150.248 29.5162 147.905 31.9206 146.939 38.4983C145.992 44.9541 144.285 53.2092 143.92 56.793C143.007 61.2841 141.503 71.2695 140.846 73.8825C140.189 76.4955 139.842 82.411 139.751 85.0421L147.227 86.1224C147.227 86.1224 147.946 82.9594 149.24 77.1487C150.266 72.5416 151.704 64.3106 151.978 60.6815L154.265 49.1136V64.0838C153.58 68.8017 151.769 74.7028 151.389 81.6398C149.24 120.915 148.521 138.024 148.521 170.804C155.134 170.804 158.872 170.804 164.336 170.804L167.93 90.4355H168.793L172.387 170.804C178.138 170.804 182.307 170.804 188.202 170.804C187.483 138.599 187.483 123.072 185.333 81.6398C184.973 74.7017 183.142 68.8017 182.458 64.0838V49.1136L184.744 60.6815C185.018 64.3106 186.457 72.5416 187.483 77.1487C188.777 82.9594 189.496 86.1224 189.496 86.1224L196.972 85.0421C196.88 82.411 196.534 76.4955 195.876 73.8825C195.219 71.2695 193.715 61.2841 192.802 56.793C192.437 53.2092 190.731 44.9541 189.783 38.4983C188.818 31.9206 186.474 29.5162 186.155 28.9718L176.296 26.1138L173.537 22.7192L168.505 22.4316"
                stroke="black"
                stroke-width="0.287543"
              />
              <g id="Group 170">
                <path
                  id="Vector 260"
                  d="M148.665 168.647C149.585 168.647 159.4 168.647 164.192 168.647"
                  stroke="black"
                  stroke-width="0.287543"
                  stroke-dasharray="0.58 0.58"
                />
                <path
                  id="Vector 261"
                  d="M172.531 168.647C173.451 168.647 183.266 168.647 188.058 168.647"
                  stroke="black"
                  stroke-width="0.287543"
                  stroke-dasharray="0.58 0.58"
                />
                <g id="Group 169">
                  <path id="Vector 262" d="M140.038 81.0898L147.946 82.5276" stroke="black" stroke-width="0.287543" />
                  <path id="Vector 263" d="M196.684 81.665L188.777 82.959" stroke="black" stroke-width="0.287543" />
                  <path
                    id="Vector 265"
                    d="M163.186 22.7188C163.636 23.8648 164.575 26.5888 165.198 27.8067M165.198 27.8067C165.305 28.0148 165.402 28.1916 165.486 28.3258C166.061 29.246 167.642 31.2013 168.361 32.0639L167.499 33.0703L164.336 32.2077L164.871 29.476M165.198 27.8067L164.871 29.476M164.871 29.476L160.454 29.6198L160.166 26.313"
                    stroke="black"
                    stroke-width="0.287543"
                  />
                  <path
                    id="Vector 266"
                    d="M176.413 26.1693C176.413 26.3993 176.365 28.5655 176.269 29.7636L172.035 29.3322L171.668 27.5018L172.387 32.0639L167.499 33.7892L167.355 33.2141C168.218 32.2077 169.671 30.8279 170.805 29.0447C172.25 26.7743 172.914 24.3002 173.681 22.7188L173.613 22.8625L176.269 26.1693H176.413Z"
                    stroke="black"
                    stroke-width="0.287543"
                  />
                  <g id="Group 168">
                    <g id="Group 167">
                      <path
                        id="Vector 271"
                        d="M169.799 33.0703C169.751 47.9267 170.081 77.1745 169.799 77.927C169.368 79.0772 168.218 79.0772 167.642 79.5085"
                        stroke="black"
                        stroke-width="0.287543"
                        stroke-dasharray="0.58 0.58"
                      />
                      <path
                        id="Vector 270"
                        d="M154.415 64.269C154.847 64.5566 162.817 65.1088 168.217 65.1317C174.01 65.1562 178.473 64.7962 182.451 64.4128"
                        stroke="black"
                        stroke-width="0.287543"
                        stroke-dasharray="0.58 0.58"
                      />
                    </g>
                    <path
                      id="Vector 267"
                      d="M167.355 33.0703C167.259 48.5497 167.412 79.7098 167.642 83.3903C167.873 87.0709 168.361 90.4351 168.361 90.4351"
                      stroke="black"
                      stroke-width="0.287543"
                    />
                    <path
                      id="Vector 264"
                      d="M153.84 67.4316C155.614 68.0067 161.604 68.8694 168.361 68.8694C175.378 68.8694 180.821 68.0547 183.026 67.5754"
                      stroke="black"
                      stroke-width="0.287543"
                    />
                    <g id="Group 163">
                      <path
                        id="Vector 268"
                        d="M150.965 89.2849H160.885L162.323 68.7256"
                        stroke="black"
                        stroke-width="0.287543"
                      />
                      <path
                        id="Vector 269"
                        d="M151.54 79.0772C151.77 78.6172 155.23 71.1219 156.572 68.0068"
                        stroke="black"
                        stroke-width="0.287543"
                      />
                    </g>
                    <g id="Group 164">
                      <path
                        id="Vector 268_2"
                        d="M185.614 89.2849H175.694L174.256 68.7256"
                        stroke="black"
                        stroke-width="0.287543"
                      />
                      <path
                        id="Vector 269_2"
                        d="M185.039 78.5022C184.809 78.0421 181.349 71.1219 180.007 68.0068"
                        stroke="black"
                        stroke-width="0.287543"
                      />
                    </g>
                    <g id="Group 165">
                      <path
                        id="Vector 273"
                        d="M155.853 39.8276C156.083 40.0577 159.016 41.1695 160.454 41.6967L165.63 39.8276"
                        stroke="black"
                        stroke-width="0.287543"
                      />
                      <path
                        id="Vector 274"
                        d="M158.729 40.9775C158.729 41.0926 158.681 46.2012 158.729 48.7412"
                        stroke="black"
                        stroke-width="0.287543"
                      />
                      <path
                        id="Vector 275"
                        d="M162.754 40.9775C162.754 41.0926 162.706 46.2012 162.754 48.7412"
                        stroke="black"
                        stroke-width="0.287543"
                      />
                      <path
                        id="Vector 272"
                        d="M165.63 37.3833H155.853V47.1598L158.297 48.7412H163.617L165.63 47.4473V37.3833Z"
                        stroke="black"
                        stroke-width="0.287543"
                      />
                    </g>
                    <g id="Group 166">
                      <path
                        id="Vector 273_2"
                        d="M181.445 39.8276C181.214 40.0577 178.282 41.1695 176.844 41.6967L171.668 39.8276"
                        stroke="black"
                        stroke-width="0.287543"
                      />
                      <path
                        id="Vector 274_2"
                        d="M178.569 40.9775C178.569 41.0926 178.617 46.2012 178.569 48.7412"
                        stroke="black"
                        stroke-width="0.287543"
                      />
                      <path
                        id="Vector 275_2"
                        d="M174.544 40.9775C174.544 41.0926 174.591 46.2012 174.544 48.7412"
                        stroke="black"
                        stroke-width="0.287543"
                      />
                      <path
                        id="Vector 272_2"
                        d="M171.668 37.3833H181.444V47.1598L179 48.7412H173.681L171.668 47.4473V37.3833Z"
                        stroke="black"
                        stroke-width="0.287543"
                      />
                    </g>
                  </g>
                  <path
                    id="Vector 276"
                    d="M150.677 29.188C151.971 32.0634 153.646 39.1327 154.128 45.8655C154.334 48.7409 154.32 52.0956 154.272 54.2042"
                    stroke="black"
                    stroke-width="0.287543"
                  />
                  <path
                    id="Vector 277"
                    d="M186.045 29.188C184.751 32.0634 183.076 39.1327 182.595 45.8655C182.389 48.7409 182.403 52.0956 182.451 54.2042"
                    stroke="black"
                    stroke-width="0.287543"
                  />
                </g>
              </g>
              <path
                id="Vector 41"
                d="M148.444 59.1306C149.257 58.713 151.13 56.892 151.235 56.7993C151.157 57.069 150.558 58.4553 150.558 58.4553C150.503 58.5281 149.655 59.2004 148.545 59.5113C147.657 59.7601 146.395 59.7349 146.078 59.6044C146.462 59.6313 147.706 59.5097 148.444 59.1306Z"
                fill="url(#paint3_linear_208_4970)"
                fill-opacity="0.87"
              />
              <path
                id="Vector 42"
                d="M189.123 59.1306C188.311 58.713 186.437 56.892 186.333 56.7993C186.41 57.069 187.009 58.4553 187.009 58.4553C187.065 58.5281 187.912 59.2004 189.022 59.5113C189.91 59.7601 191.172 59.7349 191.49 59.6044C191.106 59.6313 189.861 59.5097 189.123 59.1306Z"
                fill="url(#paint4_linear_208_4970)"
                fill-opacity="0.87"
              />
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 21"
                  d="M180.279 44.9264L155.59 44.9199"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M157.217 42.7119L154.592 44.9645L156.896 47.3116"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27"
                  d="M179.751 47.4279L181.895 44.7139L179.184 42.8521"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 21_2"
                  d="M183.026 79.7036L153.4 79.7036"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M155.027 77.4956L152.403 79.7482L154.706 82.0953"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_2"
                  d="M182.299 82.2116L184.443 79.4976L181.732 77.6357"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 21_3"
                  d="M180.294 53.8252L156.276 53.8252"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M157.903 51.6172L155.278 53.8698L157.582 56.2168"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_3"
                  d="M179.136 56.3332L181.28 53.6192L178.569 51.7573"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_thigh_width') && (
              <g id="product_thigh_width">
                <path
                  id="Vector 21_4"
                  d="M165.917 93.1666L152.834 92.5916"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M154.441 90.3079L151.684 92.3959L153.839 94.8793"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_4"
                  d="M164.47 95.6122L166.776 93.0344L164.184 91.0103"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_sleeve') && (
              <g id="product_sleeve">
                <path
                  id="Vector 21_5"
                  d="M146.728 30.3629L136.986 82.168"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_5"
                  d="M134.875 80.5079L136.571 83.5219L139.323 81.721"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_5"
                  d="M148.836 31.7032L147.055 28.7383L144.355 30.6158"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_lower_length') && (
              <g id="product_lower_length">
                <path
                  id="Vector 21_6"
                  d="M187.435 69.2985L191.365 169.51"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_6"
                  d="M189.003 168.407L191.679 170.598L193.588 167.921"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_6"
                  d="M189.793 70.1192L187.38 67.6416L185.183 70.0882"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_inside_leg') && (
              <g id="product_inside_leg">
                <path
                  id="Vector 21_7"
                  d="M173.357 92.302L175.694 167.641"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_7"
                  d="M173.341 166.651L175.68 169.199L177.949 166.818"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_7"
                  d="M175.716 93.1226L173.302 90.645L171.105 93.0916"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 21_8"
                  d="M167.894 24.729L167.894 169.797"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_8"
                  d="M165.511 168.226L167.838 170.784L170.118 168.414"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_8"
                  d="M170.252 25.5498L167.839 23.0722L165.642 25.5189"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_fullbody_length') && (
              <g id="product_fullbody_length">
                <path
                  id="Vector 21_9"
                  d="M167.894 24.729L167.894 169.797"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_9"
                  d="M165.511 168.226L167.838 170.784L170.118 168.414"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_9"
                  d="M170.252 25.5498L167.839 23.0722L165.642 25.5189"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 19"
                  d="M152.403 30.4824L184.032 30.4824"
                  stroke="#EDA7A7"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                  stroke-dasharray="2.88 2.88"
                />
                <path
                  id="Vector 27_10"
                  d="M182.9 32.9364L185.323 30.4682L182.828 28.3261"
                  stroke="#EDA7A7"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 28"
                  d="M153.76 32.5961L151.347 30.3193L152.599 29.3227L153.85 28.3261"
                  stroke="#EDA7A7"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_back_hook') && (
              <g id="product_back_hook">
                <path
                  id="Vector 21_10"
                  d="M164.649 89.8603L164.649 70.7329"
                  stroke="#EDA7A7"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                  stroke-dasharray="2.88 2.88"
                />
                <path
                  id="Vector 26_10"
                  d="M166.96 72.1734L164.493 69.7491L162.349 72.2431"
                  stroke="#EDA7A7"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_11"
                  d="M162.357 88.7277L164.794 91.1819L166.968 88.7141"
                  stroke="#EDA7A7"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_hook') && (
              <g id="product_hook">
                <path
                  id="Vector 21_11"
                  d="M164.649 89.8603L164.649 70.7329"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_11"
                  d="M166.96 72.1734L164.493 69.7491L162.349 72.2431"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_12"
                  d="M162.357 88.7277L164.794 91.1819L166.968 88.7141"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_collar') && (
              <g id="product_collar">
                <path
                  id="Vector 464"
                  d="M166.117 30.6263C166.117 30.6263 162.235 25.8593 163.633 23.5369C165.03 21.2145 172.067 21.6423 172.721 24.0258C173.244 25.9326 171.544 28.9762 170.237 30.6263"
                  stroke="#E55959"
                  stroke-width="1.43771"
                />
                <path
                  id="Vector 465"
                  d="M170.165 27.387L170.242 30.791L173.474 30.4107"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 28_2"
                  d="M166.351 27.387L166.274 30.791L163.042 30.4107"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_thigh_circumference') && (
              <g id="product_thigh_circumference">
                <path
                  id="Vector 21_12"
                  d="M165.917 93.1666L152.834 92.5916"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_12"
                  d="M154.441 90.3079L151.684 92.3959L153.839 94.8793"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_13"
                  d="M164.47 95.6122L166.776 93.0344L164.184 91.0103"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 21_13"
                  d="M183.026 79.7036L153.4 79.7036"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_13"
                  d="M155.027 77.4956L152.403 79.7482L154.706 82.0953"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_14"
                  d="M182.299 82.2116L184.443 79.4976L181.732 77.6357"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 21_14"
                  d="M180.279 44.9264L155.59 44.9199"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_14"
                  d="M157.217 42.7119L154.592 44.9645L156.896 47.3116"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_15"
                  d="M179.751 47.4279L181.895 44.7139L179.184 42.8521"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 21_15"
                  d="M180.294 53.8252L156.276 53.8252"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_15"
                  d="M157.903 51.6172L155.278 53.8698L157.582 56.2168"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_16"
                  d="M179.136 56.3332L181.28 53.6192L178.569 51.7573"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_high_waist_width') && (
              <g id="product_high_waist_width">
                <path
                  id="Vector 21_16"
                  d="M180.294 53.8252L156.276 53.8252"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_16"
                  d="M157.903 51.6172L155.278 53.8698L157.582 56.2168"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_17"
                  d="M179.136 56.3332L181.28 53.6192L178.569 51.7573"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 21_17"
                  d="M168.258 24.729L168.258 169.797"
                  stroke="#EDA7A7"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                  stroke-dasharray="2.88 2.88"
                />
                <path
                  id="Vector 26_17"
                  d="M165.875 168.226L168.203 170.784L170.482 168.414"
                  stroke="#EDA7A7"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_18"
                  d="M170.617 25.5498L168.203 23.0722L166.006 25.5189"
                  stroke="#EDA7A7"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 21_18"
                  d="M159.345 29.0449L159.345 168.359"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_18"
                  d="M156.961 166.938L159.289 169.497L161.568 167.126"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_19"
                  d="M161.703 30.0879L159.29 27.6103L157.092 30.057"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_high_waist_circumference') && (
              <g id="product_high_waist_circumference">
                <path
                  id="Vector 21_19"
                  d="M181.445 70.5024L155.015 70.5024"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_19"
                  d="M156.642 68.2944L154.017 70.5471L156.321 72.8941"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_20"
                  d="M180.574 73.0105L182.718 70.2965L180.007 68.4346"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_circumference') && (
              <g id="product_lower_waist_circumference">
                <path
                  id="Vector 21_20"
                  d="M181.445 70.5024L155.015 70.5024"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_20"
                  d="M156.642 68.2944L154.017 70.5471L156.321 72.8941"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_21"
                  d="M180.574 73.0105L182.718 70.2965L180.007 68.4346"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_width') && (
              <g id="product_lower_waist_width">
                <path
                  id="Vector 21_21"
                  d="M181.445 70.5024L155.015 70.5024"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_21"
                  d="M156.642 68.2944L154.017 70.5471L156.321 72.8941"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_22"
                  d="M180.574 73.0105L182.718 70.2965L180.007 68.4346"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
            {findMeasure('product_biceps') && (
              <g id="product_biceps">
                <path
                  id="Vector 21_22"
                  d="M189.322 47.5101L184.191 48.67"
                  stroke="#E55959"
                  stroke-width="1.43771"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_22"
                  d="M185.041 45.8561L183.552 48.9778L186.608 50.1926"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
                <path
                  id="Vector 27_23"
                  d="M189.137 49.7823L190.587 46.6424L187.516 45.4656"
                  stroke="#E55959"
                  stroke-width="1.15017"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_208_4970"
              x1="160.566"
              y1="125.435"
              x2="159.931"
              y2="129.796"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_208_4970"
              x1="175.237"
              y1="125.435"
              x2="175.872"
              y2="129.796"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_208_4970"
              x1="173.505"
              y1="58.1507"
              x2="172.871"
              y2="62.5114"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_208_4970"
              x1="150.539"
              y1="58.1488"
              x2="148.798"
              y2="61.0047"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_208_4970"
              x1="187.029"
              y1="58.1488"
              x2="188.77"
              y2="61.0047"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_long_overalls">
            <g id="long_overalls">
              <path
                id="Vector"
                d="M162.679 34.4877L166.999 28.9069L175.561 28.6882L183.357 29.1337L187.709 34.4877L203.258 38.9952C203.761 39.8537 207.458 43.6459 208.98 54.0202C210.475 64.2022 213.166 77.222 213.742 82.8743C215.182 89.9575 217.554 105.706 218.59 109.828C219.627 113.949 220.174 123.278 220.318 127.428L208.527 129.132C208.527 129.132 207.393 124.143 205.352 114.979C203.734 107.713 201.465 94.7309 201.033 89.0071L197.427 70.7624V94.3732C198.506 101.814 201.372 111.121 201.962 122.062C205.352 184.913 205.352 211.897 206.486 262.69C196.962 261.556 190.16 262.01 181.543 262.69L175.874 135.935H174.514L168.845 262.69C160.228 262.01 153.426 261.556 143.902 262.69C143.902 210.99 145.036 179.018 148.426 122.062C149.077 111.124 151.882 101.814 152.961 94.3732V70.7624L149.355 89.0071C148.923 94.7309 146.654 107.713 145.036 114.979C142.995 124.143 141.861 129.132 141.861 129.132L130.07 127.428C130.214 123.278 130.761 113.949 131.798 109.828C132.834 105.706 135.206 89.9575 136.646 82.8743C137.222 77.222 139.913 64.2022 141.408 54.0202C142.93 43.6459 146.627 39.8537 147.131 38.9952L162.679 34.4877Z"
                fill="white"
              />
              <path
                id="Vector 44"
                d="M157.228 194.361C159.393 193.039 163.564 188.333 163.805 188.088C163.817 188.658 163.253 191.716 163.253 191.716C163.149 191.892 161.117 193.728 157.911 195.032C155.346 196.076 151.34 196.827 150.213 196.776C151.451 196.585 155.262 195.561 157.228 194.361Z"
                fill="url(#paint0_linear_207_3579)"
                fill-opacity="0.87"
              />
              <path
                id="Vector 45"
                d="M191.71 194.361C189.545 193.039 185.374 188.333 185.133 188.088C185.12 188.658 185.684 191.716 185.684 191.716C185.789 191.892 187.821 193.728 191.027 195.032C193.592 196.076 197.598 196.827 198.725 196.776C197.486 196.585 193.676 195.561 191.71 194.361Z"
                fill="url(#paint1_linear_207_3579)"
                fill-opacity="0.87"
              />
              <path
                id="Vector 43"
                d="M177.635 88.2403C179.801 86.9185 183.972 82.2126 184.212 81.9673C184.225 82.5376 183.661 85.5957 183.661 85.5957C183.557 85.7713 181.525 87.6075 178.319 88.9119C175.754 89.9554 171.748 90.7064 170.621 90.6554C171.859 90.464 175.67 89.4404 177.635 88.2403Z"
                fill="url(#paint2_linear_207_3579)"
                fill-opacity="0.87"
              />
              <path
                id="Vector_2"
                d="M175.874 28.6802L166.999 28.9069L162.679 34.4877L147.131 38.9952C146.627 39.8537 142.93 43.6459 141.408 54.0202C139.913 64.2022 137.222 77.222 136.646 82.8743C135.206 89.9575 132.834 105.706 131.798 109.828C130.761 113.949 130.214 123.278 130.07 127.428L141.861 129.132C141.861 129.132 142.995 124.143 145.036 114.979C146.654 107.713 148.923 94.7309 149.355 89.0071L152.961 70.7624V94.3732C151.882 101.814 149.025 111.121 148.426 122.062C145.036 184.006 143.902 210.99 143.902 262.69C154.333 262.69 160.228 262.69 168.845 262.69L174.514 135.935H175.874L181.543 262.69C190.613 262.69 197.189 262.69 206.486 262.69C205.352 211.897 205.352 187.408 201.962 122.062C201.394 111.12 198.506 101.814 197.427 94.3732V70.7624L201.033 89.0071C201.465 94.7309 203.734 107.713 205.352 114.979C207.393 124.143 208.527 129.132 208.527 129.132L220.318 127.428C220.174 123.278 219.627 113.949 218.59 109.828C217.554 105.706 215.182 89.9575 213.742 82.8743C213.166 77.222 210.475 64.2022 208.98 54.0202C207.458 43.6459 203.761 39.8537 203.258 38.9952L187.709 34.4877L183.357 29.1337L175.421 28.6802"
                stroke="black"
                stroke-width="0.453507"
              />
              <g id="Group 170">
                <path
                  id="Vector 260"
                  d="M144.129 259.289C145.58 259.289 161.06 259.289 168.618 259.289"
                  stroke="black"
                  stroke-width="0.453507"
                  stroke-dasharray="0.91 0.91"
                />
                <path
                  id="Vector 261"
                  d="M181.77 259.289C183.221 259.289 198.701 259.289 206.259 259.289"
                  stroke="black"
                  stroke-width="0.453507"
                  stroke-dasharray="0.91 0.91"
                />
                <g id="Group 169">
                  <path id="Vector 262" d="M130.524 121.196L142.995 123.463" stroke="black" stroke-width="0.453507" />
                  <path id="Vector 263" d="M219.865 122.103L207.393 124.143" stroke="black" stroke-width="0.453507" />
                  <path
                    id="Vector 265"
                    d="M167.031 29.1338C167.741 30.9413 169.223 35.2375 170.205 37.1585M170.205 37.1585C170.373 37.4867 170.527 37.7654 170.659 37.9772C171.566 39.4284 174.06 42.5123 175.194 43.8728L173.834 45.4601L168.845 44.0995L169.689 39.7912M170.205 37.1585L169.689 39.7912M169.689 39.7912L162.723 40.018L162.269 34.8026"
                    stroke="black"
                    stroke-width="0.453507"
                  />
                  <path
                    id="Vector 266"
                    d="M187.892 34.5759C187.892 34.9387 187.817 38.3551 187.665 40.2447L180.987 39.5645L180.409 36.6775L181.543 43.8728L173.834 46.5938L173.607 45.6868C174.967 44.0995 177.259 41.9234 179.049 39.111C181.327 35.5302 182.375 31.6281 183.584 29.1338L183.476 29.3605L187.665 34.5759H187.892Z"
                    stroke="black"
                    stroke-width="0.453507"
                  />
                  <g id="Group 168">
                    <g id="Group 167">
                      <path
                        id="Vector 271"
                        d="M177.462 45.46C177.386 68.8912 177.907 115.02 177.462 116.207C176.781 118.021 174.967 118.021 174.06 118.701"
                        stroke="black"
                        stroke-width="0.453507"
                        stroke-dasharray="0.91 0.91"
                      />
                      <path
                        id="Vector 270"
                        d="M153.199 94.6655C153.879 95.119 166.45 95.99 174.967 96.026C184.104 96.0647 191.142 95.497 197.416 94.8923"
                        stroke="black"
                        stroke-width="0.453507"
                        stroke-dasharray="0.91 0.91"
                      />
                    </g>
                    <path
                      id="Vector 267"
                      d="M173.607 45.46C173.456 69.8738 173.697 119.019 174.06 124.824C174.423 130.629 175.194 135.935 175.194 135.935"
                      stroke="black"
                      stroke-width="0.453507"
                    />
                    <path
                      id="Vector 264"
                      d="M152.292 99.6543C155.088 100.561 164.537 101.922 175.194 101.922C186.261 101.922 194.846 100.637 198.323 99.8811"
                      stroke="black"
                      stroke-width="0.453507"
                    />
                    <g id="Group 163">
                      <path
                        id="Vector 268"
                        d="M147.757 134.121H163.403L165.67 101.695"
                        stroke="black"
                        stroke-width="0.453507"
                      />
                      <path
                        id="Vector 269"
                        d="M148.664 118.021C149.027 117.295 154.484 105.474 156.6 100.561"
                        stroke="black"
                        stroke-width="0.453507"
                      />
                    </g>
                    <g id="Group 164">
                      <path
                        id="Vector 268_2"
                        d="M202.404 134.121H186.758L184.491 101.695"
                        stroke="black"
                        stroke-width="0.453507"
                      />
                      <path
                        id="Vector 269_2"
                        d="M201.497 117.114C201.135 116.388 195.677 105.474 193.561 100.561"
                        stroke="black"
                        stroke-width="0.453507"
                      />
                    </g>
                    <g id="Group 165">
                      <path
                        id="Vector 273"
                        d="M155.466 56.1177C155.829 56.4805 160.455 58.234 162.723 59.0655L170.886 56.1177"
                        stroke="black"
                        stroke-width="0.453507"
                      />
                      <path
                        id="Vector 274"
                        d="M160.001 57.9316C160.001 58.113 159.926 66.1704 160.001 70.1763"
                        stroke="black"
                        stroke-width="0.453507"
                      />
                      <path
                        id="Vector 275"
                        d="M166.351 57.9316C166.351 58.113 166.275 66.1704 166.351 70.1763"
                        stroke="black"
                        stroke-width="0.453507"
                      />
                      <path
                        id="Vector 272"
                        d="M170.886 52.2627H155.466V67.6819L159.321 70.1762H167.711L170.886 68.1355V52.2627Z"
                        stroke="black"
                        stroke-width="0.453507"
                      />
                    </g>
                    <g id="Group 166">
                      <path
                        id="Vector 273_2"
                        d="M195.828 56.1177C195.466 56.4805 190.84 58.234 188.572 59.0655L180.409 56.1177"
                        stroke="black"
                        stroke-width="0.453507"
                      />
                      <path
                        id="Vector 274_2"
                        d="M191.293 57.9316C191.293 58.113 191.369 66.1704 191.293 70.1763"
                        stroke="black"
                        stroke-width="0.453507"
                      />
                      <path
                        id="Vector 275_2"
                        d="M184.944 57.9316C184.944 58.113 185.02 66.1704 184.944 70.1763"
                        stroke="black"
                        stroke-width="0.453507"
                      />
                      <path
                        id="Vector 272_2"
                        d="M180.409 52.2627H195.829V67.6819L191.974 70.1762H183.584L180.409 68.1355V52.2627Z"
                        stroke="black"
                        stroke-width="0.453507"
                      />
                    </g>
                  </g>
                  <path
                    id="Vector 276"
                    d="M147.303 39.3379C149.344 43.873 151.986 55.0226 152.745 65.6413C153.07 70.1764 153.048 75.4673 152.972 78.793"
                    stroke="black"
                    stroke-width="0.453507"
                  />
                  <path
                    id="Vector 277"
                    d="M203.085 39.3379C201.044 43.873 198.402 55.0226 197.643 65.6413C197.318 70.1764 197.34 75.4673 197.416 78.793"
                    stroke="black"
                    stroke-width="0.453507"
                  />
                </g>
              </g>
              <path
                id="Vector 41"
                d="M143.781 86.5616C145.062 85.9031 148.017 83.031 148.182 82.8848C148.06 83.3101 147.115 85.4965 147.115 85.4965C147.028 85.6113 145.691 86.6717 143.94 87.1621C142.539 87.5544 140.549 87.5146 140.049 87.3088C140.655 87.3513 142.617 87.1595 143.781 86.5616Z"
                fill="url(#paint3_linear_207_3579)"
                fill-opacity="0.87"
              />
              <path
                id="Vector 42"
                d="M207.94 86.5616C206.658 85.9031 203.703 83.031 203.538 82.8848C203.661 83.3101 204.605 85.4965 204.605 85.4965C204.693 85.6113 206.029 86.6717 207.78 87.1621C209.181 87.5544 211.171 87.5146 211.672 87.3088C211.066 87.3513 209.103 87.1595 207.94 86.5616Z"
                fill="url(#paint4_linear_207_3579)"
                fill-opacity="0.87"
              />
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 21"
                  d="M193.991 64.1596L155.051 64.1494"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M157.617 60.667L153.478 64.2198L157.111 67.9215"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27"
                  d="M193.157 68.1047L196.539 63.8242L192.264 60.8877"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 21_2"
                  d="M198.323 119.009L151.598 119.009"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M154.164 115.527L150.024 119.08L153.657 122.781"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_2"
                  d="M197.176 122.965L200.558 118.685L196.282 115.748"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 21_3"
                  d="M194.015 78.1938L156.133 78.1938"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M158.699 74.7109L154.56 78.2637L158.193 81.9654"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_3"
                  d="M192.187 82.1491L195.569 77.8687L191.294 74.9321"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_thigh_width') && (
              <g id="product_thigh_width">
                <path
                  id="Vector 21_4"
                  d="M171.339 140.243L150.705 139.336"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M153.239 135.734L148.89 139.028L152.29 142.944"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_4"
                  d="M169.057 144.1L172.694 140.034L168.606 136.842"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_sleeve') && (
              <g id="product_sleeve">
                <path
                  id="Vector 21_5"
                  d="M141.075 41.1904L125.709 122.896"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_5"
                  d="M122.38 120.278L125.056 125.032L129.395 122.192"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_5"
                  d="M144.399 43.3033L141.59 38.627L137.332 41.5883"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_lower_length') && (
              <g id="product_lower_length">
                <path
                  id="Vector 21_6"
                  d="M205.277 102.598L211.475 260.649"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_6"
                  d="M207.75 258.91L211.97 262.367L214.982 258.144"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_6"
                  d="M208.996 103.893L205.19 99.9851L201.724 103.844"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_inside_leg') && (
              <g id="product_inside_leg">
                <path
                  id="Vector 21_7"
                  d="M183.074 138.878L186.759 257.701"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_7"
                  d="M183.048 256.141L186.737 260.159L190.315 256.404"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_7"
                  d="M186.793 140.173L182.987 136.265L179.521 140.124"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 21_8"
                  d="M174.457 32.3047L174.457 261.103"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_8"
                  d="M170.698 258.626L174.369 262.661L177.964 258.922"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_8"
                  d="M178.177 33.5993L174.37 29.6917L170.905 33.5505"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_full_body_length') && (
              <g id="product_fullbody_length">
                <path
                  id="Vector 21_9"
                  d="M174.457 32.3047L174.457 261.103"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_9"
                  d="M170.698 258.626L174.369 262.661L177.964 258.922"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_9"
                  d="M178.177 33.5993L174.37 29.6917L170.905 33.5505"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 19"
                  d="M150.024 41.3784L199.91 41.3784"
                  stroke="#EDA7A7"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                  stroke-dasharray="4.54 4.54"
                />
                <path
                  id="Vector 27_10"
                  d="M198.125 45.2486L201.946 41.3558L198.011 37.9773"
                  stroke="#EDA7A7"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 28"
                  d="M152.166 44.7115L148.359 41.1206L150.333 39.5488L152.307 37.9769"
                  stroke="#EDA7A7"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_back_hook') && (
              <g id="product_back_hook">
                <path
                  id="Vector 21_10"
                  d="M169.339 135.028L169.339 104.86"
                  stroke="#EDA7A7"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                  stroke-dasharray="4.54 4.54"
                />
                <path
                  id="Vector 26_10"
                  d="M172.984 107.132L169.093 103.309L165.712 107.242"
                  stroke="#EDA7A7"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_11"
                  d="M165.725 133.242L169.568 137.112L172.997 133.22"
                  stroke="#EDA7A7"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_hook') && (
              <g id="product_hook">
                <path
                  id="Vector 21_11"
                  d="M169.339 135.028L169.339 104.86"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_11"
                  d="M172.984 107.132L169.093 103.309L165.712 107.242"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_12"
                  d="M165.725 133.242L169.568 137.112L172.997 133.22"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_collar') && (
              <g id="product_collar">
                <path
                  id="Vector 464"
                  d="M171.655 41.6052C171.655 41.6052 165.532 34.0868 167.736 30.424C169.941 26.7611 181.039 27.4359 182.07 31.1951C182.895 34.2025 180.214 39.0027 178.152 41.6052"
                  stroke="#E55959"
                  stroke-width="2.26754"
                />
                <path
                  id="Vector 465"
                  d="M178.039 36.4963L178.16 41.865L183.258 41.2651"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 28_2"
                  d="M172.023 36.4963L171.902 41.865L166.804 41.2651"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_thigh_circumference') && (
              <g id="product_thigh_circumference">
                <path
                  id="Vector 21_12"
                  d="M171.339 140.243L150.705 139.336"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_12"
                  d="M153.239 135.734L148.89 139.028L152.29 142.944"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_13"
                  d="M169.057 144.1L172.694 140.034L168.606 136.842"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 21_13"
                  d="M198.323 119.009L151.598 119.009"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_13"
                  d="M154.164 115.527L150.024 119.08L153.657 122.781"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_14"
                  d="M197.176 122.965L200.558 118.685L196.282 115.748"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 21_14"
                  d="M193.991 64.1596L155.051 64.1494"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_14"
                  d="M157.617 60.667L153.478 64.2198L157.111 67.9215"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_15"
                  d="M193.157 68.1047L196.539 63.8242L192.264 60.8877"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 21_15"
                  d="M194.015 78.1938L156.133 78.1938"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_15"
                  d="M158.699 74.7109L154.56 78.2637L158.193 81.9654"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_16"
                  d="M192.187 82.1491L195.569 77.8687L191.294 74.9321"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_high_waist_width') && (
              <g id="product_high_waist_width">
                <path
                  id="Vector 21_16"
                  d="M194.015 78.1938L156.133 78.1938"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_16"
                  d="M158.699 74.7109L154.56 78.2637L158.193 81.9654"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_17"
                  d="M192.187 82.1491L195.569 77.8687L191.294 74.9321"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 21_17"
                  d="M175.032 32.3047L175.032 261.103"
                  stroke="#EDA7A7"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                  stroke-dasharray="4.54 4.54"
                />
                <path
                  id="Vector 26_17"
                  d="M171.273 258.626L174.944 262.661L178.539 258.922"
                  stroke="#EDA7A7"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_18"
                  d="M178.751 33.5993L174.945 29.6917L171.479 33.5505"
                  stroke="#EDA7A7"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 21_18"
                  d="M160.973 39.1108L160.973 258.835"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_18"
                  d="M157.214 256.594L160.885 260.629L164.48 256.89"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_19"
                  d="M164.692 40.756L160.886 36.8484L157.42 40.7072"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_high_waist_circumference') && (
              <g id="product_high_waist_circumference">
                <path
                  id="Vector 21_19"
                  d="M195.829 104.497L154.144 104.497"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_19"
                  d="M156.71 101.015L152.571 104.567L156.204 108.269"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_20"
                  d="M194.455 108.453L197.836 104.172L193.561 101.236"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_circumference') && (
              <g id="product_lower_waist_circumference">
                <path
                  id="Vector 21_20"
                  d="M195.829 104.497L154.144 104.497"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_20"
                  d="M156.71 101.015L152.571 104.567L156.204 108.269"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_21"
                  d="M194.455 108.453L197.836 104.172L193.561 101.236"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_width') && (
              <g id="product_lower_waist_width">
                <path
                  id="Vector 21_21"
                  d="M195.829 104.497L154.144 104.497"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_21"
                  d="M156.71 101.015L152.571 104.567L156.204 108.269"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_22"
                  d="M194.455 108.453L197.836 104.172L193.561 101.236"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
            {findMeasure('product_biceps') && (
              <g id="product_biceps">
                <path
                  id="Vector 21_22"
                  d="M208.252 68.2346L200.161 70.0639"
                  stroke="#E55959"
                  stroke-width="2.26754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_22"
                  d="M201.501 65.6252L199.152 70.5486L203.972 72.4647"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
                <path
                  id="Vector 27_23"
                  d="M207.96 71.8178L210.248 66.8656L205.405 65.0095"
                  stroke="#E55959"
                  stroke-width="1.81403"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_3579"
              x1="162.899"
              y1="191.136"
              x2="161.898"
              y2="198.014"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_207_3579"
              x1="186.039"
              y1="191.136"
              x2="187.039"
              y2="198.014"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_207_3579"
              x1="183.307"
              y1="85.0158"
              x2="182.306"
              y2="91.8934"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_207_3579"
              x1="147.085"
              y1="85.0132"
              x2="144.339"
              y2="89.5174"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_207_3579"
              x1="204.636"
              y1="85.0132"
              x2="207.382"
              y2="89.5174"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
