#!/bin/bash

minimum_coverage=80
coverage=$(pnpm coverage)
lines=$(echo "$coverage" | grep "Lines" | awk '{print $3}' | tr -d '%')
stataments=$(echo "$coverage" | grep "Statements" | awk '{print $3}' | tr -d '%')
functions=$(echo "$coverage" | grep "Functions" | awk '{print $3}' | tr -d '%')
branches=$(echo "$coverage" | grep "Branches" | awk '{print $3}' | tr -d '%')

average=$(awk "BEGIN {print int(($lines + $stataments + $functions + $branches) / 4)}")

if awk "BEGIN {exit !($average < $minimum_coverage)}"; then
  echo "Coverage is below ${minimum_coverage}%. Current coverage: ${average}%"
  exit 1
else
  echo "Coverage is sufficient: ${average}% "
fi
