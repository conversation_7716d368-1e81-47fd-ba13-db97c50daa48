import i18n, { InitOptions } from 'i18next';
import HttpBackend from 'i18next-http-backend';
import { initReactI18next } from 'react-i18next';
import type { AxiosResponse } from 'axios';

import getQueryParams from '@/lib/get-query-params';
import { getMappedLanguages } from '@/lib/get-mapped-languages';
import api from '@/api';

interface ClientTranslations {
  [key: string]: unknown;
}

interface QueryParams {
  lang?: string;
  tenantId: string;
}

const { lang = 'en', tenantId } = getQueryParams<QueryParams>();

class TranslationManager {
  private language = getMappedLanguages(lang);
  private tenantId = tenantId;
  private baseTranslationUrl = 'https://static.sizebay.technology/b1c105110fd0413dbba83ae6a985891f/_production/';
  private clientTranslationBaseUrl = import.meta.env.VITE_S3_URL;

  public async initialize(): Promise<typeof i18n> {
    try {
      const i18nConfig: InitOptions = {
        lng: this.language.value,
        fallbackLng: 'en',
        debug: false,
        ns: ['translation'],
        defaultNS: 'translation',
        interpolation: { escapeValue: false },
        react: {
          useSuspense: false,
          bindI18n: 'languageChanged loaded',
          bindI18nStore: 'added removed',
        },
        initImmediate: false,
        backend: {
          loadPath: `${this.baseTranslationUrl}{{lng}}`,
        },
      };

      await this._initializeI18n(i18nConfig);

      await this._loadClientTranslations();

      this._setupLanguageChangeListener();

      return i18n;
    } catch (error) {
      console.error('i18n initialization failed:', error);
      throw error;
    }
  }

  private async _initializeI18n(config: InitOptions): Promise<void> {
    await i18n.use(HttpBackend).use(initReactI18next).init(config);
  }

  private _setupLanguageChangeListener(): void {
    i18n.on('languageChanged', async (newLanguage: string) => {
      this.language = getMappedLanguages(newLanguage);

      // Recarrega as traduções específicas do cliente para o novo idioma
      if (this.tenantId) {
        try {
          await this._loadClientTranslations();
        } catch (error) {
          console.error('Failed to load client translations:', error);
        }
      }
    });
  }

  private async _loadClientTranslations(): Promise<void> {
    try {
      const url = `${this.clientTranslationBaseUrl}/${this.tenantId}/translations/${this.language.key}.json`;
      const response: AxiosResponse<ClientTranslations> = await api.get(url);

      if (response.data) {
        this._mergeClientTranslations(response.data);
      }
    } catch (error) {
      console.error('Failed to load client translations:', error);
    }
  }

  private _mergeClientTranslations(clientTranslations: ClientTranslations): void {
    try {
      for (const [key, value] of Object.entries(clientTranslations)) {
        i18n.addResourceBundle(this.language.value, 'translation', { [key]: value }, true, true);
      }
    } catch (error) {
      console.error('Failed to merge client translations:', error instanceof Error ? error.message : String(error));
    }
  }
}

const translationManager = new TranslationManager();

translationManager.initialize().catch((error: unknown) => {
  console.error('Failed to initialize translation manager:', error instanceof Error ? error.message : String(error));
});

export default i18n;
