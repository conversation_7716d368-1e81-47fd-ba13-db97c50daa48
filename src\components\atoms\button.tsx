import type { ButtonHTMLAttributes } from 'react';
import { cn } from '@/lib/utils';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  /** The visual style of the button */
  variant?: 'primary' | 'secondary' | 'blank';
  /** Whether the button should take up the full width of its container */
  fullWidth?: boolean;
}

export function Button({ variant = 'primary', fullWidth, className, ...props }: ButtonProps) {
  return (
    <button
      className={cn(
        'rounded-lg px-4 py-3 font-medium cursor-pointer',
        {
          'bg-[#262626] text-white hover:bg-[#262626]/90': variant === 'primary',
          'bg-[#e7e7e7] text-[#262626] hover:bg-[#e7e7e7]/90': variant === 'secondary',
          'w-full': fullWidth,
        },
        className
      )}
      {...props}
    />
  );
}
