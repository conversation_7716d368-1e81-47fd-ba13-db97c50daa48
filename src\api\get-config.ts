import api from '.';
import { Config } from '@/types/config';
import getQueryParams from '@/lib/get-query-params';

const qp = getQueryParams<{ tenantId: string }>();
const { tenantId } = qp;
const path = '/config_v4.json';
const secondPath = '/publish/config_v4.json';

export async function getConfig() {
  const fileUrl = `${import.meta.env.VITE_S3_URL}/${tenantId}`;

  const { data: config } = await api.get(`${fileUrl}${path}`).catch(() => api.get(`${fileUrl}${secondPath}`));

  return config as Config;
}
