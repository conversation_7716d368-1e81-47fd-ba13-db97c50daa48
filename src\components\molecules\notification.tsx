import { Text } from '../atoms/text';
import { cn } from '@/lib/utils';

export interface NotificationProps {
  content: string;
  title?: string;
  className?: string;
}

export function Notification({ title, content, className }: NotificationProps) {
  return (
    <div
      className={cn(
        'max-w-full flex flex-col items-center text-center bg-[#F6F6F6] p-2',
        { 'gap-0.5': title },
        className
      )}
    >
      {title && (
        <Text variant="h1" className="text-sm">
          {title}
        </Text>
      )}
      <Text variant="body" className="text-sm">
        {content}
      </Text>
    </div>
  );
}
