import { useState } from 'react';
import { AppContextType, AppStateActions, selectedImage, UnitSystem } from './types';
import { AppContext, appInitialState } from './index';
import { Product } from '@/types/size-chart.types';
import { CarouselItem } from '@/components/organisms';
import { Config } from '@/types/config';

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState(appInitialState);

  const handleSetState = ({ action, payload }: { action: AppStateActions; payload: unknown }) => {
    switch (action) {
      case AppStateActions.SET_LANG:
        setState((prevState) => ({ ...prevState, app: { ...prevState.app, lang: payload as string } }));
        break;
      case AppStateActions.SET_UNIT_SYSTEM:
        setState((prevState) => {
          return {
            ...prevState,
            app: { ...prevState.app, unitSystem: payload as UnitSystem },
          };
        });
        break;
      case AppStateActions.SET_PRODUCT:
        setState((prevState) => ({
          ...prevState,
          app: { ...prevState.app, loading: false },
          product: payload as Product,
        }));
        break;
      case AppStateActions.SET_CAROUSEL_SELECTED:
        {
          const item = payload as CarouselItem;
          setState((prevState) => ({
            ...prevState,
            carousel: {
              ...prevState.carousel,
              selected: {
                id: item.id,
                content: item.content,
              },
            },
          }));
        }
        break;
      case AppStateActions.TOGGLE_MENU_OPEN:
        setState((prevState) => ({ ...prevState, app: { ...prevState.app, menuOpen: !prevState.app.menuOpen } }));
        break;
      case AppStateActions.SET_CONFIG:
        setState((prevState) => ({ ...prevState, app: { ...prevState.app, config: payload as Config } }));
        break;
      case AppStateActions.SET_SELECTED_IMAGE:
        setState((prevState) => ({ ...prevState, app: { ...prevState.app, selectedImage: payload as selectedImage } }));
        break;
      default:
        break;
    }
  };

  const contextValue: AppContextType = {
    ...state,
    setState: handleSetState,
  };

  return <AppContext.Provider value={contextValue}>{children}</AppContext.Provider>;
}
