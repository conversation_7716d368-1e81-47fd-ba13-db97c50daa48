import { useDevice } from '@/hooks/use-device';
import { BodyMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface UpperBodyProps {
  measure: BodyMeasure;
  className?: string;
}

export function UpperBody({ measure, className }: UpperBodyProps) {
  const { measures } = measure;
  const mappedMeasures = measures.map((item) => item.measure);
  const { isMobile } = useDevice();

  const findMeasure = (measure: string) => {
    const foundMeasure = mappedMeasures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full flex justify-center', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_upper_body">
            <mask id="mask0_128_3266" maskUnits="userSpaceOnUse" x="0" y="0" width="331" height="193">
              <rect id="rect" width="331" height="193" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_3266)">
              <g id="group_upper_body_mask">
                <g id="male">
                  <g id="Group 20">
                    <path
                      id="Union"
                      d="M142.16 82.7449V105.148L131.492 110.482L122.602 114.394L109.088 120.795L100.198 125.418L90.9523 130.752L82.7733 134.664L78.1504 137.864L73.8831 141.776L70.327 146.755L66.4153 153.156L63.2148 161.69L62.1479 169.869L61.4367 176.626V186.227L62.5036 192.984L57.8806 224.277L57.1694 234.235L51.8353 253.082L50.7684 261.972L47.9236 271.929L45.7899 286.154L44.3675 293.621L44.0119 307.49V322.07L44.3675 332.383L44.0119 343.407L44.7231 356.564L45.4343 360.476L43.6562 379.679L44.0119 384.657L45.0787 389.636L46.8567 403.86L52.1909 412.395L59.3031 421.641L61.7923 423.419L62.8592 422.708L62.5036 420.218C62.385 419.981 62.2191 419.436 62.5036 419.152C62.8592 418.796 63.926 417.373 63.926 417.018V414.884L57.525 403.86L56.1026 400.66V391.414L57.8806 381.813L63.926 375.767L68.9045 389.28L72.105 394.614H73.5274L77.0835 391.414C75.187 385.606 71.3227 373.634 71.0382 372.211C70.9772 371.906 70.9162 371.611 70.8569 371.325C70.571 369.945 70.327 368.767 70.327 367.588C70.327 366.166 68.5489 358.698 68.1933 357.987C67.9088 357.418 65.9411 353.956 64.9928 352.297L62.5036 349.452C62.385 347.555 62.1479 343.691 62.1479 343.407C62.1479 343.122 62.6221 338.31 62.8592 335.939L65.3484 322.07L68.9045 311.757L77.0835 292.199L79.9284 282.597C81.1138 276.196 82.4177 263.323 82.4177 263.039C82.4177 262.754 83.1289 257.231 83.4845 254.504L84.1957 248.103L91.6635 214.676L93.7587 206.497L96.9977 217.521L100.198 229.967L104.11 242.414L105.888 251.304L106.599 258.416L105.177 269.44L103.399 288.998L103.754 295.755L103.399 303.934L100.909 328.471L96.6421 371.855L95.5752 390.703L95.9308 399.949L97.7089 435.154L159.736 434.313L163.141 391.414H165.986L169.71 434.712L231.418 435.154L233.196 399.949L233.552 390.703L232.485 371.855L228.218 328.471L225.728 303.934L225.373 295.755L225.728 288.998L223.95 269.44L222.528 258.416L223.239 251.304L225.017 242.414L228.929 229.967L232.129 217.521L235.368 206.497L237.463 214.676L244.931 248.103L245.642 254.504C245.998 257.231 246.709 262.754 246.709 263.039C246.709 263.323 248.013 276.196 249.199 282.597L252.043 292.199L260.222 311.757L263.779 322.07L266.268 335.939C266.505 338.31 266.979 343.122 266.979 343.407C266.979 343.691 266.742 347.555 266.623 349.452L264.134 352.297C263.186 353.956 261.218 357.418 260.934 357.987C260.578 358.698 258.8 366.166 258.8 367.588C258.8 368.767 258.556 369.945 258.27 371.325C258.211 371.611 258.15 371.906 258.089 372.211C257.804 373.634 253.94 385.606 252.043 391.414L255.6 394.614H257.022L260.222 389.28L265.201 375.767L271.246 381.813L273.024 391.414V400.66L271.602 403.86L265.201 414.884V417.018C265.201 417.373 266.268 418.796 266.623 419.152C266.908 419.436 266.742 419.981 266.623 420.218L266.268 422.708L267.335 423.419L269.824 421.641L276.936 412.395L282.27 403.86L284.048 389.636L285.115 384.657L285.471 379.679L283.693 360.476L284.404 356.564L285.115 343.407L284.759 332.383L285.115 322.07V307.49L284.759 293.621L283.337 286.154L281.203 271.929L278.359 261.972L277.292 253.082L271.958 234.235L271.246 224.277L266.623 192.984L267.69 186.227V176.626L266.979 169.869L265.912 161.69L262.712 153.156L258.8 146.755L255.244 141.776L250.977 137.864L246.354 134.664L238.175 130.752L228.929 125.418L220.039 120.795L206.525 114.394L197.635 110.482L186.967 105.148V82.7449L169.186 56.3082V42.561L164.563 49.4346L159.941 42.561V56.3082L142.16 82.7449Z"
                      fill="white"
                    />
                    <path
                      id="Vector 18"
                      d="M165.95 101.948C176.347 97.5383 181.597 88.0792 183.375 85.2343V82.7451C179.108 79.9002 170.431 74.2104 169.862 74.2104C169.293 74.2104 151.37 79.189 142.48 81.6783V99.4587C149.592 104.082 159.35 104.747 165.95 101.948Z"
                      fill="url(#paint0_linear_128_3266)"
                    />
                    <g id="Group 24">
                      <g id="Group 22">
                        <path
                          id="Vector 16"
                          d="M132.878 221.077C147.458 218.232 148.525 213.965 152.081 209.698C150.303 211.831 137.999 210.907 137.146 211.476C136.292 212.045 128.967 211.476 124.699 211.12C121.499 211.713 114.458 208.631 113.32 208.631C112.182 208.631 107.63 206.023 105.496 205.43L96.2505 199.385L91.272 190.495C92.3388 200.808 95.8949 215.388 95.8949 215.388C95.8949 215.388 118.744 223.835 132.878 221.077Z"
                          fill="url(#paint1_linear_128_3266)"
                        />
                      </g>
                      <g id="Group 23">
                        <path
                          id="Vector 16_2"
                          d="M195.857 221.077C181.277 218.232 180.21 213.965 176.654 209.698C178.432 211.831 190.736 210.907 191.589 211.476C192.443 212.045 199.768 211.476 204.036 211.12C207.236 211.713 214.277 208.631 215.415 208.631C216.553 208.631 221.105 206.023 223.239 205.43L232.484 199.385L237.463 190.495C237.072 203.297 232.84 215.388 232.84 215.388C232.84 215.388 209.991 223.835 195.857 221.077Z"
                          fill="url(#paint2_linear_128_3266)"
                        />
                      </g>
                    </g>
                    <g id="Group 12">
                      <path
                        id="Vector 35"
                        d="M186.966 81.6777V105.504C198.109 110.482 223.096 122.004 233.907 128.263C247.42 136.086 257.021 138.575 263.778 155.645C269.183 169.3 268.045 187.294 266.978 194.762L272.313 234.59C274.565 240.517 276.955 254.024 278.358 259.838C280.847 270.151 283.692 278.685 285.114 301.8C286.198 319.414 284.403 331.908 285.114 337.361C285.114 339.613 285.114 344.758 285.114 347.318C285.114 349.878 284.166 356.92 283.692 360.12C284.64 367.114 286.252 381.812 285.114 384.657C283.692 388.213 284.048 399.948 281.203 405.638C278.358 411.328 268.045 424.841 266.978 423.063C266.125 421.64 266.623 419.625 266.978 418.796C266.267 418.559 264.916 417.658 265.2 415.951C265.485 414.244 270.772 405.045 273.379 400.66V391.414L271.246 381.457L265.2 375.411C263.185 381.694 258.657 394.33 256.666 394.614C254.674 394.899 252.991 392.599 252.398 391.414C254.414 384.42 258.515 370.148 258.799 369.01C259.155 367.588 258.444 365.81 260.933 359.053C262.924 353.648 265.793 350.4 266.978 349.452C267.215 345.184 266.907 333.876 263.778 322.781C259.866 308.912 254.888 300.733 250.62 287.22C247.207 276.41 245.76 256.519 244.931 248.103L235.583 205.785L237.107 197.251C236.277 204.244 232.698 219.156 227.861 232.812C223.025 246.467 222.527 256.519 222.883 259.838C223.95 268.373 226.012 286.793 225.728 292.199C225.443 297.604 225.609 302.748 225.728 304.645L227.861 322.781C229.284 335.939 232.342 366.094 233.195 381.457C234.049 396.819 232.5 424.011 231.552 435.509L169.311 433.514L165.985 391.414H163.852"
                        stroke="black"
                        stroke-width="0.711219"
                      />
                      <path
                        id="Vector 27"
                        d="M142.161 81.6775V105.503C131.018 110.482 106.031 122.004 95.2204 128.262C81.7072 136.086 72.1056 138.575 65.349 155.644C59.9438 169.3 61.0817 187.294 62.1486 194.761L56.8144 234.59C54.5622 240.516 52.1724 254.024 50.769 259.838C48.2798 270.151 45.4349 278.685 44.0125 301.8C42.9285 319.414 44.7237 331.908 44.0125 337.361C44.0125 339.613 44.0125 344.757 44.0125 347.318C44.0125 349.878 44.9608 356.919 45.4349 360.12C44.4866 367.113 42.8745 381.812 44.0125 384.657C45.4349 388.213 45.0793 399.948 47.9242 405.638C50.769 411.328 61.0817 424.841 62.1486 423.063C63.002 421.64 62.5042 419.625 62.1486 418.795C62.8598 418.558 64.2111 417.657 63.9266 415.95C63.6421 414.244 58.3554 405.045 55.7476 400.659V391.413L57.8812 381.456L63.9266 375.411C65.9417 381.693 70.4698 394.329 72.4612 394.614C74.4527 394.898 76.1359 392.599 76.7286 391.413C74.7134 384.42 70.6121 370.148 70.3276 369.01C69.972 367.588 70.6832 365.81 68.1939 359.053C66.2025 353.648 63.3339 350.4 62.1486 349.451C61.9115 345.184 62.2197 333.876 65.349 322.781C69.2608 308.912 74.2393 300.733 78.5066 287.22C81.9205 276.409 83.3666 256.519 84.1964 248.103L93.5443 205.785L92.0198 197.251C92.8495 204.244 96.4293 219.156 101.266 232.812C106.102 246.467 106.6 256.519 106.244 259.838C105.177 268.373 103.115 286.793 103.399 292.198C103.684 297.604 103.518 302.748 103.399 304.645L101.266 322.781C99.8432 335.938 96.7849 366.094 95.9315 381.456C95.078 396.819 96.9452 424.011 97.8935 435.509L159.999 435.153L163.142 391.413H165.275"
                        stroke="black"
                        stroke-width="0.711219"
                      />
                    </g>
                    <g id="Group 19">
                      <g id="Group 11">
                        <path
                          id="Vector 4"
                          d="M131.444 49.5233C132.582 44.687 134.763 48.4564 135.711 50.9457L137.134 56.9911L137.845 69.4374C137.726 69.5559 137.134 70.8598 135.711 69.793C133.89 68.4268 130.022 55.5686 131.444 49.5233Z"
                          fill="white"
                          stroke="black"
                          stroke-width="0.711219"
                        />
                        <path
                          id="Vector 5"
                          d="M197.991 49.5233C196.853 44.687 194.672 48.4564 193.724 50.9457L192.301 56.9911L191.59 69.4374C191.709 69.5559 192.301 70.8598 193.724 69.793C195.545 68.4268 199.414 55.5686 197.991 49.5233Z"
                          fill="white"
                          stroke="black"
                          stroke-width="0.711219"
                        />
                        <path
                          id="Vector"
                          d="M137.893 23.7136C141.631 13.1223 154.607 7 164.208 7C174.756 6.99999 188.207 13.1223 191.946 23.7136C196.213 35.8044 195.839 55.2975 190.523 73.8546C188.022 82.5846 185.622 88.7031 182.7 91.9907C179.855 95.1912 168.599 98.7473 164.919 98.7473C162.074 98.7473 149.984 95.1912 147.139 91.9907C144.217 88.7031 141.816 82.5846 139.316 73.8546C134 55.2975 133.626 35.8044 137.893 23.7136Z"
                          fill="white"
                          stroke="black"
                          stroke-width="0.711219"
                        />
                      </g>
                      <g id="Group 18">
                        <g id="Group 14">
                          <path
                            id="Vector 32"
                            d="M161.008 102.659C161.008 103.032 161.707 105.208 162.948 106.459C164.189 107.711 165.017 109.663 165.276 110.483"
                            stroke="black"
                            stroke-width="0.711219"
                            stroke-linecap="round"
                          />
                          <g id="Group 13">
                            <path
                              id="Vector 31"
                              d="M159.942 132.886C159.942 132.886 158.804 129.97 155.675 128.263C152.546 126.556 142.518 126.485 137.539 123.285"
                              stroke="black"
                              stroke-width="0.711219"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              id="Vector 14"
                              d="M167.766 132.886C167.766 132.886 168.904 129.97 172.033 128.263C175.162 126.556 185.19 126.485 190.169 123.285"
                              stroke="black"
                              stroke-width="0.711219"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                  </g>
                </g>
                {findMeasure('collar') && (
                  <g id="collar">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M187 99.5C187 99.5 175.265 101.706 162.938 102.224M142 99.9998C142 99.9998 147 100.626 158.014 102.224"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                          <path
                            id="Vector 27_2"
                            d="M165.072 100.024L161.978 102.103L164.636 104.1"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                          <path
                            id="Vector 28"
                            d="M155.324 100.024L158.417 102.103L155.76 104.1"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('neck') && (
                  <g id="neck">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M187 99.5C187 99.5 175.265 101.706 162.938 102.224M142 99.9998C142 99.9998 147 100.626 158.014 102.224"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                          <path
                            id="Vector 27_3"
                            d="M165.072 100.024L161.978 102.103L164.636 104.1"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                          <path
                            id="Vector 28_2"
                            d="M155.324 100.024L158.417 102.103L155.76 104.1"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('headCircumference') && (
                  <g id="head_circumference">
                    <g id="Group 221_3">
                      <g id="Group 217_3">
                        <g id="Group 220_3">
                          <path
                            id="Ellipse 23_3"
                            d="M193.5 29C193.5 29 181.606 34.5632 161.421 33.6936M136 30.5C136 30.5 148.241 33.6936 156.931 33.6936"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                          <path
                            id="Vector 27_4"
                            d="M163.365 31.6881L160.545 33.5838L162.968 35.4037"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                          <path
                            id="Vector 28_3"
                            d="M154.478 31.6881L157.298 33.5838L154.876 35.4037"
                            stroke="#E55959"
                            stroke-width="1.5"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_3266"
              x1="156.349"
              y1="74.2104"
              x2="156.349"
              y2="116.222"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_3266"
              x1="117.943"
              y1="125.418"
              x2="120.076"
              y2="236.369"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_3266"
              x1="210.792"
              y1="125.418"
              x2="208.659"
              y2="236.369"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className=" h-full m-auto "
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_upper_body">
            <mask id="mask0_128_3335" maskUnits="userSpaceOnUse" x="0" y="0" width="342" height="291">
              <rect id="rect" width="342" height="291" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_3335)">
              <g id="group_upper_body_mask">
                <g id="male">
                  <g id="Group 20">
                    <path
                      id="Union"
                      d="M138.034 123.731V158.257L121.593 166.477L107.892 172.505L87.0671 182.37L73.3664 189.494L59.1176 197.715L46.5129 203.743L39.3885 208.675L32.8121 214.704L27.3318 222.376L21.3035 232.241L16.3712 245.393L14.7271 257.998L13.631 268.411V283.208L15.2751 293.62L8.15072 341.847L7.05466 357.192L-1.1658 386.237L-2.8099 399.938L-7.19414 415.283L-10.4823 437.204L-12.6744 448.713L-13.2225 470.086V492.555L-12.6744 508.448L-13.2225 525.437L-12.1264 545.714L-11.0304 551.743L-13.7705 581.336L-13.2225 589.009L-11.5784 596.681L-8.83823 618.602L-0.617774 631.755L10.3428 646.004L14.1791 648.744L15.8231 647.648L15.2751 643.812C15.0924 643.446 14.8367 642.606 15.2751 642.168C15.8231 641.62 17.4672 639.427 17.4672 638.879V635.591L7.60268 618.602L5.41056 613.67V599.421L8.15072 584.624L17.4672 575.308L25.1397 596.133L30.0719 604.354H32.2641L37.7444 599.421C34.8215 590.47 28.8663 572.02 28.4278 569.828C28.3338 569.357 28.2398 568.903 28.1485 568.463C27.7079 566.335 27.3318 564.519 27.3318 562.703C27.3318 560.511 24.5916 549.002 24.0436 547.906C23.6052 547.03 20.5727 541.695 19.1113 539.138L15.2751 534.754C15.0924 531.831 14.7271 525.876 14.7271 525.437C14.7271 524.999 15.4578 517.582 15.8231 513.928L19.6594 492.555L25.1397 476.662L37.7444 446.521L42.1286 431.724C43.9554 421.859 45.9648 402.021 45.9648 401.582C45.9648 401.144 47.0609 392.631 47.6089 388.429L48.705 378.565L60.2136 327.05L63.4424 314.445L68.4341 331.434L73.3664 350.615L79.3947 369.796L82.1348 383.497L83.2309 394.458L81.0388 411.447L78.2986 441.588L78.8467 452.001L78.2986 464.606L74.4624 502.42L67.8861 569.28L66.242 598.325L66.79 612.574L69.5301 666.829L165.12 665.532L170.368 599.421H174.752L180.491 666.147L275.59 666.829L278.33 612.574L278.878 598.325L277.234 569.28L270.657 502.42L266.821 464.606L266.273 452.001L266.821 441.588L264.081 411.447L261.889 394.458L262.985 383.497L265.725 369.796L271.753 350.615L276.686 331.434L281.677 314.445L284.906 327.05L296.415 378.565L297.511 388.429C298.059 392.631 299.155 401.144 299.155 401.582C299.155 402.021 301.164 421.859 302.991 431.724L307.375 446.521L319.98 476.662L325.46 492.555L329.297 513.928C329.662 517.582 330.393 524.999 330.393 525.437C330.393 525.876 330.027 531.831 329.845 534.754L326.008 539.138C324.547 541.695 321.515 547.03 321.076 547.906C320.528 549.002 317.788 560.511 317.788 562.703C317.788 564.519 317.412 566.335 316.971 568.463C316.88 568.903 316.786 569.357 316.692 569.828C316.254 572.02 310.298 590.47 307.375 599.421L312.856 604.354H315.048L319.98 596.133L327.653 575.308L336.969 584.624L339.709 599.421V613.67L337.517 618.602L327.653 635.591V638.879C327.653 639.427 329.297 641.62 329.845 642.168C330.283 642.606 330.027 643.446 329.845 643.812L329.297 647.648L330.941 648.744L334.777 646.004L345.738 631.755L353.958 618.602L356.698 596.681L358.342 589.009L358.89 581.336L356.15 551.743L357.246 545.714L358.342 525.437L357.794 508.448L358.342 492.555V470.086L357.794 448.713L355.602 437.204L352.314 415.283L347.93 399.938L346.286 386.237L338.065 357.192L336.969 341.847L329.845 293.62L331.489 283.208V268.411L330.393 257.998L328.749 245.393L323.816 232.241L317.788 222.376L312.308 214.704L305.731 208.675L298.607 203.743L286.002 197.715L271.753 189.494L258.053 182.37L237.228 172.505L223.527 166.477L207.086 158.257V123.731L179.684 82.9889V61.8032L172.56 72.3961L165.435 61.8032V82.9889L138.034 123.731Z"
                      fill="white"
                    />
                    <path
                      id="Vector 18"
                      d="M174.697 153.325C190.719 146.529 198.81 131.951 201.55 127.567V123.731C194.974 119.347 181.602 110.578 180.725 110.578C179.848 110.578 152.228 118.251 138.527 122.087V149.488C149.487 156.613 164.525 157.639 174.697 153.325Z"
                      fill="url(#paint0_linear_128_3335)"
                    />
                    <g id="Group 24">
                      <g id="Group 22">
                        <path
                          id="Vector 16"
                          d="M123.73 336.915C146.199 332.531 147.843 325.954 153.324 319.378C150.583 322.666 131.622 321.241 130.306 322.118C128.991 322.995 117.702 322.118 111.125 321.57C106.193 322.483 95.342 317.734 93.5883 317.734C91.8346 317.734 84.8198 313.715 81.5316 312.801L67.2828 303.485L59.6104 289.784C61.2544 305.677 66.7348 328.146 66.7348 328.146C66.7348 328.146 101.947 341.165 123.73 336.915Z"
                          fill="url(#paint1_linear_128_3335)"
                        />
                      </g>
                      <g id="Group 23">
                        <path
                          id="Vector 16_2"
                          d="M220.786 336.915C198.317 332.531 196.673 325.954 191.193 319.378C193.933 322.666 212.895 321.241 214.21 322.118C215.525 322.995 226.815 322.118 233.391 321.57C238.323 322.483 249.174 317.734 250.928 317.734C252.682 317.734 259.696 313.715 262.985 312.801L277.233 303.485L284.906 289.784C284.303 309.513 277.781 328.146 277.781 328.146C277.781 328.146 242.569 341.165 220.786 336.915Z"
                          fill="url(#paint2_linear_128_3335)"
                        />
                      </g>
                    </g>
                    <g id="Group 12">
                      <path
                        id="Vector 35"
                        d="M207.085 122.086V158.804C224.257 166.477 262.765 184.233 279.425 193.878C300.25 205.935 315.047 209.771 325.46 236.077C333.79 257.121 332.036 284.851 330.392 296.36L338.613 357.739C342.083 366.873 345.766 387.689 347.929 396.65C351.765 412.542 356.15 425.695 358.342 461.317C360.012 488.462 357.246 507.717 358.342 516.12C358.342 519.591 358.342 527.519 358.342 531.465C358.342 535.411 356.88 546.262 356.15 551.194C357.611 561.972 360.095 584.624 358.342 589.008C356.15 594.489 356.698 612.574 352.313 621.342C347.929 630.111 332.036 650.936 330.392 648.196C329.077 646.003 329.844 642.898 330.392 641.619C329.296 641.254 327.214 639.865 327.652 637.235C328.09 634.604 336.238 620.429 340.257 613.67V599.421L336.968 584.076L327.652 574.759C324.546 584.441 317.568 603.915 314.499 604.353C311.43 604.792 308.836 601.248 307.923 599.421C311.028 588.643 317.349 566.649 317.787 564.895C318.335 562.703 317.239 559.963 321.076 549.55C324.145 541.22 328.565 536.215 330.392 534.753C330.757 528.177 330.283 510.749 325.46 493.651C319.431 472.278 311.759 459.673 305.183 438.848C299.922 422.188 297.693 391.535 296.414 378.565L282.008 313.349L284.358 300.196C283.079 310.974 277.562 333.955 270.109 354.999C262.656 376.044 261.888 391.535 262.436 396.65C264.08 409.802 267.259 438.19 266.821 446.52C266.382 454.85 266.638 462.779 266.821 465.701L270.109 493.651C272.301 513.928 277.014 560.401 278.329 584.076C279.644 607.751 277.258 649.657 275.797 667.377L179.876 664.302L174.751 599.421H171.463"
                        stroke="black"
                        stroke-width="1.09606"
                      />
                      <path
                        id="Vector 27"
                        d="M138.035 122.086V158.804C120.864 166.476 82.3552 184.232 65.6951 193.878C44.8699 205.934 30.0729 209.771 19.6603 236.076C11.3302 257.12 13.0839 284.851 14.728 296.359L6.50755 357.739C3.03669 366.873 -0.646223 387.689 -2.80898 396.649C-6.64518 412.542 -11.0294 425.695 -13.2215 461.317C-14.892 488.462 -12.1255 507.717 -13.2215 516.12C-13.2215 519.591 -13.2215 527.519 -13.2215 531.465C-13.2215 535.41 -11.7601 546.261 -11.0294 551.194C-12.4908 561.972 -14.9752 584.623 -13.2215 589.008C-11.0294 594.488 -11.5774 612.573 -7.19322 621.342C-2.80898 630.11 13.0839 650.935 14.728 648.195C16.0433 646.003 15.276 642.897 14.728 641.619C15.8241 641.253 17.9066 639.865 17.4682 637.234C17.0297 634.604 8.88235 620.428 4.86346 613.669V599.42L8.15164 584.075L17.4682 574.759C20.5737 584.441 27.5519 603.914 30.6209 604.353C33.6899 604.791 36.2839 601.247 37.1973 599.42C34.0918 588.642 27.7711 566.648 27.3327 564.894C26.7847 562.702 27.8807 559.962 24.0445 549.55C20.9756 541.219 16.5548 536.214 14.728 534.753C14.3627 528.176 14.8376 510.749 19.6603 493.65C25.6886 472.277 33.3611 459.673 39.9374 438.847C45.1985 422.187 47.4272 391.534 48.7059 378.564L63.1121 313.348L60.7626 300.196C62.0413 310.974 67.5582 333.954 75.0114 354.999C82.4646 376.043 83.2318 391.534 82.6838 396.649C81.0397 409.802 77.8611 438.19 78.2996 446.52C78.738 454.85 78.4822 462.778 78.2996 465.701L75.0114 493.65C72.8193 513.928 68.1062 560.401 66.7909 584.075C65.4756 607.75 68.3532 649.656 69.8147 667.376L165.525 666.828L170.369 599.42H173.657"
                        stroke="black"
                        stroke-width="1.09606"
                      />
                    </g>
                    <g id="Group 19">
                      <g id="Group 11">
                        <path
                          id="Vector 4"
                          d="M121.519 72.5325C123.273 65.0793 126.634 70.8885 128.096 74.7247L130.288 84.0412L131.384 103.222C131.201 103.405 130.288 105.414 128.096 103.77C125.288 101.665 119.327 81.8491 121.519 72.5325Z"
                          fill="white"
                          stroke="black"
                          stroke-width="1.09606"
                        />
                        <path
                          id="Vector 5"
                          d="M224.076 72.5325C222.322 65.0793 218.961 70.8885 217.499 74.7247L215.307 84.0412L214.211 103.222C214.394 103.405 215.307 105.414 217.499 103.77C220.306 101.665 226.268 81.8491 224.076 72.5325Z"
                          fill="white"
                          stroke="black"
                          stroke-width="1.09606"
                        />
                        <path
                          id="Vector"
                          d="M131.458 32.7574C137.219 16.435 157.215 7 172.012 7C188.268 6.99998 208.997 16.435 214.758 32.7574C221.335 51.3905 220.758 81.4313 212.566 110.03C208.712 123.484 205.013 132.913 200.51 137.979C196.125 142.912 178.779 148.392 173.108 148.392C168.723 148.392 150.091 142.912 145.706 137.979C141.203 132.913 137.504 123.484 133.65 110.03C125.458 81.4313 124.881 51.3905 131.458 32.7574Z"
                          fill="white"
                          stroke="black"
                          stroke-width="1.09606"
                        />
                      </g>
                      <g id="Group 18">
                        <g id="Group 14">
                          <path
                            id="Vector 32"
                            d="M167.081 154.421C167.081 154.995 168.157 158.348 170.07 160.277C171.983 162.206 173.259 165.214 173.657 166.478"
                            stroke="black"
                            stroke-width="1.09606"
                            stroke-linecap="round"
                          />
                          <g id="Group 13">
                            <path
                              id="Vector 31"
                              d="M165.438 201.003C165.438 201.003 163.684 196.51 158.862 193.879C154.039 191.248 138.584 191.139 130.912 186.207"
                              stroke="black"
                              stroke-width="1.09606"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              id="Vector 14"
                              d="M177.494 201.003C177.494 201.003 179.248 196.51 184.071 193.879C188.893 191.248 204.348 191.139 212.02 186.207"
                              stroke="black"
                              stroke-width="1.09606"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                  </g>
                </g>
                {findMeasure('collar') && (
                  <g id="collar">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M207.136 149.552C207.136 149.552 189.051 152.951 170.054 153.75M137.786 150.322C137.786 150.322 145.492 151.287 162.465 153.75"
                            stroke="#E55959"
                            stroke-width="2.31165"
                          />
                          <path
                            id="Vector 27_2"
                            d="M173.342 150.359L168.574 153.564L172.67 156.64"
                            stroke="#E55959"
                            stroke-width="2.31165"
                          />
                          <path
                            id="Vector 28"
                            d="M158.32 150.359L163.087 153.564L158.991 156.64"
                            stroke="#E55959"
                            stroke-width="2.31165"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('neck') && (
                  <g id="neck">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M207.136 149.552C207.136 149.552 189.051 152.951 170.054 153.75M137.786 150.322C137.786 150.322 145.492 151.287 162.465 153.75"
                            stroke="#E55959"
                            stroke-width="2.31165"
                          />
                          <path
                            id="Vector 27_3"
                            d="M173.342 150.359L168.574 153.564L172.67 156.64"
                            stroke="#E55959"
                            stroke-width="2.31165"
                          />
                          <path
                            id="Vector 28_2"
                            d="M158.32 150.359L163.087 153.564L158.991 156.64"
                            stroke="#E55959"
                            stroke-width="2.31165"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('headCircumference') && (
                  <g id="head_circumference">
                    <g id="Group 221_3">
                      <g id="Group 217_3">
                        <g id="Group 220_3">
                          <path
                            id="Ellipse 23_3"
                            d="M217.154 40.9043C217.154 40.9043 198.824 49.4777 167.717 48.1376M128.541 43.2159C128.541 43.2159 147.405 48.1376 160.797 48.1376"
                            stroke="#E55959"
                            stroke-width="2.31165"
                          />
                          <path
                            id="Vector 27_4"
                            d="M170.713 45.047L166.367 47.9685L170.101 50.7731"
                            stroke="#E55959"
                            stroke-width="2.31165"
                          />
                          <path
                            id="Vector 28_3"
                            d="M157.018 45.047L161.364 47.9685L157.63 50.7731"
                            stroke="#E55959"
                            stroke-width="2.31165"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_3335"
              x1="159.9"
              y1="110.578"
              x2="159.9"
              y2="175.323"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_3335"
              x1="100.713"
              y1="189.495"
              x2="104.001"
              y2="360.48"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_3335"
              x1="243.803"
              y1="189.495"
              x2="240.515"
              y2="360.48"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
