import { cn } from '@/lib/utils';

type TypographyElement = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';

interface TypographyProps {
  /** The visual style to apply to the text */
  variant: 'h1' | 'body' | 'label';
  /** The HTML element to render (optional, defaults based on variant) */
  as?: TypographyElement;
  /** The content to display */
  children: React.ReactNode;
  /** Additional CSS classes to apply */
  className?: string;
}

export function Text({ variant, as, children, className, ...props }: TypographyProps) {
  const Component = as || (variant === 'h1' ? 'h1' : variant === 'label' ? 'span' : 'p');

  return (
    <Component
      className={cn(
        'text-[#262626]',
        {
          'text-2xl font-medium': variant === 'h1',
          'text-base': variant === 'body',
          'text-sm': variant === 'label',
        },
        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
}
