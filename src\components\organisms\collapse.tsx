import React, { useState } from 'react';
import { Container } from '../atoms/container';
import { Button } from '../atoms/button';
import Arrow from '../icons/arrow';
import { cn } from '@/lib/utils';

interface CollapseProps {
  header: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

export const Collapse = ({ header, children, className }: CollapseProps) => {
  const [collapsed, setCollapsed] = useState(false);

  const toggleCollapse = () => {
    setCollapsed(!collapsed);
  };

  return (
    <Container className={cn('border-b-2 border-[#E7E7E7] px-2 pb-4', className)}>
      <div className="flex justify-between items-center">
        {header}

        <Button variant="blank" onClick={toggleCollapse} className="translate-x-2">
          <Arrow className={cn('transition-transform', collapsed ? 'rotate-180' : 'rotate-0')} />
        </Button>
      </div>

      <div className={cn('overflow-hidden transition delay-150 duration-300 ease-out', collapsed ? 'block' : 'hidden')}>
        {children}
      </div>
    </Container>
  );
};
