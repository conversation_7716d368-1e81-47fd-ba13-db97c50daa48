import React from 'react';
import { useObservation, useProductObservation } from '@/hooks/use-observation';
import { Text } from '../atoms/text';
import { cn } from '@/lib/utils';

/**
 * Example component showing how to use the useObservation hook
 * This demonstrates the same behavior as the original Observation component
 */
export function ObservationHookExample() {
  // Example 1: Using the hook with simple observation text
  const simpleObservation = useObservation(
    'This product runs small, consider sizing up.'
  );

  // Example 2: Using the hook with multi-language observation text
  const multiLanguageObservation = useObservation(
    '[en]This product runs small, consider sizing up.' +
    '[pt-BR]Este produto veste pequeno, considere um tamanho maior.' +
    '[es]Este producto es pequeño, considera una talla más grande.'
  );

  // Example 3: Using the hook with observation translations array
  const translationsObservation = useObservation(
    '',
    [
      {
        id: '1',
        modelingId: '123',
        language: 'en',
        observation: 'This garment fits true to size. Choose your usual size for the best fit.',
      },
      {
        id: '2',
        modelingId: '123',
        language: 'pt-BR',
        observation: 'Esta peça veste no tamanho. Escolha seu tamanho habitual para o melhor caimento.',
      },
    ]
  );

  // Example 4: Using the product-specific hook
  const productObservation = useProductObservation();

  return (
    <div className="space-y-6 p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">useObservation Hook Examples</h2>
      
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold mb-2">1. Simple Observation</h3>
          <p className="text-sm text-gray-600 mb-2">
            Current language: {simpleObservation.currentLanguage} | 
            Has observation: {simpleObservation.hasObservation.toString()}
          </p>
          {simpleObservation.hasObservation && (
            <div className="max-w-full flex flex-col items-center text-center bg-[#F6F6F6] p-2 rounded-lg">
              <Text variant="body" className="text-sm">
                {simpleObservation.observationText}
              </Text>
            </div>
          )}
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">2. Multi-Language Observation</h3>
          <p className="text-sm text-gray-600 mb-2">
            This will show different text based on the current language setting
          </p>
          {multiLanguageObservation.hasObservation && (
            <div className="max-w-full flex flex-col items-center text-center bg-[#F6F6F6] p-2 rounded-lg">
              <Text variant="body" className="text-sm">
                {multiLanguageObservation.observationText}
              </Text>
            </div>
          )}
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">3. Observation with Translation Array</h3>
          <p className="text-sm text-gray-600 mb-2">
            This uses the observationTranslations array
          </p>
          {translationsObservation.hasObservation && (
            <div className="max-w-full flex flex-col items-center text-center bg-[#F6F6F6] p-2 rounded-lg">
              <Text variant="body" className="text-sm">
                {translationsObservation.observationText}
              </Text>
            </div>
          )}
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">4. Product Observation</h3>
          <p className="text-sm text-gray-600 mb-2">
            This uses the product's modeling observation data
          </p>
          {productObservation.hasObservation ? (
            <div className="max-w-full flex flex-col items-center text-center bg-[#F6F6F6] p-2 rounded-lg">
              <Text 
                variant="body" 
                className="text-sm"
                dangerouslySetInnerHTML={{
                  __html: productObservation.observationText,
                }}
              />
            </div>
          ) : (
            <p className="text-sm text-gray-500 italic">No product observation available</p>
          )}
        </div>
      </div>

      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">How to Use the Hook</h3>
        <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
{`// Import the hook
import { useObservation, useProductObservation } from '@/hooks/use-observation';

// Use with custom observation data
const observation = useObservation(observationText, observationTranslations);

// Or use with product data
const productObservation = useProductObservation();

// Then render conditionally
{observation.hasObservation && (
  <div className="observation-container">
    <Text dangerouslySetInnerHTML={{ __html: observation.observationText }} />
  </div>
)}`}
        </pre>
      </div>
    </div>
  );
}

/**
 * Simplified observation component using the hook
 */
export function SimpleObservationWithHook({ 
  observationText, 
  observationTranslations,
  className 
}: {
  observationText: string;
  observationTranslations?: Array<{
    id: string;
    modelingId: string;
    language: string;
    observation: string;
  }>;
  className?: string;
}) {
  const observation = useObservation(observationText, observationTranslations);

  if (!observation.hasObservation) {
    return null;
  }

  return (
    <div
      className={cn(
        'max-w-full flex flex-col items-center text-center bg-[#F6F6F6] p-2 rounded-lg',
        className
      )}
    >
      <Text
        variant="body"
        className="text-sm"
        dangerouslySetInnerHTML={{
          __html: observation.observationText,
        }}
      />
    </div>
  );
}
