import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Collapse } from './collapse';
import { Text } from '../atoms/text';

const meta = {
  title: 'Organisms/Collapse',
  component: Collapse,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    header: { control: 'text' },
    children: { control: 'text' },
  },
} satisfies Meta<typeof Collapse>;

export default meta;
type Story = StoryObj<typeof meta>;

const Header = () => (
  <div className="flex justify-between items-center w-3xs">
    <Text variant="body">Garment chest</Text>
    <Text variant="label">28,5 cm</Text>
  </div>
);

const Content = () => (
  <div>
    <Text variant="body">This is the content of the collapse.</Text>
    <Text variant="body">This is the content of the collapse.</Text>
    <Text variant="body">This is the content of the collapse.</Text>
    <Text variant="body">This is the content of the collapse.</Text>
    <Text variant="body">This is the content of the collapse.</Text>
    <Text variant="body">This is the content of the collapse.</Text>
    <Text variant="body">This is the content of the collapse.</Text>
    <Text variant="body">This is the content of the collapse.</Text>
    <Text variant="body">This is the content of the collapse.</Text>
    <Text variant="body">This is the content of the collapse.</Text>
    <Text variant="body">This is the content of the collapse.</Text>
    <Text variant="body">This is the content of the collapse.</Text>
    <Text variant="body">This is the content of the collapse.</Text>
  </div>
);

export const Default: Story = {
  args: {
    header: <Header />,
    children: <Content />,
  },
};
