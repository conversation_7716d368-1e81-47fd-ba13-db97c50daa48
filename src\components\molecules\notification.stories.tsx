import type { Meta, StoryObj } from '@storybook/react';
import { Notification, NotificationProps } from './notification';

const meta = {
  title: 'Molecules/Notification',
  component: Notification,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    content: { control: 'text' },
  },
} satisfies Meta<typeof Notification>;

export default meta;

type Story = StoryObj<typeof meta>;

const NotificationContainer = (args: NotificationProps) => (
  <div className="w-md">
    <Notification {...args} />
  </div>
);

export const Default: Story = {
  args: {
    title: 'Attention! Buy a larger size.',
    content: 'We recommend choosing a size larger than usual for a more comfortable fit.',
  },

  render: NotificationContainer,
};
