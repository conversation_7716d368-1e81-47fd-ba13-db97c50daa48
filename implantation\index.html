<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Measurements Table Implantation</title>
  </head>
  <style>
    * {
      box-sizing: border-box;
      padding: 0;
      margin: 0;
    }
    body {
      width: 100dvw;
      height: 100dvh;
    }

    #measurementsTable {
      position: fixed;
      inset: 0;
      margin: auto;
      z-index: 9999;
    }

    .mobile {
      width: calc(100% - 20px);
      height: calc(100% - 20px);
      border-radius: 17.5px;
      background-color: #ffffff;
    }

    .desktop {
      width: 950px;
      height: 550px;
      border-radius: 0.25rem;
      border: none;
    }

    #backdrop {
      width: 100dvw;
      height: 100dvh;
      position: fixed;
      inset: 0;
      margin: auto;
      z-index: 9998;
      background-color: rgba(0, 0, 0, 0.5);
    }
  </style>
  <body>
    <button id="mobileBTN">Open mobile mode</button>
    <button id="desktopBTN">Open desktop mode</button>

    <script src="./index.js"></script>
  </body>
</html>
