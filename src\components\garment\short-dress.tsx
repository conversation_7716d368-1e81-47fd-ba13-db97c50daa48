import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface ShortDressProps {
  measure: GarmentMeasure;
  className?: string;
}

export function ShortDress({ measure, className }: ShortDressProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_short_dress">
            <g id="short_dress">
              <path
                id="Vector 43"
                d="M133.339 124.467C137.349 104.761 141.989 88.658 146.017 74.3145H147.601L190.982 75.7983C194.086 86.828 196.727 106.81 198.51 118.235C200.036 128.018 205.047 166.014 205.047 166.014C205.047 166.014 199.5 167.795 198.51 167.795H186.823C186.691 168.19 185.991 168.982 184.247 168.982H172.956V166.014H168.004H165.429L160.477 165.124L156.911 166.014H151.761L150.374 168.982C148.922 168.883 145.779 168.744 144.828 168.982C143.877 169.219 137.697 167.102 134.726 166.014L127 162.453C128.915 152.165 130.332 139.246 133.339 124.467Z"
                fill="white"
                stroke="black"
                stroke-width="0.313853"
              />
              <path
                id="Union"
                d="M168.499 45.0933C164.525 45.0933 162.183 44.2683 158.694 42.3647C151.231 38.2934 147.007 24 147.007 24H142.847C143.875 28.4975 148.005 37.668 145.62 45.9877C144.781 48.9173 143.639 51.5342 142.451 53.1189L145.818 74.5123L191.379 74.3142L194.548 53.1189C193.24 51.1818 192.218 48.9173 191.379 45.9877C188.994 37.668 194.152 24 194.152 24L189.992 24C189.992 24 185.767 38.2934 178.305 42.3647C174.816 44.2683 172.474 45.0933 168.499 45.0933Z"
                fill="white"
                stroke="black"
                stroke-width="0.313853"
              />
              <path
                id="Vector 44"
                d="M145.62 76.4931V73.918C159.222 74.3141 191.577 73.918 191.577 73.918L191.18 76.4931C191.18 76.4931 160.411 77.2194 145.62 76.4931Z"
                fill="white"
                stroke="black"
                stroke-width="0.313853"
              />
              <g id="Group 25">
                <path
                  id="Vector 45"
                  d="M154.138 141.736C153.016 150.374 150.335 167.767 150.176 168.833L151.761 165.872H154.138V141.736Z"
                  fill="url(#paint0_linear_208_4971)"
                />
                <path
                  id="Vector 46"
                  d="M162.458 151.064C161.46 156.073 161.41 164.513 161.269 165.131L162.26 165.279L162.458 151.064Z"
                  fill="url(#paint1_linear_208_4971)"
                />
                <path
                  id="Vector 50"
                  d="M198.312 160.837C198.312 165.871 197.717 167.648 197.717 167.648H198.51L198.312 160.837Z"
                  fill="url(#paint2_linear_208_4971)"
                />
                <path
                  id="Vector 47"
                  d="M173.154 141.292C172.032 149.929 171.53 164.806 171.372 165.872H172.956L173.154 141.292Z"
                  fill="url(#paint3_linear_208_4971)"
                />
                <path
                  id="Vector 48"
                  d="M188.209 143.069C187.087 151.707 186.981 166.583 186.823 167.649H189.596L188.209 143.069Z"
                  fill="url(#paint4_linear_208_4971)"
                />
              </g>
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 21"
                  d="M192.387 56.54L144.358 56.54"
                  stroke="#E55959"
                  stroke-width="1.56926"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M146.134 54.1299L143.269 56.5886L145.783 59.1504"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
                <path
                  id="Vector 27"
                  d="M190.652 59.2768L192.992 56.3145L190.033 54.2822"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 21_2"
                  d="M193.486 100.479L141.219 100.479"
                  stroke="#E55959"
                  stroke-width="1.56926"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M142.995 98.0693L140.13 100.528L142.645 103.09"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
                <path
                  id="Vector 27_2"
                  d="M191.907 103.217L194.247 100.255L191.289 98.2227"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 21_3"
                  d="M188.935 75.3711L147.496 75.3711"
                  stroke="#E55959"
                  stroke-width="1.56926"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M149.272 72.9609L146.407 75.4197L148.922 77.9815"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
                <path
                  id="Vector 27_3"
                  d="M188.141 78.1088L190.481 75.1465L187.522 73.1143"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 19"
                  d="M145.152 27.2959L191.132 27.2959"
                  stroke="#EDA7A7"
                  stroke-width="1.56926"
                  stroke-linecap="square"
                  stroke-dasharray="3.14 3.14"
                />
                <path
                  id="Vector 27_4"
                  d="M190.053 29.974L192.697 27.2799L189.974 24.9418"
                  stroke="#EDA7A7"
                  stroke-width="1.25541"
                />
                <path
                  id="Vector 28"
                  d="M146.634 29.6019L144 27.1168L145.366 26.029L146.732 24.9412"
                  stroke="#EDA7A7"
                  stroke-width="1.25541"
                />
              </g>
            )}
            {findMeasure('product_fullbody_length') && (
              <g id="product_fullbody_length">
                <path
                  id="Vector 21_4"
                  d="M184.071 26.04C184.07 81.6855 184.089 111.942 184.089 167.588"
                  stroke="#E55959"
                  stroke-width="1.56926"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M181.502 165.567L184.055 168.347L186.531 165.749"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
                <path
                  id="Vector 27_5"
                  d="M186.596 27.8749L183.962 25.1707L181.563 27.8412"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 21_5"
                  d="M192.387 56.54L144.358 56.54"
                  stroke="#E55959"
                  stroke-width="1.56926"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_5"
                  d="M146.134 54.1299L143.269 56.5886L145.783 59.1504"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
                <path
                  id="Vector 27_6"
                  d="M190.652 59.2768L192.992 56.3145L190.033 54.2822"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 21_6"
                  d="M188.935 75.3711L147.496 75.3711"
                  stroke="#E55959"
                  stroke-width="1.56926"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_6"
                  d="M149.272 72.9609L146.407 75.4197L148.922 77.9815"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
                <path
                  id="Vector 27_7"
                  d="M188.141 78.1088L190.481 75.1465L187.522 73.1143"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 21_7"
                  d="M193.486 100.479L141.219 100.479"
                  stroke="#E55959"
                  stroke-width="1.56926"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_7"
                  d="M142.995 98.0693L140.13 100.528L142.645 103.09"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
                <path
                  id="Vector 27_8"
                  d="M191.907 103.217L194.247 100.255L191.289 98.2227"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 21_8"
                  d="M184.071 26.04C184.07 81.6855 184.089 111.942 184.089 167.588"
                  stroke="#E55959"
                  stroke-width="1.56926"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_8"
                  d="M181.502 165.567L184.055 168.347L186.531 165.749"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
                <path
                  id="Vector 27_9"
                  d="M186.596 27.8749L183.962 25.1707L181.563 27.8412"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 21_9"
                  d="M184.071 26.04C184.07 81.6855 184.089 111.942 184.089 167.588"
                  stroke="#EDA7A7"
                  stroke-width="1.56926"
                  stroke-linecap="square"
                  stroke-dasharray="3.14 3.14"
                />
                <path
                  id="Vector 26_9"
                  d="M181.502 165.567L184.055 168.347L186.531 165.749"
                  stroke="#EDA7A7"
                  stroke-width="1.25541"
                />
                <path
                  id="Vector 27_10"
                  d="M186.596 27.8749L183.962 25.1707L181.563 27.8412"
                  stroke="#EDA7A7"
                  stroke-width="1.25541"
                />
              </g>
            )}
            {findMeasure('product_high_waist_circumference') && (
              <g id="product_high_waist_circumference">
                <path
                  id="Vector 21_10"
                  d="M188.935 75.3711L147.496 75.3711"
                  stroke="#E55959"
                  stroke-width="1.56926"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_10"
                  d="M149.272 72.9609L146.407 75.4197L148.922 77.9815"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
                <path
                  id="Vector 27_11"
                  d="M188.141 78.1088L190.481 75.1465L187.522 73.1143"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
              </g>
            )}
            {findMeasure('product_high_waist_width') && (
              <g id="product_high_waist_width">
                <path
                  id="Vector 21_11"
                  d="M188.935 75.3711L147.496 75.3711"
                  stroke="#E55959"
                  stroke-width="1.56926"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_11"
                  d="M149.272 72.9609L146.407 75.4197L148.922 77.9815"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
                <path
                  id="Vector 27_12"
                  d="M188.141 78.1088L190.481 75.1465L187.522 73.1143"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_circumference') && (
              <g id="product_lower_waist_circumference">
                <path
                  id="Vector 21_12"
                  d="M188.935 75.3711L147.496 75.3711"
                  stroke="#E55959"
                  stroke-width="1.56926"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_12"
                  d="M149.272 72.9609L146.407 75.4197L148.922 77.9815"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
                <path
                  id="Vector 27_13"
                  d="M188.141 78.1088L190.481 75.1465L187.522 73.1143"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_width') && (
              <g id="product_lower_waist_width">
                <path
                  id="Vector 21_13"
                  d="M188.935 75.3711L147.496 75.3711"
                  stroke="#E55959"
                  stroke-width="1.56926"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_13"
                  d="M149.272 72.9609L146.407 75.4197L148.922 77.9815"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
                <path
                  id="Vector 27_14"
                  d="M188.141 78.1088L190.481 75.1465L187.522 73.1143"
                  stroke="#E55959"
                  stroke-width="1.25541"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_208_4971"
              x1="152.355"
              y1="141.736"
              x2="152.355"
              y2="168.981"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#B6B6B6" />
              <stop offset="1" stop-color="#E8E8E8" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_208_4971"
              x1="160.675"
              y1="151.064"
              x2="160.675"
              y2="166.863"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#B6B6B6" />
              <stop offset="1" stop-color="#DFDFDF" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_208_4971"
              x1="196.727"
              y1="154.47"
              x2="196.727"
              y2="170.269"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#B6B6B6" />
              <stop offset="1" stop-color="#E3E3E3" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_208_4971"
              x1="171.372"
              y1="141.292"
              x2="171.372"
              y2="168.537"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#B6B6B6" />
              <stop offset="1" stop-color="#E7E7E7" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_208_4971"
              x1="186.426"
              y1="143.069"
              x2="186.426"
              y2="170.314"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#B6B6B6" />
              <stop offset="1" stop-color="#E4E4E4" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_short_dress">
            <g id="short_dress">
              <path
                id="Vector 43"
                d="M119.836 188.898C126.058 158.319 133.258 133.331 139.508 111.074H141.967L209.283 113.377C214.098 130.492 218.197 161.498 220.963 179.227C223.332 194.408 231.106 253.367 231.106 253.367C231.106 253.367 222.5 256.13 220.963 256.13H202.828C202.623 256.744 201.537 257.972 198.832 257.972H181.311V253.367H173.627H169.631L161.947 251.985L156.414 253.367H148.422L146.27 257.972C144.016 257.818 139.139 257.603 137.664 257.972C136.188 258.34 126.598 255.055 121.988 253.367L110 247.841C112.971 231.877 115.17 211.831 119.836 188.898Z"
                fill="white"
                stroke="black"
                stroke-width="0.487013"
              />
              <path
                id="Union"
                d="M174.395 65.731C168.228 65.731 164.595 64.4509 159.18 61.497C147.6 55.1794 141.045 33 141.045 33H134.59C136.186 39.9789 142.594 54.2089 138.893 67.1189C137.59 71.6647 135.82 75.7254 133.975 78.1844L139.201 111.381L209.897 111.074L214.815 78.1844C212.786 75.1786 211.201 71.6647 209.897 67.1189C206.197 54.2089 214.201 33 214.201 33L207.746 33C207.746 33 201.191 55.1794 189.611 61.497C184.196 64.4509 180.563 65.731 174.395 65.731Z"
                fill="white"
                stroke="black"
                stroke-width="0.487013"
              />
              <path
                id="Vector 44"
                d="M138.893 114.455V110.459C160 111.074 210.205 110.459 210.205 110.459L209.59 114.455C209.59 114.455 161.844 115.582 138.893 114.455Z"
                fill="white"
                stroke="black"
                stroke-width="0.487013"
              />
              <g id="Group 25">
                <path
                  id="Vector 45"
                  d="M152.111 215.695C150.369 229.098 146.209 256.088 145.963 257.742L148.422 253.147H152.111V215.695Z"
                  fill="url(#paint0_linear_208_4972)"
                />
                <path
                  id="Vector 46"
                  d="M165.02 230.17C163.472 237.942 163.395 251.038 163.176 251.998L164.713 252.227L165.02 230.17Z"
                  fill="url(#paint1_linear_208_4972)"
                />
                <path
                  id="Vector 50"
                  d="M220.656 245.335C220.656 253.147 219.734 255.904 219.734 255.904H220.963L220.656 245.335Z"
                  fill="url(#paint2_linear_208_4972)"
                />
                <path
                  id="Vector 47"
                  d="M181.619 215.006C179.877 228.409 179.098 251.493 178.852 253.147H181.311L181.619 215.006Z"
                  fill="url(#paint3_linear_208_4972)"
                />
                <path
                  id="Vector 48"
                  d="M204.979 217.763C203.238 231.166 203.074 254.25 202.828 255.904H207.131L204.979 217.763Z"
                  fill="url(#paint4_linear_208_4972)"
                />
              </g>
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 21"
                  d="M211.462 83.4932L136.934 83.4932"
                  stroke="#E55959"
                  stroke-width="2.43506"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M139.69 79.7539L135.245 83.5692L139.146 87.5444"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
                <path
                  id="Vector 27"
                  d="M208.769 87.7404L212.401 83.1437L207.81 79.9902"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 21_2"
                  d="M213.167 151.675L132.064 151.675"
                  stroke="#E55959"
                  stroke-width="2.43506"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M134.82 147.936L130.375 151.751L134.276 155.726"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
                <path
                  id="Vector 27_2"
                  d="M210.718 155.923L214.349 151.326L209.758 148.173"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 21_3"
                  d="M206.105 112.714L141.804 112.714"
                  stroke="#E55959"
                  stroke-width="2.43506"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M144.56 108.975L140.115 112.79L144.016 116.765"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
                <path
                  id="Vector 27_3"
                  d="M204.873 116.961L208.505 112.364L203.914 109.211"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 19"
                  d="M138.167 38.1133L209.514 38.1133"
                  stroke="#EDA7A7"
                  stroke-width="2.43506"
                  stroke-linecap="square"
                  stroke-dasharray="4.87 4.87"
                />
                <path
                  id="Vector 27_4"
                  d="M207.84 42.2694L211.944 38.089L207.718 34.461"
                  stroke="#EDA7A7"
                  stroke-width="1.94805"
                />
                <path
                  id="Vector 28"
                  d="M140.466 41.6933L136.379 37.8371L138.498 36.1491L140.618 34.4612"
                  stroke="#EDA7A7"
                  stroke-width="1.94805"
                />
              </g>
            )}
            {findMeasure('product_full_body_length') && (
              <g id="product_fullbody_length">
                <path
                  id="Vector 21_4"
                  d="M198.558 36.166C198.557 122.512 198.587 169.462 198.586 255.809"
                  stroke="#E55959"
                  stroke-width="2.43506"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M194.572 252.672L198.533 256.987L202.376 252.955"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
                <path
                  id="Vector 27_5"
                  d="M202.476 39.0125L198.389 34.8162L194.667 38.9601"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 21_5"
                  d="M211.462 83.4932L136.934 83.4932"
                  stroke="#E55959"
                  stroke-width="2.43506"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_5"
                  d="M139.69 79.7539L135.245 83.5692L139.146 87.5444"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
                <path
                  id="Vector 27_6"
                  d="M208.769 87.7404L212.401 83.1437L207.81 79.9902"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 21_6"
                  d="M206.105 112.714L141.804 112.714"
                  stroke="#E55959"
                  stroke-width="2.43506"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_6"
                  d="M144.56 108.975L140.115 112.79L144.016 116.765"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
                <path
                  id="Vector 27_7"
                  d="M204.873 116.961L208.505 112.364L203.914 109.211"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 21_7"
                  d="M213.167 151.675L132.064 151.675"
                  stroke="#E55959"
                  stroke-width="2.43506"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_7"
                  d="M134.82 147.936L130.375 151.751L134.276 155.726"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
                <path
                  id="Vector 27_8"
                  d="M210.718 155.923L214.349 151.326L209.758 148.173"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 21_8"
                  d="M198.558 36.166C198.557 122.512 198.587 169.462 198.586 255.809"
                  stroke="#E55959"
                  stroke-width="2.43506"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_8"
                  d="M194.572 252.672L198.533 256.987L202.376 252.955"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
                <path
                  id="Vector 27_9"
                  d="M202.476 39.0125L198.389 34.8162L194.667 38.9601"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 21_9"
                  d="M198.558 36.166C198.557 122.512 198.587 169.462 198.586 255.809"
                  stroke="#EDA7A7"
                  stroke-width="2.43506"
                  stroke-linecap="square"
                  stroke-dasharray="4.87 4.87"
                />
                <path
                  id="Vector 26_9"
                  d="M194.572 252.672L198.533 256.987L202.376 252.955"
                  stroke="#EDA7A7"
                  stroke-width="1.94805"
                />
                <path
                  id="Vector 27_10"
                  d="M202.476 39.0125L198.389 34.8162L194.667 38.9601"
                  stroke="#EDA7A7"
                  stroke-width="1.94805"
                />
              </g>
            )}
            {findMeasure('product_high_waist_circumference') && (
              <g id="product_high_waist_circumference">
                <path
                  id="Vector 21_10"
                  d="M206.105 112.714L141.804 112.714"
                  stroke="#E55959"
                  stroke-width="2.43506"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_10"
                  d="M144.56 108.975L140.115 112.79L144.016 116.765"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
                <path
                  id="Vector 27_11"
                  d="M204.873 116.961L208.505 112.364L203.914 109.211"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
              </g>
            )}
            {findMeasure('product_high_waist_width') && (
              <g id="product_high_waist_width">
                <path
                  id="Vector 21_11"
                  d="M206.105 112.714L141.804 112.714"
                  stroke="#E55959"
                  stroke-width="2.43506"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_11"
                  d="M144.56 108.975L140.115 112.79L144.016 116.765"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
                <path
                  id="Vector 27_12"
                  d="M204.873 116.961L208.505 112.364L203.914 109.211"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_circumference') && (
              <g id="product_lower_waist_circumference">
                <path
                  id="Vector 21_12"
                  d="M206.105 112.714L141.804 112.714"
                  stroke="#E55959"
                  stroke-width="2.43506"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_12"
                  d="M144.56 108.975L140.115 112.79L144.016 116.765"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
                <path
                  id="Vector 27_13"
                  d="M204.873 116.961L208.505 112.364L203.914 109.211"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_width') && (
              <g id="product_lower_waist_width">
                <path
                  id="Vector 21_13"
                  d="M206.105 112.714L141.804 112.714"
                  stroke="#E55959"
                  stroke-width="2.43506"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_13"
                  d="M144.56 108.975L140.115 112.79L144.016 116.765"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
                <path
                  id="Vector 27_14"
                  d="M204.873 116.961L208.505 112.364L203.914 109.211"
                  stroke="#E55959"
                  stroke-width="1.94805"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_208_4972"
              x1="149.344"
              y1="215.695"
              x2="149.344"
              y2="257.972"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#B6B6B6" />
              <stop offset="1" stop-color="#E8E8E8" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_208_4972"
              x1="162.254"
              y1="230.17"
              x2="162.254"
              y2="254.685"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#B6B6B6" />
              <stop offset="1" stop-color="#DFDFDF" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_208_4972"
              x1="218.197"
              y1="235.455"
              x2="218.197"
              y2="259.971"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#B6B6B6" />
              <stop offset="1" stop-color="#E3E3E3" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_208_4972"
              x1="178.852"
              y1="215.006"
              x2="178.852"
              y2="257.283"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#B6B6B6" />
              <stop offset="1" stop-color="#E7E7E7" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_208_4972"
              x1="202.213"
              y1="217.763"
              x2="202.213"
              y2="260.04"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#B6B6B6" />
              <stop offset="1" stop-color="#E4E4E4" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
