import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Notification as NotificationComponent } from './notification';
import { useAppContext } from '@/store';
import { getMappedLanguages } from '@/lib/get-mapped-languages';

export interface ObservationProps {
  /** The observation text content */
  observationText: string;
  /** Array of observation translations */
  observationTranslations?: ObservationTranslations;
  /** Optional title for the observation */
  title?: string;
  /** Additional CSS classes */
  className?: string;
}

export type ObservationTranslations = {
  id: string;
  modelingId: string;
  language: string;
  observation: string;
}[];

type Translation = {
  [key: string]: string;
};

/**
 * Parses the language from different formats to a standardized format
 */
const parseLanguage = (language: string) => {
  if (['esAR', 'esAR'].includes(language)) return 'esAR';
  if (['pt-BR', 'ptBR', 'ptBr', 'br'].includes(language)) return 'ptBR';
  if (['pt', 'pt-PT', 'ptPT', 'PT'].includes(language)) return 'pt';
  if (['cs', 'cz'].includes(language)) return 'cs';
  if (['gr', 'el'].includes(language)) return 'el';
  if (['dk', 'da'].includes(language)) return 'da';

  return language;
};

/**
 * Parses translation text and extracts language-specific content
 */
const parseTranslation = (text: string, lang: string, languages: string[]) => {
  const matchContent = text.matchAll(/\[([^\]]+)\]([^[\]]+)/g);
  const translations: Translation = {};

  for (const match of matchContent) {
    const langCode = match[1].match(/\w+/)?.[0];
    const content = match[2];

    if (langCode) {
      translations[langCode] = content;
    }
  }

  const translationsKey = Object.keys(translations);

  if (!translationsKey.length || languages.length === 1) return text;

  return translations[lang] || translations[translationsKey[0]];
};

/**
 * Observation component that displays localized observation text
 * Uses the Notification component internally for consistent styling
 */
export function Observation({ observationText, observationTranslations, title, className }: ObservationProps) {
  const {
    app: { config },
  } = useAppContext();
  const { i18n } = useTranslation();

  const { value: innerLanguage } = getMappedLanguages(i18n.language);
  const sanitizedLanguage = parseLanguage(innerLanguage.split('-')[0]);

  const newModelingObservationTranslation = useMemo(
    () =>
      observationTranslations?.find(({ language }) => language === parseLanguage(sanitizedLanguage)) ||
      observationTranslations?.[0],
    [sanitizedLanguage, observationTranslations]
  );

  const parsedTranslation = parseTranslation(observationText, sanitizedLanguage, config.general.language);

  const finalObservationText = observationTranslations?.length
    ? newModelingObservationTranslation?.observation || parsedTranslation
    : parsedTranslation;

  if (!observationText && !observationTranslations?.length) return null;

  // Create a temporary div to handle HTML content and extract text for the Notification component
  const createMarkup = (htmlString: string) => {
    return { __html: htmlString };
  };

  // If the content contains HTML, we need to render it differently
  const containsHTML = /<[^>]*>/g.test(finalObservationText);

  if (containsHTML) {
    return (
      <div className={className}>
        <div
          className="max-w-full flex flex-col items-center text-center bg-[#F6F6F6] p-2 rounded-lg"
          dangerouslySetInnerHTML={createMarkup(finalObservationText)}
        />
      </div>
    );
  }

  // Use the Notification component for non-HTML content
  return (
    <NotificationComponent
      title={title}
      content={finalObservationText}
      className={className}
    />
  );
}
