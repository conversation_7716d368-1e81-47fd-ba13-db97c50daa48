import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppContext } from '@/store';
import { getMappedLanguages } from '@/lib/get-mapped-languages';
import { Text } from '../atoms/text';
import { cn } from '@/lib/utils';

export interface ObservationProps {
  /** The observation text content */
  observationText: string;
  /** Array of observation translations */
  observationTranslations?: ObservationTranslations;
  /** Additional CSS classes */
  className?: string;
}

export type ObservationTranslations = {
  id: string;
  modelingId: string;
  language: string;
  observation: string;
}[];

type Translation = {
  [key: string]: string;
};

/**
 * Parses the language from different formats to a standardized format
 */
const parseLanguage = (language: string) => {
  if (['esAR', 'esAR'].includes(language)) return 'esAR';
  if (['pt-BR', 'ptBR', 'ptBr', 'br'].includes(language)) return 'ptBR';
  if (['pt', 'pt-PT', 'ptPT', 'PT'].includes(language)) return 'pt';
  if (['cs', 'cz'].includes(language)) return 'cs';
  if (['gr', 'el'].includes(language)) return 'el';
  if (['dk', 'da'].includes(language)) return 'da';

  return language;
};

/**
 * Parses translation text and extracts language-specific content
 */
const parseTranslation = (text: string, lang: string, languages: string[]) => {
  const matchContent = text.matchAll(/\[([^\]]+)\]([^[\]]+)/g);
  const translations: Translation = {};

  for (const match of matchContent) {
    const langCode = match[1].match(/\w+/)?.[0];
    const content = match[2];

    if (langCode) {
      translations[langCode] = content;
    }
  }

  const translationsKey = Object.keys(translations);

  if (!translationsKey.length || languages.length === 1) return text;

  return translations[lang] || translations[translationsKey[0]];
};

/**
 * Observation component that displays localized observation text
 * Similar to the original Observation component behavior
 */
export function Observation({ observationText, observationTranslations }: ObservationProps) {
  const {
    app: { config },
  } = useAppContext();
  const { i18n } = useTranslation();

  const { language: innerLanguage } = getMappedLanguages(i18n.language);
  const sanitizedLanguage = parseLanguage(innerLanguage.split('-')[0]);

  const newModelingObservationTranslation = useMemo(
    () =>
      observationTranslations?.find(({ language }) => language === parseLanguage(sanitizedLanguage)) ||
      observationTranslations?.[0],
    [sanitizedLanguage, observationTranslations]
  );

  const parsedTranslation = parseTranslation(observationText, sanitizedLanguage, config.general.language);

  if (!observationText && !observationTranslations?.length) return <></>;

  return (
    <div
      className={cn(
        'max-w-full flex flex-col items-center text-center bg-[#F6F6F6] p-2 rounded-lg'
      )}
    >
      <Text
        variant="body"
        className="text-sm"
        dangerouslySetInnerHTML={{
          __html: observationTranslations?.length
            ? newModelingObservationTranslation?.observation || parsedTranslation
            : parsedTranslation,
        }}
      />
    </div>
  );
}
