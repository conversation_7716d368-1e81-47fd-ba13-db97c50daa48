name: Run E2E Tests

on:
  pull_request:
    branches:
      - main
      - develop

jobs:
  cypress-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4.1.7

      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '22.13.1'
      
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10

      - name: Install Dependencies
        run: pnpm install

      - name: Cypress run
        uses: cypress-io/github-action@v6
        with:
          build: pnpm build
          start: pnpm preview:test

      - name: Upload screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-screenshots
          path: cypress/screenshots

      - name: Check coverage with NYC
        run: pnpm coverage:check