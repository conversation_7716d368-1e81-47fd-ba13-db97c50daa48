import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface SportsBraProps {
  measure: GarmentMeasure;
  className?: string;
}

export function SportsBra({ measure, className }: SportsBraProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_sports_bra">
            <g id="sports_bra">
              <g id="Group 95">
                <path
                  id="Union"
                  d="M186.37 76.5341C186.111 76.0525 181.46 67.1426 187.92 57.2696C193.089 49.3711 203.167 40.0117 207.561 36.3194L209.37 27.1687L211.179 18.7405L209.37 15.8508L208.336 14.1652C208.336 14.1652 207.044 12.9611 202.651 17.0548C198.257 21.1485 189.908 27.2296 180.168 30.0584C174.51 31.7014 171.128 32.2769 165.237 32.2105C159.773 32.149 156.654 31.5982 151.411 30.0584C141.756 27.223 133.513 21.1485 129.167 17.0548C125.35 13.4596 124.578 14.1812 124.578 14.1812L122.519 15.8508L120.73 18.7405L122.519 27.1687L124.309 36.3194C128.656 40.0117 138.627 49.3711 143.74 57.2696C150.132 67.1426 144.56 77.7469 144.304 78.2285L153.968 82.3134L164.992 86.4667V86.648L165.243 86.5614L165.473 86.648V86.4823L177.583 82.3134L186.37 76.5341Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.481112"
                />
                <g id="Group 94">
                  <path
                    id="Vector 167"
                    d="M125.06 31.2607C125.06 31.2607 149.596 50.5533 151.521 62.2925C153.445 74.0316 151.521 80.0936 151.521 80.0936"
                    stroke="black"
                    stroke-width="0.481112"
                  />
                  <path
                    id="Vector 168"
                    d="M207.463 31.5012C207.463 31.5012 182.927 50.7938 181.002 62.533C179.078 74.2721 181.109 79.3719 181.109 79.3719"
                    stroke="black"
                    stroke-width="0.481112"
                  />
                  <path
                    id="Vector 169"
                    d="M123.857 18.5113C124.338 17.3085 125.781 18.5113 128.427 20.6763C135.245 26.2545 138.557 28.0589 148.394 32.7041C157.054 36.7935 165.954 36.0719 165.954 36.0719"
                    stroke="black"
                    stroke-width="0.481112"
                  />
                  <path
                    id="Vector 170"
                    d="M208.773 18.2707C208.292 17.068 206.608 18.5113 203.962 20.6763C197.144 26.2545 191.427 30.1148 182.071 33.1852C174.396 35.7043 165.714 36.0719 165.714 36.0719"
                    stroke="black"
                    stroke-width="0.481112"
                  />
                </g>
              </g>
              <g id="Group 93">
                <path
                  id="Union_2"
                  d="M107.74 19.7139L124.338 14.4217C123.777 15.865 123.183 21.4459 125.3 33.1851C127.946 47.859 131.314 66.6224 146.229 77.2069C153.185 82.1437 158.475 82.5154 166.676 82.4991C174.784 82.483 179.824 80.5361 186.882 76.2446C202.789 66.5738 204.684 47.859 207.33 33.1851C209.447 21.4459 208.853 15.6245 208.292 14.1812L224.89 19.7139C225.169 22.2818 225.468 25.1607 225.787 28.2287C227.581 45.4714 229.995 68.6886 232.829 76.2446C236.812 86.866 245.117 96.6747 250.032 102.48C250.237 102.723 250.437 102.958 250.63 103.187L247.022 172.948C246.42 173.09 245.787 173.244 245.118 173.407C237.836 175.176 226.211 178 203.962 178H128.668C106.418 178 94.7936 175.176 87.5123 173.407C86.8425 173.244 86.2095 173.09 85.6083 172.948L82 103.187C82.1931 102.958 82.3928 102.723 82.5983 102.48C87.5134 96.6747 95.8181 86.866 99.8012 76.2446C102.635 68.6885 105.049 45.4712 106.842 28.2286C107.162 25.1606 107.461 22.2817 107.74 19.7139Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.481112"
                />
                <g id="Group 88">
                  <path
                    id="Vector 157"
                    d="M84.4056 149.133C101.485 150.737 144.015 153.944 172.69 153.944C201.364 153.944 236.437 150.015 248.224 148.412"
                    stroke="black"
                    stroke-width="0.481112"
                  />
                  <path
                    id="Vector 158"
                    d="M84.4055 151.539C101.485 153.143 144.015 156.35 172.69 156.35C201.364 156.35 236.437 152.421 248.224 150.817"
                    stroke="black"
                    stroke-width="0.481112"
                    stroke-dasharray="0.96 0.96"
                  />
                </g>
                <g id="Group 92">
                  <path
                    id="Vector 161"
                    d="M120.008 15.6245C120.008 15.6245 121.665 45.1051 129.149 59.1652C137.087 74.0797 142.62 80.0936 153.686 83.7019C161.672 86.3061 167.638 85.1452 167.638 85.1452"
                    stroke="black"
                    stroke-width="0.481112"
                  />
                  <path
                    id="Vector 162"
                    d="M212.381 15.6245C212.381 15.6245 211.346 45.6437 202.519 60.6085C195.634 72.2805 191.453 78.6502 178.463 82.9802C174.24 84.3878 167.397 85.1452 167.397 85.1452"
                    stroke="black"
                    stroke-width="0.481112"
                  />
                </g>
                <g id="Group 89">
                  <path
                    id="Vector 165"
                    d="M135.885 70.4712C135.885 70.4712 124.097 84.1829 108.942 104.149C93.7872 124.115 83.9244 138.789 83.9244 138.789"
                    stroke="black"
                    stroke-width="0.481112"
                  />
                  <path
                    id="Vector 166"
                    d="M196.264 70.4712C196.264 70.4712 208.051 84.1829 223.206 104.149C238.362 124.115 248.705 139.27 248.705 139.27"
                    stroke="black"
                    stroke-width="0.481112"
                  />
                </g>
                <g id="Group 90">
                  <path
                    id="Vector 159"
                    d="M82.2406 107.758C83.6839 107.758 99.5606 84.6642 102.207 77.9286C106.207 67.7454 107.74 48.8213 110.867 18.5112"
                    stroke="black"
                    stroke-width="0.481112"
                    stroke-dasharray="0.96 0.96"
                  />
                  <path id="Vector 163" d="M108.221 45.4536L129.871 60.6087" stroke="black" stroke-width="0.481112" />
                </g>
                <g id="Group 91">
                  <path
                    id="Vector 160"
                    d="M250.63 107.758C249.187 107.758 233.31 84.6642 230.664 77.9286C226.663 67.7454 225.131 48.8213 222.004 18.5112"
                    stroke="black"
                    stroke-width="0.481112"
                    stroke-dasharray="0.96 0.96"
                  />
                  <path id="Vector 164" d="M224.65 43.5291L203.24 59.1652" stroke="black" stroke-width="0.481112" />
                </g>
              </g>
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19"
                  d="M246.3 117.68L86.2549 117.68"
                  stroke="#E55959"
                  stroke-width="2.40556"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27"
                  d="M88.6304 113.785L84.5763 117.915L88.7513 121.499"
                  stroke="#E55959"
                  stroke-width="1.92445"
                />
                <path
                  id="Vector 28"
                  d="M243.679 114.153L248.064 117.556L246.14 119.417L244.217 121.278"
                  stroke="#E55959"
                  stroke-width="1.92445"
                />
              </g>
            )}
            {findMeasure('product_underbust') && (
              <g id="product_underbust">
                <path
                  id="Vector 19_2"
                  d="M244.135 165.791L88.6605 165.791"
                  stroke="#E55959"
                  stroke-width="2.40556"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M91.0359 161.896L86.9819 166.026L91.1569 169.61"
                  stroke="#E55959"
                  stroke-width="1.92445"
                />
                <path
                  id="Vector 28_2"
                  d="M241.273 162.264L245.659 165.668L243.735 167.529L241.811 169.39"
                  stroke="#E55959"
                  stroke-width="1.92445"
                />
              </g>
            )}
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 19_3"
                  d="M244.135 165.791L88.6605 165.791"
                  stroke="#E55959"
                  stroke-width="2.40556"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M91.0359 161.896L86.9819 166.026L91.1569 169.61"
                  stroke="#E55959"
                  stroke-width="1.92445"
                />
                <path
                  id="Vector 28_3"
                  d="M241.273 162.264L245.659 165.668L243.735 167.529L241.811 169.39"
                  stroke="#E55959"
                  stroke-width="1.92445"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_4"
                  d="M115.437 20.7356L115.437 174.21"
                  stroke="#E55959"
                  stroke-width="2.40556"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M111.543 171.835L115.672 175.889L119.256 171.714"
                  stroke="#E55959"
                  stroke-width="1.92445"
                />
                <path
                  id="Vector 28_4"
                  d="M111.91 23.1718L115.314 18.7866L117.175 20.7104L119.036 22.6343"
                  stroke="#E55959"
                  stroke-width="1.92445"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_5"
                  d="M115.437 20.7356L115.437 174.21"
                  stroke="#E55959"
                  stroke-width="2.40556"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_5"
                  d="M111.543 171.835L115.672 175.889L119.256 171.714"
                  stroke="#E55959"
                  stroke-width="1.92445"
                />
                <path
                  id="Vector 28_5"
                  d="M111.91 23.1718L115.314 18.7866L117.175 20.7104L119.036 22.6343"
                  stroke="#E55959"
                  stroke-width="1.92445"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_6"
                  d="M246.3 117.68L86.2549 117.68"
                  stroke="#E55959"
                  stroke-width="2.40556"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M88.6304 113.785L84.5763 117.915L88.7513 121.499"
                  stroke="#E55959"
                  stroke-width="1.92445"
                />
                <path
                  id="Vector 28_6"
                  d="M243.679 114.153L248.064 117.556L246.14 119.417L244.217 121.278"
                  stroke="#E55959"
                  stroke-width="1.92445"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_7"
                  d="M115.437 20.7356L115.437 174.21"
                  stroke="#EDA7A7"
                  stroke-width="2.40556"
                  stroke-linecap="square"
                  stroke-dasharray="4.81 4.81"
                />
                <path
                  id="Vector 27_7"
                  d="M111.543 171.835L115.672 175.889L119.256 171.714"
                  stroke="#EDA7A7"
                  stroke-width="1.92445"
                />
                <path
                  id="Vector 28_7"
                  d="M111.91 23.1718L115.314 18.7866L117.175 20.7104L119.036 22.6343"
                  stroke="#EDA7A7"
                  stroke-width="1.92445"
                />
              </g>
            )}
          </g>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_sports_bra">
            <g id="sports_bra">
              <g id="Group 95">
                <path
                  id="Union"
                  d="M198.554 118.412C198.201 117.755 191.847 105.585 200.672 92.0999C207.731 81.3117 221.497 68.5282 227.497 63.485L229.968 50.9866L232.439 39.4748L229.968 35.5279L228.556 33.2256C228.556 33.2256 226.792 31.581 220.791 37.1724C214.79 42.7639 203.387 51.0697 190.082 54.9334C182.355 57.1775 177.735 57.9635 169.69 57.8729C162.226 57.7889 157.966 57.0366 150.805 54.9334C137.618 51.0607 126.36 42.7639 120.423 37.1724C115.209 32.2619 114.156 33.2475 114.156 33.2475L111.343 35.5279L108.899 39.4748L111.343 50.9866L113.788 63.485C119.725 68.5282 133.344 81.3117 140.328 92.0999C149.059 105.585 141.447 120.069 141.098 120.727L154.297 126.306L169.355 131.979V132.226L169.698 132.108L170.012 132.226V132L186.553 126.306L198.554 118.412Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.657129"
                />
                <g id="Group 94">
                  <path
                    id="Vector 167"
                    d="M114.813 56.5757C114.813 56.5757 148.326 82.9266 150.955 98.9605C153.583 114.994 150.955 123.274 150.955 123.274"
                    stroke="black"
                    stroke-width="0.657129"
                  />
                  <path
                    id="Vector 168"
                    d="M227.365 56.9041C227.365 56.9041 193.851 83.2549 191.222 99.2889C188.594 115.323 191.368 122.288 191.368 122.288"
                    stroke="black"
                    stroke-width="0.657129"
                  />
                  <path
                    id="Vector 169"
                    d="M113.17 39.1617C113.827 37.5189 115.799 39.1617 119.413 42.1188C128.725 49.7379 133.248 52.2024 146.684 58.547C158.512 64.1326 170.669 63.1469 170.669 63.1469"
                    stroke="black"
                    stroke-width="0.657129"
                  />
                  <path
                    id="Vector 170"
                    d="M229.153 38.8331C228.496 37.1903 226.196 39.1616 222.582 42.1187C213.27 49.7378 205.46 55.0103 192.683 59.2041C182.2 62.6447 170.34 63.1468 170.34 63.1468"
                    stroke="black"
                    stroke-width="0.657129"
                  />
                </g>
              </g>
              <g id="Group 93">
                <path
                  id="Union_2"
                  d="M91.1564 40.8045L113.827 33.5761C113.061 35.5475 112.25 43.1702 115.142 59.2042C118.756 79.2466 123.356 104.875 143.727 119.331C153.228 126.074 160.453 126.582 171.655 126.56C182.73 126.538 189.613 123.879 199.254 118.017C220.98 104.808 223.568 79.2466 227.182 59.2042C230.073 43.1702 229.263 35.2189 228.496 33.2476L251.167 40.8045C251.548 44.3119 251.957 48.244 252.393 52.4345C254.842 75.9854 258.14 107.697 262.01 118.017C267.45 132.524 278.793 145.922 285.507 153.851C285.787 154.182 286.06 154.504 286.324 154.816L281.395 250.1C280.574 250.294 279.71 250.504 278.795 250.726C268.85 253.142 252.972 257 222.582 257H119.742C89.352 257 73.4742 253.142 63.529 250.726C62.6142 250.504 61.7496 250.294 60.9285 250.1L56 154.816C56.2638 154.504 56.5365 154.182 56.8172 153.851C63.5304 145.922 74.8736 132.524 80.3138 118.017C84.184 107.697 87.4819 75.9852 89.9312 52.4343C90.367 48.2438 90.7759 44.3118 91.1564 40.8045Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.657129"
                />
                <g id="Group 88">
                  <path
                    id="Vector 157"
                    d="M59.2856 217.572C82.6137 219.763 140.704 224.143 179.869 224.143C219.034 224.143 266.938 218.777 283.038 216.586"
                    stroke="black"
                    stroke-width="0.657129"
                  />
                  <path
                    id="Vector 158"
                    d="M59.2856 220.858C82.6137 223.048 140.704 227.429 179.869 227.429C219.034 227.429 266.938 222.063 283.038 219.872"
                    stroke="black"
                    stroke-width="0.657129"
                    stroke-dasharray="1.31 1.31"
                  />
                </g>
                <g id="Group 92">
                  <path
                    id="Vector 161"
                    d="M107.913 35.219C107.913 35.219 110.177 75.4852 120.399 94.6892C131.241 115.06 138.798 123.274 153.912 128.203C164.82 131.76 172.969 130.174 172.969 130.174"
                    stroke="black"
                    stroke-width="0.657129"
                  />
                  <path
                    id="Vector 162"
                    d="M234.082 35.219C234.082 35.219 232.668 76.2208 220.611 96.6606C211.207 112.603 205.497 121.303 187.754 127.217C181.987 129.14 172.64 130.174 172.64 130.174"
                    stroke="black"
                    stroke-width="0.657129"
                  />
                </g>
                <g id="Group 89">
                  <path
                    id="Vector 165"
                    d="M129.598 110.132C129.598 110.132 113.499 128.86 92.7992 156.131C72.0997 183.402 58.6285 203.444 58.6285 203.444"
                    stroke="black"
                    stroke-width="0.657129"
                  />
                  <path
                    id="Vector 166"
                    d="M212.068 110.132C212.068 110.132 228.168 128.86 248.867 156.131C269.567 183.402 283.695 204.101 283.695 204.101"
                    stroke="black"
                    stroke-width="0.657129"
                  />
                </g>
                <g id="Group 90">
                  <path
                    id="Vector 159"
                    d="M56.3286 161.059C58.3 161.059 79.9853 129.517 83.5995 120.317C89.0636 106.408 91.1565 80.5608 95.4278 39.1616"
                    stroke="black"
                    stroke-width="0.657129"
                    stroke-dasharray="1.31 1.31"
                  />
                  <path id="Vector 163" d="M91.8136 75.9609L121.384 96.6605" stroke="black" stroke-width="0.657129" />
                </g>
                <g id="Group 91">
                  <path
                    id="Vector 160"
                    d="M286.324 161.059C284.352 161.059 262.667 129.517 259.053 120.317C253.589 106.408 251.496 80.5608 247.225 39.1616"
                    stroke="black"
                    stroke-width="0.657129"
                    stroke-dasharray="1.31 1.31"
                  />
                  <path id="Vector 164" d="M250.839 73.3323L221.597 94.689" stroke="black" stroke-width="0.657129" />
                </g>
              </g>
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19"
                  d="M280.409 174.611L61.8115 174.611"
                  stroke="#E55959"
                  stroke-width="3.28565"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27"
                  d="M65.0561 169.292L59.5189 174.932L65.2213 179.828"
                  stroke="#E55959"
                  stroke-width="2.62852"
                />
                <path
                  id="Vector 28"
                  d="M276.83 169.794L282.819 174.443L280.192 176.984L277.564 179.526"
                  stroke="#E55959"
                  stroke-width="2.62852"
                />
              </g>
            )}
            {findMeasure('product_under_bust') && (
              <g id="product_underbust">
                <path
                  id="Vector 19_2"
                  d="M277.453 240.324L65.0973 240.324"
                  stroke="#E55959"
                  stroke-width="3.28565"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M68.3418 235.005L62.8046 240.645L68.507 245.541"
                  stroke="#E55959"
                  stroke-width="2.62852"
                />
                <path
                  id="Vector 28_2"
                  d="M273.544 235.506L279.534 240.156L276.906 242.697L274.278 245.239"
                  stroke="#E55959"
                  stroke-width="2.62852"
                />
              </g>
            )}
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 19_3"
                  d="M277.453 240.324L65.0973 240.324"
                  stroke="#E55959"
                  stroke-width="3.28565"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M68.3418 235.005L62.8046 240.645L68.507 245.541"
                  stroke="#E55959"
                  stroke-width="2.62852"
                />
                <path
                  id="Vector 28_3"
                  d="M273.544 235.506L279.534 240.156L276.906 242.697L274.278 245.239"
                  stroke="#E55959"
                  stroke-width="2.62852"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_4"
                  d="M101.67 42.1997L101.67 251.824"
                  stroke="#E55959"
                  stroke-width="3.28565"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M96.3507 248.58L101.991 254.117L106.887 248.414"
                  stroke="#E55959"
                  stroke-width="2.62852"
                />
                <path
                  id="Vector 28_4"
                  d="M96.8529 45.5276L101.502 39.538L104.044 42.1657L106.586 44.7934"
                  stroke="#E55959"
                  stroke-width="2.62852"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_5"
                  d="M101.67 42.1997L101.67 251.824"
                  stroke="#E55959"
                  stroke-width="3.28565"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_5"
                  d="M96.3507 248.58L101.991 254.117L106.887 248.414"
                  stroke="#E55959"
                  stroke-width="2.62852"
                />
                <path
                  id="Vector 28_5"
                  d="M96.8529 45.5276L101.502 39.538L104.044 42.1657L106.586 44.7934"
                  stroke="#E55959"
                  stroke-width="2.62852"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_6"
                  d="M280.409 174.611L61.8115 174.611"
                  stroke="#E55959"
                  stroke-width="3.28565"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M65.0561 169.292L59.5189 174.932L65.2213 179.828"
                  stroke="#E55959"
                  stroke-width="2.62852"
                />
                <path
                  id="Vector 28_6"
                  d="M276.83 169.794L282.819 174.443L280.192 176.984L277.564 179.526"
                  stroke="#E55959"
                  stroke-width="2.62852"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_7"
                  d="M101.67 42.1997L101.67 251.824"
                  stroke="#EDA7A7"
                  stroke-width="3.28565"
                  stroke-linecap="square"
                  stroke-dasharray="6.57 6.57"
                />
                <path
                  id="Vector 27_7"
                  d="M96.3507 248.58L101.991 254.117L106.887 248.414"
                  stroke="#EDA7A7"
                  stroke-width="2.62852"
                />
                <path
                  id="Vector 28_7"
                  d="M96.8529 45.5276L101.502 39.538L104.044 42.1657L106.586 44.7934"
                  stroke="#EDA7A7"
                  stroke-width="2.62852"
                />
              </g>
            )}
          </g>
        </svg>
      )}
    </div>
  );
}
