import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface BraProps {
  measure: GarmentMeasure;
  className?: string;
}

export function Bra({ measure, className }: BraProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_bra">
            <g id="bra">
              <rect
                id="Rectangle 6"
                x="87.2439"
                y="20.2443"
                width="5.37398"
                height="85.0065"
                rx="2.68699"
                fill="white"
                stroke="black"
                stroke-width="0.488543"
              />
              <rect
                id="Rectangle 7"
                x="240.646"
                y="20.2443"
                width="5.37398"
                height="85.0065"
                rx="2.68699"
                fill="white"
                stroke="black"
                stroke-width="0.488543"
              />
              <path
                id="Union"
                d="M55 114.045C59.4783 113.149 68.7374 109.378 75.0303 103.053C81.2665 96.7839 83.8241 89.6177 85.8726 83.1751C86.1914 82.5007 86.4868 81.8755 86.7553 81.3125C95.2234 82.941 115.726 88.7384 129.991 98.9001C144.257 109.062 157.106 121.699 161.747 126.747H170.296C174.937 121.699 187.786 109.062 202.052 98.9001C216.317 88.7384 236.82 82.941 245.288 81.3125C245.556 81.8755 245.852 82.5006 246.17 83.175C248.848 88.8407 250.776 96.7839 257.013 103.053C263.306 109.378 272.565 113.149 277.043 114.045L266.295 163.632C263.608 163.632 257.931 164.174 251.639 165.586C247.764 166.456 244.811 167.15 242.051 167.8C236.283 169.156 231.354 170.315 220.616 172.463C207.914 175.003 184.953 167.947 176.403 163.632L166.021 162.286L155.64 163.632C147.09 167.947 124.129 175.003 111.427 172.463C100.688 170.315 95.7602 169.156 89.9921 167.8C87.2315 167.15 84.2786 166.456 80.4042 165.586C74.1121 164.174 68.4349 163.632 65.748 163.632L55 114.045Z"
                fill="white"
              />
              <path
                id="Union_2"
                d="M55 114.045C59.4783 113.149 68.7374 109.378 75.0303 103.053C81.2665 96.7839 84.5569 88.8849 86.7553 81.3125C95.2234 82.941 115.726 88.7384 129.991 98.9001C144.257 109.062 157.106 121.699 161.747 126.747H170.296C174.937 121.699 187.786 109.062 202.052 98.9001C216.317 88.7384 236.82 82.941 245.288 81.3125C247.965 86.9782 250.138 96.2856 257.013 103.053C263.371 109.312 272.565 113.149 277.043 114.045L266.295 163.632C263.608 163.632 257.931 164.174 251.639 165.586C247.764 166.456 244.811 167.15 242.051 167.8C236.283 169.156 231.354 170.315 220.616 172.463C207.914 175.003 184.953 167.947 176.403 163.632L166.021 162.286L155.64 163.632C147.09 167.947 124.129 175.003 111.427 172.463C100.688 170.315 95.7602 169.156 89.9921 167.8C87.2315 167.15 84.2786 166.456 80.4042 165.586C74.1121 164.174 68.4349 163.632 65.748 163.632L55 114.045Z"
                stroke="black"
                stroke-width="0.488543"
              />
              <path
                id="Vector 306"
                d="M65.0151 161.677C65.0151 161.677 71.0603 160.759 79.9157 162.41C94.3277 165.097 109.717 170.715 120.953 170.715C132.19 170.715 159.304 158.99 159.304 158.99H160.525"
                stroke="black"
                stroke-width="0.488543"
                stroke-dasharray="0.98 0.98"
              />
              <path
                id="Vector 307"
                d="M266.295 161.677C266.295 161.677 260.25 160.759 251.394 162.41C236.982 165.097 221.593 170.715 210.357 170.715C199.12 170.715 172.006 158.99 172.006 158.99H160.281"
                stroke="black"
                stroke-width="0.488543"
                stroke-dasharray="0.98 0.98"
              />
              <g id="Group 181">
                <path id="Vector 305" d="M122.175 164.608V172.914" stroke="black" stroke-width="0.488543" />
                <path
                  id="Vector 304"
                  d="M68.1907 113.801C69.6563 128.701 89.3741 160.258 114.602 161.189C136.4 161.994 154.907 143.113 157.594 128.701"
                  stroke="black"
                  stroke-width="0.488543"
                  stroke-dasharray="0.98 0.98"
                />
                <path
                  id="Vector 303"
                  d="M90.1751 84.9766C90.1751 84.9766 103.785 85.9536 126.816 99.3886C138.541 106.228 158.327 124.304 161.258 129.434"
                  stroke="black"
                  stroke-width="0.488543"
                  stroke-dasharray="0.98 0.98"
                />
                <path
                  id="Vector 302"
                  d="M89.198 86.9307C100.109 90.1876 102.993 88.9334 128.037 103.297C144.648 112.823 155.396 125.77 160.525 131.877"
                  stroke="black"
                  stroke-width="0.488543"
                />
                <path
                  id="Vector 301"
                  d="M55.4885 117.22C59.6412 116.487 66.5444 112.748 72.3433 108.182C82.1157 100.488 86.2668 90.3503 89.4423 81.8008"
                  stroke="black"
                  stroke-width="0.488543"
                  stroke-dasharray="0.98 0.98"
                />
                <path
                  id="Vector 300"
                  d="M56.2213 119.174C61.2696 117.627 69.0933 113.834 75.5188 107.938C84.186 99.985 88.2209 90.3502 90.9079 82.0449"
                  stroke="black"
                  stroke-width="0.488543"
                />
                <path
                  id="Vector 299"
                  d="M161.747 126.503C156.861 151.907 131.464 170.767 105.808 162.899C69.1677 151.663 64.7709 115.755 64.7709 115.755"
                  stroke="black"
                  stroke-width="0.488543"
                />
              </g>
              <g id="Group 182">
                <path id="Vector 305_2" d="M210.601 164.608V172.914" stroke="black" stroke-width="0.488543" />
                <path
                  id="Vector 304_2"
                  d="M264.585 113.801C263.119 128.701 243.402 160.258 218.173 161.189C196.376 161.994 177.869 143.113 175.182 128.701"
                  stroke="black"
                  stroke-width="0.488543"
                  stroke-dasharray="0.98 0.98"
                />
                <path
                  id="Vector 303_2"
                  d="M242.601 84.9766C242.601 84.9766 228.991 85.9536 205.96 99.3886C194.235 106.228 174.449 124.304 171.518 129.434"
                  stroke="black"
                  stroke-width="0.488543"
                  stroke-dasharray="0.98 0.98"
                />
                <path
                  id="Vector 302_2"
                  d="M243.578 86.9307C232.667 90.1876 229.783 88.9334 204.739 103.297C188.128 112.823 177.136 126.014 172.006 132.121"
                  stroke="black"
                  stroke-width="0.488543"
                />
                <path
                  id="Vector 301_2"
                  d="M276.554 116.976C272.402 116.243 266.231 112.748 260.432 108.182C250.66 100.488 246.509 90.3503 243.333 81.8008"
                  stroke="black"
                  stroke-width="0.488543"
                  stroke-dasharray="0.98 0.98"
                />
                <path
                  id="Vector 300_2"
                  d="M276.066 119.174C271.018 117.627 263.682 113.834 257.257 107.938C248.59 99.985 244.555 90.3502 241.868 82.0449"
                  stroke="black"
                  stroke-width="0.488543"
                />
                <path
                  id="Vector 299_2"
                  d="M170.541 126.747C175.426 152.151 201.312 170.767 226.967 162.899C263.608 151.663 268.005 115.755 268.005 115.755"
                  stroke="black"
                  stroke-width="0.488543"
                />
              </g>
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 21"
                  d="M273.623 115.598L59.3818 115.598"
                  stroke="#E55959"
                  stroke-width="2.44272"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M62.1462 111.846L57.687 115.673L61.6007 119.661"
                  stroke="#E55959"
                  stroke-width="1.95417"
                />
                <path
                  id="Vector 27"
                  d="M270.674 119.676L274.723 115.417L270.426 111.846"
                  stroke="#E55959"
                  stroke-width="1.95417"
                />
              </g>
            )}
            {findMeasure('product_underbust') && (
              <g id="product_underbust">
                <path
                  id="Vector 21_2"
                  d="M263.608 156.636L68.4349 156.636"
                  stroke="#E55959"
                  stroke-width="2.44272"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M70.94 152.884L66.4808 156.711L70.3945 160.699"
                  stroke="#E55959"
                  stroke-width="1.95417"
                />
                <path
                  id="Vector 27_2"
                  d="M261.391 160.714L265.441 156.455L261.144 152.884"
                  stroke="#E55959"
                  stroke-width="1.95417"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 21_3"
                  d="M273.623 115.598L59.3818 115.598"
                  stroke="#E55959"
                  stroke-width="2.44272"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M62.1462 111.846L57.687 115.673L61.6007 119.661"
                  stroke="#E55959"
                  stroke-width="1.95417"
                />
                <path
                  id="Vector 27_3"
                  d="M270.674 119.676L274.723 115.417L270.426 111.846"
                  stroke="#E55959"
                  stroke-width="1.95417"
                />
              </g>
            )}
          </g>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_bra">
            <g id="bra">
              <rect
                id="Rectangle 6"
                x="72.2521"
                y="50.3049"
                width="6.70869"
                height="106.119"
                rx="3.35434"
                fill="white"
                stroke="black"
                stroke-width="0.609881"
              />
              <rect
                id="Rectangle 7"
                x="263.755"
                y="50.3049"
                width="6.70869"
                height="106.119"
                rx="3.35434"
                fill="white"
                stroke="black"
                stroke-width="0.609881"
              />
              <path
                id="Union"
                d="M32 167.402C37.5906 166.284 49.1493 161.577 57.0051 153.68C64.7902 145.854 67.983 136.908 70.5403 128.865C70.9382 128.023 71.307 127.243 71.6423 126.54C82.2135 128.573 107.808 135.81 125.617 148.496C143.425 161.181 159.465 176.957 165.259 183.259H175.932C181.726 176.957 197.766 161.181 215.574 148.496C233.383 135.81 258.977 128.573 269.549 126.54C269.884 127.243 270.253 128.023 270.65 128.865C273.993 135.938 276.401 145.854 284.186 153.68C292.042 161.577 303.6 166.284 309.191 167.402L295.773 229.305C292.419 229.305 285.332 229.981 277.477 231.744C272.64 232.83 268.954 233.697 265.508 234.508C258.307 236.201 252.155 237.648 238.75 240.329C222.893 243.5 194.228 234.692 183.555 229.305L170.595 227.625L157.635 229.305C146.963 234.692 118.298 243.5 102.441 240.329C89.0359 237.648 82.8836 236.201 75.683 234.508C72.2367 233.697 68.5504 232.83 63.7138 231.744C55.8589 229.981 48.7717 229.305 45.4174 229.305L32 167.402Z"
                fill="white"
              />
              <path
                id="Union_2"
                d="M32 167.402C37.5906 166.284 49.1493 161.577 57.0051 153.68C64.7902 145.854 68.8978 135.993 71.6423 126.54C82.2135 128.573 107.808 135.81 125.617 148.496C143.425 161.181 159.465 176.957 165.259 183.259H175.932C181.726 176.957 197.766 161.181 215.574 148.496C233.383 135.81 258.977 128.573 269.549 126.54C272.891 133.613 275.604 145.232 284.186 153.68C292.124 161.494 303.6 166.284 309.191 167.402L295.773 229.305C292.419 229.305 285.332 229.981 277.477 231.744C272.64 232.83 268.954 233.697 265.508 234.508C258.307 236.201 252.155 237.648 238.75 240.329C222.893 243.5 194.228 234.692 183.555 229.305L170.595 227.625L157.635 229.305C146.963 234.692 118.298 243.5 102.441 240.329C89.0359 237.648 82.8836 236.201 75.683 234.508C72.2367 233.697 68.5504 232.83 63.7138 231.744C55.8589 229.981 48.7717 229.305 45.4174 229.305L32 167.402Z"
                stroke="black"
                stroke-width="0.609881"
              />
              <path
                id="Vector 306"
                d="M44.5026 226.865C44.5026 226.865 52.0491 225.719 63.1039 227.78C81.0954 231.134 100.307 238.148 114.334 238.148C128.361 238.148 162.21 223.511 162.21 223.511H163.734"
                stroke="black"
                stroke-width="0.609881"
                stroke-dasharray="1.22 1.22"
              />
              <path
                id="Vector 307"
                d="M295.773 226.865C295.773 226.865 288.227 225.719 277.172 227.78C259.181 231.134 239.969 238.148 225.942 238.148C211.915 238.148 178.066 223.511 178.066 223.511H163.429"
                stroke="black"
                stroke-width="0.609881"
                stroke-dasharray="1.22 1.22"
              />
              <g id="Group 181">
                <path id="Vector 305" d="M115.859 230.525V240.893" stroke="black" stroke-width="0.609881" />
                <path
                  id="Vector 304"
                  d="M48.4668 167.098C50.2964 185.699 74.9115 225.093 106.405 226.256C133.617 227.261 156.721 203.691 160.075 185.699"
                  stroke="black"
                  stroke-width="0.609881"
                  stroke-dasharray="1.22 1.22"
                />
                <path
                  id="Vector 303"
                  d="M75.9114 131.114C75.9114 131.114 92.901 132.334 121.652 149.106C136.29 157.644 160.99 180.21 164.649 186.613"
                  stroke="black"
                  stroke-width="0.609881"
                  stroke-dasharray="1.22 1.22"
                />
                <path
                  id="Vector 302"
                  d="M74.6917 133.554C88.3124 137.62 91.9131 136.054 123.177 153.985C143.913 165.877 157.331 182.039 163.734 189.663"
                  stroke="black"
                  stroke-width="0.609881"
                />
                <path
                  id="Vector 301"
                  d="M32.6099 171.367C37.7939 170.452 46.4116 165.784 53.6508 160.084C65.8503 150.479 71.0324 137.823 74.9966 127.15"
                  stroke="black"
                  stroke-width="0.609881"
                  stroke-dasharray="1.22 1.22"
                />
                <path
                  id="Vector 300"
                  d="M33.5247 173.806C39.8268 171.875 49.5936 167.139 57.615 159.779C68.4348 149.851 73.4719 137.823 76.8263 127.455"
                  stroke="black"
                  stroke-width="0.609881"
                />
                <path
                  id="Vector 299"
                  d="M165.259 182.954C159.16 214.668 127.455 238.212 95.4276 228.391C49.6866 214.363 44.1976 169.537 44.1976 169.537"
                  stroke="black"
                  stroke-width="0.609881"
                />
              </g>
              <g id="Group 182">
                <path id="Vector 305_2" d="M226.247 230.525V240.893" stroke="black" stroke-width="0.609881" />
                <path
                  id="Vector 304_2"
                  d="M293.639 167.098C291.809 185.699 267.194 225.093 235.7 226.256C208.488 227.261 185.385 203.691 182.031 185.699"
                  stroke="black"
                  stroke-width="0.609881"
                  stroke-dasharray="1.22 1.22"
                />
                <path
                  id="Vector 303_2"
                  d="M266.194 131.114C266.194 131.114 249.205 132.334 220.453 149.106C205.816 157.644 181.116 180.21 177.457 186.613"
                  stroke="black"
                  stroke-width="0.609881"
                  stroke-dasharray="1.22 1.22"
                />
                <path
                  id="Vector 302_2"
                  d="M267.414 133.554C253.793 137.62 250.193 136.054 218.928 153.985C198.193 165.877 184.47 182.344 178.066 189.968"
                  stroke="black"
                  stroke-width="0.609881"
                />
                <path
                  id="Vector 301_2"
                  d="M308.581 171.062C303.397 170.147 295.694 165.784 288.455 160.084C276.255 150.479 271.073 137.823 267.109 127.15"
                  stroke="black"
                  stroke-width="0.609881"
                  stroke-dasharray="1.22 1.22"
                />
                <path
                  id="Vector 300_2"
                  d="M307.971 173.806C301.669 171.875 292.512 167.139 284.491 159.779C273.671 149.851 268.634 137.823 265.279 127.455"
                  stroke="black"
                  stroke-width="0.609881"
                />
                <path
                  id="Vector 299_2"
                  d="M176.237 183.259C182.336 214.973 214.65 238.212 246.678 228.391C292.419 214.363 297.908 169.537 297.908 169.537"
                  stroke="black"
                  stroke-width="0.609881"
                />
              </g>
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 21"
                  d="M304.922 169.341L37.4701 169.341"
                  stroke="#E55959"
                  stroke-width="3.0494"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M40.9211 164.658L35.3544 169.436L40.2401 174.414"
                  stroke="#E55959"
                  stroke-width="2.43952"
                />
                <path
                  id="Vector 27"
                  d="M301.24 174.432L306.295 169.116L300.931 164.657"
                  stroke="#E55959"
                  stroke-width="2.43952"
                />
              </g>
            )}
            {findMeasure('product_under_bust') && (
              <g id="product_underbust">
                <path
                  id="Vector 21_2"
                  d="M292.419 220.571L48.7717 220.571"
                  stroke="#E55959"
                  stroke-width="3.0494"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M51.8989 215.888L46.3322 220.666L51.218 225.644"
                  stroke="#E55959"
                  stroke-width="2.43952"
                />
                <path
                  id="Vector 27_2"
                  d="M289.652 225.662L294.707 220.346L289.343 215.888"
                  stroke="#E55959"
                  stroke-width="2.43952"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 21_3"
                  d="M304.922 169.341L37.4701 169.341"
                  stroke="#E55959"
                  stroke-width="3.0494"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M40.9211 164.658L35.3544 169.436L40.2401 174.414"
                  stroke="#E55959"
                  stroke-width="2.43952"
                />
                <path
                  id="Vector 27_3"
                  d="M301.24 174.432L306.295 169.116L300.931 164.657"
                  stroke="#E55959"
                  stroke-width="2.43952"
                />
              </g>
            )}
          </g>
        </svg>
      )}
    </div>
  );
}
