import type React from 'react';
import { useTranslation } from 'react-i18next';
import { Container } from '../atoms/container';
import { Link, Switch, Button, Text } from '../atoms';
import { cn } from '@/lib/utils';
import { useDevice } from '@/hooks/use-device';
import { handleLinkUrl } from '@/lib/handle-measurement-tape';
import { handleOpenVFR } from '@/lib/open-vfr';
import { useAppContext } from '@/store';

export type UnitTypes = 'cm' | 'in';

interface FooterProps {
  /** The currently selected unit */
  unit: UnitTypes;
  /** Function to call when the unit is changed */
  onUnitChange: (unit: UnitTypes) => void;
  /** Custom class name */
  className?: string;
  /** Flag to indicate if the product is an accessory */
  isAccessory?: boolean;
}

export const Footer: React.FC<FooterProps> = ({ unit, onUnitChange, className, isAccessory }) => {
  const { t } = useTranslation();
  const MEASURING_TAPE_URL = handleLinkUrl({ unit });
  const { isMobile } = useDevice();
  const {
    app: {
      config: {
        general: { measurementSwitcher },
      },
    },
  } = useAppContext();

  return (
    <Container as="footer" className={cn('w-full flex flex-col gap-4', className)}>
      {isMobile ? (
        <>
          <div className="flex items-center justify-between">
            <Link external href={MEASURING_TAPE_URL} className="button__measuring-tape">
              {t('footer.print_measurement_tape')}
            </Link>
            {measurementSwitcher && (
              <div className="flex items-center gap-2 w-fit footer__switcher">
                <Text variant="body" className={unit === 'cm' ? 'text-[#262626]' : 'text-[#8a8a8d]'}>
                  cm
                </Text>
                <Switch checked={unit === 'in'} onChange={(checked) => onUnitChange(checked ? 'in' : 'cm')} />
                <Text variant="body" className={unit === 'in' ? 'text-[#262626]' : 'text-[#8a8a8d]'}>
                  in
                </Text>
              </div>
            )}
          </div>
          {!isAccessory && (
            <Button fullWidth className="w-full button__primary" onClick={handleOpenVFR}>
              {t('footer.virtual_fitting_room')}
            </Button>
          )}
        </>
      ) : (
        <>
          <div className="flex items-center justify-between w-full gap-7">
            {measurementSwitcher && (
              <div className="flex items-center gap-2 w-fit footer__switcher">
                <Text variant="body" className={unit === 'cm' ? 'text-[#262626]' : 'text-[#8a8a8d]'}>
                  cm
                </Text>
                <Switch checked={unit === 'in'} onChange={(checked) => onUnitChange(checked ? 'in' : 'cm')} />
                <Text variant="body" className={unit === 'in' ? 'text-[#262626]' : 'text-[#8a8a8d]'}>
                  in
                </Text>
              </div>
            )}
            {!isAccessory && (
              <Button fullWidth className="w-full button__primary" onClick={handleOpenVFR}>
                {t('footer.virtual_fitting_room')}
              </Button>
            )}
          </div>
        </>
      )}
    </Container>
  );
};
