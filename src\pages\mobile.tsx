import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { t } from 'i18next';
import { Container, Loading, Text } from '@/components/atoms';
import { MeasurementRow, MeasurementContent, Notification } from '@/components/molecules';
import { <PERSON><PERSON><PERSON>, Header, Carousel, <PERSON><PERSON><PERSON><PERSON><PERSON>, Footer } from '@/components/organisms';
import { BodyMeasure, GarmentMeasure, SizeSystemResponse, useSizeChart } from '@/hooks/use-size-chart';
import useGarment from '@/hooks/use-garment';
import { useAppContext } from '@/store';
import { AppStateActions } from '@/store/types';
import { getBodyMeasureTemplate } from '@/hooks/use-body-measures';
import { unitConverter } from '@/lib/unit-converter';
import { handleCloseApp } from '@/lib/close-app';
import { filterByClotheTypes } from '@/components/body_measures';
import { getTablePriority } from '@/lib/get-table-priority';
import { decodedRecommendedSize } from '@/lib/decode-base64';

const GarmentMeasurements = ({ garmentMeasure }: { garmentMeasure: GarmentMeasure }) => {
  const { t } = useTranslation();
  const {
    app: { unitSystem },
  } = useAppContext();

  const unit = unitSystem === 'metric' ? 'cm' : 'in';

  const template = garmentMeasure?.template ?? '';
  const Garment = useGarment({ template });
  const convert = (value: number) => unitConverter({ value, unit });

  const measures = garmentMeasure?.garmentMeasurements ?? ([] as GarmentMeasure[]);
  const composedMeasures = measures.map((item) => ({
    ...garmentMeasure,
    garmentMeasurements: [item],
  })) as GarmentMeasure[];

  return (
    <>
      {composedMeasures.map((item) => {
        const size = item.garmentMeasurements[0];
        const { initialValue, finalValue } = size.label;
        const measure = size.measure;

        const label =
          initialValue === finalValue
            ? `${convert(initialValue)} ${unit}`
            : `${convert(initialValue)} - ${convert(finalValue)} ${unit}`;

        const translationKey = measure.replace('product_', '');

        const title = t(`garment_measurements.${translationKey}`);
        const content = t(`garment_measures_info.${translationKey}`);

        return (
          <Collapse
            className="mt-2"
            header={<MeasurementRow className="p-0 measurement-row" label={title} value={label} />}
          >
            <MeasurementContent
              className="m-auto measurement-content"
              image={Garment ? <Garment measure={item} /> : null}
              value={content}
            />
          </Collapse>
        );
      })}
    </>
  );
};

const BodyMeasurements = ({ bodyMeasure }: { bodyMeasure: BodyMeasure }) => {
  const { product, app } = useAppContext();
  const { productInfo } = product;
  const { gender, ageGroup, clothesType } = productInfo;
  const {
    config: {
      general: { filterChartByClothesType },
    },
  } = app;
  const unit = app.unitSystem === 'metric' ? 'cm' : 'in';
  const weightUnit = app.unitSystem === 'metric' ? 'kg' : 'lb';

  const measures = bodyMeasure?.measures ?? ([] as BodyMeasure[]);
  const convert = (value: number) => unitConverter({ value, unit });

  const composedMeasures = measures
    .filter((item) => !item.hidden)
    .sort((a, b) => (a.position ?? 0) - (b.position ?? 0))
    .filter(({ measure }) => !filterChartByClothesType || filterByClotheTypes[clothesType]?.includes(measure))
    .map((item) => ({ ...bodyMeasure, measures: [item] }));

  return (
    <>
      {composedMeasures
        .sort((a, b) => {
          const typeA = getTablePriority(a.measures[0]);
          const typeB = getTablePriority(b.measures[0]);

          return typeA - typeB;
        })
        .map((item) => {
          const size = item.measures[0];
          const { initialValue, finalValue, textValue } = size.label;
          const type = item.measures[0]?.type;
          const key = size.measure;
          const isLbs = unit === 'in';

          const getTranslationKey = () => {
            if (ageGroup === 'child') return key;

            if (key === 'chest') {
              if (gender === 'M') return 'chest_m';
              if (gender === 'F') return 'chest_f';
              return 'chest';
            }

            return key;
          };

          const translationKey = getTranslationKey();

          const Template = getBodyMeasureTemplate({
            gender,
            ageGroup,
            measure: size,
          });

          const title = t(`body_measurements.${translationKey}`);
          const content = t(`measures_info.${translationKey}`);
          const label =
            key === 'weight' || key === 'weightChart'
              ? `${unitConverter({ value: initialValue, unit, isLbs })} - ${unitConverter({ value: finalValue, unit, isLbs })} ${weightUnit}`
              : textValue
                ? textValue
                : initialValue === finalValue
                  ? `${convert(initialValue)} ${unit}`
                  : `${convert(initialValue)} - ${convert(finalValue)} ${unit}`;

          return type === 'TEXT' || type === 'LABEL' || key === 'weight' || key === 'weightChart' ? (
            <div key={size.measure} className="border-b-2 border-[#E7E7E7] py-4">
              <MeasurementRow className="p-0 measurement-row" label={title} value={label} />
            </div>
          ) : (
            <Collapse
              key={key}
              className="mt-2"
              header={<MeasurementRow className="p-0 measurement-row" label={title} value={label} />}
            >
              <MeasurementContent
                className="m-auto measurement-content"
                image={Template ? <Template measure={item} /> : null}
                value={content}
              />
            </Collapse>
          );
        })}
    </>
  );
};

const SizeSystem = ({ sizeSystem }: { sizeSystem: SizeSystemResponse }) => {
  const { sizeSystem: sizeSystemData } = sizeSystem;
  const { t } = useTranslation();

  return (
    <div className="overflow-y-auto max-h-full custom-scroll">
      <table className="w-full border-collapse">
        <thead className="sticky top-0 bg-white border-b border-[#E7E7E7]">
          <tr className="tab__content__size-system-title">
            <th className="text-[12px] text-center font-medium py-3 w-[50%]">{t('regions.region')}</th>
            <th className="text-[12px] text-center font-medium py-3 w-[50%]">
              {t('product_comparison.elements.size')}
            </th>
          </tr>
        </thead>
        <tbody>
          {Object.entries(sizeSystemData).map(([key, value]) => (
            <tr key={key} className="border-b border-[#E7E7E7] tab__content__size-system-item">
              <td className="text-[12px] text-center font-normal py-3">{t(`regions.code.${key}`)}</td>
              <td className="text-[12px] text-center font-normal py-3">{value}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

interface TabProps {
  garmentMeasure: GarmentMeasure | null;
  bodyMeasure: BodyMeasure | null;
  sizeSystem: SizeSystemResponse | null;
}

const useTabs = ({ garmentMeasure, bodyMeasure, sizeSystem }: TabProps) => {
  const [activeTab, setActiveTab] = useState(0);
  const { t } = useTranslation();

  const tabs = [];

  if (bodyMeasure && bodyMeasure.measures.length > 0) {
    tabs.push({
      id: 1,
      label: t('drawers.knobs.product_info'),
      content: <BodyMeasurements bodyMeasure={bodyMeasure!} />,
      customClass: 'body-measures',
    });
  }

  if (garmentMeasure && garmentMeasure.garmentMeasurements.length > 0) {
    tabs.push({
      id: 2,
      label: t('drawers.knobs.garment_info'),
      content: <GarmentMeasurements garmentMeasure={garmentMeasure!} />,
      customClass: 'garment-measures',
    });
  }

  if (sizeSystem && Object.keys(sizeSystem.sizeSystem).length > 0) {
    tabs.push({
      id: 3,
      label: t('drawers.knobs.sizes_conversions'),
      content: <SizeSystem sizeSystem={sizeSystem!} />,
      customClass: 'size-system',
    });
  }

  const handleTabChange = (index: number) => {
    setActiveTab(index);
  };

  return { tabs, activeTab, handleTabChange };
};

export function Mobile() {
  const { app, setState, carousel: carouselState, product } = useAppContext();
  const { carousel, selectedGarmentMeasure, selectedBodyMeasure, selectedSizeSystem } = useSizeChart();

  const { tabs, activeTab, handleTabChange } = useTabs({
    garmentMeasure: selectedGarmentMeasure,
    bodyMeasure: selectedBodyMeasure,
    sizeSystem: selectedSizeSystem,
  });

  const toggleUnit = () => {
    setState({ action: AppStateActions.SET_UNIT_SYSTEM, payload: app.unitSystem === 'metric' ? 'imperial' : 'metric' });
  };

  const handleCloseMenu = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!app.menuOpen) return;

    event.stopPropagation();
    const element = event.target as HTMLElement;

    const isClickInsideMenu = element instanceof Node && Boolean(element.closest('.menu-container'));

    if (!isClickInsideMenu) {
      setState({ action: AppStateActions.TOGGLE_MENU_OPEN, payload: null });
    }
  };

  if (app.loading)
    return <Loading className="w-full h-dvh max-h-dvh absolute flex justify-center items-center opacity-80" />;

  return (
    <Container
      className="w-full h-dvh max-h-dvh p-0 flex flex-col gap-3 relative overflow-hidden"
      onClick={handleCloseMenu}
    >
      <Header
        onClose={handleCloseApp}
        className="text-sm border-b-[1px] pt-4 pb-4 border-[#E7E7E7]"
        data-test-id="header"
      />
      <div className=" flex flex-col gap-5 items-center h-full relative max-h-[calc(100vh-70px)]">
        <div className="p-3 w-full flex flex-col items-center gap-5 ">
          <Text variant="label" className="text-center carousel__title">
            {t('size_chart.title')}
          </Text>
          <Carousel
            data-test-id="carousel"
            className="max-w-[90%] w-full "
            items={carousel.content}
            selectedItem={carouselState.selected}
            onSelectItem={(item) => setState({ action: AppStateActions.SET_CAROUSEL_SELECTED, payload: item })}
            defaultValue={decodedRecommendedSize || undefined}
          />
          <Notification
            className='rounded-lg'
            content={'Atenção! Compre um tamanho maior. Recomendamos escolher um tamanho maior que o usual para um ajuste mais confortável.'}
          />
        </div>
        <TabContainer
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
          className="flex-1 p-3 w-full overflow-hidden"
        />
        <div className="w-full p-3">
          <Footer
            className="w-full h-24 p-0 flex-none"
            unit={app.unitSystem === 'metric' ? 'cm' : 'in'}
            onUnitChange={toggleUnit}
            isAccessory={product?.productInfo?.accessory}
          />
        </div>
      </div>
    </Container>
  );
}
