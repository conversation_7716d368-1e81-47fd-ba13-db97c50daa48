import React from 'react';
import { Observation } from './observation';

/**
 * Example component showing how to use the Observation component
 * This demonstrates the same behavior as the original Observation component
 */
export function ObservationExample() {
  // Example 1: Simple observation text without translations
  const simpleObservationText = 'This product runs small, consider sizing up.';

  // Example 2: Multi-language observation text with embedded translations
  const multiLanguageObservationText = 
    '[en]This product runs small, consider sizing up.' +
    '[pt-BR]Este produto veste pequeno, considere um tamanho maior.' +
    '[es]Este producto es pequeño, considera una talla más grande.' +
    '[fr]Ce produit taille petit, envisagez de prendre une taille au-dessus.';

  // Example 3: Observation translations array (similar to modelingObservationTranslations)
  const observationTranslations = [
    {
      id: '1',
      modelingId: '123',
      language: 'en',
      observation: 'This garment fits true to size. Choose your usual size for the best fit.',
    },
    {
      id: '2',
      modelingId: '123',
      language: 'pt-BR',
      observation: 'Esta peça veste no tamanho. Escolha seu tamanho habitual para o melhor caimento.',
    },
    {
      id: '3',
      modelingId: '123',
      language: 'es',
      observation: 'Esta prenda se ajusta a la talla. Elige tu talla habitual para el mejor ajuste.',
    },
    {
      id: '4',
      modelingId: '123',
      language: 'fr',
      observation: 'Ce vêtement taille normalement. Choisissez votre taille habituelle pour le meilleur ajustement.',
    },
  ];

  // Example 4: Observation with HTML content
  const htmlObservationTranslations = [
    {
      id: '1',
      modelingId: '456',
      language: 'en',
      observation: '<strong>Important:</strong> This item runs <em>large</em>. We recommend ordering one size <u>smaller</u> than usual.',
    },
    {
      id: '2',
      modelingId: '456',
      language: 'pt-BR',
      observation: '<strong>Importante:</strong> Este item veste <em>grande</em>. Recomendamos pedir um tamanho <u>menor</u> que o habitual.',
    },
  ];

  return (
    <div className="space-y-6 p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Observation Component Examples</h2>
      
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold mb-2">1. Simple Observation Text</h3>
          <Observation 
            observationText={simpleObservationText}
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">2. Multi-Language Observation Text</h3>
          <p className="text-sm text-gray-600 mb-2">
            This will show different text based on the current language setting
          </p>
          <Observation 
            observationText={multiLanguageObservationText}
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">3. Observation with Translation Array</h3>
          <p className="text-sm text-gray-600 mb-2">
            This uses the observationTranslations array (similar to modelingObservationTranslations)
          </p>
          <Observation 
            observationText=""
            observationTranslations={observationTranslations}
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">4. Observation with HTML Content</h3>
          <p className="text-sm text-gray-600 mb-2">
            This supports HTML content in the observation text
          </p>
          <Observation 
            observationText=""
            observationTranslations={htmlObservationTranslations}
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">5. Empty Observation</h3>
          <p className="text-sm text-gray-600 mb-2">
            This will render nothing when both observationText and observationTranslations are empty
          </p>
          <Observation 
            observationText=""
            observationTranslations={[]}
          />
        </div>
      </div>

      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">How to Use in Your Components</h3>
        <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
{`// Import the component
import { Observation } from '@/components/molecules';

// Use with modelingObservationTranslations from product data
const { modelingInfo: { modelingObservationTranslations } } = product;

// Replace the current Notification component with:
<Observation 
  observationText={observationAdvice || ''}
  observationTranslations={modelingObservationTranslations}
/>`}
        </pre>
      </div>
    </div>
  );
}
