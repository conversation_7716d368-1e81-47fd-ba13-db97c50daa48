import getQueryParams from './get-query-params';

const qp = getQueryParams<{ sizes: string; recommendedSize: string }>();

const tryDecodeBase64 = (str: string) => {
  try {
    return JSON.parse(atob(str));
  } catch {
    return str;
  }
};

const tryDecodeBase64Text = (str: string) => {
  try {
    return atob(str);
  } catch {
    return str;
  }
};

const sanatizeEachSize = () => {
  if (!qp.sizes) return null;

  const decoded = tryDecodeBase64(qp.sizes);
  const sizes = Array.isArray(decoded) ? decoded : decoded.split(',');
  return sizes.join(',');
};

const decodeRecommendedSize = () => (qp.recommendedSize ? tryDecodeBase64Text(qp.recommendedSize) : null);
export const decodedRecommendedSize = decodeRecommendedSize();

export default sanatizeEachSize;
