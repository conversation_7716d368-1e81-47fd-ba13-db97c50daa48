import { useDevice } from '@/hooks/use-device';
import { BodyMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface LowerBodyProps {
  measure: BodyMeasure;
  className?: string;
}

export function LowerBody({ measure, className }: LowerBodyProps) {
  const { measures } = measure;
  const mappedMeasures = measures.map((item) => item.measure);
  const { isMobile } = useDevice();

  const findMeasure = (measure: string) => {
    const foundMeasure = mappedMeasures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full flex justify-center', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_lower_body">
            <mask id="mask0_128_1140" maskUnits="userSpaceOnUse" x="0" y="0" width="331" height="193">
              <rect id="rect" width="331" height="193" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_1140)">
              <g id="group_lower_body_mask">
                <g id="female">
                  <g id="female_2">
                    <path
                      id="Union"
                      d="M157.021 -82.2618L156.875 -73.9633L145.519 -67.703L141.152 -66.6839L137.658 -65.3736L135.619 -64.0634L134.164 -62.3163L131.397 -56.3472L130.378 -52.4164L129.359 -46.7385L128.777 -39.7503L127.321 -26.793L126.593 -21.2607L125.865 -16.4563L123.245 -5.97401L121.206 3.63476L119.605 13.2435L118.586 22.4155L118.003 28.8214L117.421 36.8287L116.256 43.6713C116.596 46.2918 117.275 51.5912 117.275 51.8242C117.275 52.0571 119.023 54.4447 119.896 55.6094L121.497 57.6477L122.225 57.5021C122.322 57.2594 122.225 57.0071 122.225 56.7741C122.225 56.5598 122.122 55.1861 121.856 54.4557C121.76 54.1941 121.681 53.9776 121.643 53.8624C121.527 53.513 120.624 51.9697 120.187 51.2418C120.09 50.2712 119.896 48.1845 119.896 47.6021C119.896 47.0198 120.672 44.4477 121.061 43.2345L121.934 40.7595C122.177 40.7595 122.662 40.7886 122.662 40.9051C122.662 40.9654 122.862 41.9 123.096 42.9949C123.427 44.5433 123.827 46.4125 123.827 46.583C123.827 46.816 124.215 47.8448 124.409 48.3301C124.555 48.4271 124.875 48.563 124.992 48.3301C125.108 48.0971 125.622 47.2624 125.865 46.8742L125.428 41.7786V39.0125L125.72 36.3919C125.671 35.8581 125.574 34.7322 125.574 34.4993C125.574 34.3287 125.324 33.3588 125.117 32.5553C124.971 31.9872 124.846 31.5022 124.846 31.4419C124.846 31.3691 124.628 30.7868 124.409 30.2044C124.191 29.6221 123.972 29.0397 123.972 28.9669V27.9478L125.428 21.542C126.108 19.4067 127.467 15.107 127.467 14.9906C127.467 14.8832 128.496 12.9537 129.095 11.8319C129.308 11.4329 129.467 11.1361 129.505 11.0597C129.621 10.8268 131.106 7.3715 131.834 5.67298L133.581 0.723007L134.6 -2.91667L135.619 -10.924L136.202 -15.146L139.987 -31.4518L141.297 -37.4209C141.928 -33.1503 143.19 -24.4636 143.19 -23.8812C143.19 -23.5173 143.518 -20.6419 143.845 -17.7666C144.173 -14.8912 144.5 -12.0159 144.5 -11.6519C144.5 -11.0696 144.694 -8.20635 144.791 -6.84753L139.987 9.02148L137.367 16.3008L135.183 26.2008C134.94 27.7537 134.455 30.9469 134.455 31.2963C134.455 31.6458 134.261 35.7125 134.164 37.7022L135.183 44.108L137.745 62.1609H164.3L164.155 51.0962L163.863 44.9816L164.009 41.1963C164.058 40.9051 164.184 40.2936 164.3 40.1772C164.417 40.0607 165.028 39.7404 165.319 39.5948C165.61 39.7404 166.222 40.0607 166.338 40.1772C166.455 40.2936 166.581 40.9051 166.63 41.1963L166.775 44.9816L166.484 51.0962L166.504 62.1883L192.618 62.3536L195.456 44.108L196.475 37.7022C196.378 35.7125 196.184 31.6458 196.184 31.2963C196.184 30.9469 195.699 27.7537 195.456 26.2008L193.272 16.3008L190.651 9.02148L185.847 -6.84753C185.944 -8.20635 186.138 -11.0696 186.138 -11.6519C186.138 -12.0159 186.466 -14.8912 186.793 -17.7666C187.121 -20.6419 187.449 -23.5173 187.449 -23.8812C187.449 -24.4636 188.71 -33.1503 189.341 -37.4209L190.651 -31.4518L194.437 -15.146L195.019 -10.924L196.038 -2.91667L197.057 0.723007L198.804 5.67298C199.532 7.3715 201.017 10.8268 201.134 11.0597C201.172 11.1361 201.33 11.4329 201.543 11.8319C202.142 12.9537 203.172 14.8832 203.172 14.9906C203.172 15.107 204.531 19.4067 205.21 21.542L206.666 27.9478V28.9669C206.666 29.0397 206.448 29.6221 206.229 30.2044C206.011 30.7868 205.793 31.3691 205.793 31.4419C205.793 31.5022 205.668 31.9872 205.521 32.5553C205.314 33.3588 205.065 34.3287 205.065 34.4993C205.065 34.7322 204.968 35.8581 204.919 36.3919L205.21 39.0125V41.7786L204.773 46.8742C205.016 47.2624 205.53 48.0971 205.647 48.3301C205.763 48.563 206.084 48.4271 206.229 48.3301C206.423 47.8448 206.812 46.816 206.812 46.583C206.812 46.4125 207.211 44.5433 207.542 42.9949C207.777 41.9 207.976 40.9654 207.976 40.9051C207.976 40.7886 208.462 40.7595 208.704 40.7595L209.578 43.2345C209.966 44.4477 210.743 47.0198 210.743 47.6021C210.743 48.1845 210.548 50.2712 210.451 51.2418C210.015 51.9697 209.112 53.513 208.995 53.8624C208.957 53.9776 208.878 54.1941 208.783 54.4557C208.517 55.1861 208.122 56.2686 208.122 56.483C208.122 56.7159 208.316 57.2594 208.413 57.5021H208.995L210.743 55.6094C211.616 54.4447 213.363 52.0571 213.363 51.8242C213.363 51.5912 214.043 46.2918 214.382 43.6713L213.218 36.8287L212.635 28.8214L212.053 22.4155L211.034 13.2435L209.432 3.63476L207.394 -5.97401L204.773 -16.4563L204.045 -21.2607L203.318 -26.793L201.862 -39.7503L201.279 -46.7385L200.26 -52.4164L199.241 -56.3472L196.475 -62.3163L195.019 -64.0634L192.981 -65.3736L189.487 -66.6839L185.119 -67.703L173.763 -73.9633L173.618 -82.2618L166.477 -85.2278L166.484 -86.1926L165.319 -85.7088L164.155 -86.1926L164.161 -85.2278L157.021 -82.2618Z"
                      fill="white"
                    />
                    <g id="Group 3">
                      <path
                        id="Vector 17"
                        d="M155.126 32.4608C158.579 36.3079 163.862 41.0505 163.862 41.0505V44.1078L164.298 56.046V61.8694L137.801 63.1797L135.763 49.9313L134.307 38.7211L134.599 30.8593L136.054 21.6873L140.131 9.02124L145.081 19.5035C146.682 23.0947 150.031 26.7829 155.126 32.4608Z"
                        fill="url(#paint0_linear_128_1140)"
                      />
                      <path
                        id="Vector 20"
                        d="M132.997 -12.2345C134.162 -11.5357 135.521 -13.6904 136.055 -14.8551H136.2L135.181 -7.28455C135.035 -5.87721 134.744 -2.97517 134.744 -2.62576C134.744 -2.27635 133.774 0.916866 133.288 2.4698L130.522 9.60358C129.697 11.1565 127.756 14.5535 127.756 14.6991C127.756 15.1109 126.737 17.3682 126.3 18.4844L124.116 26.7829V28.8211C124.262 29.3064 124.553 30.3352 124.553 30.5682C124.553 30.8593 125.718 34.3534 125.718 34.6446V36.974L125.427 39.7402C125.427 42.2151 126.096 46.2625 125.863 46.7283C125.572 47.3107 125.621 47.6989 125.427 47.7475L124.699 48.4754L124.262 48.1842L123.825 47.1651C123.437 45.1754 122.806 41.1378 122.806 40.9049C122.806 40.6719 122.224 40.8078 121.933 40.9049L119.749 47.8931L120.331 51.5327C121.011 52.6489 122.224 55.3762 122.224 55.6092C122.224 55.8421 122.369 56.5312 122.369 57.065L121.933 57.793L121.059 57.065L117.419 52.1151L116.109 43.9622L117.419 36.974L118.439 26.0549L120.186 10.0403L121.641 1.59627C122.369 -1.50959 123.825 -7.80867 123.825 -8.15808C123.825 -8.50749 125.184 -13.5448 125.863 -16.0198C126.155 -17.136 126.708 -19.4848 126.591 -19.9507C126.475 -20.4165 127.416 -27.0359 127.902 -30.2874C129.018 -25.483 131.279 -15.7286 131.396 -15.1463C131.541 -14.4183 131.541 -13.108 132.997 -12.2345Z"
                        fill="url(#paint1_linear_128_1140)"
                      />
                      <path
                        id="Vector 39"
                        d="M144.521 101.69C144.653 100.103 139.067 75.5754 137.579 61.6919L144.686 61.8572H164.189V86.4839C164.079 89.0733 163.859 94.814 163.859 97.0618C163.859 99.8716 162.206 105.326 162.206 107.144C162.206 108.962 161.545 112.268 162.206 117.226C162.867 122.184 162.867 132.101 163.032 134.25C163.197 136.398 161.545 148.794 161.545 150.612V161.521C161.765 162.347 162.239 164.331 162.371 165.653C162.536 167.306 161.379 170.281 161.379 170.611C161.379 170.876 161.049 175.57 160.884 177.884L160.222 180.032V182.842C160.222 182.974 159.451 183.999 159.065 184.495L158.735 185.817C158.57 186.478 156.586 187.139 155.099 186.974C153.909 186.842 153.942 186.037 154.107 185.652C153.611 185.872 152.454 186.346 151.793 186.478C151.132 186.61 150.526 186.093 150.306 185.817C149.975 185.982 149.182 186.313 148.653 186.313C148.124 186.313 147.771 185.982 147.661 185.817H146.339C145.546 185.817 145.457 185.266 145.513 184.991C145.457 185.046 145.281 185.189 145.017 185.321C144.686 185.487 144.356 185.156 144.19 183.999C144.058 183.073 144.907 181.74 145.347 181.189L151.793 171.768C151.738 171.162 151.661 169.818 151.793 169.289C151.958 168.628 152.785 160.364 153.115 158.381C153.446 156.397 147.331 134.911 145.182 129.291C143.033 123.672 144.356 103.673 144.521 101.69Z"
                        fill="#F4F3F3"
                        stroke="black"
                        stroke-width="0.33056"
                      />
                      <path
                        id="Vector 40"
                        d="M186.006 102.185C185.874 100.599 191.295 76.2365 192.782 61.6919L185.84 61.8572L166.337 62.0225V86.4839C166.448 89.0733 166.668 94.814 166.668 97.0618C166.668 99.8716 168.321 105.326 168.321 107.144C168.321 108.962 168.982 112.268 168.321 117.226C167.66 122.184 167.66 132.101 167.494 134.25C167.329 136.398 168.982 148.794 168.982 150.612V161.521C168.762 162.347 168.288 164.331 168.156 165.653C167.99 167.306 169.147 170.281 169.147 170.611C169.147 170.876 169.478 175.57 169.643 177.884L170.304 180.032V182.842C170.304 182.974 171.075 183.999 171.461 184.495L171.792 185.817C171.957 186.478 173.94 187.139 175.428 186.974C176.618 186.842 176.585 186.037 176.42 185.652C176.915 185.872 178.072 186.346 178.733 186.478C179.395 186.61 180.001 186.093 180.221 185.817C180.552 185.982 181.345 186.313 181.874 186.313C182.403 186.313 182.755 185.982 182.865 185.817H184.188C184.981 185.817 185.069 185.266 185.014 184.991C185.069 185.046 185.245 185.189 185.51 185.321C185.84 185.487 186.171 185.156 186.336 183.999C186.469 183.073 185.62 181.74 185.179 181.189L178.733 171.768C178.789 171.162 178.866 169.818 178.733 169.289C178.568 168.628 177.742 160.364 177.411 158.381C177.081 156.397 183.196 134.911 185.345 129.291C187.493 123.672 186.171 104.169 186.006 102.185Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.33056"
                      />
                      <g id="Group 223">
                        <path
                          id="Vector 12"
                          d="M147.827 102.517C147.827 102.517 148.645 103.67 149.389 104.439C150.039 105.111 150.802 105.822 150.802 105.822"
                          stroke="black"
                          stroke-width="0.293348"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          id="Vector 41"
                          d="M159.066 102.517C159.066 102.517 158.248 103.67 157.504 104.439C156.854 105.111 156.091 105.822 156.091 105.822"
                          stroke="black"
                          stroke-width="0.293348"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </g>
                      <g id="Group 224">
                        <path
                          id="Vector 12_2"
                          d="M171.296 102.517C171.296 102.517 172.115 103.67 172.858 104.439C173.509 105.111 174.271 105.822 174.271 105.822"
                          stroke="black"
                          stroke-width="0.293348"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          id="Vector 41_2"
                          d="M182.535 102.517C182.535 102.517 181.717 103.67 180.973 104.439C180.323 105.111 179.56 105.822 179.56 105.822"
                          stroke="black"
                          stroke-width="0.293348"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </g>
                    </g>
                    <path
                      id="Union_2"
                      d="M157.057 -82.502C157.057 -82.5824 156.992 -82.6476 156.912 -82.6476C156.831 -82.6476 156.766 -82.5824 156.766 -82.502V-73.8547C153.871 -72.3332 147.77 -69.1102 145.921 -68.0702C145.361 -67.755 144.483 -67.5314 143.415 -67.2891C143.255 -67.2528 143.091 -67.2162 142.924 -67.1788C141.984 -66.9684 140.938 -66.7344 139.889 -66.408C137.419 -65.6395 134.895 -64.3482 133.631 -61.5982C133.545 -61.4108 133.46 -61.2257 133.375 -61.0425C132.248 -58.5945 131.278 -56.4875 130.53 -53.9535C129.725 -51.2281 129.178 -48.0132 128.959 -43.3458C128.61 -35.9041 126.682 -22.4095 125.759 -16.5813C124.545 -12.0628 121.799 -1.17532 120.517 6.28162C119.235 13.741 117.75 29.5807 117.167 36.5707L116 43.7206L117.173 52.0788L120.979 57.0563L120.98 57.0576C121.082 57.1967 121.211 57.3489 121.351 57.4772C121.489 57.6032 121.651 57.7179 121.816 57.7627C121.901 57.7857 121.993 57.7918 122.083 57.7663C122.176 57.7404 122.256 57.6852 122.321 57.6042C122.448 57.4485 122.523 57.1947 122.553 56.8372C122.584 56.4617 122.505 56.0001 122.365 55.5085C122.224 55.0143 122.017 54.4782 121.784 53.9492C121.323 52.9045 120.753 51.8776 120.362 51.2404C120.266 50.6831 120.131 49.8345 120.033 49.0652C119.984 48.6739 119.944 48.305 119.924 48.006C119.914 47.8564 119.909 47.726 119.91 47.62C119.911 47.5113 119.918 47.4379 119.928 47.3972C120.042 46.9433 121.301 43.0417 121.941 41.0715C122.078 41.0278 122.253 40.9772 122.401 40.9456C122.468 40.9315 122.523 40.9223 122.566 40.9183C122.57 40.9452 122.575 40.9773 122.581 41.014C122.597 41.1175 122.621 41.2601 122.651 41.4325C122.71 41.7773 122.792 42.2426 122.883 42.7541C123.065 43.7762 123.283 44.9835 123.429 45.7843C123.502 46.2743 123.649 46.9965 123.854 47.5708C123.955 47.8564 124.075 48.1184 124.215 48.2964C124.286 48.3856 124.368 48.4631 124.465 48.5075C124.567 48.5542 124.679 48.5609 124.791 48.5162C125.126 48.382 125.402 48.0575 125.606 47.7345C125.814 47.4064 125.964 47.0533 126.04 46.8256L126.05 46.7949L126.046 46.7628C125.925 45.7202 125.75 44.1056 125.621 42.6502C125.556 41.9224 125.502 41.2354 125.472 40.6802C125.442 40.119 125.437 39.7077 125.463 39.5208C125.486 39.3625 125.524 39.1696 125.567 38.9453C125.799 37.7555 126.204 35.6811 125.459 33.1981C124.771 30.9063 124.309 29.4587 124.155 28.9943V28.0132C124.519 26.1951 125.164 23.3017 125.922 20.5138C126.683 17.7124 127.553 15.0375 128.357 13.659C129.382 11.9022 130.841 8.69098 132.116 5.41273C133.39 2.13674 134.487 -1.22553 134.781 -3.28189C135.03 -5.02617 135.306 -7.38234 135.562 -9.57482C135.606 -9.94721 135.649 -10.3149 135.691 -10.674C135.837 -11.9116 135.973 -13.0479 136.091 -13.9292C136.15 -14.3699 136.205 -14.7458 136.253 -15.0382C136.303 -15.3344 136.344 -15.5348 136.376 -15.6314C136.437 -15.8133 136.656 -16.6797 136.969 -17.9529C137.284 -19.2311 137.695 -20.9293 138.141 -22.7844C139.034 -26.4946 140.068 -30.8333 140.748 -33.6966L140.748 -33.6997L141.314 -36.6422C141.961 -32.1821 142.843 -25.7411 143.53 -19.9115C143.895 -16.8052 144.205 -13.8737 144.395 -11.5084C144.585 -9.13618 144.652 -7.35089 144.538 -6.5253C144.307 -4.85244 143.075 -0.754186 141.621 3.70611C140.17 8.15947 138.503 12.9552 137.411 16.0117L137.41 16.0146L137.41 16.0175C136.655 18.4506 135.605 22.6142 134.904 27.2052C134.203 31.7942 133.849 36.822 134.493 40.9784C135.774 49.2458 136.977 58.1508 137.414 61.6925L164.355 61.8578C164.355 58.2203 164.423 50.5511 164.191 47.6415C164.045 45.8208 163.928 44.0142 163.995 42.5894C164.029 41.8765 164.109 41.2677 164.251 40.8038C164.395 40.3353 164.593 40.0446 164.839 39.9216C164.967 39.8571 165.098 39.8242 165.198 39.8076C165.247 39.7993 165.288 39.7953 165.316 39.7933C165.33 39.7923 165.341 39.7918 165.348 39.7916L165.355 39.7914L165.356 39.7914L165.357 39.7914L165.364 39.7916C165.371 39.7918 165.381 39.7923 165.395 39.7933C165.423 39.7953 165.464 39.7993 165.514 39.8076C165.613 39.8242 165.744 39.8571 165.873 39.9216C166.119 40.0446 166.317 40.3353 166.46 40.8038C166.602 41.2677 166.682 41.8765 166.716 42.5894C166.784 44.0142 166.667 45.8208 166.521 47.6415C166.288 50.5511 166.173 58.2203 166.173 61.8578L192.948 61.6925C193.385 58.1508 194.938 49.2458 196.219 40.9784C196.863 36.822 196.508 31.7942 195.807 27.2052C195.106 22.6142 194.056 18.4506 193.302 16.0175L193.301 16.0146L193.3 16.0117C192.208 12.9552 190.542 8.15947 189.09 3.70611C187.636 -0.754186 186.404 -4.85244 186.173 -6.5253C186.06 -7.35089 186.127 -9.13618 186.317 -11.5084C186.506 -13.8737 186.816 -16.8052 187.182 -19.9115C187.868 -25.7411 188.75 -32.1821 189.397 -36.6422L189.963 -33.7027L189.963 -33.6997L189.964 -33.6966C190.643 -30.8333 191.677 -26.4946 192.57 -22.7844C193.017 -20.9293 193.428 -19.2311 193.742 -17.9529C194.055 -16.6797 194.274 -15.8133 194.335 -15.6314C194.367 -15.5348 194.409 -15.3344 194.458 -15.0382C194.507 -14.7458 194.561 -14.3699 194.62 -13.9292C194.738 -13.0479 194.875 -11.9116 195.02 -10.674C195.062 -10.3149 195.105 -9.94721 195.149 -9.57482C195.406 -7.38234 195.681 -5.02617 195.931 -3.28189C196.224 -1.22553 197.322 2.13674 198.596 5.41273C199.871 8.69098 201.33 11.9022 202.355 13.659C203.159 15.0375 204.028 17.7124 204.79 20.5138C205.547 23.3017 206.193 26.1951 206.557 28.0132V28.9943C206.403 29.4587 205.94 30.9063 205.253 33.1981C204.508 35.6811 204.912 37.7555 205.144 38.9453C205.188 39.1696 205.226 39.3625 205.248 39.5208C205.275 39.7077 205.27 40.119 205.239 40.6802C205.209 41.2354 205.156 41.9224 205.091 42.6502C204.961 44.1056 204.787 45.7202 204.665 46.7628L204.662 46.7949L204.672 46.8256C204.748 47.0533 204.898 47.4064 205.105 47.7345C205.31 48.0575 205.585 48.382 205.921 48.5162C206.032 48.5609 206.144 48.5542 206.246 48.5075C206.343 48.4631 206.426 48.3856 206.496 48.2964C206.636 48.1184 206.756 47.8564 206.858 47.5708C207.062 46.9965 207.209 46.2743 207.283 45.7843C207.429 44.9835 207.647 43.7762 207.829 42.7541C207.92 42.2426 208.002 41.7773 208.061 41.4325C208.09 41.2601 208.114 41.1175 208.131 41.014C208.137 40.9773 208.142 40.9452 208.146 40.9183C208.188 40.9223 208.244 40.9315 208.31 40.9456C208.458 40.9772 208.634 41.0278 208.771 41.0715C209.411 43.0417 210.67 46.9433 210.783 47.3972C210.794 47.4379 210.801 47.5113 210.802 47.62C210.803 47.726 210.798 47.8564 210.788 48.006C210.767 48.305 210.728 48.6739 210.678 49.0652C210.581 49.8345 210.446 50.6831 210.349 51.2404C209.959 51.8776 209.389 52.9045 208.928 53.9492C208.695 54.4782 208.488 55.0143 208.347 55.5085C208.207 56.0001 208.128 56.4617 208.159 56.8372C208.189 57.1947 208.264 57.4485 208.39 57.6042C208.456 57.6852 208.536 57.7404 208.628 57.7663C208.719 57.7918 208.81 57.7857 208.895 57.7627C209.061 57.7179 209.222 57.6032 209.36 57.4772C209.501 57.3489 209.63 57.1967 209.731 57.0576L209.732 57.0563L213.538 52.0788L214.712 43.7206L213.544 36.5707C212.962 29.5807 211.477 13.741 210.195 6.28162C208.913 -1.17532 206.167 -12.0628 204.953 -16.5813C204.03 -22.4095 202.101 -35.9041 201.752 -43.3458C201.534 -48.0132 200.986 -51.2281 200.182 -53.9535C199.433 -56.4875 198.463 -58.5945 197.336 -61.0425L197.223 -61.2885C197.176 -61.3911 197.128 -61.4943 197.081 -61.5982C195.817 -64.3482 193.292 -65.6395 190.822 -66.408C189.773 -66.7344 188.727 -66.9684 187.787 -67.1788L187.786 -67.179C187.619 -67.2163 187.456 -67.2529 187.297 -67.2891C186.229 -67.5314 185.351 -67.755 184.79 -68.0702C182.941 -69.1102 176.841 -72.3332 173.945 -73.8547V-82.502C173.945 -82.5824 173.88 -82.6476 173.8 -82.6476C173.719 -82.6476 173.654 -82.5824 173.654 -82.502V-73.6788L173.732 -73.6379C176.596 -72.133 182.786 -68.8633 184.648 -67.8164C185.252 -67.4764 186.175 -67.245 187.232 -67.0051C187.393 -66.9685 187.558 -66.9316 187.726 -66.8941C188.666 -66.6837 189.699 -66.4525 190.736 -66.13C193.179 -65.3699 195.604 -64.1134 196.816 -61.4766C196.9 -61.2944 196.983 -61.1143 197.065 -60.9362L197.07 -60.9245C198.199 -58.4729 199.16 -56.384 199.902 -53.871C200.699 -51.1733 201.244 -47.9824 201.462 -43.3322C201.811 -35.869 203.744 -22.3511 204.666 -16.5282L204.667 -16.5206L204.669 -16.5131C205.882 -12.0007 208.628 -1.11617 209.908 6.33094C211.188 13.7777 212.672 29.6111 213.255 36.6005L213.255 36.6063L214.417 43.7239L213.261 51.9626L209.499 56.8822L209.497 56.885C209.404 57.0125 209.287 57.1497 209.164 57.2622C209.038 57.3773 208.918 57.4549 208.819 57.4816C208.772 57.4944 208.736 57.494 208.707 57.486C208.679 57.4782 208.649 57.4607 208.616 57.4209C208.547 57.3349 208.478 57.1543 208.449 56.813C208.422 56.4897 208.49 56.0681 208.627 55.5884C208.763 55.1114 208.964 54.5884 209.194 54.0667C209.655 53.0233 210.226 51.9955 210.612 51.3691L210.626 51.3453L210.631 51.3177C210.729 50.7581 210.867 49.8896 210.967 49.1018C211.017 48.7081 211.057 48.3327 211.078 48.0254C211.088 47.8719 211.094 47.7336 211.093 47.6176C211.092 47.5044 211.085 47.4024 211.066 47.3266C210.948 46.8543 209.654 42.8475 209.025 40.9111L209.002 40.8412L208.932 40.818C208.784 40.7686 208.56 40.7012 208.371 40.6608C208.278 40.641 208.184 40.6256 208.11 40.6249C208.075 40.6245 208.028 40.6269 207.985 40.6437C207.963 40.6526 207.933 40.6687 207.908 40.6983C207.881 40.7305 207.867 40.77 207.867 40.8105C207.867 40.8078 207.867 40.8081 207.867 40.8133C207.866 40.8187 207.865 40.8293 207.862 40.8472C207.858 40.8763 207.852 40.9168 207.844 40.9678C207.827 41.0697 207.803 41.2112 207.774 41.3832C207.715 41.7272 207.633 42.1918 207.542 42.7031C207.36 43.7256 207.142 44.9338 206.996 45.7344L206.996 45.7366L206.995 45.7389C206.923 46.2194 206.779 46.9226 206.584 47.4733C206.485 47.7506 206.378 47.9764 206.267 48.1163C206.213 48.186 206.164 48.2249 206.125 48.2427C206.092 48.2582 206.062 48.2594 206.029 48.2459C205.782 48.1471 205.548 47.8893 205.352 47.5789C205.168 47.288 205.031 46.9736 204.958 46.7638C205.08 45.7187 205.252 44.1197 205.381 42.676C205.446 41.9467 205.5 41.256 205.53 40.696C205.56 40.1419 205.568 39.7003 205.536 39.4796C205.512 39.3108 205.473 39.1093 205.428 38.8779C205.196 37.6824 204.81 35.6882 205.532 33.2818C206.23 30.9531 206.696 29.4988 206.841 29.064L206.848 29.0415V27.9844L206.845 27.9702C206.481 26.1478 205.832 23.2401 205.071 20.4374C204.31 17.6409 203.433 14.9291 202.606 13.5123C201.593 11.775 200.14 8.58039 198.867 5.3072C197.594 2.03176 196.507 -1.30299 196.219 -3.32307C195.97 -5.06335 195.695 -7.41507 195.438 -9.60801C195.395 -9.98053 195.352 -10.3485 195.309 -10.708C195.164 -11.9455 195.027 -13.084 194.909 -13.9679C194.849 -14.4098 194.795 -14.7891 194.745 -15.0859C194.697 -15.3789 194.652 -15.6017 194.611 -15.7234C194.556 -15.8909 194.34 -16.74 194.025 -18.0225C193.711 -19.2999 193.3 -20.9976 192.853 -22.8525C191.961 -26.5611 190.927 -30.8976 190.248 -33.7608L189.521 -37.543L189.234 -37.5366C188.578 -33.0711 187.625 -26.1615 186.893 -19.9456C186.527 -16.8377 186.216 -13.9021 186.026 -11.5316C185.837 -9.16794 185.766 -7.34872 185.885 -6.48551C186.12 -4.78074 187.363 -0.653303 188.813 3.79635C190.266 8.25155 191.933 13.0484 193.025 16.1067C193.775 18.527 194.821 22.6749 195.519 27.2492C196.219 31.8273 196.568 36.8208 195.931 40.9338C194.664 49.1075 193.067 58.3914 192.618 62.0231L166.54 62.0937C166.542 58.3955 166.583 50.522 166.811 47.6647C166.957 45.8457 167.076 44.0215 167.007 42.5755C166.973 41.853 166.891 41.2163 166.738 40.7185C166.587 40.2254 166.357 39.8383 166.003 39.6612C165.841 39.58 165.68 39.5402 165.562 39.5204C165.502 39.5104 165.452 39.5054 165.416 39.5028C165.398 39.5016 165.384 39.5009 165.374 39.5006C165.369 39.5004 165.364 39.5003 165.361 39.5003L165.358 39.5002H165.357H165.356H165.356H165.355H165.354L165.35 39.5003C165.347 39.5003 165.343 39.5004 165.338 39.5006C165.328 39.5009 165.313 39.5016 165.295 39.5028C165.26 39.5054 165.21 39.5104 165.15 39.5204C165.031 39.5402 164.871 39.58 164.708 39.6612C164.354 39.8383 164.124 40.2254 163.973 40.7185C163.821 41.2163 163.739 41.853 163.705 42.5755C163.636 44.0215 163.755 45.8457 163.9 47.6647C164.129 50.522 164.023 58.1575 164.024 61.8558L137.693 61.6942C137.244 58.0625 136.047 49.1075 134.781 40.9338C134.143 36.8208 134.493 31.8273 135.192 27.2492C135.891 22.6749 136.937 18.527 137.687 16.1067C138.779 13.0484 140.446 8.25155 141.898 3.79635C143.349 -0.653303 144.591 -4.78074 144.827 -6.48551C144.946 -7.34872 144.874 -9.16794 144.685 -11.5316C144.495 -13.9021 144.185 -16.8377 143.819 -19.9456C143.087 -26.1615 142.133 -33.0711 141.478 -37.5366L141.191 -37.543L140.464 -33.7608C139.784 -30.8976 138.751 -26.5611 137.858 -22.8525C137.412 -20.9976 137.001 -19.2999 136.687 -18.0225C136.371 -16.74 136.156 -15.8909 136.1 -15.7234C136.06 -15.6017 136.015 -15.3789 135.966 -15.0859C135.917 -14.7891 135.862 -14.4098 135.803 -13.9679C135.684 -13.084 135.548 -11.9455 135.402 -10.708C135.36 -10.3485 135.317 -9.98053 135.273 -9.60801C135.017 -7.41516 134.741 -5.06328 134.493 -3.32307C134.204 -1.30299 133.118 2.03176 131.844 5.3072C130.571 8.58039 129.119 11.775 128.105 13.5123C127.279 14.9291 126.401 17.6409 125.641 20.4374C124.879 23.2401 124.231 26.1478 123.866 27.9702L123.863 27.9844V29.0415L123.871 29.064C124.016 29.4988 124.481 30.9531 125.18 33.2818C125.902 35.6883 125.515 37.6824 125.283 38.8779C125.238 39.1093 125.199 39.3108 125.175 39.4796C125.144 39.7003 125.152 40.1419 125.182 40.696C125.212 41.256 125.266 41.9467 125.331 42.676C125.459 44.1197 125.632 45.7187 125.753 46.7638C125.68 46.9736 125.544 47.288 125.36 47.5789C125.164 47.8893 124.93 48.1471 124.683 48.2459C124.649 48.2594 124.62 48.2582 124.586 48.2427C124.547 48.2249 124.499 48.186 124.444 48.1163C124.334 47.9764 124.227 47.7506 124.128 47.4733C123.932 46.9226 123.788 46.2194 123.716 45.7389L123.716 45.7366L123.715 45.7344C123.57 44.9338 123.352 43.7256 123.17 42.7031C123.079 42.1918 122.997 41.7272 122.938 41.3832C122.908 41.2112 122.884 41.0697 122.868 40.9678C122.86 40.9168 122.854 40.8763 122.849 40.8472C122.847 40.8293 122.846 40.8187 122.845 40.8133C122.844 40.8081 122.844 40.8078 122.844 40.8105C122.844 40.77 122.831 40.7305 122.804 40.6983C122.779 40.6687 122.749 40.6526 122.726 40.6437C122.683 40.6269 122.637 40.6245 122.602 40.6249C122.527 40.6256 122.434 40.641 122.341 40.6608C122.151 40.7012 121.927 40.7686 121.779 40.818L121.709 40.8412L121.687 40.9111C121.057 42.8475 119.764 46.8543 119.646 47.3266C119.627 47.4024 119.62 47.5044 119.619 47.6176C119.618 47.7336 119.623 47.8719 119.633 48.0254C119.654 48.3327 119.695 48.7081 119.744 49.1018C119.844 49.8896 119.983 50.7581 120.08 51.3177L120.085 51.3453L120.1 51.3691C120.485 51.9955 121.057 53.0233 121.517 54.0667C121.747 54.5884 121.948 55.1114 122.085 55.5884C122.222 56.0681 122.289 56.4897 122.262 56.813C122.234 57.1543 122.165 57.3349 122.095 57.4209C122.063 57.4607 122.032 57.4782 122.005 57.486C121.976 57.494 121.939 57.4944 121.892 57.4816C121.793 57.4549 121.673 57.3773 121.548 57.2622C121.424 57.1497 121.308 57.0125 121.215 56.885L121.213 56.8822L117.451 51.9626L116.294 43.7239L117.457 36.6063L117.457 36.6005C118.039 29.6111 119.524 13.7777 120.804 6.33094C122.084 -1.11617 124.829 -12.0007 126.042 -16.5131L126.044 -16.5206L126.045 -16.5282C126.967 -22.3511 128.9 -35.869 129.25 -43.3322C129.468 -47.9824 130.013 -51.1733 130.809 -53.871C131.551 -56.384 132.513 -58.4729 133.642 -60.9245C133.725 -61.1064 133.81 -61.2904 133.896 -61.4766C135.107 -64.1134 137.533 -65.3699 139.976 -66.13C141.012 -66.4525 142.045 -66.6837 142.986 -66.8941C143.153 -66.9316 143.318 -66.9685 143.479 -67.0051C144.536 -67.245 145.46 -67.4764 146.064 -67.8164C147.925 -68.8633 154.115 -72.133 156.979 -73.6379L157.057 -73.6788V-82.502Z"
                      fill="black"
                    />
                    <path
                      id="Vector 11"
                      d="M165.445 1.49119C165.467 1.41324 165.422 1.33218 165.344 1.31015C165.266 1.28812 165.185 1.33345 165.163 1.4114L165.445 1.49119ZM165.163 1.4114C165.108 1.6034 165.114 1.88334 165.146 2.18612C165.178 2.49507 165.24 2.84974 165.312 3.20308C165.383 3.55689 165.465 3.91223 165.538 4.2228C165.612 4.53503 165.675 4.79887 165.711 4.97492L165.999 4.91586C165.961 4.7345 165.896 4.46409 165.824 4.15571C165.751 3.84566 165.67 3.49399 165.599 3.14486C165.528 2.79527 165.469 2.45105 165.438 2.15564C165.406 1.85404 165.407 1.62411 165.445 1.49119L165.163 1.4114ZM165.711 4.97492C165.855 5.67313 165.89 6.96646 165.712 7.75494L165.998 7.81963C166.187 6.9835 166.149 5.64731 165.999 4.91586L165.711 4.97492ZM165.712 7.75494C165.669 7.94241 165.611 7.98613 165.589 7.99584C165.566 8.0058 165.522 8.00684 165.444 7.96354C165.37 7.92256 165.295 7.85758 165.236 7.79807C165.207 7.7691 165.183 7.74302 165.167 7.72435C165.159 7.71505 165.153 7.70766 165.149 7.70277C165.147 7.70033 165.145 7.69851 165.144 7.6974C165.144 7.69685 165.143 7.69647 165.143 7.69628C165.143 7.69618 165.143 7.69613 165.143 7.69613C165.143 7.69613 165.143 7.69614 165.143 7.69616C165.143 7.69617 165.143 7.6962 165.143 7.6962C165.143 7.69623 165.143 7.69626 165.028 7.78729C164.913 7.87831 164.913 7.87835 164.913 7.87839C164.913 7.87841 164.913 7.87845 164.913 7.87849C164.914 7.87856 164.914 7.87864 164.914 7.87874C164.914 7.87893 164.914 7.87918 164.914 7.87947C164.915 7.88005 164.915 7.88082 164.916 7.88178C164.918 7.88369 164.92 7.88634 164.923 7.88965C164.928 7.89626 164.936 7.90554 164.946 7.91685C164.965 7.93943 164.993 7.97047 165.028 8.00497C165.095 8.07237 165.192 8.15971 165.302 8.22027C165.407 8.2785 165.558 8.33031 165.708 8.26412C165.858 8.19767 165.948 8.03832 165.998 7.81963L165.712 7.75494Z"
                      fill="black"
                    />
                    <path
                      id="Vector 12_3"
                      d="M152.651 29.6951C152.651 29.6951 154.253 31.8789 155.709 33.3348C156.982 34.6076 158.475 35.9553 158.475 35.9553"
                      stroke="black"
                      stroke-width="0.293348"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </g>
                  <path
                    id="Vector 41_3"
                    d="M192.452 63.1801L166.668 62.3537V62.0231V61.5273L168.453 59.3787L192.783 60.7009L192.617 62.0231L192.452 63.1801Z"
                    fill="white"
                  />
                  <path
                    id="Rectangle 1"
                    d="M166.668 59.3787L166.833 61.5273V62.1884H166.503L166.668 59.3787Z"
                    fill="white"
                  />
                  <path
                    id="Vector 42"
                    d="M164.024 62.3537V61.6926L159.231 60.0398L137.58 60.8662L137.91 63.0148L138.075 62.519L164.024 62.3537Z"
                    fill="#F4F3F3"
                  />
                </g>
                {findMeasure('lower_length') && (
                  <g id="lower_length">
                    <path
                      id="Vector 19"
                      d="M197.945 130.734L197.945 15.3637"
                      stroke="#E55959"
                      stroke-width="1.5"
                      stroke-linecap="square"
                    />
                    <path
                      id="Vector 29"
                      d="M135 15L198.19 15"
                      stroke="#E55959"
                      stroke-linecap="square"
                      stroke-dasharray="3.33 3.33"
                    />
                    <path
                      id="Vector 28"
                      d="M200.791 128.921L197.929 131.731L195.445 128.837"
                      stroke="#E55959"
                      stroke-width="1.5"
                    />
                  </g>
                )}
                {findMeasure('insideLeg') && (
                  <g id="inside_leg">
                    <path
                      id="Vector 29_2"
                      d="M135 15L198 15"
                      stroke="#E55959"
                      stroke-linecap="square"
                      stroke-dasharray="3.39 3.39"
                    />
                    <g id="Group 225">
                      <path
                        id="Vector 19_2"
                        d="M165.314 148.621L165.314 15.5085"
                        stroke="#E55959"
                        stroke-width="1.5"
                        stroke-linecap="square"
                      />
                      <path
                        id="Vector 28_2"
                        d="M168.205 146.779L165.297 149.633L162.774 146.694"
                        stroke="#E55959"
                        stroke-width="1.5"
                      />
                    </g>
                  </g>
                )}
                {findMeasure('thigh') && (
                  <g id="thigh">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M194.5 49.5C194.5 49.5 189.677 50.1352 178.584 50.3996M166.7 49.5793C166.7 49.5793 169.727 49.9499 175.484 50.3996"
                            stroke="#E55959"
                          />
                          <path id="Vector 27" d="M179.926 49.0153L177.979 50.3239L179.652 51.5802" stroke="#E55959" />
                          <path
                            id="Vector 28_3"
                            d="M173.792 49.0153L175.738 50.3239L174.066 51.5802"
                            stroke="#E55959"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('hip') && (
                  <g id="hip">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M196 30C196 30 186.999 33.169 162.687 34.2009M134.583 30C134.583 30 139.591 32.1549 159.506 34.2009"
                            stroke="#E55959"
                          />
                          <path
                            id="Vector 27_2"
                            d="M164.064 32.7802L162.067 34.1231L163.783 35.4122"
                            stroke="#E55959"
                          />
                          <path
                            id="Vector 28_4"
                            d="M157.769 32.7802L159.767 34.1231L158.051 35.4122"
                            stroke="#E55959"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_1140"
              x1="151.778"
              y1="20.9594"
              x2="140.568"
              y2="85.6002"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_1140"
              x1="129.794"
              y1="-28.3947"
              x2="119.458"
              y2="54.8812"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className=" h-full m-auto "
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_lower_body">
            <mask id="mask0_128_2684" maskUnits="userSpaceOnUse" x="0" y="0" width="342" height="291">
              <rect id="rect" width="342" height="291" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_2684)">
              <g id="group_lower_body_mask">
                <g id="female">
                  <g id="female_2">
                    <path
                      id="Union"
                      d="M159.172 -114.605L158.959 -102.475L142.359 -93.3233L135.975 -91.8336L130.867 -89.9182L127.887 -88.0029L125.759 -85.4491L121.716 -76.7235L120.226 -70.9774L118.736 -62.6774L117.885 -52.4622L115.757 -33.5213L114.693 -25.4342L113.629 -18.4112L109.798 -3.08821L106.818 10.9578L104.477 25.0038L102.988 38.4114L102.136 47.7755L101.285 59.4805L99.5825 69.483C100.079 73.3137 101.072 81.0603 101.072 81.4008C101.072 81.7413 103.626 85.2316 104.903 86.9341L107.244 89.9136L108.308 89.7007C108.45 89.346 108.308 88.9771 108.308 88.6366C108.308 88.3233 108.157 86.3153 107.768 85.2476C107.628 84.8651 107.513 84.5487 107.457 84.3803C107.287 83.8695 105.967 81.6136 105.329 80.5495C105.187 79.1307 104.903 76.0804 104.903 75.2291C104.903 74.3778 106.038 70.618 106.606 68.8445L107.882 65.2266C108.237 65.2266 108.947 65.2691 108.947 65.4394C108.947 65.5276 109.239 66.8937 109.581 68.4942C110.065 70.7577 110.649 73.49 110.649 73.7393C110.649 74.0798 111.217 75.5838 111.5 76.2932C111.713 76.435 112.181 76.6337 112.352 76.2932C112.522 75.9527 113.274 74.7325 113.629 74.165L112.99 66.7163V62.6728L113.416 58.842C113.345 58.0617 113.203 56.4159 113.203 56.0754C113.203 55.8261 112.838 54.4083 112.535 53.2338C112.321 52.4032 112.139 51.6943 112.139 51.6062C112.139 51.4998 111.82 50.6485 111.5 49.7972C111.181 48.946 110.862 48.0947 110.862 47.9883V46.4985L112.99 37.1345C113.983 34.0132 115.97 27.7279 115.97 27.5577C115.97 27.4007 117.475 24.5803 118.35 22.9404C118.662 22.3572 118.893 21.9232 118.949 21.8116C119.119 21.4711 121.29 16.4202 122.354 13.9373L124.908 6.70145L126.398 1.38098L127.887 -10.3241L128.739 -16.4958L134.272 -40.3315L136.187 -49.0571C137.11 -42.8144 138.954 -30.1162 138.954 -29.2649C138.954 -28.7329 139.433 -24.5297 139.912 -20.3265C140.391 -16.1234 140.869 -11.9202 140.869 -11.3881C140.869 -10.5369 141.153 -6.35143 141.295 -4.36513L134.272 18.8321L130.441 29.4731L127.249 43.9447C126.894 46.2148 126.185 50.8826 126.185 51.3934C126.185 51.9041 125.901 57.8489 125.759 60.7574L127.249 70.1214L130.995 96.5109H169.813L169.6 80.3367L169.174 71.3983L169.387 65.8651C169.458 65.4394 169.642 64.5456 169.813 64.3753C169.983 64.2051 170.877 63.7369 171.302 63.524C171.728 63.7369 172.622 64.2051 172.792 64.3753C172.962 64.5456 173.147 65.4394 173.218 65.8651L173.431 71.3983L173.005 80.3367L173.034 96.5511L211.207 96.7927L215.356 70.1214L216.846 60.7574C216.704 57.8489 216.42 51.9041 216.42 51.3934C216.42 50.8826 215.711 46.2148 215.356 43.9447L212.164 29.4731L208.333 18.8321L201.31 -4.36513C201.452 -6.35143 201.735 -10.5369 201.735 -11.3881C201.735 -11.9202 202.214 -16.1234 202.693 -20.3265C203.172 -24.5297 203.651 -28.7329 203.651 -29.2649C203.651 -30.1162 205.495 -42.8144 206.418 -49.0571L208.333 -40.3315L213.866 -16.4958L214.717 -10.3241L216.207 1.38098L217.697 6.70145L220.251 13.9373C221.315 16.4202 223.486 21.4711 223.656 21.8116C223.712 21.9232 223.943 22.3572 224.255 22.9404C225.13 24.5803 226.635 27.4007 226.635 27.5577C226.635 27.7279 228.622 34.0132 229.615 37.1345L231.743 46.4985V47.9883C231.743 48.0947 231.424 48.946 231.104 49.7972C230.785 50.6485 230.466 51.4998 230.466 51.6062C230.466 51.6943 230.283 52.4032 230.07 53.2338C229.767 54.4083 229.402 55.8261 229.402 56.0754C229.402 56.4159 229.26 58.0617 229.189 58.842L229.615 62.6728V66.7163L228.976 74.165C229.331 74.7325 230.083 75.9527 230.253 76.2932C230.423 76.6337 230.892 76.435 231.104 76.2932C231.388 75.5838 231.956 74.0798 231.956 73.7393C231.956 73.49 232.54 70.7577 233.024 68.4942C233.366 66.8937 233.658 65.5276 233.658 65.4394C233.658 65.2691 234.368 65.2266 234.722 65.2266L235.999 68.8445C236.567 70.618 237.702 74.3778 237.702 75.2291C237.702 76.0804 237.418 79.1307 237.276 80.5495C236.638 81.6136 235.318 83.8695 235.148 84.3803C235.092 84.5487 234.977 84.8651 234.837 85.2476C234.448 86.3153 233.871 87.8976 233.871 88.211C233.871 88.5515 234.155 89.346 234.297 89.7007H235.148L237.702 86.9341C238.979 85.2316 241.533 81.7413 241.533 81.4008C241.533 81.0603 242.526 73.3137 243.022 69.483L241.32 59.4805L240.468 47.7755L239.617 38.4114L238.128 25.0038L235.786 10.9578L232.807 -3.08821L228.976 -18.4112L227.912 -25.4342L226.848 -33.5213L224.72 -52.4622L223.869 -62.6774L222.379 -70.9774L220.889 -76.7235L216.846 -85.4491L214.717 -88.0029L211.738 -89.9182L206.63 -91.8336L200.246 -93.3233L183.646 -102.475L183.433 -114.605L172.995 -118.941L173.005 -120.351L171.302 -119.644L169.6 -120.351L169.61 -118.941L159.172 -114.605Z"
                      fill="white"
                    />
                    <g id="Group 3">
                      <path
                        id="Vector 17"
                        d="M156.402 53.0956C161.449 58.7193 169.172 65.6519 169.172 65.6519V70.1211L169.81 87.5722V96.085L131.077 98.0004L128.098 78.6339L125.969 62.2468L126.395 50.7546L128.523 37.347L134.482 18.8318L141.718 34.1547C144.059 39.4043 148.954 44.7957 156.402 53.0956Z"
                        fill="url(#paint0_linear_128_2684)"
                      />
                      <path
                        id="Vector 20"
                        d="M124.054 -12.2399C125.757 -11.2183 127.743 -14.3681 128.524 -16.0706H128.736L127.247 -5.00404C127.034 -2.94679 126.608 1.2954 126.608 1.80616C126.608 2.31693 125.189 6.98475 124.48 9.25482L120.436 19.6829C119.23 21.953 116.393 26.9188 116.393 27.1316C116.393 27.7335 114.903 31.0333 114.265 32.6649L111.072 44.7955V47.775C111.285 48.4844 111.711 49.9883 111.711 50.3288C111.711 50.7545 113.413 55.8621 113.413 56.2878V59.6929L112.988 63.7364C112.988 67.3543 113.967 73.2707 113.626 73.9517C113.201 74.803 113.272 75.3705 112.988 75.4414L111.924 76.5055L111.285 76.0799L110.647 74.5902C110.079 71.6816 109.157 65.7795 109.157 65.439C109.157 65.0984 108.306 65.2971 107.88 65.439L104.688 75.6543L105.539 80.9747C106.532 82.6063 108.306 86.5931 108.306 86.9337C108.306 87.2742 108.519 88.2815 108.519 89.0618L107.88 90.1259L106.603 89.0618L101.283 81.826L99.3673 69.9082L101.283 59.6929L102.772 43.7315L105.326 20.3214L107.454 7.97791C108.519 3.43777 110.647 -5.77018 110.647 -6.28095C110.647 -6.79171 112.633 -14.1552 113.626 -17.7732C114.052 -19.4048 114.861 -22.8382 114.69 -23.5193C114.52 -24.2003 115.896 -33.8764 116.606 -38.6294C118.237 -31.6064 121.543 -17.3475 121.713 -16.4962C121.926 -15.4322 121.926 -13.5168 124.054 -12.2399Z"
                        fill="url(#paint1_linear_128_2684)"
                      />
                      <path
                        id="Vector 39"
                        d="M140.899 154.294C141.093 151.974 132.926 116.12 130.752 95.8253L141.141 96.0669H169.65V132.066C169.489 135.851 169.167 144.243 169.167 147.529C169.167 151.636 166.751 159.609 166.751 162.267C166.751 164.924 165.785 169.757 166.751 177.005C167.718 184.253 167.718 198.749 167.959 201.89C168.201 205.031 165.785 223.151 165.785 225.809V241.755C166.107 242.963 166.799 245.862 166.993 247.795C167.234 250.211 165.543 254.56 165.543 255.043C165.543 255.43 165.06 262.291 164.818 265.674L163.852 268.815V272.922C163.852 273.115 162.724 274.613 162.161 275.338L161.677 277.271C161.436 278.237 158.537 279.204 156.362 278.962C154.623 278.769 154.671 277.593 154.912 277.029C154.188 277.351 152.496 278.044 151.53 278.237C150.564 278.431 149.678 277.674 149.356 277.271C148.872 277.512 147.713 277.996 146.94 277.996C146.166 277.996 145.651 277.512 145.49 277.271H143.557C142.397 277.271 142.268 276.465 142.349 276.063C142.268 276.143 142.011 276.353 141.624 276.546C141.141 276.788 140.658 276.304 140.416 274.613C140.223 273.26 141.463 271.311 142.107 270.506L151.53 256.734C151.449 255.848 151.337 253.883 151.53 253.11C151.772 252.144 152.98 240.064 153.463 237.164C153.946 234.265 145.007 202.856 141.866 194.642C138.725 186.427 140.658 157.193 140.899 154.294Z"
                        fill="#F4F3F3"
                        stroke="black"
                        stroke-width="0.48321"
                      />
                      <path
                        id="Vector 40"
                        d="M201.542 155.019C201.349 152.699 209.273 117.087 211.448 95.8253L201.3 96.0669L172.791 96.3085V132.066C172.952 135.851 173.274 144.243 173.274 147.529C173.274 151.636 175.69 159.609 175.69 162.267C175.69 164.924 176.657 169.757 175.69 177.005C174.724 184.253 174.724 198.749 174.482 201.89C174.241 205.031 176.657 223.151 176.657 225.809V241.755C176.334 242.963 175.642 245.862 175.449 247.795C175.207 250.211 176.898 254.56 176.898 255.043C176.898 255.43 177.381 262.291 177.623 265.674L178.589 268.815V272.922C178.589 273.115 179.717 274.613 180.281 275.338L180.764 277.271C181.005 278.237 183.905 279.204 186.079 278.962C187.819 278.769 187.77 277.593 187.529 277.029C188.254 277.351 189.945 278.044 190.911 278.237C191.878 278.431 192.764 277.674 193.086 277.271C193.569 277.512 194.729 277.996 195.502 277.996C196.275 277.996 196.79 277.512 196.951 277.271H198.884C200.044 277.271 200.173 276.465 200.092 276.063C200.173 276.143 200.431 276.353 200.817 276.546C201.3 276.788 201.784 276.304 202.025 274.613C202.218 273.26 200.978 271.311 200.334 270.506L190.911 256.734C190.992 255.848 191.105 253.883 190.911 253.11C190.67 252.144 189.462 240.064 188.978 237.164C188.495 234.265 197.435 202.856 200.576 194.642C203.716 186.427 201.784 157.918 201.542 155.019Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.48321"
                      />
                      <g id="Group 223">
                        <path
                          id="Vector 12"
                          d="M145.731 155.503C145.731 155.503 146.927 157.189 148.015 158.312C148.965 159.295 150.08 160.335 150.08 160.335"
                          stroke="black"
                          stroke-width="0.428814"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          id="Vector 41"
                          d="M162.162 155.503C162.162 155.503 160.966 157.189 159.878 158.312C158.928 159.295 157.813 160.335 157.813 160.335"
                          stroke="black"
                          stroke-width="0.428814"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </g>
                      <g id="Group 224">
                        <path
                          id="Vector 12_2"
                          d="M180.04 155.503C180.04 155.503 181.236 157.189 182.323 158.312C183.274 159.295 184.389 160.335 184.389 160.335"
                          stroke="black"
                          stroke-width="0.428814"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          id="Vector 41_2"
                          d="M196.468 155.503C196.468 155.503 195.272 157.189 194.185 158.312C193.234 159.295 192.119 160.335 192.119 160.335"
                          stroke="black"
                          stroke-width="0.428814"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </g>
                    </g>
                    <path
                      id="Union_2"
                      d="M159.225 -114.956C159.225 -115.074 159.13 -115.169 159.012 -115.169C158.895 -115.169 158.8 -115.074 158.8 -114.956V-102.316C154.567 -100.092 145.65 -95.3805 142.947 -93.8601C142.128 -93.3994 140.844 -93.0726 139.283 -92.7183C139.049 -92.6653 138.81 -92.6117 138.566 -92.5571C137.192 -92.2496 135.663 -91.9074 134.129 -91.4304C130.518 -90.307 126.828 -88.4194 124.981 -84.3994C124.855 -84.1255 124.73 -83.8549 124.607 -83.5871C122.96 -80.0087 121.542 -76.9286 120.448 -73.2245C119.271 -69.2405 118.471 -64.5409 118.152 -57.7182C117.642 -46.8399 114.822 -27.1136 113.474 -18.594C111.698 -11.9888 107.684 3.92637 105.811 14.8269C103.937 25.731 101.766 48.8854 100.914 59.1032L99.208 69.5549L100.923 81.7729L106.487 89.049L106.488 89.0509C106.636 89.2542 106.825 89.4768 107.03 89.6642C107.232 89.8485 107.468 90.0161 107.71 90.0816C107.834 90.1153 107.968 90.1241 108.101 90.0868C108.235 90.049 108.353 89.9684 108.448 89.8499C108.633 89.6223 108.743 89.2513 108.787 88.7287C108.832 88.1798 108.717 87.505 108.512 86.7865C108.306 86.0641 108.004 85.2803 107.663 84.5071C106.989 82.9799 106.156 81.4789 105.585 80.5473C105.444 79.7327 105.246 78.4923 105.104 77.3676C105.031 76.7957 104.973 76.2564 104.944 75.8193C104.929 75.6006 104.922 75.41 104.923 75.2551C104.925 75.0961 104.935 74.9889 104.95 74.9294C105.116 74.2659 106.956 68.5625 107.892 65.6826C108.093 65.6187 108.349 65.5446 108.566 65.4985C108.662 65.4778 108.744 65.4644 108.806 65.4586C108.811 65.4979 108.819 65.5448 108.827 65.5985C108.852 65.7497 108.887 65.9583 108.93 66.2102C109.017 66.7143 109.136 67.3945 109.269 68.1421C109.535 69.6362 109.854 71.401 110.067 72.5717C110.175 73.288 110.39 74.3437 110.688 75.1832C110.837 75.6007 111.012 75.9836 111.217 76.2438C111.32 76.3742 111.44 76.4876 111.582 76.5524C111.731 76.6207 111.895 76.6305 112.059 76.5651C112.549 76.369 112.952 75.8946 113.25 75.4225C113.553 74.9429 113.773 74.4267 113.884 74.0938L113.899 74.049L113.893 74.002C113.716 72.478 113.461 70.1177 113.272 67.9902C113.177 66.9263 113.099 65.9221 113.055 65.1106C113.01 64.2902 113.002 63.689 113.042 63.4157C113.075 63.1844 113.13 62.9024 113.193 62.5744C113.532 60.8352 114.124 57.8029 113.035 54.1732C112.03 50.8231 111.354 48.7071 111.128 48.0282V46.594C111.66 43.9362 112.604 39.7067 113.712 35.6313C114.825 31.5364 116.096 27.6262 117.271 25.6111C118.769 23.043 120.902 18.3489 122.766 13.5567C124.628 8.76792 126.232 3.85297 126.662 0.846992C127.026 -1.70278 127.429 -5.14702 127.804 -8.35197C127.868 -8.89633 127.931 -9.43379 127.993 -9.95879C128.206 -11.7679 128.405 -13.4289 128.578 -14.7172C128.664 -15.3615 128.743 -15.9109 128.814 -16.3383C128.886 -16.7713 128.947 -17.0642 128.994 -17.2054C129.083 -17.4714 129.403 -18.7379 129.861 -20.599C130.32 -22.4675 130.922 -24.9499 131.574 -27.6617C132.88 -33.0853 134.391 -39.4275 135.384 -43.6131L135.385 -43.6175L136.212 -47.9189C137.158 -41.3992 138.447 -31.9838 139.451 -23.4621C139.985 -18.9213 140.438 -14.636 140.715 -11.1784C140.993 -7.71078 141.091 -5.10105 140.925 -3.8942C140.588 -1.44883 138.786 4.54198 136.661 11.062C134.539 17.5719 132.103 24.5823 130.507 29.0502L130.506 29.0545L130.504 29.0588C129.402 32.6154 127.867 38.7017 126.842 45.4128C125.818 52.1211 125.3 59.4706 126.241 65.5464C128.113 77.6316 129.873 90.6489 130.511 95.8262L169.893 96.0678C169.893 90.7505 169.993 79.5397 169.653 75.2865C169.44 72.625 169.268 69.9842 169.368 67.9013C169.417 66.8593 169.534 65.9693 169.742 65.2912C169.951 64.6064 170.24 64.1814 170.6 64.0016C170.788 63.9074 170.979 63.8592 171.125 63.8349C171.197 63.8229 171.257 63.8169 171.298 63.814C171.318 63.8126 171.334 63.8119 171.344 63.8115L171.355 63.8113L171.356 63.8112L171.357 63.8113L171.368 63.8115C171.378 63.8119 171.394 63.8126 171.414 63.814C171.455 63.8169 171.515 63.8229 171.587 63.8349C171.733 63.8592 171.924 63.9074 172.112 64.0016C172.472 64.1814 172.761 64.6064 172.97 65.2912C173.178 65.9693 173.295 66.8593 173.344 67.9013C173.444 69.9842 173.272 72.625 173.059 75.2865C172.719 79.5397 172.55 90.7505 172.55 96.0678L211.69 95.8262C212.329 90.6489 214.599 77.6316 216.471 65.5464C217.412 59.4706 216.894 52.1211 215.87 45.4128C214.845 38.7017 213.31 32.6154 212.208 29.0588L212.206 29.0545L212.205 29.0502C210.609 24.5823 208.173 17.5719 206.051 11.062C203.926 4.54198 202.124 -1.44883 201.787 -3.8942C201.621 -5.10105 201.719 -7.71078 201.997 -11.1784C202.274 -14.636 202.727 -18.9213 203.261 -23.4621C204.265 -31.9838 205.554 -41.3992 206.5 -47.9189L207.326 -43.622L207.327 -43.6175L207.328 -43.6131C208.321 -39.4275 209.832 -33.0853 211.138 -27.6617C211.79 -24.9499 212.392 -22.4675 212.851 -20.599C213.309 -18.7379 213.629 -17.4714 213.718 -17.2054C213.765 -17.0642 213.826 -16.7713 213.898 -16.3383C213.969 -15.9109 214.048 -15.3615 214.134 -14.7172C214.307 -13.4289 214.506 -11.7679 214.719 -9.95879C214.781 -9.43379 214.844 -8.89633 214.908 -8.35197C215.283 -5.14702 215.686 -1.70278 216.05 0.846992C216.479 3.85297 218.084 8.76792 219.946 13.5567C221.81 18.3489 223.943 23.043 225.441 25.6111C226.616 27.6262 227.887 31.5364 229 35.6313C230.108 39.7067 231.052 43.9362 231.584 46.594V48.0282C231.358 48.7071 230.682 50.8231 229.677 54.1732C228.588 57.8029 229.179 60.8352 229.518 62.5744C229.582 62.9024 229.637 63.1844 229.67 63.4157C229.709 63.689 229.702 64.2902 229.657 65.1106C229.613 65.9221 229.535 66.9263 229.44 67.9902C229.251 70.1177 228.996 72.478 228.818 74.002L228.813 74.049L228.828 74.0938C228.939 74.4267 229.159 74.9429 229.462 75.4225C229.76 75.8946 230.163 76.369 230.653 76.5651C230.817 76.6305 230.981 76.6207 231.13 76.5524C231.272 76.4876 231.392 76.3742 231.495 76.2438C231.7 75.9836 231.875 75.6007 232.024 75.1832C232.322 74.3437 232.537 73.288 232.645 72.5717C232.858 71.401 233.177 69.6362 233.443 68.1421C233.576 67.3945 233.695 66.7143 233.782 66.2102C233.825 65.9583 233.86 65.7497 233.885 65.5985C233.893 65.5448 233.901 65.4979 233.906 65.4586C233.968 65.4644 234.05 65.4778 234.146 65.4985C234.363 65.5446 234.619 65.6187 234.82 65.6826C235.756 68.5625 237.596 74.2659 237.762 74.9294C237.777 74.9889 237.787 75.0961 237.789 75.2551C237.79 75.41 237.783 75.6006 237.768 75.8193C237.739 76.2564 237.681 76.7957 237.608 77.3676C237.466 78.4923 237.268 79.7327 237.127 80.5473C236.556 81.4789 235.723 82.9799 235.049 84.5071C234.708 85.2803 234.406 86.0641 234.2 86.7865C233.995 87.505 233.88 88.1798 233.925 88.7287C233.969 89.2513 234.079 89.6223 234.263 89.8499C234.359 89.9684 234.477 90.049 234.611 90.0868C234.744 90.1241 234.877 90.1153 235.002 90.0816C235.244 90.0161 235.48 89.8485 235.682 89.6642C235.887 89.4768 236.076 89.2542 236.224 89.0509L236.225 89.049L241.789 81.7729L243.504 69.5549L241.798 59.1032C240.946 48.8854 238.775 25.731 236.901 14.8269C235.028 3.92637 231.014 -11.9888 229.238 -18.594C227.89 -27.1136 225.07 -46.8399 224.56 -57.7182C224.241 -64.5409 223.441 -69.2405 222.264 -73.2245C221.17 -76.9286 219.752 -80.0087 218.105 -83.5871L217.939 -83.9467C217.87 -84.0967 217.801 -84.2476 217.731 -84.3994C215.884 -88.4194 212.194 -90.307 208.583 -91.4304C207.049 -91.9074 205.52 -92.2496 204.146 -92.5571L204.145 -92.5574C203.901 -92.6119 203.662 -92.6654 203.429 -92.7183C201.868 -93.0726 200.584 -93.3994 199.765 -93.8601C197.062 -95.3805 188.145 -100.092 183.912 -102.316V-114.956C183.912 -115.074 183.817 -115.169 183.699 -115.169C183.582 -115.169 183.487 -115.074 183.487 -114.956V-102.059L183.6 -101.999C187.787 -99.7991 196.836 -95.0195 199.557 -93.4891C200.44 -92.9921 201.79 -92.6539 203.335 -92.3032C203.57 -92.2497 203.811 -92.1958 204.056 -92.141C205.431 -91.8334 206.941 -91.4954 208.456 -91.024C212.028 -89.9128 215.573 -88.0761 217.344 -84.2217C217.467 -83.9553 217.588 -83.6921 217.708 -83.4317L217.716 -83.4146C219.365 -79.8309 220.771 -76.7774 221.856 -73.1039C223.02 -69.1604 223.817 -64.496 224.135 -57.6983C224.647 -46.7887 227.472 -27.0283 228.82 -18.5163L228.821 -18.5052L228.824 -18.4944C230.598 -11.8982 234.611 4.01283 236.482 14.899C238.353 25.7846 240.523 48.9298 241.374 59.1469L241.375 59.1552L243.073 69.5598L241.383 81.6031L235.884 88.7946L235.881 88.7987C235.745 88.985 235.575 89.1855 235.395 89.35C235.211 89.5182 235.035 89.6317 234.891 89.6708C234.822 89.6894 234.768 89.6889 234.726 89.6771C234.686 89.6658 234.641 89.6402 234.594 89.5819C234.492 89.4562 234.391 89.1922 234.35 88.6934C234.31 88.2207 234.409 87.6044 234.61 86.9033C234.809 86.206 235.102 85.4415 235.439 84.6789C236.112 83.1536 236.948 81.6512 237.511 80.7355L237.532 80.7007L237.539 80.6604C237.682 79.8424 237.884 78.5728 238.03 77.4212C238.103 76.8456 238.162 76.2969 238.193 75.8477C238.208 75.6233 238.216 75.4211 238.214 75.2515C238.213 75.086 238.202 74.937 238.175 74.8262C238.002 74.1357 236.111 68.2786 235.191 65.448L235.158 65.3459L235.056 65.3119C234.839 65.2397 234.512 65.1413 234.235 65.0822C234.099 65.0532 233.962 65.0308 233.854 65.0296C233.803 65.0291 233.735 65.0327 233.672 65.0572C233.639 65.0702 233.595 65.0937 233.559 65.1369C233.519 65.184 233.499 65.2419 233.499 65.301C233.499 65.297 233.499 65.2975 233.498 65.3051C233.497 65.313 233.495 65.3285 233.492 65.3546C233.485 65.3972 233.476 65.4564 233.464 65.531C233.44 65.6799 233.406 65.8867 233.362 66.1382C233.276 66.641 233.156 67.3202 233.023 68.0676C232.758 69.5623 232.438 71.3284 232.226 72.4987L232.225 72.502L232.224 72.5052C232.119 73.2077 231.909 74.2357 231.623 75.0406C231.478 75.446 231.321 75.776 231.16 75.9805C231.08 76.0824 231.01 76.1393 230.953 76.1654C230.903 76.188 230.861 76.1897 230.811 76.17C230.451 76.0256 230.108 75.6487 229.822 75.1951C229.553 74.7698 229.353 74.3102 229.247 74.0034C229.424 72.4757 229.676 70.1383 229.864 68.028C229.959 66.9619 230.038 65.9523 230.082 65.1336C230.126 64.3236 230.138 63.6781 230.092 63.3555C230.057 63.1088 229.999 62.8142 229.934 62.476C229.595 60.7283 229.03 57.8133 230.085 54.2955C231.106 50.8915 231.787 48.7656 231.998 48.13L232.009 48.0972V46.5518L232.005 46.5312C231.472 43.8672 230.524 39.6167 229.411 35.5197C228.3 31.4318 227.017 27.4677 225.809 25.3966C224.327 22.8571 222.204 18.1872 220.343 13.4025C218.481 8.61446 216.893 3.73973 216.471 0.786797C216.108 -1.75713 215.706 -5.19487 215.33 -8.40048C215.267 -8.94503 215.204 -9.48289 215.142 -10.0085C214.929 -11.8174 214.729 -13.4816 214.556 -14.7737C214.47 -15.4197 214.39 -15.9742 214.318 -16.4081C214.246 -16.8363 214.181 -17.162 214.122 -17.34C214.04 -17.5847 213.725 -18.826 213.264 -20.7007C212.805 -22.5681 212.204 -25.0497 211.552 -27.7613C210.247 -33.1823 208.736 -39.5215 207.743 -43.7068L206.68 -49.2357L206.261 -49.2264C205.303 -42.6986 203.908 -32.5983 202.839 -23.5119C202.304 -18.9689 201.85 -14.6776 201.572 -11.2124C201.295 -7.75719 201.191 -5.09788 201.365 -3.83604C201.709 -1.34402 203.526 4.68945 205.646 11.1939C207.769 17.7065 210.206 24.7185 211.802 29.1892C212.899 32.7271 214.428 38.7905 215.449 45.4771C216.471 52.1694 216.982 59.4688 216.05 65.4812C214.199 77.4295 211.864 91.0006 211.207 96.3094L173.087 96.4126C173.09 91.0066 173.149 79.4973 173.483 75.3204C173.696 72.6614 173.87 69.9949 173.77 67.8811C173.719 66.8249 173.6 65.8942 173.377 65.1666C173.156 64.4456 172.82 63.8798 172.302 63.6209C172.065 63.5023 171.831 63.444 171.657 63.4151C171.57 63.4006 171.497 63.3932 171.444 63.3895C171.418 63.3876 171.397 63.3866 171.382 63.3861C171.375 63.3859 171.369 63.3858 171.364 63.3857L171.359 63.3856H171.357H171.356H171.356H171.355H171.353L171.348 63.3857C171.343 63.3858 171.337 63.3859 171.33 63.3861C171.315 63.3866 171.294 63.3876 171.268 63.3895C171.215 63.3932 171.142 63.4006 171.055 63.4151C170.881 63.444 170.647 63.5023 170.41 63.6209C169.892 63.8798 169.556 64.4456 169.335 65.1666C169.112 65.8942 168.993 66.8249 168.942 67.8811C168.842 69.9949 169.016 72.6614 169.228 75.3204C169.563 79.4973 169.407 90.6588 169.409 96.0648L130.919 95.8287C130.262 90.5199 128.513 77.4295 126.662 65.4812C125.73 59.4688 126.241 52.1694 127.263 45.4771C128.284 38.7905 129.813 32.7271 130.91 29.1892C132.506 24.7185 134.943 17.7065 137.066 11.1939C139.186 4.68945 141.003 -1.34402 141.347 -3.83604C141.521 -5.09788 141.417 -7.75719 141.14 -11.2124C140.862 -14.6776 140.408 -18.9689 139.873 -23.5119C138.804 -32.5983 137.409 -42.6986 136.451 -49.2264L136.032 -49.2357L134.969 -43.7068C133.976 -39.5215 132.465 -33.1823 131.16 -27.7613C130.508 -25.0497 129.907 -22.5681 129.447 -20.7007C128.986 -18.826 128.672 -17.5847 128.59 -17.34C128.531 -17.162 128.466 -16.8363 128.394 -16.4081C128.322 -15.9742 128.242 -15.4197 128.156 -14.7737C127.982 -13.4816 127.783 -11.8174 127.57 -10.0085C127.508 -9.48289 127.445 -8.94503 127.381 -8.40048C127.006 -5.19499 126.604 -1.75703 126.241 0.786797C125.819 3.73973 124.231 8.61446 122.369 13.4025C120.508 18.1872 118.385 22.8571 116.903 25.3966C115.695 27.4677 114.412 31.4318 113.301 35.5197C112.188 39.6167 111.24 43.8672 110.707 46.5312L110.703 46.5518V48.0972L110.714 48.13C110.925 48.7656 111.606 50.8915 112.627 54.2955C113.682 57.8133 113.117 60.7283 112.778 62.476C112.713 62.8142 112.655 63.1088 112.62 63.3555C112.574 63.6781 112.586 64.3236 112.63 65.1336C112.674 65.9523 112.753 66.9619 112.848 68.028C113.036 70.1383 113.288 72.4757 113.465 74.0034C113.359 74.3102 113.159 74.7698 112.89 75.1951C112.604 75.6487 112.261 76.0256 111.901 76.17C111.851 76.1897 111.809 76.188 111.759 76.1654C111.702 76.1393 111.632 76.0824 111.552 75.9805C111.391 75.776 111.234 75.446 111.089 75.0406C110.803 74.2357 110.593 73.2077 110.487 72.5052L110.487 72.502L110.486 72.4987C110.274 71.3284 109.954 69.5623 109.688 68.0676C109.555 67.3202 109.436 66.641 109.35 66.1382C109.306 65.8867 109.272 65.6799 109.248 65.531C109.236 65.4564 109.227 65.3972 109.22 65.3546C109.217 65.3285 109.215 65.313 109.214 65.3051C109.213 65.2975 109.213 65.297 109.213 65.301C109.213 65.2419 109.193 65.184 109.153 65.1369C109.117 65.0937 109.073 65.0702 109.04 65.0572C108.977 65.0327 108.909 65.0291 108.858 65.0296C108.75 65.0308 108.613 65.0532 108.477 65.0822C108.2 65.1413 107.873 65.2397 107.656 65.3119L107.554 65.3459L107.521 65.448C106.601 68.2786 104.71 74.1357 104.537 74.8262C104.51 74.937 104.499 75.086 104.498 75.2515C104.496 75.4211 104.504 75.6233 104.519 75.8477C104.55 76.2969 104.609 76.8456 104.682 77.4212C104.828 78.5728 105.03 79.8424 105.173 80.6604L105.18 80.7007L105.201 80.7355C105.764 81.6512 106.6 83.1536 107.273 84.6789C107.609 85.4415 107.903 86.206 108.102 86.9033C108.303 87.6044 108.402 88.2207 108.362 88.6934C108.321 89.1922 108.22 89.4562 108.118 89.5819C108.071 89.6402 108.026 89.6658 107.986 89.6771C107.944 89.6889 107.89 89.6894 107.821 89.6708C107.677 89.6317 107.501 89.5182 107.317 89.35C107.137 89.1855 106.967 88.985 106.831 88.7987L106.828 88.7946L101.329 81.6031L99.6385 69.5598L101.337 59.1552L101.338 59.1469C102.189 48.9298 104.359 25.7846 106.23 14.899C108.101 4.01283 112.114 -11.8982 113.888 -18.4944L113.891 -18.5052L113.892 -18.5163C115.24 -27.0283 118.065 -46.7887 118.577 -57.6983C118.895 -64.496 119.691 -69.1604 120.856 -73.1039C121.941 -76.7774 123.347 -79.8309 124.996 -83.4146C125.119 -83.6806 125.243 -83.9495 125.368 -84.2217C127.139 -88.0761 130.684 -89.9128 134.256 -91.024C135.771 -91.4954 137.281 -91.8334 138.656 -92.141C138.901 -92.1958 139.141 -92.2497 139.377 -92.3032C140.922 -92.6539 142.272 -92.9921 143.155 -93.4891C145.876 -95.0195 154.925 -99.7991 159.111 -101.999L159.225 -102.059V-114.956Z"
                      fill="black"
                    />
                    <path
                      id="Vector 11"
                      d="M171.486 7.82443C171.518 7.71048 171.452 7.592 171.338 7.55979C171.224 7.52758 171.106 7.59385 171.073 7.7078L171.486 7.82443ZM171.073 7.7078C170.994 7.98847 171.003 8.39768 171.049 8.84027C171.096 9.2919 171.186 9.81035 171.291 10.3269C171.396 10.8441 171.516 11.3635 171.622 11.8175C171.729 12.2739 171.822 12.6596 171.875 12.9169L172.295 12.8306C172.241 12.5655 172.146 12.1702 172.04 11.7194C171.933 11.2662 171.815 10.7521 171.711 10.2418C171.608 9.73073 171.52 9.22756 171.475 8.79572C171.429 8.35484 171.431 8.01873 171.486 7.82443L171.073 7.7078ZM171.875 12.9169C172.085 13.9376 172.137 15.8282 171.876 16.9807L172.294 17.0753C172.571 15.8531 172.515 13.8998 172.295 12.8306L171.875 12.9169ZM171.876 16.9807C171.814 17.2548 171.728 17.3187 171.696 17.3329C171.663 17.3474 171.599 17.349 171.484 17.2857C171.376 17.2258 171.267 17.1308 171.18 17.0438C171.138 17.0014 171.103 16.9633 171.08 16.936C171.068 16.9224 171.059 16.9116 171.053 16.9045C171.05 16.9009 171.048 16.8983 171.046 16.8966C171.046 16.8958 171.045 16.8953 171.045 16.895C171.045 16.8949 171.045 16.8948 171.045 16.8948C171.045 16.8948 171.045 16.8948 171.045 16.8948C171.045 16.8948 171.045 16.8949 171.045 16.8949C171.045 16.8949 171.045 16.895 170.877 17.028C170.709 17.1611 170.709 17.1611 170.709 17.1612C170.709 17.1612 170.709 17.1613 170.709 17.1613C170.709 17.1615 170.709 17.1616 170.709 17.1617C170.71 17.162 170.71 17.1624 170.71 17.1628C170.711 17.1636 170.712 17.1648 170.713 17.1662C170.715 17.169 170.718 17.1728 170.722 17.1777C170.73 17.1873 170.742 17.2009 170.756 17.2174C170.785 17.2504 170.826 17.2958 170.876 17.3462C170.974 17.4448 171.117 17.5724 171.277 17.661C171.431 17.7461 171.651 17.8218 171.87 17.7251C172.089 17.6279 172.222 17.395 172.294 17.0753L171.876 16.9807Z"
                      fill="black"
                    />
                    <path
                      id="Vector 12_3"
                      d="M152.784 49.0526C152.784 49.0526 155.125 52.2449 157.253 54.3731C159.114 56.2337 161.297 58.2038 161.297 58.2038"
                      stroke="black"
                      stroke-width="0.428814"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </g>
                  <path
                    id="Vector 41_3"
                    d="M210.965 98.0008L173.275 96.7927V96.3095V95.5847L175.884 92.4438L211.449 94.3767L211.207 96.3095L210.965 98.0008Z"
                    fill="white"
                  />
                  <path
                    id="Rectangle 1"
                    d="M173.274 92.4438L173.516 95.5847V96.5511H173.033L173.274 92.4438Z"
                    fill="white"
                  />
                  <path
                    id="Vector 42"
                    d="M169.41 96.7928V95.8263L162.403 93.4103L130.753 94.6183L131.236 97.7592L131.478 97.0344L169.41 96.7928Z"
                    fill="#F4F3F3"
                  />
                </g>
                {findMeasure('lower_length') && (
                  <g id="lower_length">
                    <path
                      id="Vector 19"
                      d="M218.995 196.751L218.995 28.1031"
                      stroke="#E55959"
                      stroke-width="2.19269"
                      stroke-linecap="square"
                    />
                    <path
                      id="Vector 29"
                      d="M126.982 27.5715L219.353 27.5715"
                      stroke="#E55959"
                      stroke-width="1.46179"
                      stroke-linecap="square"
                      stroke-dasharray="4.87 4.87"
                    />
                    <path
                      id="Vector 28"
                      d="M223.155 194.1L218.971 198.207L215.34 193.978"
                      stroke="#E55959"
                      stroke-width="2.19269"
                    />
                  </g>
                )}
                {findMeasure('insideLeg') && (
                  <g id="inside_leg">
                    <path
                      id="Vector 29_2"
                      d="M126.982 27.5715L219.075 27.5715"
                      stroke="#E55959"
                      stroke-width="1.46179"
                      stroke-linecap="square"
                      stroke-dasharray="4.95 4.95"
                    />
                    <g id="Group 225">
                      <path
                        id="Vector 19_2"
                        d="M171.296 222.897L171.296 28.3148"
                        stroke="#E55959"
                        stroke-width="2.19269"
                        stroke-linecap="square"
                      />
                      <path
                        id="Vector 28_2"
                        d="M175.521 220.206L171.271 224.378L167.582 220.081"
                        stroke="#E55959"
                        stroke-width="2.19269"
                      />
                    </g>
                  </g>
                )}
                {findMeasure('thigh') && (
                  <g id="thigh">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M213.959 78.0031C213.959 78.0031 206.908 78.9316 190.693 79.3181M173.321 78.119C173.321 78.119 177.745 78.6608 186.162 79.3181"
                            stroke="#E55959"
                            stroke-width="1.46179"
                          />
                          <path
                            id="Vector 27"
                            d="M192.655 77.2945L189.809 79.2075L192.254 81.0439"
                            stroke="#E55959"
                            stroke-width="1.46179"
                          />
                          <path
                            id="Vector 28_3"
                            d="M183.687 77.2945L186.533 79.2075L184.088 81.0439"
                            stroke="#E55959"
                            stroke-width="1.46179"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('hip') && (
                  <g id="hip">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M216.151 49.4983C216.151 49.4983 202.995 54.1308 167.454 55.6392M126.374 49.4983C126.374 49.4983 133.693 52.6484 162.805 55.6392"
                            stroke="#E55959"
                            stroke-width="1.46179"
                          />
                          <path
                            id="Vector 27_2"
                            d="M169.469 53.5623L166.548 55.5253L169.057 57.4098"
                            stroke="#E55959"
                            stroke-width="1.46179"
                          />
                          <path
                            id="Vector 28_4"
                            d="M160.266 53.5623L163.186 55.5253L160.678 57.4098"
                            stroke="#E55959"
                            stroke-width="1.46179"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_2684"
              x1="151.508"
              y1="36.2829"
              x2="135.121"
              y2="130.774"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_2684"
              x1="119.372"
              y1="-35.8628"
              x2="104.262"
              y2="85.8696"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
