import { cn } from '@/lib/utils';

interface LinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  /** The URL the link points to */
  href: string;
  /** Whether the link is to an external site */
  external?: boolean;
  /** The content of the link */
  children: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
}

export const Link = ({ href, external = false, children, className, ...props }: LinkProps) => {
  const linkProps = external ? { target: '_blank', rel: 'noopener noreferrer' } : {};

  return (
    <a
      href={href}
      {...linkProps}
      {...props}
      className={cn('text-[#272727] underline underline-offset-4 focus:outline-none focus:ring-0', className)}
    >
      {children}
    </a>
  );
};
