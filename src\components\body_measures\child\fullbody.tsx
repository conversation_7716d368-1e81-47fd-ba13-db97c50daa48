import { useDevice } from '@/hooks/use-device';
import { BodyMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface FullBodyProps {
  measure: BodyMeasure;
  className?: string;
}

export function FullBody({ measure, className }: FullBodyProps) {
  const { measures } = measure;
  const mappedMeasures = measures.map((item) => item.measure);
  const { isMobile } = useDevice();

  const findMeasure = (measure: string) => {
    const foundMeasure = mappedMeasures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full flex justify-center', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g xmlns="http://www.w3.org/2000/svg" id="group_fullbody">
            <g id="child">
              <g id="Group 38">
                <g id="Group 37">
                  <path
                    id="Vector"
                    d="M160.264 38.2924C160.993 36.1065 161.059 33.0265 161.059 33.0265H161.456H170.2H170.597C170.597 33.0265 170.663 36.1065 171.392 38.2924C171.922 39.8821 174.074 40.1802 177.453 41.2731C180.754 42.3414 182.917 41.9686 184.308 45.7442C185.699 49.5197 189.574 61.3432 190.17 63.9264C190.647 65.9931 191.296 68.9605 191.561 70.1859C192.091 71.047 193.389 74.4185 194.343 81.0158C195.297 87.6131 195.469 95.555 195.436 98.7013C195.602 100.622 195.632 105.161 195.632 105.161L195.436 106.992C195.436 106.992 194.939 108.617 193.747 110.127C192.257 112.015 189.773 114.499 189.475 113.804C189.236 113.247 189.574 112.512 189.773 112.214C189.442 112.512 188.759 112.989 188.68 112.512C188.581 111.916 187.984 112.015 189.475 110.624C190.965 109.233 191.561 108.041 191.561 106.451C191.561 105.179 190.369 103.073 189.773 102.179C189.442 102.378 188.581 103.033 187.786 104.067C186.792 105.358 186.395 106.551 185.799 105.954C185.202 105.358 185.202 104.961 186.295 103.172C187.388 101.384 188.879 98.2045 189.773 97.6084C190.488 97.1315 190.932 96.8135 191.064 96.7142C190.899 95.4557 190.488 92.5809 190.17 91.1502C189.773 89.3618 188.183 86.9772 187.09 83.1023C186.216 80.0024 185.269 74.0608 184.904 71.4776C183.679 68.2319 181.188 61.5419 181.029 60.747C180.87 59.9522 180.102 56.9052 179.738 55.4811L179.142 56.9715V73.9615C179.34 75.1206 179.678 78.1146 179.44 80.8171C179.201 83.5196 179.34 84.6589 179.44 84.8907C179.903 86.646 180.831 90.6932 180.831 92.8393V98.3039C181.526 100.854 182.917 106.61 182.917 109.233C182.917 112.512 182.818 118.771 182.619 121.553C182.46 123.779 182.089 126.455 181.924 127.515H170.498C169.802 122.812 168.352 112.85 168.113 110.624C167.815 107.842 167.914 106.551 167.616 105.954C167.318 105.358 166.821 104.762 166.225 104.762H165.43C164.834 104.762 164.338 105.358 164.039 105.954C163.741 106.551 163.841 107.842 163.543 110.624C163.304 112.85 161.854 122.812 161.158 127.515H149.732C149.566 126.455 149.196 123.779 149.037 121.553C148.838 118.771 148.739 112.512 148.739 109.233C148.739 106.61 150.129 100.854 150.825 98.3039V92.8393C150.825 90.6932 151.752 86.646 152.216 84.8907C152.315 84.6589 152.454 83.5196 152.216 80.8171C151.978 78.1146 152.315 75.1206 152.514 73.9615V56.9715L151.918 55.4811C151.554 56.9052 150.785 59.9522 150.626 60.747C150.467 61.5419 147.977 68.2319 146.751 71.4776C146.387 74.0608 145.44 80.0024 144.566 83.1023C143.473 86.9772 141.883 89.3618 141.485 91.1502C141.168 92.5809 140.757 95.4557 140.591 96.7142C140.724 96.8135 141.168 97.1315 141.883 97.6084C142.777 98.2045 144.267 101.384 145.36 103.172C146.453 104.961 146.453 105.358 145.857 105.954C145.261 106.551 144.864 105.358 143.87 104.067C143.075 103.033 142.214 102.378 141.883 102.179C141.287 103.073 140.094 105.179 140.094 106.451C140.094 108.041 140.691 109.233 142.181 110.624C143.671 112.015 143.075 111.916 142.976 112.512C142.896 112.989 142.214 112.512 141.883 112.214C142.082 112.512 142.419 113.247 142.181 113.804C141.883 114.499 139.399 112.015 137.909 110.127C136.716 108.617 136.133 106.981 136.298 106.65C136.232 106.319 135.979 106.252 136.099 105.358C136.208 104.547 135.934 100.622 136.099 98.7013C136.066 95.555 136.359 87.6131 137.312 81.0158C138.266 74.4185 139.565 71.047 140.094 70.1859C140.359 68.9605 141.009 65.9931 141.485 63.9264C142.082 61.3432 145.957 49.5197 147.348 45.7442C148.739 41.9686 150.901 42.3414 154.203 41.2731C157.581 40.1802 159.734 39.8821 160.264 38.2924Z"
                    fill="white"
                  />
                  <g id="Group 36">
                    <g id="Group 35">
                      <path
                        id="Vector 21"
                        d="M166.602 37.7955C169.507 36.5634 170.974 33.9206 171.47 33.1257V32.4302C170.278 31.6354 167.854 30.0457 167.695 30.0457C167.536 30.0457 162.528 31.4366 160.044 32.1321L161 35L160.5 37C162.487 38.2916 164.758 38.5776 166.602 37.7955Z"
                        fill="url(#paint0_linear_128_2185)"
                      />
                      <g id="Group 34">
                        <path
                          id="Vector 20"
                          d="M144.346 72.173C145.124 72.4695 146.831 71.64 147.029 70.8814L146.532 72.8685C146.594 73.7319 146.134 75.3525 146.134 75.3525C146.134 75.3525 145.666 77.942 145.538 78.9293L144.048 84.5927C143.716 85.6163 142.172 89.0792 142.259 89.0637C142.259 89.0637 141.51 90.8286 141.365 91.5476L140.873 94.727L140.57 96.118C140.71 96.3948 140.546 96.4745 140.57 96.6148C140.601 96.7902 142.335 98.0058 142.335 98.0058C142.253 98.1226 143.352 99.6949 143.352 99.6949L144.445 101.583L146.035 104.464L146.333 105.358L145.439 106.054L144.942 105.557L143.551 103.669L141.962 102.179C141.962 102.179 140.736 104.374 140.57 104.464L139.974 106.451L140.272 107.803L141.348 109.829L143.146 111.816L142.991 112.611L142.629 112.611L141.962 112.214C141.987 112.354 142.278 113.085 142.335 113.406L142.137 114.002L141.348 113.495L139.974 112.413L138.623 110.902L137.49 109.432L136.795 108.041L136.497 107.416L136.199 105.954L136 103.868L136.199 101.781L136.199 95.2238L136.795 85.1888L137.49 80.221C137.599 78.2722 138.881 73.3653 138.881 73.3653C138.881 73.3653 139.477 71.4776 140.173 69.8878C140.23 69.1844 140.718 66.4191 140.769 66.7084L141.365 64.5226C141.663 67.4039 141.465 67.3046 141.763 68.9559C142.082 70.7242 143.374 71.8024 144.346 72.173Z"
                          fill="url(#paint1_linear_128_2185)"
                        />
                        <path
                          id="Vector 18"
                          d="M161.336 102.477C162.686 103.973 163.919 106.252 163.919 106.252L163.522 111.32L162.528 118.374L161.535 124.931L161.237 127.415L149.807 127.418L149.088 122.263L148.916 117.678L148.817 114.698V110.227V107.544L150.903 98.3038V91.7462L159.547 100.589C159.547 100.589 159.344 100.268 161.336 102.477Z"
                          fill="url(#paint2_linear_128_2185)"
                        />
                        <g id="Vector 47">
                          <path
                            d="M149.615 131.465C149.846 130.155 149.776 128.157 149.711 127.322L153.084 125.684L161.177 127.322V129.249C161.177 129.827 160.985 132.911 160.407 135.03C159.944 136.726 158.544 141.454 157.901 143.606C157.805 144.216 157.574 145.976 157.42 148.134C157.227 150.832 156.071 156.228 155.204 158.83C154.336 161.431 153.662 165.96 153.566 166.923C153.469 167.887 153.758 169.525 153.758 170.488C153.758 171.452 153.855 172.897 153.566 173.668C153.276 174.439 153.566 176.848 153.566 177.715C153.566 178.582 153.084 178.775 152.891 179.738C152.698 180.702 153.084 180.895 153.084 181.473C153.084 182.051 152.987 181.954 152.891 182.436C152.795 182.918 152.409 183.785 151.157 183.978C150.211 184.123 150.049 183.536 150.088 183.173C150.037 183.262 149.824 183.414 149.23 183.592C148.459 183.824 148.266 183.368 148.266 183.111C148.234 183.175 148.035 183.361 147.495 183.592C146.956 183.824 146.821 183.368 146.821 183.111C146.789 183.175 146.647 183.303 146.339 183.303C146.031 183.303 145.889 183.046 145.857 182.918C145.857 182.982 145.722 183.149 145.183 183.303C144.508 183.496 144.316 182.147 144.412 181.473C144.508 180.798 145.472 178.678 145.954 178.004C146.435 177.329 147.303 173.668 147.399 173.186C147.495 172.705 147.206 170.97 147.688 167.694C148.17 164.418 147.11 160.179 147.206 152.181C147.303 144.184 149.326 133.103 149.615 131.465Z"
                            fill="#F5F3F3"
                          />
                          <path
                            d="M149.711 127.322C149.776 128.157 149.846 130.155 149.615 131.465C149.326 133.103 147.303 144.184 147.206 152.181C147.11 160.179 148.17 164.418 147.688 167.694C147.206 170.97 147.495 172.705 147.399 173.186C147.303 173.668 146.435 177.329 145.954 178.004C145.472 178.678 144.508 180.798 144.412 181.473C144.316 182.147 144.508 183.496 145.183 183.303C145.722 183.149 145.857 182.982 145.857 182.918C145.889 183.046 146.031 183.303 146.339 183.303C146.647 183.303 146.789 183.175 146.821 183.111C146.821 183.368 146.956 183.824 147.495 183.592C148.035 183.361 148.234 183.175 148.266 183.111C148.266 183.368 148.459 183.824 149.23 183.592C150 183.361 150.129 183.175 150.097 183.111C150.033 183.464 150.155 184.132 151.157 183.978C152.409 183.785 152.795 182.918 152.891 182.436C152.987 181.954 153.084 182.051 153.084 181.473C153.084 180.895 152.698 180.702 152.891 179.738C153.084 178.775 153.566 178.582 153.566 177.715C153.566 176.848 153.276 174.439 153.566 173.668C153.855 172.897 153.758 171.452 153.758 170.488C153.758 169.525 153.469 167.887 153.566 166.923C153.662 165.96 154.336 161.431 155.204 158.83C156.071 156.228 157.227 150.832 157.42 148.134C157.574 145.976 157.805 144.216 157.901 143.606C158.544 141.454 159.944 136.726 160.407 135.03C160.985 132.911 161.177 129.827 161.177 129.249C161.177 128.787 161.177 127.772 161.177 127.322L153.084 125.684L149.711 127.322Z"
                            stroke="black"
                            stroke-width="0.192707"
                          />
                        </g>
                      </g>
                    </g>
                    <g id="Group 33">
                      <path
                        id="Vector 11"
                        d="M166.065 84.7428C166.093 84.6957 166.078 84.6347 166.031 84.6066C165.984 84.5785 165.923 84.5938 165.895 84.641L166.065 84.7428ZM165.895 84.641C165.868 84.685 165.859 84.7348 165.859 84.781C165.858 84.8277 165.864 84.8774 165.875 84.9268C165.897 85.0255 165.938 85.1358 165.984 85.2421C166.029 85.3492 166.081 85.4561 166.127 85.5485C166.174 85.6427 166.212 85.7183 166.234 85.7682L166.416 85.6891C166.392 85.633 166.35 85.55 166.305 85.4601C166.26 85.3683 166.21 85.2655 166.166 85.164C166.122 85.0617 166.087 84.9647 166.069 84.884C166.06 84.8437 166.057 84.8108 166.057 84.7852C166.058 84.7591 166.063 84.747 166.065 84.7428L165.895 84.641ZM166.234 85.7682C166.274 85.8593 166.301 85.9963 166.304 86.1413C166.306 86.2866 166.284 86.4274 166.236 86.5291L166.415 86.6147C166.482 86.4753 166.505 86.3006 166.502 86.1376C166.499 85.9743 166.469 85.8101 166.416 85.6891L166.234 85.7682ZM166.236 86.5291C166.222 86.5583 166.203 86.5707 166.175 86.5763C166.143 86.5831 166.099 86.5795 166.048 86.5662C165.999 86.5532 165.951 86.5334 165.915 86.5161C165.897 86.5076 165.882 86.4999 165.872 86.4945C165.867 86.4918 165.863 86.4897 165.861 86.4883C165.86 86.4876 165.859 86.4871 165.859 86.4869C165.858 86.4867 165.858 86.4866 165.858 86.4866C165.858 86.4866 165.858 86.4866 165.858 86.4866C165.858 86.4866 165.858 86.4866 165.858 86.4866C165.858 86.4866 165.858 86.4866 165.858 86.4866C165.858 86.4866 165.858 86.4866 165.807 86.5719C165.756 86.6572 165.756 86.6572 165.756 86.6572C165.756 86.6572 165.756 86.6572 165.756 86.6572C165.756 86.6573 165.756 86.6573 165.756 86.6573C165.757 86.6574 165.757 86.6575 165.757 86.6576C165.757 86.6578 165.758 86.658 165.758 86.6583C165.759 86.6589 165.761 86.6598 165.762 86.6608C165.766 86.6628 165.771 86.6656 165.777 86.6691C165.79 86.6759 165.808 86.6852 165.829 86.6955C165.872 86.7159 165.932 86.7412 165.998 86.7584C166.062 86.7752 166.141 86.7867 166.216 86.7708C166.297 86.7539 166.371 86.706 166.415 86.6147L166.236 86.5291Z"
                        fill="black"
                      />
                      <path
                        id="Vector 17"
                        d="M165.273 57.2695C165.273 57.2695 165.173 57.9058 165.171 58.3949C165.169 58.8225 165.192 59.2996 165.192 59.2996"
                        stroke="black"
                        stroke-width="0.198714"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        id="Vector 12"
                        d="M159.051 99.7942C159.051 99.7942 159.707 100.695 160.303 101.296C160.824 101.821 161.435 102.377 161.435 102.377"
                        stroke="black"
                        stroke-width="0.198714"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <g id="Group 32">
                        <path
                          id="Vector 15"
                          d="M143.551 69.9872C143.551 69.9872 143.651 70.8814 143.949 71.2788C144.247 71.6762 144.644 71.7756 144.644 71.7756"
                          stroke="black"
                          stroke-width="0.198714"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          id="Vector 16"
                          d="M188.56 69.9872C188.56 69.9872 188.46 70.8814 188.162 71.2788C187.864 71.6762 187.467 71.7756 187.467 71.7756"
                          stroke="black"
                          stroke-width="0.198714"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </g>
                      <g id="Group 27">
                        <path
                          id="Vector 15_2"
                          d="M164.813 44.5518C164.813 44.5518 164.602 43.9483 164.019 43.5951C163.436 43.2418 161.568 43.2271 160.64 42.5647"
                          stroke="black"
                          stroke-width="0.198714"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          id="Vector 14"
                          d="M166.403 44.5518C166.403 44.5518 166.615 43.9483 167.198 43.5951C167.781 43.2418 169.649 43.2271 170.576 42.5647"
                          stroke="black"
                          stroke-width="0.198714"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </g>
                    </g>
                  </g>
                </g>
              </g>
              <path
                id="Vector_2"
                d="M166.225 104.762H165.43C164.834 104.762 164.337 105.358 164.039 105.954C163.741 106.551 163.841 107.842 163.543 110.624C163.304 112.85 161.854 122.812 161.158 127.515H149.732C149.566 126.455 149.195 123.779 149.037 121.553C148.838 118.771 148.738 112.512 148.738 109.233C148.738 106.61 150.129 100.854 150.825 98.3039V92.8393C150.825 90.6932 151.752 86.646 152.216 84.8907C152.315 84.6589 152.454 83.5196 152.216 80.8171C151.977 78.1146 152.315 75.1206 152.514 73.9615V56.9715L151.918 55.4811C151.554 56.9052 150.785 59.9522 150.626 60.747C150.467 61.5419 147.977 68.2319 146.751 71.4776C146.387 74.0608 145.44 80.0024 144.565 83.1023C143.473 86.9772 141.883 89.3618 141.485 91.1502C141.167 92.5809 140.757 95.4557 140.591 96.7142C140.724 96.8135 141.167 97.1315 141.883 97.6084C142.777 98.2045 144.267 101.384 145.36 103.172C146.453 104.961 146.453 105.358 145.857 105.954C145.261 106.551 144.864 105.358 143.87 104.067C143.075 103.033 142.214 102.378 141.883 102.179C141.287 103.073 140.094 105.179 140.094 106.451C140.094 108.041 140.691 109.233 142.181 110.624C143.671 112.015 143.075 111.916 142.976 112.512C142.896 112.989 142.214 112.512 141.883 112.214C142.082 112.512 142.419 113.247 142.181 113.804C141.883 114.499 139.399 112.015 137.909 110.127C136.716 108.617 136.397 107.246 136.397 107.246C136.397 107.246 136.178 106.451 136.099 105.358C136.041 104.542 136.054 100.622 136.22 98.7013C136.186 95.555 136.359 87.6131 137.312 81.0158C138.266 74.4185 139.565 71.047 140.094 70.1859C140.359 68.9605 141.009 65.9931 141.485 63.9264C142.082 61.3432 145.956 49.5197 147.347 45.7442C148.738 41.9686 150.901 42.3414 154.203 41.2731C157.581 40.1802 159.734 39.8821 160.264 38.2924C160.992 36.1065 161.059 33.0265 161.059 33.0265H170.2M166.225 104.762C166.821 104.762 167.318 105.358 167.616 105.954C167.914 106.551 167.815 107.842 168.113 110.624C168.351 112.85 169.732 122.619 170.427 127.322L181.893 127.418C182.059 126.359 182.46 123.779 182.619 121.553C182.818 118.771 182.917 112.512 182.917 109.233C182.917 106.61 181.526 100.854 180.831 98.3039V92.8393C180.831 90.6932 179.903 86.646 179.44 84.8907C179.34 84.6589 179.201 83.5196 179.44 80.8171C179.678 78.1146 179.34 75.1206 179.142 73.9615V56.9715L179.738 55.4811C180.102 56.9052 180.87 59.9522 181.029 60.747C181.188 61.5419 183.679 68.2319 184.904 71.4776C185.269 74.0608 186.216 80.0024 187.09 83.1023C188.183 86.9772 189.773 89.3618 190.17 91.1502C190.488 92.5809 190.899 95.4557 191.064 96.7142C190.932 96.8135 190.488 97.1315 189.773 97.6084C188.879 98.2045 187.388 101.384 186.295 103.172C185.202 104.961 185.202 105.358 185.799 105.954C186.395 106.551 186.792 105.358 187.786 104.067C188.581 103.033 189.442 102.378 189.773 102.179C190.369 103.073 191.561 105.179 191.561 106.451C191.561 108.041 190.965 109.233 189.475 110.624C187.984 112.015 188.581 111.916 188.68 112.512C188.759 112.989 189.442 112.512 189.773 112.214C189.574 112.512 189.236 113.247 189.475 113.804C189.773 114.499 192.257 112.015 193.747 110.127C194.939 108.617 195.337 107.246 195.436 106.749C195.813 105.458 195.602 100.622 195.436 98.7013C195.469 95.555 195.297 87.6131 194.343 81.0158C193.389 74.4185 192.091 71.047 191.561 70.1859C191.296 68.9605 190.647 65.9931 190.17 63.9264C189.574 61.3432 185.699 49.5197 184.308 45.7442C182.917 41.9686 180.754 42.3414 177.453 41.2731C174.074 40.1802 171.922 39.8821 171.392 38.2924C170.663 36.1065 170.597 33.0265 170.597 33.0265H161.456M166.225 104.762H165.331"
                stroke="black"
                stroke-width="0.198714"
                stroke-linecap="round"
              />
              <g id="Group 31">
                <g id="Group 2">
                  <path
                    id="Vector 4"
                    d="M155.177 22.3256C155.547 21.0317 156.258 22.0402 156.567 22.7061L157.031 24.3234L157.262 27.6532C157.224 27.6849 157.031 28.0337 156.567 27.7483C155.974 27.3828 154.713 23.9429 155.177 22.3256Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.198714"
                  />
                  <path
                    id="Vector 5"
                    d="M177.232 22.3256C176.861 21.0317 176.151 22.0402 175.842 22.7061L175.378 24.3234L175.146 27.6532C175.185 27.6849 175.378 28.0337 175.842 27.7483C176.435 27.3828 177.695 23.9429 177.232 22.3256Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.198714"
                  />
                </g>
                <g id="Vector_3">
                  <path
                    d="M157.79 14.019C159.714 9.81877 165.201 9.58951 166.006 9.57863C166.81 9.58951 172.297 9.81877 174.221 14.019C175.78 17.4213 176.671 24.6041 174.221 30.8167C172.977 33.9732 170.955 35.354 169.569 36.0297C168.183 36.7055 166.105 36.8019 166.105 36.8019C166.105 36.8019 163.729 36.8019 162.442 36.0297C161.156 35.2576 159.035 33.9732 157.79 30.8167C155.34 24.6041 156.232 17.4213 157.79 14.019Z"
                    fill="white"
                  />
                  <path
                    d="M166.105 9.57829C166.105 9.57829 159.869 9.48173 157.79 14.019C156.232 17.4213 155.34 24.6041 157.79 30.8167C159.035 33.9732 161.156 35.2576 162.442 36.0297C163.729 36.8019 166.105 36.8019 166.105 36.8019C166.105 36.8019 168.183 36.7055 169.569 36.0297C170.955 35.354 172.977 33.9732 174.221 30.8167C176.671 24.6041 175.78 17.4213 174.221 14.019C172.143 9.48173 165.907 9.57829 165.907 9.57829"
                    stroke="black"
                    stroke-width="0.198714"
                    stroke-linecap="round"
                  />
                </g>
              </g>
              <path
                id="Vector 48"
                d="M149.904 128.382C149.808 127.707 149.786 127.287 149.711 126.648L151.542 125.106L154.529 123.853L161.274 126.069L161.081 127.611L149.904 128.382Z"
                fill="#F5F3F3"
              />
              <g id="Vector 47_2">
                <path
                  d="M181.99 131.465C181.758 130.155 181.829 128.157 181.893 127.322L178.521 125.684L170.427 127.322V129.249C170.427 129.827 170.62 132.911 171.198 135.03C171.661 136.726 173.061 141.454 173.703 143.606C173.8 144.216 174.031 145.976 174.185 148.134C174.378 150.832 175.534 156.228 176.401 158.83C177.268 161.431 177.943 165.96 178.039 166.923C178.136 167.887 177.847 169.525 177.847 170.488C177.847 171.452 177.75 172.897 178.039 173.668C178.328 174.439 178.039 176.848 178.039 177.715C178.039 178.582 178.521 178.775 178.714 179.738C178.906 180.702 178.521 180.895 178.521 181.473C178.521 182.051 178.617 181.954 178.714 182.436C178.81 182.918 179.195 183.785 180.448 183.978C181.394 184.123 181.556 183.536 181.517 183.173C181.568 183.262 181.781 183.414 182.375 183.592C183.146 183.824 183.339 183.368 183.339 183.111C183.371 183.175 183.57 183.361 184.109 183.592C184.649 183.824 184.784 183.368 184.784 183.111C184.816 183.175 184.957 183.303 185.266 183.303C185.574 183.303 185.715 183.046 185.747 182.918C185.747 182.982 185.882 183.149 186.422 183.303C187.096 183.496 187.289 182.147 187.193 181.473C187.096 180.798 186.133 178.678 185.651 178.004C185.169 177.329 184.302 173.668 184.206 173.186C184.109 172.705 184.399 170.97 183.917 167.694C183.435 164.418 184.495 160.179 184.399 152.181C184.302 144.184 182.279 133.103 181.99 131.465Z"
                  fill="white"
                />
                <path
                  d="M181.893 127.322C181.829 128.157 181.758 130.155 181.99 131.465C182.279 133.103 184.302 144.184 184.399 152.181C184.495 160.179 183.435 164.418 183.917 167.694C184.399 170.97 184.109 172.705 184.206 173.186C184.302 173.668 185.169 177.329 185.651 178.004C186.133 178.678 187.096 180.798 187.193 181.473C187.289 182.147 187.096 183.496 186.422 183.303C185.882 183.149 185.747 182.982 185.747 182.918C185.715 183.046 185.574 183.303 185.266 183.303C184.957 183.303 184.816 183.175 184.784 183.111C184.784 183.368 184.649 183.824 184.109 183.592C183.57 183.361 183.371 183.175 183.339 183.111C183.339 183.368 183.146 183.824 182.375 183.592C181.604 183.361 181.476 183.175 181.508 183.111C181.572 183.464 181.45 184.132 180.448 183.978C179.195 183.785 178.81 182.918 178.714 182.436C178.617 181.954 178.521 182.051 178.521 181.473C178.521 180.895 178.906 180.702 178.714 179.738C178.521 178.775 178.039 178.582 178.039 177.715C178.039 176.848 178.328 174.439 178.039 173.668C177.75 172.897 177.847 171.452 177.847 170.488C177.847 169.525 178.136 167.887 178.039 166.923C177.943 165.96 177.268 161.431 176.401 158.83C175.534 156.228 174.378 150.832 174.185 148.134C174.031 145.976 173.8 144.216 173.703 143.606C173.061 141.454 171.661 136.726 171.198 135.03C170.62 132.911 170.427 129.827 170.427 129.249C170.427 128.787 170.427 127.772 170.427 127.322L178.521 125.684L181.893 127.322Z"
                  stroke="black"
                  stroke-width="0.192707"
                />
              </g>
              <path
                id="Vector 49"
                d="M181.7 128.382C181.797 127.707 181.818 127.287 181.893 126.648L180.062 125.106L177.075 123.853L170.331 125.588L170.523 127.611L181.7 128.382Z"
                fill="white"
              />
              <g id="Group 223">
                <path
                  id="Vector 12_2"
                  d="M151.69 132.685C151.69 132.685 152.097 133.402 152.483 133.892C152.82 134.32 153.22 134.778 153.22 134.778"
                  stroke="black"
                  stroke-width="0.171013"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  id="Vector 41"
                  d="M158.209 133.348C158.209 133.348 157.666 133.969 157.189 134.371C156.773 134.722 156.288 135.09 156.288 135.09"
                  stroke="black"
                  stroke-width="0.171013"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </g>
              <g id="Group 224">
                <path
                  id="Vector 12_3"
                  d="M179.932 132.685C179.932 132.685 179.526 133.402 179.14 133.892C178.803 134.32 178.402 134.778 178.402 134.778"
                  stroke="black"
                  stroke-width="0.171013"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  id="Vector 41_2"
                  d="M173.414 133.348C173.414 133.348 173.957 133.969 174.433 134.371C174.85 134.722 175.335 135.09 175.335 135.09"
                  stroke="black"
                  stroke-width="0.171013"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </g>
            </g>
            {findMeasure('height') && (
              <g id="height">
                <path
                  id="Vector 19"
                  d="M189.465 172.03L189.465 9.48194"
                  stroke="#E55959"
                  stroke-width="0.963533"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 30"
                  d="M142.734 9.00006L189.787 9.00005"
                  stroke="#E55959"
                  stroke-width="0.385413"
                  stroke-linecap="square"
                  stroke-dasharray="1.93 1.93"
                />
                <path
                  id="Vector 28"
                  d="M191.11 171.079L189.455 172.702L188.02 171.03"
                  stroke="#E55959"
                  stroke-width="0.770827"
                />
                <path
                  id="Vector 31"
                  d="M142.734 174.92L193.223 174.92"
                  stroke="#E55959"
                  stroke-width="0.385413"
                  stroke-linecap="square"
                  stroke-dasharray="1.93 1.93"
                />
              </g>
            )}
            {findMeasure('fullbody_length') && (
              <g id="fullbody_length">
                <path id="Vector 28_2" d="M186.31 148.204L184.661 149.823L183.229 148.156" stroke="#E55959" />
                <path id="Vector 19_2" d="M184.699 149.4L184.699 37.9999" stroke="#E55959" stroke-linecap="square" />
                <path
                  id="Vector 29"
                  d="M146 37.102L185 37.5"
                  stroke="#E55959"
                  stroke-width="0.5"
                  stroke-linecap="square"
                  stroke-dasharray="2.04 2.04"
                />
              </g>
            )}
          </g>
          <defs xmlns="http://www.w3.org/2000/svg">
            <linearGradient
              id="paint0_linear_128_2185"
              x1="163.919"
              y1="30.0457"
              x2="163.919"
              y2="41.7837"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_2185"
              x1="137.508"
              y1="61.0813"
              x2="140.175"
              y2="112.349"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_2185"
              x1="155.348"
              y1="110.995"
              x2="151.009"
              y2="136.143"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className=" h-full m-auto "
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_fullbody">
            <g id="child">
              <g id="Group 38">
                <g id="Group 37">
                  <path
                    id="Vector"
                    d="M162.297 54.0264C163.417 50.6665 163.519 45.932 163.519 45.932H164.13H177.57H178.18C178.18 45.932 178.282 50.6665 179.402 54.0264C180.217 56.47 183.526 56.9282 188.718 58.6082C193.794 60.2503 197.118 59.6773 199.257 65.4808C201.395 71.2844 207.351 89.4587 208.267 93.4295C209 96.6062 209.998 101.168 210.405 103.051C211.22 104.375 213.216 109.557 214.682 119.698C216.148 129.839 216.413 142.047 216.362 146.883C216.616 149.836 216.662 156.812 216.662 156.812L216.362 159.627C216.362 159.627 215.598 162.125 213.765 164.447C211.475 167.349 207.656 171.167 207.198 170.098C206.832 169.242 207.351 168.112 207.656 167.654C207.147 168.112 206.099 168.845 205.976 168.112C205.824 167.196 204.907 167.349 207.198 165.21C209.489 163.072 210.405 161.24 210.405 158.796C210.405 156.841 208.573 153.603 207.656 152.229C207.147 152.534 205.824 153.542 204.602 155.131C203.075 157.116 202.464 158.949 201.547 158.032C200.631 157.116 200.631 156.505 202.311 153.756C203.991 151.007 206.282 146.12 207.656 145.203C208.756 144.47 209.438 143.982 209.642 143.829C209.387 141.894 208.756 137.475 208.267 135.276C207.656 132.527 205.213 128.862 203.533 122.906C202.189 118.14 200.733 109.008 200.173 105.037C198.289 100.048 194.461 89.7641 194.217 88.5423C193.972 87.3205 192.791 82.637 192.231 80.4479L191.315 82.7388V108.855C191.62 110.637 192.14 115.239 191.773 119.393C191.406 123.547 191.62 125.298 191.773 125.655C192.486 128.353 193.911 134.574 193.911 137.873V146.272C194.98 150.192 197.118 159.04 197.118 163.072C197.118 168.112 196.966 177.734 196.66 182.01C196.416 185.431 195.846 189.545 195.591 191.174H178.028C176.959 183.945 174.729 168.631 174.362 165.21C173.904 160.934 174.057 158.949 173.599 158.032C173.141 157.116 172.377 156.2 171.461 156.2H170.239C169.322 156.2 168.559 157.116 168.101 158.032C167.642 158.949 167.795 160.934 167.337 165.21C166.97 168.631 164.741 183.945 163.672 191.174H146.108C145.854 189.545 145.283 185.431 145.039 182.01C144.734 177.734 144.581 168.112 144.581 163.072C144.581 159.04 146.719 150.192 147.788 146.272V137.873C147.788 134.574 149.214 128.353 149.926 125.655C150.079 125.298 150.293 123.547 149.926 119.393C149.56 115.239 150.079 110.637 150.384 108.855V82.7388L149.468 80.4479C148.908 82.637 147.727 87.3205 147.483 88.5423C147.238 89.7641 143.41 100.048 141.526 105.037C140.966 109.008 139.51 118.14 138.166 122.906C136.486 128.862 134.043 132.527 133.432 135.276C132.943 137.475 132.312 141.894 132.057 143.829C132.261 143.982 132.943 144.47 134.043 145.203C135.417 146.12 137.708 151.007 139.388 153.756C141.068 156.505 141.068 157.116 140.152 158.032C139.236 158.949 138.625 157.116 137.097 155.131C135.876 153.542 134.552 152.534 134.043 152.229C133.127 153.603 131.294 156.841 131.294 158.796C131.294 161.24 132.21 163.072 134.501 165.21C136.792 167.349 135.876 167.196 135.723 168.112C135.601 168.845 134.552 168.112 134.043 167.654C134.348 168.112 134.868 169.242 134.501 170.098C134.043 171.167 130.225 167.349 127.934 164.447C126.101 162.125 125.204 159.61 125.458 159.101C125.357 158.592 124.968 158.49 125.153 157.116C125.32 155.869 124.898 149.836 125.153 146.883C125.102 142.047 125.551 129.839 127.018 119.698C128.484 109.557 130.479 104.375 131.294 103.051C131.701 101.168 132.699 96.6062 133.432 93.4295C134.348 89.4587 140.305 71.2844 142.443 65.4808C144.581 59.6773 147.905 60.2503 152.981 58.6082C158.173 56.9282 161.482 56.47 162.297 54.0264Z"
                    fill="white"
                  />
                  <g id="Group 36">
                    <g id="Group 35">
                      <path
                        id="Vector 21"
                        d="M172.039 53.2627C176.505 51.3688 178.759 47.3064 179.523 46.0846V45.0155C177.69 43.7937 173.964 41.3501 173.719 41.3501C173.475 41.3501 165.778 43.4883 161.96 44.5573L163.428 48.9656L162.66 52.0399C165.714 54.0253 169.205 54.4649 172.039 53.2627Z"
                        fill="url(#paint0_linear_128_2234)"
                      />
                      <g id="Group 34">
                        <path
                          id="Vector 20"
                          d="M137.829 106.106C139.024 106.562 141.648 105.286 141.952 104.12L141.189 107.175C141.284 108.502 140.578 110.993 140.578 110.993C140.578 110.993 139.857 114.973 139.662 116.491L137.371 125.196C136.86 126.77 134.487 132.093 134.622 132.069C134.622 132.069 133.469 134.782 133.247 135.887L132.49 140.774L132.025 142.913C132.24 143.338 131.987 143.46 132.025 143.676C132.073 143.946 134.737 145.814 134.737 145.814C134.612 145.994 136.302 148.411 136.302 148.411L137.982 151.312L140.425 155.741L140.883 157.116L139.509 158.185L138.745 157.421L136.607 154.52L134.165 152.229C134.165 152.229 132.28 155.604 132.025 155.741L131.109 158.796L131.567 160.874L133.221 163.989L135.984 167.043L135.746 168.265L135.19 168.265L134.165 167.654C134.203 167.87 134.65 168.992 134.737 169.487L134.434 170.403L133.221 169.624L131.109 167.959L129.032 165.638L127.291 163.378L126.222 161.24L125.764 160.278L125.305 158.032L125 154.825L125.305 151.618L125.305 141.538L126.222 126.113L127.291 118.477C127.457 115.481 129.429 107.938 129.429 107.938C129.429 107.938 130.345 105.037 131.414 102.593C131.502 101.512 132.252 97.2612 132.331 97.7059L133.247 94.3459C133.705 98.775 133.4 98.6222 133.858 101.161C134.349 103.879 136.335 105.536 137.829 106.106Z"
                          fill="url(#paint1_linear_128_2234)"
                        />
                        <path
                          id="Vector 18"
                          d="M163.945 152.687C166.02 154.987 167.916 158.49 167.916 158.49L167.305 166.279L165.778 177.123L164.25 187.203L163.792 191.021L146.224 191.025L145.118 183.1L144.854 176.054L144.702 171.472V164.599V160.476L147.909 146.272V136.193L161.196 149.785C161.196 149.785 160.883 149.292 163.945 152.687Z"
                          fill="url(#paint2_linear_128_2234)"
                        />
                        <path
                          id="Vector 47"
                          d="M145.928 197.246C146.284 195.232 146.175 192.161 146.076 190.877L151.26 188.36L163.701 190.877V193.84C163.701 194.728 163.405 199.468 162.516 202.726C161.805 205.333 159.653 212.6 158.665 215.908C158.517 216.846 158.162 219.551 157.925 222.869C157.629 227.016 155.851 235.31 154.518 239.309C153.185 243.308 152.149 250.269 152.001 251.75C151.852 253.231 152.297 255.749 152.297 257.23C152.297 258.711 152.445 260.933 152.001 262.118C151.556 263.303 152.001 267.005 152.001 268.338C152.001 269.671 151.26 269.967 150.964 271.448C150.668 272.93 151.26 273.226 151.26 274.114C151.26 275.003 151.112 274.855 150.964 275.596C150.816 276.336 150.223 277.669 148.298 277.965C146.844 278.189 146.595 277.287 146.655 276.728C146.576 276.866 146.25 277.099 145.336 277.373C144.151 277.728 143.855 277.027 143.855 276.632C143.805 276.731 143.499 277.017 142.67 277.373C141.84 277.728 141.633 277.027 141.633 276.632C141.584 276.731 141.366 276.929 140.892 276.929C140.419 276.929 140.201 276.534 140.152 276.336C140.152 276.435 139.945 276.692 139.115 276.929C138.078 277.225 137.782 275.151 137.93 274.114C138.078 273.078 139.559 269.819 140.3 268.783C141.041 267.746 142.374 262.118 142.522 261.377C142.67 260.637 142.225 257.971 142.966 252.935C143.707 247.899 142.077 241.382 142.225 229.089C142.374 216.796 145.484 199.764 145.928 197.246Z"
                          fill="#F5F3F3"
                          stroke="black"
                          stroke-width="0.296217"
                        />
                      </g>
                    </g>
                    <g id="Group 33">
                      <path
                        id="Vector 11"
                        d="M171.214 125.427C171.257 125.355 171.234 125.261 171.161 125.218C171.089 125.175 170.995 125.198 170.952 125.271L171.214 125.427ZM170.952 125.271C170.912 125.338 170.898 125.415 170.897 125.486C170.895 125.558 170.906 125.634 170.922 125.71C170.956 125.862 171.019 126.031 171.089 126.195C171.159 126.359 171.239 126.524 171.31 126.666C171.382 126.811 171.441 126.927 171.474 127.003L171.754 126.882C171.717 126.796 171.652 126.668 171.583 126.53C171.513 126.389 171.436 126.231 171.37 126.075C171.302 125.917 171.248 125.768 171.221 125.644C171.207 125.582 171.201 125.532 171.202 125.492C171.203 125.452 171.21 125.434 171.214 125.427L170.952 125.271ZM171.474 127.003C171.535 127.143 171.577 127.354 171.581 127.577C171.585 127.8 171.551 128.017 171.476 128.173L171.752 128.305C171.854 128.09 171.891 127.822 171.886 127.571C171.882 127.32 171.835 127.068 171.754 126.882L171.474 127.003ZM171.476 128.173C171.455 128.218 171.426 128.237 171.384 128.246C171.334 128.256 171.266 128.25 171.188 128.23C171.112 128.21 171.039 128.18 170.983 128.153C170.955 128.14 170.933 128.128 170.918 128.12C170.91 128.116 170.904 128.112 170.901 128.11C170.899 128.109 170.897 128.109 170.897 128.108C170.896 128.108 170.896 128.108 170.896 128.108C170.896 128.108 170.896 128.108 170.896 128.108C170.896 128.108 170.896 128.108 170.896 128.108C170.896 128.108 170.896 128.108 170.896 128.108C170.896 128.108 170.896 128.108 170.818 128.239C170.739 128.37 170.739 128.37 170.739 128.37C170.739 128.37 170.739 128.37 170.739 128.37C170.739 128.37 170.74 128.37 170.74 128.37C170.74 128.37 170.74 128.37 170.74 128.371C170.741 128.371 170.741 128.371 170.742 128.372C170.744 128.373 170.746 128.374 170.749 128.375C170.754 128.379 170.762 128.383 170.772 128.388C170.791 128.399 170.818 128.413 170.852 128.429C170.917 128.46 171.01 128.499 171.111 128.526C171.21 128.551 171.33 128.569 171.446 128.545C171.57 128.519 171.685 128.445 171.752 128.305L171.476 128.173Z"
                        fill="black"
                      />
                      <path
                        id="Vector 17"
                        d="M169.997 83.197C169.997 83.197 169.842 84.175 169.84 84.9268C169.837 85.5841 169.872 86.3176 169.872 86.3176"
                        stroke="black"
                        stroke-width="0.30545"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        id="Vector 12"
                        d="M160.432 148.563C160.432 148.563 161.44 149.949 162.357 150.872C163.158 151.679 164.098 152.534 164.098 152.534"
                        stroke="black"
                        stroke-width="0.30545"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <g id="Group 32">
                        <path
                          id="Vector 15"
                          d="M136.607 102.746C136.607 102.746 136.76 104.12 137.218 104.731C137.676 105.342 138.287 105.495 138.287 105.495"
                          stroke="black"
                          stroke-width="0.30545"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          id="Vector 16"
                          d="M205.792 102.746C205.792 102.746 205.639 104.12 205.181 104.731C204.723 105.342 204.112 105.495 204.112 105.495"
                          stroke="black"
                          stroke-width="0.30545"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </g>
                      <g id="Group 27">
                        <path
                          id="Vector 15_2"
                          d="M169.29 63.6481C169.29 63.6481 168.964 62.7205 168.068 62.1774C167.172 61.6344 164.301 61.6118 162.876 60.5936"
                          stroke="black"
                          stroke-width="0.30545"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          id="Vector 14"
                          d="M171.734 63.6481C171.734 63.6481 172.06 62.7205 172.956 62.1774C173.852 61.6344 176.723 61.6118 178.148 60.5936"
                          stroke="black"
                          stroke-width="0.30545"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </g>
                    </g>
                  </g>
                </g>
              </g>
              <path
                id="Vector_2"
                d="M171.46 156.2H170.239C169.322 156.2 168.559 157.116 168.101 158.032C167.642 158.949 167.795 160.934 167.337 165.21C166.97 168.631 164.741 183.945 163.671 191.174H146.108C145.854 189.545 145.283 185.431 145.039 182.01C144.734 177.734 144.581 168.112 144.581 163.072C144.581 159.04 146.719 150.192 147.788 146.272V137.873C147.788 134.574 149.213 128.353 149.926 125.655C150.079 125.298 150.293 123.547 149.926 119.393C149.56 115.239 150.079 110.637 150.384 108.855V82.7388L149.468 80.4479C148.908 82.637 147.727 87.3205 147.483 88.5423C147.238 89.7641 143.41 100.048 141.526 105.037C140.966 109.008 139.51 118.14 138.166 122.906C136.486 128.862 134.043 132.527 133.432 135.276C132.943 137.475 132.312 141.894 132.057 143.829C132.261 143.982 132.943 144.47 134.043 145.203C135.417 146.12 137.708 151.007 139.388 153.756C141.068 156.505 141.068 157.116 140.152 158.032C139.235 158.949 138.625 157.116 137.097 155.131C135.875 153.542 134.552 152.534 134.043 152.229C133.126 153.603 131.294 156.841 131.294 158.796C131.294 161.24 132.21 163.072 134.501 165.21C136.792 167.349 135.875 167.196 135.723 168.112C135.601 168.845 134.552 168.112 134.043 167.654C134.348 168.112 134.868 169.242 134.501 170.098C134.043 171.167 130.225 167.349 127.934 164.447C126.101 162.125 125.611 160.018 125.611 160.018C125.611 160.018 125.274 158.796 125.153 157.116C125.063 155.861 125.083 149.836 125.337 146.883C125.287 142.047 125.551 129.839 127.017 119.698C128.484 109.557 130.479 104.375 131.294 103.051C131.701 101.168 132.699 96.6062 133.432 93.4295C134.348 89.4587 140.305 71.2844 142.443 65.4808C144.581 59.6773 147.905 60.2503 152.981 58.6082C158.173 56.9282 161.482 56.47 162.297 54.0264C163.417 50.6665 163.519 45.932 163.519 45.932H177.569M171.46 156.2C172.377 156.2 173.14 157.116 173.599 158.032C174.057 158.949 173.904 160.934 174.362 165.21C174.729 168.631 176.85 183.648 177.92 190.877L195.544 191.026C195.799 189.396 196.416 185.431 196.66 182.01C196.966 177.734 197.118 168.112 197.118 163.072C197.118 159.04 194.98 150.192 193.911 146.272V137.873C193.911 134.574 192.486 128.353 191.773 125.655C191.62 125.298 191.406 123.547 191.773 119.393C192.139 115.239 191.62 110.637 191.315 108.855V82.7388L192.231 80.4479C192.791 82.637 193.972 87.3205 194.217 88.5423C194.461 89.7641 198.289 100.048 200.173 105.037C200.733 109.008 202.189 118.14 203.533 122.906C205.213 128.862 207.656 132.527 208.267 135.276C208.756 137.475 209.387 141.894 209.642 143.829C209.438 143.982 208.756 144.47 207.656 145.203C206.282 146.12 203.991 151.007 202.311 153.756C200.631 156.505 200.631 157.116 201.547 158.032C202.464 158.949 203.075 157.116 204.602 155.131C205.824 153.542 207.147 152.534 207.656 152.229C208.573 153.603 210.405 156.841 210.405 158.796C210.405 161.24 209.489 163.072 207.198 165.21C204.907 167.349 205.824 167.196 205.976 168.112C206.099 168.845 207.147 168.112 207.656 167.654C207.351 168.112 206.832 169.242 207.198 170.098C207.656 171.167 211.474 167.349 213.765 164.447C215.598 162.125 216.209 160.018 216.362 159.254C216.941 157.269 216.616 149.836 216.362 146.883C216.413 142.047 216.148 129.839 214.682 119.698C213.216 109.557 211.22 104.375 210.405 103.051C209.998 101.168 209 96.6062 208.267 93.4295C207.351 89.4587 201.395 71.2844 199.256 65.4808C197.118 59.6773 193.794 60.2503 188.718 58.6082C183.526 56.9282 180.217 56.47 179.402 54.0264C178.282 50.6665 178.18 45.932 178.18 45.932H164.13M171.46 156.2H170.086"
                stroke="black"
                stroke-width="0.30545"
                stroke-linecap="round"
              />
              <g id="Group 31">
                <g id="Group 2">
                  <path
                    id="Vector 4"
                    d="M154.477 29.4833C155.047 27.4945 156.14 29.0446 156.615 30.0682L157.327 32.5543L157.683 37.6726C157.624 37.7213 157.327 38.2575 156.615 37.8188C155.702 37.257 153.765 31.9693 154.477 29.4833Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.30545"
                  />
                  <path
                    id="Vector 5"
                    d="M188.379 29.4833C187.809 27.4945 186.717 29.0446 186.242 30.0682L185.53 32.5543L185.173 37.6726C185.233 37.7213 185.53 38.2575 186.242 37.8188C187.154 37.257 189.092 31.9693 188.379 29.4833Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.30545"
                  />
                </g>
                <g id="Vector_3">
                  <path
                    d="M158.495 16.715C161.452 10.2586 169.886 9.90617 171.123 9.88944C172.36 9.90617 180.794 10.2586 183.752 16.715C186.147 21.9447 187.518 32.9857 183.752 42.5353C181.838 47.3872 178.731 49.5098 176.6 50.5485C174.47 51.5872 171.275 51.7354 171.275 51.7354C171.275 51.7354 167.624 51.7354 165.646 50.5485C163.668 49.3616 160.408 47.3872 158.495 42.5353C154.729 32.9857 156.099 21.9447 158.495 16.715Z"
                    fill="white"
                  />
                  <path
                    d="M171.275 9.88892C171.275 9.88892 161.69 9.7405 158.495 16.715C156.099 21.9447 154.729 32.9857 158.495 42.5353C160.408 47.3872 163.668 49.3616 165.646 50.5485C167.624 51.7354 171.275 51.7354 171.275 51.7354C171.275 51.7354 174.47 51.5872 176.6 50.5485C178.731 49.5098 181.838 47.3872 183.752 42.5353C187.518 32.9857 186.147 21.9447 183.752 16.715C180.556 9.7405 170.971 9.88892 170.971 9.88892"
                    stroke="black"
                    stroke-width="0.30545"
                    stroke-linecap="round"
                  />
                </g>
              </g>
              <path
                id="Vector 48"
                d="M146.372 192.507C146.224 191.47 146.192 190.824 146.076 189.841L148.89 187.471L153.481 185.545L163.849 188.952L163.553 191.322L146.372 192.507Z"
                fill="#F5F3F3"
              />
              <path
                id="Vector 47_2"
                d="M195.693 197.246C195.337 195.232 195.446 192.161 195.545 190.877L190.361 188.36L177.92 190.877V193.84C177.92 194.728 178.216 199.468 179.105 202.726C179.815 205.333 181.968 212.6 182.955 215.908C183.103 216.846 183.459 219.551 183.696 222.869C183.992 227.016 185.769 235.31 187.102 239.309C188.435 243.308 189.472 250.269 189.62 251.75C189.768 253.231 189.324 255.749 189.324 257.23C189.324 258.711 189.176 260.933 189.62 262.118C190.065 263.303 189.62 267.005 189.62 268.338C189.62 269.671 190.361 269.967 190.657 271.448C190.953 272.93 190.361 273.226 190.361 274.114C190.361 275.003 190.509 274.855 190.657 275.596C190.805 276.336 191.398 277.669 193.323 277.965C194.777 278.189 195.026 277.287 194.966 276.728C195.045 276.866 195.371 277.099 196.285 277.373C197.47 277.728 197.766 277.027 197.766 276.632C197.816 276.731 198.122 277.017 198.951 277.373C199.78 277.728 199.988 277.027 199.988 276.632C200.037 276.731 200.254 276.929 200.728 276.929C201.202 276.929 201.42 276.534 201.469 276.336C201.469 276.435 201.676 276.692 202.506 276.929C203.542 277.225 203.839 275.151 203.691 274.114C203.542 273.078 202.061 269.819 201.321 268.783C200.58 267.746 199.247 262.118 199.099 261.377C198.951 260.637 199.395 257.971 198.655 252.935C197.914 247.899 199.544 241.382 199.395 229.089C199.247 216.796 196.137 199.764 195.693 197.246Z"
                fill="white"
                stroke="black"
                stroke-width="0.296217"
              />
              <path
                id="Vector 49"
                d="M195.248 192.507C195.396 191.47 195.428 190.824 195.544 189.841L192.73 187.471L188.139 185.545L177.771 188.211L178.067 191.322L195.248 192.507Z"
                fill="white"
              />
              <g id="Group 223">
                <path
                  id="Vector 12_2"
                  d="M149.118 199.121C149.118 199.121 149.743 200.223 150.336 200.976C150.855 201.634 151.47 202.338 151.47 202.338"
                  stroke="black"
                  stroke-width="0.262871"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  id="Vector 41"
                  d="M159.138 200.141C159.138 200.141 158.304 201.095 157.571 201.712C156.93 202.253 156.186 202.818 156.186 202.818"
                  stroke="black"
                  stroke-width="0.262871"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </g>
              <g id="Group 224">
                <path
                  id="Vector 12_3"
                  d="M192.53 199.121C192.53 199.121 191.906 200.223 191.312 200.976C190.794 201.634 190.178 202.338 190.178 202.338"
                  stroke="black"
                  stroke-width="0.262871"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  id="Vector 41_2"
                  d="M182.511 200.141C182.511 200.141 183.345 201.095 184.078 201.712C184.718 202.253 185.463 202.818 185.463 202.818"
                  stroke="black"
                  stroke-width="0.262871"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </g>
            </g>
            {findMeasure('height') && (
              <g id="height">
                <path
                  id="Vector 19"
                  d="M207.183 259.6L207.183 9.74079"
                  stroke="#E55959"
                  stroke-width="1.48108"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 30"
                  d="M135.351 9.00006L207.679 9.00005"
                  stroke="#E55959"
                  stroke-width="0.592434"
                  stroke-linecap="square"
                  stroke-dasharray="2.96 2.96"
                />
                <path
                  id="Vector 28"
                  d="M209.711 258.137L207.168 260.633L204.962 258.063"
                  stroke="#E55959"
                  stroke-width="1.18487"
                />
                <path
                  id="Vector 31"
                  d="M135.351 264.043L212.959 264.043"
                  stroke="#E55959"
                  stroke-width="0.592434"
                  stroke-linecap="square"
                  stroke-dasharray="2.96 2.96"
                />
              </g>
            )}
            {findMeasure('fullbody_length') && (
              <g id="fullbody_length">
                <path
                  id="Vector 28_2"
                  d="M202.334 222.976L199.799 225.465L197.598 222.902"
                  stroke="#E55959"
                  stroke-width="1.53714"
                />
                <path
                  id="Vector 19_2"
                  d="M199.858 224.815L199.858 53.5769"
                  stroke="#E55959"
                  stroke-width="1.53714"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 29"
                  d="M140.372 52.1967L200.32 52.8085"
                  stroke="#E55959"
                  stroke-width="0.76857"
                  stroke-linecap="square"
                  stroke-dasharray="3.14 3.14"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_2234"
              x1="167.916"
              y1="41.3501"
              x2="167.916"
              y2="59.3931"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_2234"
              x1="127.318"
              y1="89.0562"
              x2="131.417"
              y2="167.862"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_2234"
              x1="154.741"
              y1="165.78"
              x2="148.072"
              y2="204.436"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
