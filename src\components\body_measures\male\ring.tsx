import { useDevice } from '@/hooks/use-device';
import { BodyMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface RingProps {
  measure: BodyMeasure;
  className?: string;
}

export function Ring({ measure, className }: RingProps) {
  const { measures } = measure;
  const mappedMeasures = measures.map((item) => item.measure);
  const { isMobile } = useDevice();

  const findMeasure = (measure: string) => {
    const foundMeasure = mappedMeasures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full flex justify-center', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_ring">
            <mask id="mask0_128_2086" maskUnits="userSpaceOnUse" x="0" y="0" width="331" height="193">
              <rect id="rect" width="331" height="193" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_2086)">
              <g id="group_ring_mask">
                <g id="ring">
                  <g id="Mask group">
                    <g id="Group 260">
                      <g id="Vector">
                        <path
                          d="M165.977 128.574C165.977 155.969 165.977 156.104 165.977 156.104C132.526 156.104 105.409 132.692 105.409 105.296C105.409 100.912 106.104 96.6599 107.408 92.6107C114.252 113.311 137.88 128.574 165.977 128.574Z"
                          fill="#353631"
                        />
                        <path
                          d="M165.977 156.104C165.977 156.104 157.819 146.641 152.728 146.641C127.735 146.641 107.474 122.528 107.474 92.7827C107.474 71.6781 117.674 53.4086 132.527 44.5757C115.3 52.1074 103 71.9265 103 95.1909C103 124.936 123.107 149.049 147.91 149.049C154.847 149.049 165.977 156.104 165.977 156.104Z"
                          fill="#353631"
                        />
                      </g>
                    </g>
                  </g>
                  <g id="Mask group_2">
                    <g id="Group 260_2">
                      <g id="Vector_2">
                        <path
                          d="M165.978 128.574C165.978 155.969 165.978 156.104 165.978 156.104C199.429 156.104 226.546 132.692 226.546 105.296C226.546 100.912 225.851 96.6599 224.547 92.6107C217.703 113.311 194.075 128.574 165.978 128.574Z"
                          fill="#353631"
                        />
                        <path
                          d="M165.978 156.104C165.978 156.104 174.136 146.641 179.227 146.641C204.22 146.641 224.481 122.528 224.481 92.7827C224.481 71.6781 214.281 53.4086 199.428 44.5757C216.655 52.1074 228.955 71.9265 228.955 95.1909C228.955 124.936 208.848 149.049 184.045 149.049C177.109 149.049 165.978 156.104 165.978 156.104Z"
                          fill="#353631"
                        />
                      </g>
                      <path
                        id="Union"
                        d="M225.686 83.659V83.6633C225.686 86.6518 225.17 89.2825 224.477 92.0895C218.629 70.5335 194.742 54.7556 165.977 54.7556C137.213 54.7556 113.326 70.5335 107.478 92.0895C106.785 89.2825 106.27 86.6518 106.27 83.6633L106.27 83.659C105.927 69.9572 112.596 58.0904 123.483 49.6434C134.372 41.1952 149.476 36.1721 165.977 36.1721C182.479 36.1721 197.583 41.1952 208.472 49.6434C219.359 58.0904 226.028 69.9572 225.686 83.659Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.344139"
                      />
                    </g>
                  </g>
                </g>
                {findMeasure('finger_ring_diameter') && (
                  <g id="finger_ring_diameter">
                    <g id="Group 262">
                      <path
                        id="Vector 19"
                        d="M119.811 87.1333L164.773 87.1333"
                        stroke="#E55959"
                        stroke-width="6.09778"
                        stroke-linecap="square"
                      />
                      <path
                        id="Vector 28"
                        d="M126.442 97.5385L116.165 87.0701L126.748 77.9849"
                        stroke="#E55959"
                        stroke-width="4.87823"
                      />
                    </g>
                    <g id="Group 263">
                      <path
                        id="Vector 19_2"
                        d="M211.973 87.1328L167.011 87.1328"
                        stroke="#E55959"
                        stroke-width="6.09778"
                        stroke-linecap="square"
                      />
                      <path
                        id="Vector 28_2"
                        d="M205.34 97.5385L215.616 87.0701L205.033 77.9849"
                        stroke="#E55959"
                        stroke-width="4.87823"
                      />
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
        </svg>
      ) : (
        <svg
          className=" h-full m-auto "
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_ring">
            <mask id="mask0_128_4145" maskUnits="userSpaceOnUse" x="0" y="0" width="342" height="291">
              <rect id="rect" width="342" height="291" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_4145)">
              <g id="group_ring_mask">
                <g id="ring">
                  <g id="Mask group">
                    <g id="Group 260">
                      <g id="Vector">
                        <path
                          d="M171.482 195.823C171.482 237.793 171.482 238 171.482 238C120.235 238 78.6906 202.132 78.6906 160.162C78.6906 153.445 79.7548 146.931 81.7528 140.728C92.2378 172.441 128.437 195.823 171.482 195.823Z"
                          fill="#353631"
                        />
                        <path
                          d="M171.482 238C171.482 238 158.983 223.502 151.184 223.502C112.894 223.502 81.8539 186.561 81.8539 140.991C81.8539 108.659 97.4801 80.6703 120.235 67.1382C93.8436 78.6768 75 109.04 75 144.681C75 190.25 105.804 227.191 143.802 227.191C154.429 227.191 171.482 238 171.482 238Z"
                          fill="#353631"
                        />
                      </g>
                    </g>
                  </g>
                  <g id="Mask group_2">
                    <g id="Group 260_2">
                      <g id="Vector_2">
                        <path
                          d="M171.482 195.823C171.482 237.793 171.482 238 171.482 238C222.729 238 264.273 202.132 264.273 160.162C264.273 153.445 263.209 146.931 261.211 140.728C250.726 172.441 214.527 195.823 171.482 195.823Z"
                          fill="#353631"
                        />
                        <path
                          d="M171.482 238C171.482 238 183.981 223.502 191.78 223.502C230.07 223.502 261.11 186.561 261.11 140.991C261.11 108.659 245.484 80.6703 222.729 67.1382C249.12 78.6768 267.964 109.04 267.964 144.681C267.964 190.25 237.16 227.191 199.161 227.191C188.535 227.191 171.482 238 171.482 238Z"
                          fill="#353631"
                        />
                      </g>
                      <path
                        id="Union"
                        d="M262.955 127.014V127.02C262.955 131.599 262.165 135.629 261.104 139.929C252.145 106.905 215.55 82.7336 171.482 82.7336C127.415 82.7336 90.8207 106.905 81.8614 139.929C80.8 135.629 80.0101 131.599 80.0102 127.02L80.01 127.014C79.4848 106.023 89.7026 87.8425 106.381 74.9017C123.062 61.959 146.201 54.2636 171.482 54.2636C196.763 54.2636 219.903 61.959 236.584 74.9017C253.263 87.8425 263.48 106.023 262.955 127.014Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.527222"
                      />
                    </g>
                  </g>
                </g>
                {findMeasure('finger_ring_diameter') && (
                  <g id="finger_ring_diameter">
                    <g id="Group 262">
                      <path
                        id="Vector 19"
                        d="M100.754 132.336L169.636 132.336"
                        stroke="#E55959"
                        stroke-width="9.34181"
                        stroke-linecap="square"
                      />
                      <path
                        id="Vector 28"
                        d="M110.912 148.277L95.169 132.239L111.382 118.321"
                        stroke="#E55959"
                        stroke-width="7.47345"
                      />
                    </g>
                    <g id="Group 263">
                      <path
                        id="Vector 19_2"
                        d="M241.946 132.335L173.064 132.335"
                        stroke="#E55959"
                        stroke-width="9.34181"
                        stroke-linecap="square"
                      />
                      <path
                        id="Vector 28_2"
                        d="M231.785 148.277L247.528 132.239L231.315 118.321"
                        stroke="#E55959"
                        stroke-width="7.47345"
                      />
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
        </svg>
      )}
    </div>
  );
}
