import type { Meta, StoryObj } from '@storybook/react';
import { Image } from './image';
import placeholder from '@/assets/rick.jpeg';

const meta = {
  title: 'Atoms/Image',
  component: Image,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    src: { control: 'text' },
    alt: { control: 'text' },
    className: { control: 'text' },
  },
} satisfies Meta<typeof Image>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    src: placeholder,
    alt: 'Placeholder image',
  },
};

export const WithCustomSize: Story = {
  args: {
    src: placeholder,
    alt: 'Custom size image',
    className: 'w-64 h-64 object-cover rounded-lg',
  },
};
