import {
  Body,
  Bra,
  Cropped,
  Jacket,
  <PERSON><PERSON><PERSON>,
  <PERSON>O<PERSON>alls,
  LongSkirt,
  LongSleeveShirt,
  Panties,
  Pants,
  Shirt,
  Shorts,
  ShortDress,
  ShortOveralls,
  ShortSkirt,
  SportsBra,
  TankTop,
  Top,
  Underpants,
  Vest,
  Insole,
  MidShaftBoot,
  Hat,
  Cap,
  Gloves,
  Tshirt,
  <PERSON>ie,
  <PERSON>Hell,
} from '@/components/garment';

interface Props {
  template: string;
}

function useGarment({ template }: Props) {
  const dict = {
    bra: Bra,
    body: Body,
    cropped: Cropped,
    jacket: Jacket,
    long_dress: LongDress,
    long_overalls: LongOveralls,
    long_skirt: LongSkirt,
    long_sleeve_shirt: LongSleeveShirt,
    panties: Panties,
    pants: Pants,
    short_dress: ShortDress,
    short_overalls: ShortOveralls,
    short_skirt: ShortSkirt,
    shorts: Shorts,
    shirt: Shirt,
    sports_bra: SportsBra,
    tank_top: TankTop,
    top: Top,
    underpants: Underpants,
    vest: Vest,
    insole: Insole,
    mid_shaft_boot: MidShaftBoot,
    hat: Hat,
    cap: Cap,
    gloves: Gloves,
    tshirt: Tshirt,
    beanie: <PERSON>ie,
    high_heel: <PERSON><PERSON>ell,
  };

  type TemplateKey = keyof typeof dict;

  if (template in dict) {
    return dict[template as TemplateKey];
  }
  return null;
}

export default useGarment;
