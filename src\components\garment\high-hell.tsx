import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface HighHellProps {
  measure: GarmentMeasure;
  className?: string;
}

export function HighHell({ measure, className }: HighHellProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className="h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_high_heel">
            <g id="high_heel">
              <path
                id="Vector 337"
                d="M82.2728 49.9093C85.9306 40.7648 91.1633 30.0963 101.07 24C114.278 32.3824 136.682 65.4548 141.966 74.8025C148.57 86.4871 165.335 111.126 175.749 119.509C184.081 126.215 196.578 124.843 202.421 124.843C208.263 124.843 251.953 147.196 251.953 147.196C255.001 149.99 256.017 154.308 254.747 155.833C253.358 157.5 190.228 171.835 166.859 169.803C143.49 167.771 146.284 155.07 133.329 130.685C122.966 111.177 92.0947 84.455 77.9546 73.5324C77.87 69.4682 78.6151 59.0537 82.2728 49.9093Z"
                fill="white"
                stroke="black"
                stroke-width="0.508025"
              />
              <path
                id="Vector 341"
                d="M103.864 26.0324C103.864 26.0324 99.7997 22.4764 100.816 25.5246C101.832 28.5727 115.008 49.305 123.931 64.6425C140.148 92.5174 145.522 121.287 168.891 138.052C172.83 140.877 184.894 141.507 193.53 138.052C202.167 134.598 203.691 128.146 203.691 126.368C203.691 125.789 202.421 124.843 202.421 124.843"
                stroke="black"
                stroke-width="0.508025"
              />
              <path
                id="Vector 342"
                d="M99.5456 25.0156C100.562 28.0638 112.976 49.8121 121.899 65.1496C138.116 93.0246 141.875 121.505 165.244 138.269C169.182 141.095 178.798 144.656 192.514 140.337C206.231 136.019 206.485 125.859 206.485 125.859"
                stroke="black"
                stroke-width="0.508025"
                stroke-dasharray="1.02 1.02"
              />
              <path
                id="Vector 339"
                d="M83.2889 153.8V149.99L87.3531 147.958L90.1472 149.482V153.038L88.3691 154.562L83.2889 153.8Z"
                fill="white"
                stroke="black"
                stroke-width="0.508025"
              />
              <path
                id="Vector 338"
                d="M80.7488 86.9946C78.9707 76.0721 77.1926 74.0398 77.1926 72.0078C77.1926 69.9758 77.7006 69.7217 77.7006 69.7217C79.2247 71.9231 81.9191 75.5522 84.559 78.1041C92.1793 85.4705 96.2596 89.0724 98.2917 90.8505C99.9174 92.273 106.658 97.9171 109.96 101.981C109.96 101.981 110.265 102.083 108.436 101.473C106.15 100.711 97.5136 92.3289 94.7195 91.0588C91.9454 89.7979 91.6751 93.0437 91.4228 96.0733L91.4173 96.1391C91.1633 99.1873 90.1472 149.736 90.1472 149.736C90.1472 149.736 88.2006 150.628 86.5911 150.498C85.4637 150.406 83.2889 149.736 83.2889 149.736C83.0349 132.463 82.1712 95.7327 80.7488 86.9946Z"
                fill="white"
                stroke="black"
                stroke-width="0.508025"
              />
              <path id="Vector 340" d="M88.3691 150.361V154.563" stroke="black" stroke-width="0.508025" />
            </g>
            {findMeasure('heel_height') && (
              <g id="heel_height">
                <path
                  id="Vector 21"
                  d="M69.5873 152.784L69.5873 72.7705"
                  stroke="#E55959"
                  stroke-width="2.54013"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M73.4889 75.8831L69.509 71.2461L65.3623 75.3159"
                  stroke="#E55959"
                  stroke-width="2.0321"
                />
                <path
                  id="Vector 27"
                  d="M65.3466 149.971L69.7749 154.182L73.4889 149.714"
                  stroke="#E55959"
                  stroke-width="2.0321"
                />
              </g>
            )}
            {findMeasure('platform_height') && (
              <g id="platform_height">
                <path
                  id="Vector 21_2"
                  d="M260.72 157.865L260.72 147.196"
                  stroke="#E55959"
                  stroke-width="2.54013"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M264.622 149.547L260.642 144.91L256.495 148.98"
                  stroke="#E55959"
                  stroke-width="2.0321"
                />
                <path
                  id="Vector 27_2"
                  d="M256.594 154.816L260.757 159.29L264.737 155.058"
                  stroke="#E55959"
                  stroke-width="2.0321"
                />
              </g>
            )}
          </g>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_high_heel">
            <g id="high_heel">
              <path
                id="Vector 337"
                d="M67.6502 86.4753C72.235 75.0134 78.7937 61.6413 91.2108 54C107.767 64.5067 135.848 105.961 142.471 117.677C150.749 132.323 171.762 163.206 184.816 173.713C195.259 182.118 210.924 180.399 218.247 180.399C225.569 180.399 280.332 208.417 280.332 208.417C284.152 211.919 285.426 217.332 283.834 219.242C282.092 221.332 202.964 239.3 173.673 236.753C144.381 234.206 147.883 218.287 131.646 187.722C118.656 163.27 79.9611 129.776 62.2377 116.085C62.1315 110.991 63.0655 97.9372 67.6502 86.4753Z"
                fill="white"
                stroke="black"
                stroke-width="0.636771"
              />
              <path
                id="Vector 341"
                d="M94.7129 56.5472C94.7129 56.5472 89.6188 52.0901 90.8924 55.9107C92.1659 59.7313 108.681 85.7177 119.865 104.942C140.192 139.881 146.928 175.942 176.22 196.955C181.156 200.497 196.278 201.285 207.103 196.955C217.928 192.625 219.838 184.538 219.838 182.31C219.838 181.585 218.247 180.399 218.247 180.399"
                stroke="black"
                stroke-width="0.636771"
              />
              <path
                id="Vector 342"
                d="M89.3004 55.2734C90.5739 59.0941 106.134 86.354 117.318 105.578C137.645 140.518 142.357 176.215 171.648 197.229C176.585 200.77 188.637 205.233 205.829 199.82C223.022 194.408 223.341 181.673 223.341 181.673"
                stroke="black"
                stroke-width="0.636771"
                stroke-dasharray="1.27 1.27"
              />
              <path
                id="Vector 339"
                d="M68.9237 216.695V211.919L74.0179 209.372L77.5201 211.282V215.74L75.2914 217.65L68.9237 216.695Z"
                fill="white"
                stroke="black"
                stroke-width="0.636771"
              />
              <path
                id="Vector 338"
                d="M65.7399 132.959C63.5112 119.268 61.2825 116.721 61.2825 114.174C61.2825 111.627 61.9193 111.309 61.9193 111.309C63.8296 114.068 67.2068 118.617 70.5157 121.815C80.0672 131.049 85.1816 135.563 87.7287 137.792C89.7664 139.575 98.2152 146.649 102.354 151.744C102.354 151.744 102.736 151.871 100.444 151.107C97.5785 150.152 86.7533 139.645 83.2511 138.053C79.774 136.473 79.4352 140.541 79.119 144.338L79.1121 144.421C78.7937 148.241 77.5202 211.6 77.5202 211.6C77.5202 211.6 75.0802 212.719 73.0628 212.555C71.6498 212.441 68.9238 211.6 68.9238 211.6C68.6054 189.95 67.5229 143.911 65.7399 132.959Z"
                fill="white"
                stroke="black"
                stroke-width="0.636771"
              />
              <path id="Vector 340" d="M75.2914 212.385V217.651" stroke="black" stroke-width="0.636771" />
            </g>
            {findMeasure('heel_height') && (
              <g id="heel_height">
                <path
                  id="Vector 21"
                  d="M51.7499 215.421L51.7499 115.13"
                  stroke="#E55959"
                  stroke-width="3.18386"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M56.6402 119.031L51.6517 113.219L46.4541 118.32"
                  stroke="#E55959"
                  stroke-width="2.54708"
                />
                <path
                  id="Vector 27"
                  d="M46.4344 211.895L51.985 217.173L56.6402 211.572"
                  stroke="#E55959"
                  stroke-width="2.54708"
                />
              </g>
            )}
            {findMeasure('platform_height') && (
              <g id="platform_height">
                <path
                  id="Vector 21_2"
                  d="M291.32 221.789L291.32 208.417"
                  stroke="#E55959"
                  stroke-width="3.18386"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M296.211 211.364L291.222 205.552L286.025 210.653"
                  stroke="#E55959"
                  stroke-width="2.54708"
                />
                <path
                  id="Vector 27_2"
                  d="M286.149 217.968L291.366 223.576L296.355 218.271"
                  stroke="#E55959"
                  stroke-width="2.54708"
                />
              </g>
            )}
          </g>
        </svg>
      )}
    </div>
  );
}
