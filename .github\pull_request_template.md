## 📌 Description
<!-- Briefly describe what this PR does -->

## 🛠️ Main Changes
<!-- List the main changes made -->
- [x] Fix for bug X
- [x] Implementation of feature Y
- [x] Refactoring of module Z

## ✅ How to Test
<!-- Explain how to validate the changes made -->
1. Step-by-step guide to test the functionality.
2. Any necessary configurations or dependencies.
3. Expected results.

## 🖼️ Screenshots / Evidence
<!-- Add screenshots, GIFs, or test logs to prove the changes -->

## 📂 Related Tasks
<!-- Link issues, Jira cards, or related tasks -->
Closes #123
Related to #456

## ⚠️ Considerations
<!-- Any additional observations? Impact on other parts of the system? -->

---
### 📌 Checklist
- [ ] The code follows the project standards.
- [ ] Tests have been added to cover the changes.
- [ ] Documentation has been updated (if applicable).