import axios from 'axios';

import getQueryParams from '../lib/get-query-params';

const { tenantId, sid } = getQueryParams<{
  sid: string;
  tenantId: string;
}>();

const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  withCredentials: true,
});

api.interceptors.request.use((config) => {
  config.params = config.params || {};
  config.params.sid = sid;

  config.headers = {
    ...config.headers,
    tenant_id: tenantId,
  } as never;

  if (!config.url?.includes(import.meta.env.VITE_API_URL as string)) {
    config.withCredentials = false;
  }

  return config;
});

export default api;
