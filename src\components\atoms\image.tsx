import type React from 'react';
import { cn } from '@/lib/utils';

interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  /** The source URL of the image */
  src: string;
  /** The alt text for the image */
  alt: string;
}

export const Image: React.FC<ImageProps> = ({ src, alt, className, ...props }) => {
  return <img src={src} alt={alt} className={cn(className)} {...props} />;
};
