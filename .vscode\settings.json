{"editor.defaultFormatter": "dbaeumer.vscode-eslint", "editor.codeActionsOnSave": {"source.addMissingImports.ts": "explicit", "source.fixAll.eslint": "always"}, "[shellscript]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[dotenv]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "liveServer.settings.port": 5501, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}}