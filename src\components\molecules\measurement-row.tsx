import { Container } from '../atoms/container';
import { Text } from '../atoms/text';
import { cn } from '@/lib/utils';

export interface MeasurementRowProps {
  label: string;
  value: string;
  className?: string;
}

export function MeasurementRow({ label, value, className }: MeasurementRowProps) {
  return (
    <Container className={cn('flex flex-row justify-between items-center gap-4 w-full', className)}>
      <Text variant="label">{label}</Text>
      <Text variant="label" className="text-nowrap">
        {value}
      </Text>
    </Container>
  );
}
