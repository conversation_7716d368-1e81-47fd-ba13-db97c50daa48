import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface TankTopProps {
  measure: GarmentMeasure;
  className?: string;
}

export function TankTop({ measure, className }: TankTopProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_tank_top">
            <g id="tank_top">
              <path
                id="Vector 71"
                d="M165.527 44.7778C153.855 45.1262 143.584 30.2239 143.584 30.2239L140.673 34.702C140.972 37.165 141.569 42.3596 141.569 43.4343C141.569 44.7778 158.586 69.6313 159.705 70.9747C160.601 72.0495 178.887 67.9893 187.918 65.8249L188.589 30C188.589 30 180.529 44.33 165.527 44.7778Z"
                fill="white"
                stroke="black"
                stroke-width="0.447811"
              />
              <path
                id="Vector 70"
                d="M143.36 30L134.852 34.0303C135.3 40.0011 136.419 52.3906 134.852 60.6751C132.014 75.6768 128.135 75.6768 128.135 75.6768L125 163H167.318H207.621L203.591 75.6768C203.591 75.6768 198.496 69.9878 197.098 61.7946C195.53 52.6145 196.351 41.5685 197.769 34.0303L188.589 30.2239C188.589 30.2239 187.694 59.5556 167.542 60.6751C147.153 61.8078 143.36 30 143.36 30Z"
                fill="white"
                stroke="black"
                stroke-width="0.447811"
              />
              <path
                id="Vector 16"
                d="M170.756 126.012C161.789 117.671 142.33 89.512 141.115 87.8107C142.34 92.3105 145.329 109.199 147.483 111.198C148.229 111.891 158.499 124.731 170.333 131.946C179.801 137.717 192.646 139.817 195.679 138.345C191.798 138.016 178.898 133.585 170.756 126.012Z"
                fill="url(#paint0_linear_207_4944)"
                fill-opacity="0.5"
              />
              <path
                id="Vector 72"
                d="M128.135 77.6919C128.135 77.6919 134.673 75.0051 137.091 58.8839C138.434 49.9276 136.419 33.1347 136.419 33.1347"
                stroke="black"
                stroke-width="0.447811"
                stroke-dasharray="0.9 0.9"
              />
              <path
                id="Vector 73"
                d="M203.716 77.6919C203.716 77.6919 196.202 72.5421 194.76 58.8839C193.861 50.3757 195.431 33.1347 195.431 33.1347"
                stroke="black"
                stroke-width="0.447811"
                stroke-dasharray="0.9 0.9"
              />
              <path
                id="Vector 74"
                d="M142.465 30.6717C142.465 31.5673 146.298 61.6457 166.199 62.4663C187.918 63.3619 190.38 31.1195 190.38 31.1195"
                stroke="black"
                stroke-width="0.447811"
                stroke-dasharray="0.9 0.9"
              />
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19_4"
                  d="M131.269 86.0117L202.024 86.0117"
                  stroke="#E55959"
                  stroke-width="2.23906"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M200.348 90.0117L204.121 86.0117L200.235 82.0117"
                  stroke="#E55959"
                  stroke-width="1.79125"
                />
                <path
                  id="Vector 28_4"
                  d="M133.192 90.0117L129.318 86.0117L133.192 82.0117"
                  stroke="#E55959"
                  stroke-width="1.79125"
                />
              </g>
            )}
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 19_3"
                  d="M129.03 156.507L204.263 156.507"
                  stroke="#E55959"
                  stroke-width="2.23906"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M201.828 160.328L205.601 156.484L201.715 153.148"
                  stroke="#E55959"
                  stroke-width="1.79125"
                />
                <path
                  id="Vector 28_3"
                  d="M130.5 160.328L126.726 156.484L130.612 153.148"
                  stroke="#E55959"
                  stroke-width="1.79125"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_4"
                  d="M165.751 160.537L165.751 47.2408"
                  stroke="#E55959"
                  stroke-width="2.23906"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M169.572 49.6057L165.728 45.8322L162.392 49.7182"
                  stroke="#E55959"
                  stroke-width="1.79125"
                />
                <path
                  id="Vector 28_4"
                  d="M169.572 158.102L165.728 161.876L162.392 157.99"
                  stroke="#E55959"
                  stroke-width="1.79125"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_4"
                  d="M165.751 160.537L165.751 47.2408"
                  stroke="#E55959"
                  stroke-width="2.23906"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M169.572 49.6057L165.728 45.8322L162.392 49.7182"
                  stroke="#E55959"
                  stroke-width="1.79125"
                />
                <path
                  id="Vector 28_4"
                  d="M169.572 158.102L165.728 161.876L162.392 157.99"
                  stroke="#E55959"
                  stroke-width="1.79125"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 19_6"
                  d="M137.763 36.7172L195.306 36.7172"
                  stroke="#EDA7A7"
                  stroke-width="2.23906"
                  stroke-linecap="square"
                  stroke-dasharray="4.48 4.48"
                />
                <path
                  id="Vector 27_6"
                  d="M192.871 40.3146L196.645 36.4707L192.759 33.1347"
                  stroke="#EDA7A7"
                  stroke-width="1.79125"
                />
                <path
                  id="Vector 28_6"
                  d="M140.352 40.3146L136.578 36.4707L140.464 33.1347"
                  stroke="#EDA7A7"
                  stroke-width="1.79125"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_7"
                  d="M131.717 83.0657L200.456 83.0657"
                  stroke="#E55959"
                  stroke-width="2.23906"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M198.693 86.887L202.466 83.0431L198.58 79.7071"
                  stroke="#E55959"
                  stroke-width="1.79125"
                />
                <path
                  id="Vector 28_7"
                  d="M133.187 86.887L129.413 83.0431L133.299 79.7071"
                  stroke="#E55959"
                  stroke-width="1.79125"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_9"
                  d="M165.751 160.537L165.751 64.2574"
                  stroke="#E55959"
                  stroke-width="2.23906"
                  stroke-linecap="square"
                  stroke-dasharray="4.48
            4.48"
                />
                <path
                  id="Vector 27_9"
                  d="M169.572 66.6223L165.728 62.8489L162.392 66.7349"
                  stroke="#E55959"
                  stroke-width="1.79125"
                />
                <path
                  id="Vector 28_9"
                  d="M169.572 158.102L165.728 161.875L162.392 157.989"
                  stroke="#E55959"
                  stroke-width="1.79125"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4944"
              x1="147.793"
              y1="106.034"
              x2="181.553"
              y2="145.824"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#E6E6E6" />
              <stop offset="0.69487" stop-color="#CDCDCD" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_tank_top">
            <g id="tank_top">
              <path
                id="Vector 71"
                d="M169.865 57.1111C150.031 57.7032 132.579 32.3805 132.579 32.3805L127.633 39.9899C128.14 44.1751 129.155 53.002 129.155 54.8283C129.155 57.1111 158.071 99.3434 159.973 101.626C161.495 103.453 192.567 96.5533 207.912 92.8754L209.054 32C209.054 32 195.357 56.3502 169.865 57.1111Z"
                fill="white"
                stroke="black"
                stroke-width="0.760943"
              />
              <path
                id="Vector 70"
                d="M132.199 32L117.741 38.8485C118.502 48.9944 120.404 70.0471 117.741 84.1246C112.918 109.616 106.327 109.616 106.327 109.616L101 258H172.909H241.394L234.545 109.616C234.545 109.616 225.889 99.9491 223.512 86.0269C220.848 70.4276 222.244 51.6577 224.653 38.8485L209.054 32.3805C209.054 32.3805 207.532 82.2222 173.29 84.1246C138.643 86.0494 132.199 32 132.199 32Z"
                fill="white"
                stroke="black"
                stroke-width="0.760943"
              />
              <path
                id="Vector 16"
                d="M178.751 195.148C163.514 180.975 130.447 133.126 128.384 130.235C130.466 137.881 135.544 166.579 139.204 169.976C140.471 171.153 157.922 192.972 178.032 205.231C194.12 215.038 215.948 218.606 221.101 216.105C214.507 215.546 192.586 208.017 178.751 195.148Z"
                fill="url(#paint0_linear_207_4945)"
                fill-opacity="0.5"
              />
              <path
                id="Vector 72"
                d="M106.327 113.041C106.327 113.041 117.436 108.475 121.545 81.0809C123.828 65.8621 120.404 37.3267 120.404 37.3267"
                stroke="black"
                stroke-width="0.760943"
                stroke-dasharray="1.52 1.52"
              />
              <path
                id="Vector 73"
                d="M234.758 113.041C234.758 113.041 221.99 104.29 219.539 81.0809C218.012 66.6235 220.68 37.3267 220.68 37.3267"
                stroke="black"
                stroke-width="0.760943"
                stroke-dasharray="1.52 1.52"
              />
              <path
                id="Vector 74"
                d="M130.677 33.1417C130.677 34.6635 137.191 85.7741 171.007 87.1686C207.912 88.6905 212.098 33.9026 212.098 33.9026"
                stroke="black"
                stroke-width="0.760943"
                stroke-dasharray="1.52 1.52"
              />
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19"
                  d="M112.414 122.171L229.219 122.171"
                  stroke="#E55959"
                  stroke-width="3.80471"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27"
                  d="M226.223 128.665L232.635 122.133L226.031 116.465"
                  stroke="#E55959"
                  stroke-width="3.04377"
                />
                <path
                  id="Vector 28"
                  d="M114.911 128.665L108.499 122.133L115.102 116.465"
                  stroke="#E55959"
                  stroke-width="3.04377"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19_2"
                  d="M111.653 170.111L231.882 170.111"
                  stroke="#E55959"
                  stroke-width="3.80471"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M227.745 176.605L234.157 170.073L227.553 164.404"
                  stroke="#E55959"
                  stroke-width="3.04377"
                />
                <path
                  id="Vector 28_2"
                  d="M114.15 176.605L107.738 170.073L114.341 164.404"
                  stroke="#E55959"
                  stroke-width="3.04377"
                />
              </g>
            )}
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 19_3"
                  d="M107.848 246.966L235.687 246.966"
                  stroke="#E55959"
                  stroke-width="3.80471"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M231.549 253.459L237.961 246.928L231.358 241.259"
                  stroke="#E55959"
                  stroke-width="3.04377"
                />
                <path
                  id="Vector 28_3"
                  d="M110.345 253.459L103.933 246.928L110.537 241.259"
                  stroke="#E55959"
                  stroke-width="3.04377"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_4"
                  d="M170.246 253.815L170.246 61.2964"
                  stroke="#E55959"
                  stroke-width="3.80471"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M176.739 65.3149L170.207 58.9029L164.539 65.5062"
                  stroke="#E55959"
                  stroke-width="3.04377"
                />
                <path
                  id="Vector 28_4"
                  d="M176.739 249.678L170.207 256.09L164.539 249.486"
                  stroke="#E55959"
                  stroke-width="3.04377"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_5"
                  d="M170.246 253.815L170.246 61.2964"
                  stroke="#EDA7A7"
                  stroke-width="3.80471"
                  stroke-linecap="square"
                  stroke-dasharray="7.61 7.61"
                />
                <path
                  id="Vector 27_5"
                  d="M176.739 65.3149L170.207 58.9029L164.539 65.5062"
                  stroke="#EDA7A7"
                  stroke-width="3.04377"
                />
                <path
                  id="Vector 28_5"
                  d="M176.739 249.678L170.207 256.09L164.539 249.486"
                  stroke="#EDA7A7"
                  stroke-width="3.04377"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 19_6"
                  d="M122.687 43.4143L220.468 43.4143"
                  stroke="#EDA7A7"
                  stroke-width="3.80471"
                  stroke-linecap="square"
                  stroke-dasharray="7.61 7.61"
                />
                <path
                  id="Vector 27_6"
                  d="M216.33 49.5272L222.742 42.9954L216.139 37.3267"
                  stroke="#EDA7A7"
                  stroke-width="3.04377"
                />
                <path
                  id="Vector 28_6"
                  d="M127.086 49.5272L120.674 42.9954L127.277 37.3267"
                  stroke="#EDA7A7"
                  stroke-width="3.04377"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_7"
                  d="M112.414 122.171L229.219 122.171"
                  stroke="#E55959"
                  stroke-width="3.80471"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M226.223 128.665L232.635 122.133L226.031 116.465"
                  stroke="#E55959"
                  stroke-width="3.04377"
                />
                <path
                  id="Vector 28_7"
                  d="M114.911 128.665L108.499 122.133L115.102 116.465"
                  stroke="#E55959"
                  stroke-width="3.04377"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_8"
                  d="M111.653 170.111L231.882 170.111"
                  stroke="#E55959"
                  stroke-width="3.80471"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_8"
                  d="M227.745 176.605L234.157 170.073L227.553 164.404"
                  stroke="#E55959"
                  stroke-width="3.04377"
                />
                <path
                  id="Vector 28_8"
                  d="M114.15 176.605L107.738 170.073L114.341 164.404"
                  stroke="#E55959"
                  stroke-width="3.04377"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_9"
                  d="M170.246 253.814L170.246 90.2114"
                  stroke="#E55959"
                  stroke-width="3.80471"
                  stroke-linecap="square"
                  stroke-dasharray="7.61 7.61"
                />
                <path
                  id="Vector 27_9"
                  d="M176.739 94.2303L170.207 87.8183L164.539 94.4216"
                  stroke="#E55959"
                  stroke-width="3.04377"
                />
                <path
                  id="Vector 28_9"
                  d="M176.739 249.677L170.207 256.089L164.539 249.486"
                  stroke="#E55959"
                  stroke-width="3.04377"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4945"
              x1="139.732"
              y1="161.201"
              x2="197.098"
              y2="228.813"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#E6E6E6" />
              <stop offset="0.69487" stop-color="#CDCDCD" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
