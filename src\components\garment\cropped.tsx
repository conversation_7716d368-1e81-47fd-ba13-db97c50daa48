import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface CroppedProps {
  measure: GarmentMeasure;
  className?: string;
}

export function Cropped({ measure, className }: CroppedProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_cropped">
            <g id="cropped">
              <path
                id="Vector 51"
                d="M142.337 23C142.337 23 146.972 31.7684 165.547 31.7684C184.123 31.7684 189.789 23 189.789 23C189.789 23 199.847 26.3011 203.974 28.1579C208.1 30.0148 219.053 33.4503 228.216 37.9579C233.676 40.6438 235.695 41.9295 241.884 45.4369C248.074 48.9442 258.733 55.3228 263.289 58.0737L246.011 95.7263L230.537 97.2737V170H169.158H99.5263V96.7579L84.8263 95.4685L66 58.0737L101.589 37.9579C105.2 36.0667 113.917 31.7169 119.9 29.4474C125.883 27.1779 142.337 23 142.337 23Z"
                fill="white"
                stroke="black"
                stroke-width="0.51579"
              />
              <g id="Group 32">
                <path
                  id="Vector 16"
                  d="M150.152 131.524C139.824 121.917 117.411 89.4834 116.012 87.5239C117.423 92.7068 120.865 112.159 123.346 114.462C124.205 115.259 136.034 130.049 149.665 138.358C160.57 145.006 175.365 147.424 178.858 145.729C174.389 145.35 159.53 140.247 150.152 131.524Z"
                  fill="url(#paint0_linear_207_4942)"
                  fill-opacity="0.5"
                />
                <g id="Group 31">
                  <path
                    id="Vector 54"
                    d="M230.535 164.326L230.793 164.328L230.797 163.812L230.539 163.811L230.535 164.326ZM99.7825 163.437L100.301 163.44L100.305 162.924L99.786 162.921L99.7825 163.437ZM101.339 163.447L102.377 163.454L102.38 162.939L101.343 162.932L101.339 163.447ZM103.415 163.461L104.452 163.468L104.456 162.953L103.418 162.946L103.415 163.461ZM105.49 163.476L106.528 163.483L106.531 162.967L105.493 162.96L105.49 163.476ZM107.565 163.49L108.603 163.497L108.607 162.981L107.569 162.974L107.565 163.49ZM109.641 163.504L110.679 163.511L110.682 162.995L109.644 162.988L109.641 163.504ZM111.716 163.518L112.754 163.525L112.757 163.009L111.72 163.002L111.716 163.518ZM113.792 163.532L114.829 163.539L114.833 163.023L113.795 163.016L113.792 163.532ZM115.867 163.546L116.905 163.553L116.908 163.037L115.871 163.03L115.867 163.546ZM117.943 163.56L118.98 163.567L118.984 163.052L117.946 163.045L117.943 163.56ZM120.018 163.574L121.056 163.581L121.059 163.066L120.022 163.059L120.018 163.574ZM122.093 163.589L123.131 163.596L123.135 163.08L122.097 163.073L122.093 163.589ZM124.169 163.603L125.207 163.61L125.21 163.094L124.172 163.087L124.169 163.603ZM126.244 163.617L127.282 163.624L127.286 163.108L126.248 163.101L126.244 163.617ZM128.32 163.631L129.357 163.638L129.361 163.122L128.323 163.115L128.32 163.631ZM130.395 163.645L131.433 163.652L131.436 163.136L130.399 163.129L130.395 163.645ZM132.471 163.659L133.508 163.666L133.512 163.15L132.474 163.143L132.471 163.659ZM134.546 163.673L135.584 163.68L135.587 163.165L134.55 163.157L134.546 163.673ZM136.622 163.687L137.659 163.694L137.663 163.179L136.625 163.172L136.622 163.687ZM138.697 163.702L139.735 163.709L139.738 163.193L138.7 163.186L138.697 163.702ZM140.772 163.716L141.81 163.723L141.814 163.207L140.776 163.2L140.772 163.716ZM142.848 163.73L143.886 163.737L143.889 163.221L142.851 163.214L142.848 163.73ZM144.923 163.744L145.961 163.751L145.964 163.235L144.927 163.228L144.923 163.744ZM146.999 163.758L148.036 163.765L148.04 163.249L147.002 163.242L146.999 163.758ZM149.074 163.772L150.112 163.779L150.115 163.263L149.078 163.256L149.074 163.772ZM151.15 163.786L152.187 163.793L152.191 163.278L151.153 163.27L151.15 163.786ZM153.225 163.8L154.263 163.807L154.266 163.292L153.229 163.285L153.225 163.8ZM155.3 163.815L156.338 163.822L156.342 163.306L155.304 163.299L155.3 163.815ZM157.376 163.829L158.414 163.836L158.417 163.32L157.379 163.313L157.376 163.829ZM159.451 163.843L160.489 163.85L160.493 163.334L159.455 163.327L159.451 163.843ZM161.527 163.857L162.564 163.864L162.568 163.348L161.53 163.341L161.527 163.857ZM163.602 163.871L164.64 163.878L164.643 163.362L163.606 163.355L163.602 163.871ZM165.678 163.885L166.715 163.892L166.719 163.376L165.681 163.369L165.678 163.885ZM167.753 163.899L168.791 163.906L168.794 163.391L167.757 163.383L167.753 163.899ZM169.829 163.913L170.866 163.92L170.87 163.405L169.832 163.398L169.829 163.913ZM171.904 163.927L172.942 163.935L172.945 163.419L171.907 163.412L171.904 163.927ZM173.979 163.942L175.017 163.949L175.021 163.433L173.983 163.426L173.979 163.942ZM176.055 163.956L177.093 163.963L177.096 163.447L176.058 163.44L176.055 163.956ZM178.13 163.97L179.168 163.977L179.171 163.461L178.134 163.454L178.13 163.97ZM180.206 163.984L181.243 163.991L181.247 163.475L180.209 163.468L180.206 163.984ZM182.281 163.998L183.319 164.005L183.322 163.489L182.285 163.482L182.281 163.998ZM184.357 164.012L185.394 164.019L185.398 163.504L184.36 163.496L184.357 164.012ZM186.432 164.026L187.47 164.033L187.473 163.518L186.436 163.511L186.432 164.026ZM188.507 164.04L189.545 164.048L189.549 163.532L188.511 163.525L188.507 164.04ZM190.583 164.055L191.621 164.062L191.624 163.546L190.586 163.539L190.583 164.055ZM192.658 164.069L193.696 164.076L193.7 163.56L192.662 163.553L192.658 164.069ZM194.734 164.083L195.771 164.09L195.775 163.574L194.737 163.567L194.734 164.083ZM196.809 164.097L197.847 164.104L197.85 163.588L196.813 163.581L196.809 164.097ZM198.885 164.111L199.922 164.118L199.926 163.602L198.888 163.595L198.885 164.111ZM200.96 164.125L201.998 164.132L202.001 163.616L200.964 163.609L200.96 164.125ZM203.036 164.139L204.073 164.146L204.077 163.631L203.039 163.624L203.036 164.139ZM205.111 164.153L206.149 164.161L206.152 163.645L205.114 163.638L205.111 164.153ZM207.186 164.168L208.224 164.175L208.228 163.659L207.19 163.652L207.186 164.168ZM209.262 164.182L210.3 164.189L210.303 163.673L209.265 163.666L209.262 164.182ZM211.337 164.196L212.375 164.203L212.378 163.687L211.341 163.68L211.337 164.196ZM213.413 164.21L214.45 164.217L214.454 163.701L213.416 163.694L213.413 164.21ZM215.488 164.224L216.526 164.231L216.529 163.715L215.492 163.708L215.488 164.224ZM217.564 164.238L218.601 164.245L218.605 163.729L217.567 163.722L217.564 164.238ZM219.639 164.252L220.677 164.259L220.68 163.744L219.643 163.737L219.639 164.252ZM221.714 164.266L222.752 164.273L222.756 163.758L221.718 163.751L221.714 164.266ZM223.79 164.281L224.828 164.288L224.831 163.772L223.793 163.765L223.79 164.281ZM225.865 164.295L226.903 164.302L226.907 163.786L225.869 163.779L225.865 164.295ZM227.941 164.309L228.978 164.316L228.982 163.8L227.944 163.793L227.941 164.309ZM230.016 164.323L230.535 164.326L230.539 163.811L230.02 163.807L230.016 164.323Z"
                    fill="black"
                  />
                  <g id="Group 27">
                    <path
                      id="Vector 52"
                      d="M91.0428 96.0995L91.1575 96.3305L91.6194 96.101L91.5047 95.8701L91.0428 96.0995ZM70.927 55.6101L71.1556 56.0702L71.6175 55.8407L71.3889 55.3806L70.927 55.6101ZM71.6127 56.9904L72.0699 57.9106L72.5318 57.6811L72.0747 56.7609L71.6127 56.9904ZM72.5271 58.8308L72.9843 59.751L73.4462 59.5216L72.989 58.6013L72.5271 58.8308ZM73.4414 60.6713L73.8986 61.5915L74.3605 61.362L73.9034 60.4418L73.4414 60.6713ZM74.3558 62.5117L74.813 63.4319L75.2749 63.2024L74.8177 62.2822L74.3558 62.5117ZM75.2701 64.3521L75.7273 65.2723L76.1892 65.0428L75.7321 64.1226L75.2701 64.3521ZM76.1845 66.1925L76.6417 67.1128L77.1036 66.8833L76.6464 65.9631L76.1845 66.1925ZM77.0989 68.033L77.556 68.9532L78.018 68.7237L77.5608 67.8035L77.0989 68.033ZM78.0132 69.8734L78.4704 70.7936L78.9323 70.5641L78.4751 69.6439L78.0132 69.8734ZM78.9276 71.7138L79.3847 72.6341L79.8467 72.4046L79.3895 71.4844L78.9276 71.7138ZM79.8419 73.5543L80.2991 74.4745L80.761 74.245L80.3038 73.3248L79.8419 73.5543ZM80.7563 75.3947L81.2135 76.3149L81.6754 76.0854L81.2182 75.1652L80.7563 75.3947ZM81.6706 77.2351L82.1278 78.1554L82.5897 77.9259L82.1326 77.0056L81.6706 77.2351ZM82.585 79.0756L83.0422 79.9958L83.5041 79.7663L83.0469 78.8461L82.585 79.0756ZM83.4993 80.916L83.9565 81.8362L84.4184 81.6067L83.9613 80.6865L83.4993 80.916ZM84.4137 82.7564L84.8709 83.6766L85.3328 83.4472L84.8756 82.5269L84.4137 82.7564ZM85.328 84.5969L85.7852 85.5171L86.2471 85.2876L85.79 84.3674L85.328 84.5969ZM86.2424 86.4373L86.6996 87.3575L87.1615 87.128L86.7043 86.2078L86.2424 86.4373ZM87.1568 88.2777L87.6139 89.1979L88.0759 88.9684L87.6187 88.0482L87.1568 88.2777ZM88.0711 90.1181L88.5283 91.0384L88.9902 90.8089L88.533 89.8887L88.0711 90.1181ZM88.9855 91.9586L89.4426 92.8788L89.9046 92.6493L89.4474 91.7291L88.9855 91.9586ZM89.8998 93.799L90.357 94.7192L90.8189 94.4897L90.3617 93.5695L89.8998 93.799ZM90.8142 95.6394L91.0428 96.0995L91.5047 95.8701L91.2761 95.4099L90.8142 95.6394Z"
                      fill="black"
                    />
                    <path
                      id="Vector 55"
                      d="M99.2685 96.7583C99.2685 96.1393 99.2685 93.7495 99.2685 92.632C102.019 91.6004 107.196 88.2729 108.553 83.8635C111.647 73.8057 110.874 64.2635 108.037 56.2688C106.215 51.1355 102.965 41.5688 101.59 37.9583"
                      stroke="black"
                      stroke-width="0.51579"
                    />
                  </g>
                  <g id="Group 28">
                    <path
                      id="Vector 53"
                      d="M240.827 96.0964L240.716 96.3289L240.251 96.1055L240.362 95.8731L240.827 96.0964ZM259.911 56.3807L259.684 56.8535L259.219 56.6301L259.447 56.1573L259.911 56.3807ZM259.23 57.7991L258.775 58.7447L258.311 58.5213L258.765 57.5757L259.23 57.7991ZM258.321 59.6903L257.867 60.6359L257.402 60.4125L257.856 59.4669L258.321 59.6903ZM257.412 61.5815L256.958 62.5272L256.493 62.3038L256.947 61.3581L257.412 61.5815ZM256.504 63.4728L256.049 64.4184L255.584 64.195L256.039 63.2494L256.504 63.4728ZM255.595 65.364L255.14 66.3096L254.675 66.0862L255.13 65.1406L255.595 65.364ZM254.686 67.2552L254.232 68.2008L253.767 67.9774L254.221 67.0318L254.686 67.2552ZM253.777 69.1464L253.323 70.0921L252.858 69.8687L253.312 68.9231L253.777 69.1464ZM252.868 71.0377L252.414 71.9833L251.949 71.7599L252.404 70.8143L252.868 71.0377ZM251.96 72.9289L251.505 73.8745L251.04 73.6511L251.495 72.7055L251.96 72.9289ZM251.051 74.8201L250.597 75.7657L250.132 75.5424L250.586 74.5967L251.051 74.8201ZM250.142 76.7114L249.688 77.657L249.223 77.4336L249.677 76.488L250.142 76.7114ZM249.233 78.6026L248.779 79.5482L248.314 79.3248L248.768 78.3792L249.233 78.6026ZM248.325 80.4938L247.87 81.4394L247.405 81.216L247.86 80.2704L248.325 80.4938ZM247.416 82.385L246.961 83.3307L246.497 83.1073L246.951 82.1617L247.416 82.385ZM246.507 84.2763L246.053 85.2219L245.588 84.9985L246.042 84.0529L246.507 84.2763ZM245.598 86.1675L245.144 87.1131L244.679 86.8897L245.133 85.9441L245.598 86.1675ZM244.69 88.0587L244.235 89.0043L243.77 88.7809L244.225 87.8353L244.69 88.0587ZM243.781 89.9499L243.326 90.8956L242.861 90.6722L243.316 89.7266L243.781 89.9499ZM242.872 91.8412L242.418 92.7868L241.953 92.5634L242.407 91.6178L242.872 91.8412ZM241.963 93.7324L241.509 94.678L241.044 94.4546L241.498 93.509L241.963 93.7324ZM241.054 95.6236L240.827 96.0964L240.362 95.8731L240.59 95.4002L241.054 95.6236Z"
                      fill="black"
                    />
                    <path
                      id="Vector 56"
                      d="M230.795 97.0162C230.795 97.0162 231.568 93.2338 231.568 92.1162C228.818 91.0846 225.376 88.2729 224.019 83.8636C220.924 73.8057 220.737 66.5846 222.026 56.7846C222.737 51.3843 226.512 41.5689 227.887 37.9583"
                      stroke="black"
                      stroke-width="0.51579"
                    />
                  </g>
                </g>
                <g id="Group 30">
                  <path
                    id="Vector 59"
                    d="M135.889 24.8051C139.242 30.4788 153.663 37.184 165.289 37.184C177.926 37.184 190.821 31.5103 195.721 25.063"
                    stroke="black"
                    stroke-width="0.51579"
                  />
                  <g id="Group 29">
                    <path id="Vector 60" d="M138.726 27.9L143.626 24.8053" stroke="black" stroke-width="0.51579" />
                    <path id="Vector 61" d="M142.079 30.4799L146.721 27.1273" stroke="black" stroke-width="0.51579" />
                    <path id="Vector 68" d="M187.211 31.7694L184.374 27.643" stroke="black" stroke-width="0.51579" />
                    <path id="Vector 69" d="M192.626 28.4163L187.984 25.0636" stroke="black" stroke-width="0.51579" />
                    <path id="Vector 62" d="M147.237 33.0583L150.332 29.1899" stroke="black" stroke-width="0.51579" />
                    <path id="Vector 63" d="M152.911 35.3791L155.232 30.737" stroke="black" stroke-width="0.51579" />
                    <path id="Vector 67" d="M180.845 34.3475L179.134 29.7055" stroke="black" stroke-width="0.51579" />
                    <path id="Vector 64" d="M160.132 36.9273L160.905 31.7694" stroke="black" stroke-width="0.51579" />
                    <path id="Vector 65" d="M167.095 37.1852L166.837 31.7694" stroke="black" stroke-width="0.51579" />
                    <path id="Vector 66" d="M173.8 36.1529L173.026 31.2529" stroke="black" stroke-width="0.51579" />
                  </g>
                </g>
              </g>
            </g>
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19"
                  d="M164 166.647L164 34.6053"
                  stroke="#E55959"
                  stroke-width="2.57895"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27"
                  d="M168.401 37.3294L163.974 32.9831L160.132 37.459"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
                <path
                  id="Vector 28"
                  d="M168.401 163.843L163.974 168.189L160.132 163.713"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19_2"
                  d="M102.025 120.742L226.926 120.742"
                  stroke="#E55959"
                  stroke-width="2.57895"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M224.638 125.144L228.984 120.716L224.508 116.874"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
                <path
                  id="Vector 28_2"
                  d="M104.829 125.144L100.483 120.716L104.959 116.874"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
              </g>
            )}
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 19_3"
                  d="M102.025 159.426L226.926 159.426"
                  stroke="#E55959"
                  stroke-width="2.57895"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M224.638 163.828L228.984 159.401L224.508 155.558"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
                <path
                  id="Vector 28_3"
                  d="M104.829 163.828L100.483 159.401L104.959 155.558"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
              </g>
            )}
            {findMeasure('product_biceps') && (
              <g id="product_biceps">
                <path
                  id="Vector 21"
                  d="M75.8 56.5262L94.6263 92.8894"
                  stroke="#E55959"
                  stroke-width="2.57895"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M89.5625 92.3314L95.4522 94.2815L96.9287 88.5703"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
                <path
                  id="Vector 27_4"
                  d="M80.6506 56.4374L74.7371 54.5605L73.3316 60.2896"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
              </g>
            )}
            {findMeasure('product_sleeve') && (
              <g id="product_sleeve">
                <path
                  id="Vector 21_2"
                  d="M228.989 45.6952L258.706 60.6855"
                  stroke="#E55959"
                  stroke-width="2.57895"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M254.281 63.2108L260.192 61.3258L258.029 55.8377"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
                <path
                  id="Vector 27_5"
                  d="M232.815 42.5978L226.928 44.556L229.159 50.0168"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
              </g>
            )}
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19_4"
                  d="M102.025 102.689L226.926 102.689"
                  stroke="#E55959"
                  stroke-width="2.57895"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M224.638 107.091L228.984 102.663L224.508 98.821"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
                <path
                  id="Vector 28_4"
                  d="M104.829 107.091L100.483 102.663L104.959 98.821"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_5"
                  d="M164 166.647L164 34.6053"
                  stroke="#E55959"
                  stroke-width="2.57895"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M168.401 37.3294L163.974 32.9831L160.132 37.459"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
                <path
                  id="Vector 28_5"
                  d="M168.401 163.843L163.974 168.189L160.132 163.713"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 19_6"
                  d="M105.119 39.2481L225.121 39.2481"
                  stroke="#EDA7A7"
                  stroke-width="2.57895"
                  stroke-linecap="square"
                  stroke-dasharray="5.16 5.16"
                />
                <path
                  id="Vector 27_8"
                  d="M222.574 43.6489L226.921 39.2215L222.445 35.3791"
                  stroke="#EDA7A7"
                  stroke-width="2.06316"
                />
                <path
                  id="Vector 28_6"
                  d="M107.924 43.6489L103.578 39.2215L108.054 35.3791"
                  stroke="#EDA7A7"
                  stroke-width="2.06316"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_7"
                  d="M164 166.647L164 34.6053"
                  stroke="#E55959"
                  stroke-width="2.57895"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_9"
                  d="M168.401 37.3294L163.974 32.9831L160.132 37.459"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
                <path
                  id="Vector 28_7"
                  d="M168.401 163.843L163.974 168.189L160.132 163.713"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_8"
                  d="M102.025 120.742L226.926 120.742"
                  stroke="#E55959"
                  stroke-width="2.57895"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_10"
                  d="M224.638 125.144L228.984 120.716L224.508 116.874"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
                <path
                  id="Vector 28_8"
                  d="M104.829 125.144L100.483 120.716L104.959 116.874"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_9"
                  d="M102.025 102.689L226.926 102.689"
                  stroke="#E55959"
                  stroke-width="2.57895"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_11"
                  d="M224.638 107.091L228.984 102.663L224.508 98.821"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
                <path
                  id="Vector 28_9"
                  d="M104.829 107.091L100.483 102.663L104.959 98.821"
                  stroke="#E55959"
                  stroke-width="2.06316"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4942"
              x1="123.704"
              y1="108.514"
              x2="162.589"
              y2="154.343"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#E6E6E6" />
              <stop offset="0.69487" stop-color="#CDCDCD" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_cropped">
            <g id="cropped">
              <path
                id="Vector 51"
                d="M139.263 48C139.263 48 145.412 59.6316 170.053 59.6316C194.694 59.6316 202.211 48 202.211 48C202.211 48 215.553 52.379 221.026 54.8421C226.5 57.3053 241.03 61.8627 253.184 67.8421C260.427 71.4051 263.105 73.1106 271.316 77.7632C279.526 82.4158 293.667 90.8772 299.711 94.5264L276.789 144.474L256.263 146.526V243H174.842H82.4737V145.842L62.9737 144.132L38 94.5264L85.2105 67.8421C90 65.3334 101.563 59.5632 109.5 56.5527C117.437 53.5421 139.263 48 139.263 48Z"
                fill="white"
                stroke="black"
                stroke-width="0.684211"
              />
              <g id="Group 32">
                <path
                  id="Vector 16"
                  d="M149.631 191.96C135.93 179.216 106.198 136.192 104.343 133.592C106.215 140.468 110.781 166.271 114.071 169.326C115.211 170.384 130.902 190.003 148.985 201.026C163.45 209.844 183.077 213.052 187.71 210.803C181.781 210.301 162.07 203.531 149.631 191.96Z"
                  fill="url(#paint0_linear_207_4943)"
                  fill-opacity="0.5"
                />
                <g id="Group 31">
                  <path
                    id="Vector 54"
                    d="M256.261 235.474L256.603 235.476L256.608 234.792L256.266 234.789L256.261 235.474ZM82.8137 234.293L83.5019 234.298L83.5066 233.614L82.8183 233.609L82.8137 234.293ZM84.8785 234.307L86.2551 234.317L86.2597 233.633L84.8832 233.623L84.8785 234.307ZM87.6316 234.326L89.0082 234.336L89.0129 233.651L87.6363 233.642L87.6316 234.326ZM90.3848 234.345L91.7613 234.354L91.766 233.67L90.3894 233.661L90.3848 234.345ZM93.1379 234.364L94.5145 234.373L94.5191 233.689L93.1426 233.679L93.1379 234.364ZM95.891 234.382L97.2676 234.392L97.2723 233.708L95.8957 233.698L95.891 234.382ZM98.6442 234.401L100.021 234.41L100.025 233.726L98.6488 233.717L98.6442 234.401ZM101.397 234.42L102.774 234.429L102.779 233.745L101.402 233.736L101.397 234.42ZM104.15 234.439L105.527 234.448L105.532 233.764L104.155 233.754L104.15 234.439ZM106.904 234.457L108.28 234.467L108.285 233.782L106.908 233.773L106.904 234.457ZM109.657 234.476L111.033 234.485L111.038 233.801L109.661 233.792L109.657 234.476ZM112.41 234.495L113.786 234.504L113.791 233.82L112.414 233.811L112.41 234.495ZM115.163 234.513L116.54 234.523L116.544 233.839L115.168 233.829L115.163 234.513ZM117.916 234.532L119.293 234.542L119.297 233.857L117.921 233.848L117.916 234.532ZM120.669 234.551L122.046 234.56L122.05 233.876L120.674 233.867L120.669 234.551ZM123.422 234.57L124.799 234.579L124.804 233.895L123.427 233.885L123.422 234.57ZM126.175 234.588L127.552 234.598L127.557 233.914L126.18 233.904L126.175 234.588ZM128.929 234.607L130.305 234.617L130.31 233.932L128.933 233.923L128.929 234.607ZM131.682 234.626L133.058 234.635L133.063 233.951L131.686 233.942L131.682 234.626ZM134.435 234.645L135.811 234.654L135.816 233.97L134.44 233.96L134.435 234.645ZM137.188 234.663L138.565 234.673L138.569 233.989L137.193 233.979L137.188 234.663ZM139.941 234.682L141.318 234.691L141.322 234.007L139.946 233.998L139.941 234.682ZM142.694 234.701L144.071 234.71L144.076 234.026L142.699 234.017L142.694 234.701ZM145.447 234.72L146.824 234.729L146.829 234.045L145.452 234.035L145.447 234.72ZM148.201 234.738L149.577 234.748L149.582 234.063L148.205 234.054L148.201 234.738ZM150.954 234.757L152.33 234.766L152.335 234.082L150.958 234.073L150.954 234.757ZM153.707 234.776L155.083 234.785L155.088 234.101L153.711 234.092L153.707 234.776ZM156.46 234.794L157.836 234.804L157.841 234.12L156.465 234.11L156.46 234.794ZM159.213 234.813L160.59 234.823L160.594 234.138L159.218 234.129L159.213 234.813ZM161.966 234.832L163.343 234.841L163.347 234.157L161.971 234.148L161.966 234.832ZM164.719 234.851L166.096 234.86L166.101 234.176L164.724 234.167L164.719 234.851ZM167.472 234.869L168.849 234.879L168.854 234.195L167.477 234.185L167.472 234.869ZM170.226 234.888L171.602 234.898L171.607 234.213L170.23 234.204L170.226 234.888ZM172.979 234.907L174.355 234.916L174.36 234.232L172.983 234.223L172.979 234.907ZM175.732 234.926L177.108 234.935L177.113 234.251L175.736 234.241L175.732 234.926ZM178.485 234.944L179.861 234.954L179.866 234.27L178.49 234.26L178.485 234.944ZM181.238 234.963L182.615 234.972L182.619 234.288L181.243 234.279L181.238 234.963ZM183.991 234.982L185.368 234.991L185.372 234.307L183.996 234.298L183.991 234.982ZM186.744 235.001L188.121 235.01L188.126 234.326L186.749 234.316L186.744 235.001ZM189.497 235.019L190.874 235.029L190.879 234.344L189.502 234.335L189.497 235.019ZM192.251 235.038L193.627 235.047L193.632 234.363L192.255 234.354L192.251 235.038ZM195.004 235.057L196.38 235.066L196.385 234.382L195.008 234.373L195.004 235.057ZM197.757 235.076L199.133 235.085L199.138 234.401L197.761 234.391L197.757 235.076ZM200.51 235.094L201.886 235.104L201.891 234.419L200.515 234.41L200.51 235.094ZM203.263 235.113L204.64 235.122L204.644 234.438L203.268 234.429L203.263 235.113ZM206.016 235.132L207.393 235.141L207.397 234.457L206.021 234.448L206.016 235.132ZM208.769 235.15L210.146 235.16L210.151 234.476L208.774 234.466L208.769 235.15ZM211.522 235.169L212.899 235.179L212.904 234.494L211.527 234.485L211.522 235.169ZM214.276 235.188L215.652 235.197L215.657 234.513L214.28 234.504L214.276 235.188ZM217.029 235.207L218.405 235.216L218.41 234.532L217.033 234.522L217.029 235.207ZM219.782 235.225L221.158 235.235L221.163 234.551L219.787 234.541L219.782 235.225ZM222.535 235.244L223.912 235.253L223.916 234.569L222.54 234.56L222.535 235.244ZM225.288 235.263L226.665 235.272L226.669 234.588L225.293 234.579L225.288 235.263ZM228.041 235.282L229.418 235.291L229.423 234.607L228.046 234.597L228.041 235.282ZM230.794 235.3L232.171 235.31L232.176 234.625L230.799 234.616L230.794 235.3ZM233.548 235.319L234.924 235.328L234.929 234.644L233.552 234.635L233.548 235.319ZM236.301 235.338L237.677 235.347L237.682 234.663L236.305 234.654L236.301 235.338ZM239.054 235.357L240.43 235.366L240.435 234.682L239.058 234.672L239.054 235.357ZM241.807 235.375L243.184 235.385L243.188 234.7L241.812 234.691L241.807 235.375ZM244.56 235.394L245.937 235.403L245.941 234.719L244.565 234.71L244.56 235.394ZM247.313 235.413L248.69 235.422L248.694 234.738L247.318 234.729L247.313 235.413ZM250.066 235.431L251.443 235.441L251.448 234.757L250.071 234.747L250.066 235.431ZM252.82 235.45L254.196 235.46L254.201 234.775L252.824 234.766L252.82 235.45ZM255.573 235.469L256.261 235.474L256.266 234.789L255.577 234.785L255.573 235.469Z"
                    fill="black"
                  />
                  <g id="Group 27">
                    <path
                      id="Vector 52"
                      d="M71.2201 144.968L71.3723 145.275L71.9851 144.97L71.8328 144.664L71.2201 144.968ZM44.5359 91.2579L44.8391 91.8682L45.4519 91.5638L45.1486 90.9535L44.5359 91.2579ZM45.4456 93.0889L46.052 94.3096L46.6648 94.0052L46.0583 92.7845L45.4456 93.0889ZM46.6585 95.5303L47.2649 96.751L47.8777 96.4466L47.2712 95.2259L46.6585 95.5303ZM47.8714 97.9717L48.4779 99.1924L49.0906 98.888L48.4842 97.6673L47.8714 97.9717ZM49.0843 100.413L49.6908 101.634L50.3035 101.329L49.6971 100.109L49.0843 100.413ZM50.2972 102.854L50.9037 104.075L51.5165 103.771L50.91 102.55L50.2972 102.854ZM51.5102 105.296L52.1166 106.517L52.7294 106.212L52.1229 104.991L51.5102 105.296ZM52.7231 107.737L53.3295 108.958L53.9423 108.654L53.3358 107.433L52.7231 107.737ZM53.936 110.179L54.5425 111.399L55.1552 111.095L54.5488 109.874L53.936 110.179ZM55.1489 112.62L55.7554 113.841L56.3681 113.536L55.7617 112.316L55.1489 112.62ZM56.3618 115.061L56.9683 116.282L57.581 115.978L56.9746 114.757L56.3618 115.061ZM57.5748 117.503L58.1812 118.723L58.794 118.419L58.1875 117.198L57.5748 117.503ZM58.7877 119.944L59.3941 121.165L60.0069 120.86L59.4004 119.64L58.7877 119.944ZM60.0006 122.386L60.6071 123.606L61.2198 123.302L60.6133 122.081L60.0006 122.386ZM61.2135 124.827L61.82 126.048L62.4327 125.743L61.8263 124.523L61.2135 124.827ZM62.4264 127.268L63.0329 128.489L63.6456 128.185L63.0392 126.964L62.4264 127.268ZM63.6394 129.71L64.2458 130.93L64.8586 130.626L64.2521 129.405L63.6394 129.71ZM64.8523 132.151L65.4587 133.372L66.0715 133.067L65.465 131.847L64.8523 132.151ZM66.0652 134.593L66.6717 135.813L67.2844 135.509L66.6779 134.288L66.0652 134.593ZM67.2781 137.034L67.8846 138.255L68.4973 137.95L67.8909 136.729L67.2781 137.034ZM68.491 139.475L69.0975 140.696L69.7102 140.392L69.1038 139.171L68.491 139.475ZM69.704 141.917L70.3104 143.137L70.9232 142.833L70.3167 141.612L69.704 141.917ZM70.9169 144.358L71.2201 144.968L71.8328 144.664L71.5296 144.054L70.9169 144.358Z"
                      fill="black"
                    />
                    <path
                      id="Vector 55"
                      d="M82.1317 145.842C82.1317 145.021 82.1317 141.851 82.1317 140.368C85.7808 139 92.6478 134.586 94.4475 128.737C98.5528 115.395 97.5265 102.737 93.7633 92.1316C91.347 85.3221 87.0352 72.6316 85.2107 67.8422"
                      stroke="black"
                      stroke-width="0.684211"
                    />
                  </g>
                  <g id="Group 28">
                    <path
                      id="Vector 53"
                      d="M269.914 144.964L269.766 145.272L269.149 144.976L269.297 144.668L269.914 144.964ZM295.23 92.2798L294.928 92.907L294.311 92.6106L294.613 91.9835L295.23 92.2798ZM294.325 94.1614L293.723 95.4158L293.106 95.1194L293.709 93.865L294.325 94.1614ZM293.12 96.6701L292.517 97.9245L291.9 97.6282L292.503 96.3738L293.12 96.6701ZM291.914 99.1789L291.312 100.433L290.695 100.137L291.298 98.8826L291.914 99.1789ZM290.709 101.688L290.106 102.942L289.489 102.646L290.092 101.391L290.709 101.688ZM289.503 104.196L288.901 105.451L288.284 105.155L288.887 103.9L289.503 104.196ZM288.298 106.705L287.695 107.96L287.078 107.663L287.681 106.409L288.298 106.705ZM287.092 109.214L286.49 110.468L285.873 110.172L286.476 108.918L287.092 109.214ZM285.887 111.723L285.284 112.977L284.667 112.681L285.27 111.426L285.887 111.723ZM284.681 114.232L284.079 115.486L283.462 115.19L284.065 113.935L284.681 114.232ZM283.476 116.74L282.873 117.995L282.256 117.698L282.859 116.444L283.476 116.74ZM282.27 119.249L281.668 120.503L281.051 120.207L281.654 118.953L282.27 119.249ZM281.065 121.758L280.462 123.012L279.845 122.716L280.448 121.462L281.065 121.758ZM279.859 124.267L279.257 125.521L278.64 125.225L279.243 123.97L279.859 124.267ZM278.654 126.775L278.051 128.03L277.434 127.733L278.037 126.479L278.654 126.775ZM277.448 129.284L276.845 130.539L276.229 130.242L276.832 128.988L277.448 129.284ZM276.243 131.793L275.64 133.047L275.023 132.751L275.626 131.497L276.243 131.793ZM275.037 134.302L274.434 135.556L273.818 135.26L274.42 134.005L275.037 134.302ZM273.832 136.81L273.229 138.065L272.612 137.769L273.215 136.514L273.832 136.81ZM272.626 139.319L272.023 140.574L271.407 140.277L272.009 139.023L272.626 139.319ZM271.421 141.828L270.818 143.082L270.201 142.786L270.804 141.532L271.421 141.828ZM270.215 144.337L269.914 144.964L269.297 144.668L269.598 144.04L270.215 144.337Z"
                      fill="black"
                    />
                    <path
                      id="Vector 56"
                      d="M256.605 146.184C256.605 146.184 257.632 141.167 257.632 139.684C253.983 138.316 249.417 134.586 247.617 128.737C243.512 115.395 243.263 105.816 244.974 92.8158C245.916 85.6521 250.924 72.6316 252.748 67.8421"
                      stroke="black"
                      stroke-width="0.684211"
                    />
                  </g>
                </g>
                <g id="Group 30">
                  <path
                    id="Vector 59"
                    d="M130.711 50.3939C135.158 57.9202 154.288 66.815 169.711 66.815C186.474 66.815 203.579 59.2886 210.079 50.736"
                    stroke="black"
                    stroke-width="0.684211"
                  />
                  <g id="Group 29">
                    <path id="Vector 60" d="M134.474 54.4991L140.974 50.3938" stroke="black" stroke-width="0.684211" />
                    <path id="Vector 61" d="M138.921 57.921L145.079 53.4737" stroke="black" stroke-width="0.684211" />
                    <path id="Vector 68" d="M198.79 59.6315L195.026 54.1579" stroke="black" stroke-width="0.684211" />
                    <path id="Vector 69" d="M205.974 55.1837L199.816 50.7363" stroke="black" stroke-width="0.684211" />
                    <path id="Vector 62" d="M145.763 61.3419L149.869 56.2103" stroke="black" stroke-width="0.684211" />
                    <path id="Vector 63" d="M153.29 64.4202L156.369 58.2623" stroke="black" stroke-width="0.684211" />
                    <path id="Vector 67" d="M190.346 63.0521L188.076 56.8942" stroke="black" stroke-width="0.684211" />
                    <path id="Vector 64" d="M162.869 66.4737L163.895 59.6315" stroke="black" stroke-width="0.684211" />
                    <path id="Vector 65" d="M172.105 66.8158L171.763 59.6315" stroke="black" stroke-width="0.684211" />
                    <path id="Vector 66" d="M181 65.4469L179.974 58.9469" stroke="black" stroke-width="0.684211" />
                  </g>
                </g>
              </g>
            </g>
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19"
                  d="M168 238.553L168 63.3948"
                  stroke="#E55959"
                  stroke-width="3.42105"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27"
                  d="M173.839 67.0083L167.966 61.2429L162.868 67.1803"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
                <path
                  id="Vector 28"
                  d="M173.839 234.833L167.966 240.598L162.868 234.661"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19_2"
                  d="M85.788 177.657L251.474 177.657"
                  stroke="#E55959"
                  stroke-width="3.42105"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M248.438 183.497L254.203 177.624L248.266 172.527"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
                <path
                  id="Vector 28_2"
                  d="M89.5082 183.497L83.7428 177.624L89.6802 172.527"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
              </g>
            )}
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 19_3"
                  d="M85.7879 228.974L251.474 228.974"
                  stroke="#E55959"
                  stroke-width="3.42105"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M248.438 234.813L254.203 228.94L248.266 223.843"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
                <path
                  id="Vector 28_3"
                  d="M89.5082 234.813L83.7428 228.94L89.6802 223.843"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
              </g>
            )}
            {findMeasure('product_biceps') && (
              <g id="product_biceps">
                <path
                  id="Vector 21"
                  d="M51 92.4733L75.9737 140.71"
                  stroke="#E55959"
                  stroke-width="3.42105"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M69.2564 139.97L77.0694 142.557L79.0279 134.981"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
                <path
                  id="Vector 27_4"
                  d="M57.4345 92.3557L49.5901 89.8659L47.7256 97.4657"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
              </g>
            )}
            {findMeasure('product_sleeve') && (
              <g id="product_sleeve">
                <path
                  id="Vector 21_2"
                  d="M254.21 78.1056L293.63 97.9906"
                  stroke="#E55959"
                  stroke-width="3.42105"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M287.761 101.34L295.602 98.84L292.732 91.5599"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
                <path
                  id="Vector 27_5"
                  d="M259.286 73.9969L251.476 76.5944L254.436 83.8383"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
              </g>
            )}
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19_4"
                  d="M85.7879 153.711L251.474 153.711"
                  stroke="#E55959"
                  stroke-width="3.42105"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M248.438 159.549L254.203 153.676L248.266 148.579"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
                <path
                  id="Vector 28_4"
                  d="M89.5082 159.549L83.7428 153.676L89.6802 148.579"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_5"
                  d="M168 238.553L168 63.3948"
                  stroke="#EDA7A7"
                  stroke-width="3.42105"
                  stroke-linecap="square"
                  stroke-dasharray="6.84 6.84"
                />
                <path
                  id="Vector 27_7"
                  d="M173.839 67.0083L167.966 61.2429L162.869 67.1803"
                  stroke="#EDA7A7"
                  stroke-width="2.73684"
                />
                <path
                  id="Vector 28_5"
                  d="M173.839 234.833L167.966 240.598L162.869 234.661"
                  stroke="#EDA7A7"
                  stroke-width="2.73684"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 19_6"
                  d="M89.8932 69.5537L249.079 69.5537"
                  stroke="#EDA7A7"
                  stroke-width="3.42105"
                  stroke-linecap="square"
                  stroke-dasharray="6.84 6.84"
                />
                <path
                  id="Vector 27_8"
                  d="M245.701 75.3916L251.466 69.5185L245.529 64.4214"
                  stroke="#EDA7A7"
                  stroke-width="2.73684"
                />
                <path
                  id="Vector 28_6"
                  d="M93.6135 75.3916L87.8481 69.5185L93.7855 64.4214"
                  stroke="#EDA7A7"
                  stroke-width="2.73684"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_7"
                  d="M168 238.553L168 63.3948"
                  stroke="#E55959"
                  stroke-width="3.42105"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_9"
                  d="M173.839 67.0083L167.966 61.2429L162.869 67.1803"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
                <path
                  id="Vector 28_7"
                  d="M173.839 234.833L167.966 240.598L162.869 234.661"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_8"
                  d="M85.788 177.657L251.474 177.657"
                  stroke="#E55959"
                  stroke-width="3.42105"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_10"
                  d="M248.438 183.497L254.203 177.624L248.266 172.527"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
                <path
                  id="Vector 28_8"
                  d="M89.5082 183.497L83.7428 177.624L89.6802 172.527"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_9"
                  d="M85.788 153.711L251.474 153.711"
                  stroke="#E55959"
                  stroke-width="3.42105"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_11"
                  d="M248.438 159.549L254.203 153.676L248.266 148.579"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
                <path
                  id="Vector 28_9"
                  d="M89.5082 159.549L83.7428 153.676L89.6802 148.579"
                  stroke="#E55959"
                  stroke-width="2.73684"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4943"
              x1="114.546"
              y1="161.436"
              x2="166.128"
              y2="222.23"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#E6E6E6" />
              <stop offset="0.69487" stop-color="#CDCDCD" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
