import { useDevice } from '@/hooks/use-device';
import { BodyMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface ShoulderLengthProps {
  measure: BodyMeasure;
  className?: string;
}

export function ShoulderLength({ measure, className }: ShoulderLengthProps) {
  const { measures } = measure;
  const mappedMeasures = measures.map((item) => item.measure);
  const { isMobile } = useDevice();

  const findMeasure = (measure: string) => {
    const foundMeasure = mappedMeasures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full flex justify-center', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_shoulder_length">
            <mask id="mask0_128_1908" maskUnits="userSpaceOnUse" x="0" y="0" width="331" height="193">
              <rect id="rect" width="331" height="193" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_1908)">
              <g id="group_shoulder_length_mask">
                <g id="female">
                  <g id="Group 227">
                    <path
                      id="Union"
                      d="M98 194.365H114.783C114.783 194.365 119.772 180.607 120.735 177.095C122.686 169.972 122.784 167.045 122.686 162.947C122.608 159.668 126.752 137.968 128.833 127.528C129.516 129.284 130.902 133.031 130.98 133.968C131.058 134.904 132.118 140.798 132.638 143.627C133.289 147.01 134.668 154.185 134.98 155.824C135.292 157.463 135.566 164.833 135.663 168.313C135.566 169.842 135 173.855 133.517 177.68C132.033 181.505 128.996 190.397 127.662 194.365L165.904 193.99L204.16 194.365C202.826 190.397 199.788 181.505 198.305 177.68C196.822 173.856 196.256 169.842 196.159 168.313C196.256 164.833 196.529 157.463 196.842 155.824C197.154 154.185 198.533 147.01 199.183 143.627C199.704 140.798 200.764 134.904 200.842 133.968C200.92 133.031 202.306 129.284 202.989 127.528C205.07 137.968 209.214 159.668 209.136 162.947C209.038 167.045 209.136 169.972 211.087 177.095C212.05 180.608 217.039 195.439 217.039 195.439L234.407 195.244C232.944 186.365 228.963 166.635 227.48 163.825C226.364 161.712 225.779 158.892 225.298 156.578C224.979 155.045 224.707 153.733 224.357 152.994C223.836 151.894 223.143 143.129 222.462 134.512C221.995 128.61 221.534 122.779 221.137 119.527C220.874 117.367 220.653 114.865 220.425 112.282C219.809 105.295 219.139 97.7086 217.429 94.6457C215.088 90.45 212.453 87.3277 205.038 85.0835C197.622 82.8394 181.62 77.3789 179.571 70.8377C178.257 66.6421 178.595 59.8121 178.595 59.8121C178.595 59.8121 179.202 57.7342 179.571 56.3972C180.439 53.2523 181.62 48.2984 181.62 48.2984C181.783 48.5586 182.303 49.001 183.084 48.6887C183.841 48.3859 184.44 46.8581 184.884 45.726C185.013 45.3991 185.128 45.1053 185.23 44.8834C185.816 43.615 188.45 35.5164 185.913 35.7115C183.925 35.8644 183.398 36.4044 183.377 36.671C183.507 33.3786 183.629 26.2038 183.084 23.7101C182.401 20.5877 181.052 16.6141 177.034 13.66C173.717 11.2207 169.716 10.0498 166.496 10.0498C162.301 9.65933 158.222 11.6304 154.788 13.6599C150.494 16.1968 149.421 20.5876 148.738 23.7099C148.193 26.2037 148.315 33.3784 148.445 36.6708C148.424 36.4042 147.896 35.8643 145.908 35.7114C143.371 35.5162 146.006 43.6148 146.591 44.8832C146.694 45.1051 146.809 45.399 146.937 45.7259C147.382 46.858 147.981 48.3857 148.738 48.6886C149.519 49.0008 150.039 48.5585 150.202 48.2983C150.202 48.2983 151.244 53.4377 152.251 56.3972C152.636 57.5293 153.324 59.8121 153.324 59.8121C153.324 59.8121 153.565 66.6421 152.251 70.8377C150.202 77.3789 134.2 82.8392 126.784 85.0834C119.369 87.3276 116.734 90.4499 114.392 94.6455C112.683 97.7084 112.013 105.295 111.397 112.281C111.169 114.865 110.948 117.367 110.685 119.527C110.288 122.779 109.827 128.61 109.36 134.511C108.679 143.128 107.986 151.894 107.465 152.994C107.115 153.733 106.842 155.044 106.524 156.577C106.043 158.892 105.458 161.712 104.342 163.825C102.859 166.635 99.4636 185.486 98 194.365Z"
                      fill="white"
                    />
                    <path
                      id="Vector 19"
                      d="M153.258 37.3546C154.89 40.2203 158.534 42.9564 158.534 42.9564C158.534 42.9564 153.335 43.9625 151.007 42.2919C147.984 40.1218 149.046 25.6617 149.046 25.6617C149.046 25.6617 150.687 32.8407 153.258 37.3546Z"
                      fill="url(#paint0_linear_128_1908)"
                    />
                    <path
                      id="Vector 17"
                      d="M99.0735 188.609L112.148 143.042L121.125 165.679L119.564 180.315L114.685 194.561L98.0977 194.17L99.0735 188.609Z"
                      fill="url(#paint1_linear_128_1908)"
                    />
                    <path
                      id="Vector 18"
                      d="M129.809 188.121L145.811 143.042L200.452 184.706L204.843 195.341L127.077 195.146L129.809 188.121Z"
                      fill="url(#paint2_linear_128_1908)"
                    />
                    <path
                      id="Vector 14"
                      d="M175.112 119.527C175.112 119.527 175.531 122.175 176.069 124.226C176.538 126.018 177.165 127.991 177.165 127.991"
                      stroke="black"
                      stroke-width="0.315889"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 15"
                      d="M155.113 119.527C155.113 119.527 154.694 122.175 154.157 124.226C153.687 126.018 153.061 127.991 153.061 127.991"
                      stroke="black"
                      stroke-width="0.315889"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 20"
                      d="M165.956 162.557L165.956 168.971"
                      stroke="black"
                      stroke-width="0.315889"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 16"
                      d="M129.419 124.113C129.419 124.113 129.245 125.303 129.126 126.064C129.02 126.743 128.844 127.801 128.844 127.801"
                      stroke="black"
                      stroke-width="0.315889"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 17_2"
                      d="M202.296 124.015C202.296 124.015 202.469 125.205 202.589 125.967C202.695 126.645 202.989 127.528 202.989 127.528"
                      stroke="black"
                      stroke-width="0.315889"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 12"
                      d="M148.348 36.4922C148.348 36.4922 148.663 38.4856 149.068 40.0292C149.421 41.3787 149.893 42.8638 149.893 42.8638"
                      stroke="black"
                      stroke-width="0.315889"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 13"
                      d="M183.489 36.3948C183.489 36.3948 183.174 38.3882 182.769 39.9318C182.416 41.2813 181.944 42.7664 181.944 42.7664"
                      stroke="black"
                      stroke-width="0.315889"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </g>
                  <path
                    id="Union_2"
                    d="M98.0977 195.439H114.588C114.588 195.439 119.772 180.607 120.735 177.095C122.686 169.972 122.784 167.045 122.686 162.947C122.608 159.668 126.752 137.968 128.833 127.528C129.516 129.284 130.902 133.031 130.98 133.968C131.058 134.904 132.118 140.798 132.639 143.627C133.289 147.01 134.668 154.185 134.98 155.824C135.293 157.463 135.566 164.833 135.663 168.313C135.566 169.842 135 173.855 133.517 177.68C132.034 181.505 127.735 192.919 126.401 196.887L164.643 196.512L205.428 197C204.095 193.032 199.788 181.505 198.305 177.68C196.822 173.856 196.256 169.842 196.159 168.313C196.256 164.833 196.529 157.463 196.842 155.824C197.154 154.185 198.533 147.01 199.183 143.627C199.704 140.798 200.764 134.904 200.842 133.968C200.92 133.031 202.306 129.284 202.989 127.528C205.07 137.968 209.214 159.668 209.136 162.947C209.038 167.045 209.136 169.972 211.087 177.095C212.05 180.608 217.234 196.512 217.234 196.512L234.7 195.927C233.236 187.048 228.963 166.635 227.48 163.825C226.364 161.712 225.779 158.892 225.298 156.578C224.98 155.045 224.707 153.733 224.357 152.994C223.836 151.894 223.143 143.129 222.462 134.512C221.995 128.61 221.534 122.779 221.137 119.527C220.874 117.367 220.653 114.865 220.425 112.282C219.809 105.295 219.139 97.7086 217.43 94.6457C215.088 90.45 212.453 87.3277 205.038 85.0835C197.622 82.8394 181.62 77.3789 179.571 70.8377C178.257 66.6421 178.595 59.8121 178.595 59.8121C178.595 59.8121 179.202 57.7342 179.571 56.3972C180.439 53.2523 181.62 48.2984 181.62 48.2984C181.783 48.5586 182.303 49.001 183.084 48.6887C183.841 48.3859 184.44 46.8581 184.884 45.726C185.013 45.3991 185.128 45.1053 185.23 44.8834C185.816 43.615 188.45 35.5164 185.913 35.7115C183.925 35.8644 183.398 36.4044 183.377 36.671C183.507 33.3786 183.629 26.2038 183.084 23.7101C182.401 20.5877 181.052 16.6141 177.034 13.66C173.717 11.2207 169.716 10.0498 166.496 10.0498C162.301 9.65933 158.222 11.6304 154.788 13.6599C150.494 16.1968 149.421 20.5876 148.738 23.7099C148.193 26.2037 148.315 33.3784 148.445 36.6708C148.424 36.4042 147.897 35.8643 145.908 35.7114C143.372 35.5162 146.006 43.6148 146.591 44.8832C146.694 45.1051 146.809 45.399 146.937 45.7259C147.382 46.858 147.981 48.3857 148.738 48.6886C149.519 49.0008 150.039 48.5585 150.202 48.2983C150.202 48.2983 151.244 53.4377 152.251 56.3972C152.636 57.5293 153.324 59.8121 153.324 59.8121C153.324 59.8121 153.565 66.6421 152.251 70.8377C150.202 77.3789 134.2 82.8392 126.784 85.0834C119.369 87.3276 116.734 90.4499 114.392 94.6455C112.683 97.7084 112.013 105.295 111.397 112.281C111.169 114.865 110.948 117.367 110.685 119.527C110.288 122.779 109.827 128.61 109.36 134.511C108.679 143.128 107.986 151.894 107.465 152.994C107.115 153.733 106.842 155.044 106.524 156.577C106.043 158.892 105.458 161.712 104.342 163.825C102.859 166.635 99.5613 186.56 98.0977 195.439Z"
                    stroke="black"
                    stroke-width="0.390292"
                  />
                  <path
                    id="Vector 12_2"
                    d="M148.348 36.4922C148.348 36.4922 148.663 38.4856 149.068 40.0292C149.421 41.3787 149.893 42.8638 149.893 42.8638"
                    stroke="black"
                    stroke-width="0.315889"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </g>
                {findMeasure('shoulder_length') && (
                  <g id="shoulder_length">
                    <g id="Group 264">
                      <g id="Group 262">
                        <path
                          id="Vector 19_2"
                          d="M122.2 93.2987C122.2 93.2987 134.785 89.3833 142.59 88.0633C155.86 85.8193 166.106 85.8193 166.106 85.8193"
                          stroke="#E55959"
                          stroke-width="1.56117"
                          stroke-linecap="square"
                        />
                        <path
                          id="Vector 27"
                          d="M123.402 90.934L120.906 93.6736L123.945 94.9727"
                          stroke="#E55959"
                          stroke-width="1.26356"
                        />
                      </g>
                    </g>
                    <g id="Group 265">
                      <g id="Group 262_2">
                        <path
                          id="Vector 19_3"
                          d="M209.689 93.2982C209.689 93.2982 197.104 89.3828 189.298 88.0628C176.028 85.8188 165.783 85.8188 165.783 85.8188"
                          stroke="#E55959"
                          stroke-width="1.56117"
                          stroke-linecap="square"
                        />
                        <path
                          id="Vector 27_2"
                          d="M208.946 90.9335L211.441 93.6731L208.403 94.9722"
                          stroke="#E55959"
                          stroke-width="1.26356"
                        />
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_1908"
              x1="174.311"
              y1="-10.1818"
              x2="140.072"
              y2="57.7721"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_1908"
              x1="106.723"
              y1="176.73"
              x2="102.109"
              y2="203.279"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_1908"
              x1="140.386"
              y1="176.73"
              x2="135.772"
              y2="203.279"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_shoulder_length">
            <mask id="mask0_128_3681" maskUnits="userSpaceOnUse" x="0" y="0" width="342" height="291">
              <rect id="rect" width="342" height="291" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_3681)">
              <g id="group_shoulder_length_mask">
                <g id="female">
                  <g id="Group 227">
                    <path
                      id="Union"
                      d="M59 315.689H86.4624C86.4624 315.689 94.6272 293.176 96.202 287.428C99.3953 275.772 99.5549 270.982 99.3953 264.276C99.2675 258.912 106.048 223.402 109.454 206.318C110.572 209.192 112.839 215.323 112.967 216.856C113.095 218.389 114.83 228.033 115.681 232.663C116.746 238.198 119.002 249.939 119.513 252.621C120.024 255.303 120.471 267.363 120.631 273.058C120.471 275.559 119.545 282.127 117.118 288.386C114.691 294.645 109.72 309.196 107.538 315.689L170.115 315.075L232.716 315.689C230.534 309.196 225.563 294.645 223.136 288.386C220.709 282.127 219.783 275.56 219.623 273.058C219.783 267.364 220.23 255.304 220.741 252.621C221.252 249.939 223.508 238.198 224.573 232.663C225.424 228.033 227.159 218.389 227.287 216.856C227.415 215.323 229.682 209.192 230.8 206.318C234.206 223.402 240.986 258.912 240.859 264.277C240.699 270.983 240.859 275.773 244.052 287.428C245.627 293.176 253.791 317.445 253.791 317.445L282.212 317.126C279.817 302.597 273.302 270.312 270.876 265.714C269.051 262.256 268.092 257.642 267.306 253.854C266.785 251.346 266.339 249.199 265.766 247.991C264.913 246.19 263.779 231.847 262.664 217.746C261.901 208.09 261.146 198.547 260.497 193.226C260.066 189.691 259.705 185.598 259.332 181.37C258.323 169.938 257.227 157.523 254.43 152.511C250.598 145.645 246.287 140.536 234.153 136.864C222.018 133.192 195.833 124.256 192.48 113.553C190.329 106.687 190.883 95.5107 190.883 95.5107C190.883 95.5107 191.876 92.1105 192.48 89.9226C193.9 84.7764 195.833 76.6702 195.833 76.6702C196.099 77.0959 196.951 77.8197 198.228 77.3088C199.467 76.8132 200.448 74.3133 201.174 72.4607C201.384 71.9259 201.573 71.445 201.741 71.0819C202.699 69.0063 207.01 55.754 202.858 56.0734C199.605 56.3236 198.742 57.2072 198.708 57.6434C198.92 52.2558 199.121 40.5153 198.228 36.4346C197.11 31.3253 194.903 24.8231 188.329 19.9891C182.9 15.9975 176.354 14.0815 171.085 14.0815C164.219 13.4425 157.545 16.6679 151.925 19.9889C144.9 24.1402 143.143 31.3251 142.026 36.4344C141.133 40.5151 141.333 52.2556 141.546 57.6432C141.512 57.2069 140.649 56.3234 137.396 56.0731C133.244 55.7538 137.555 69.0061 138.513 71.0816C138.681 71.4448 138.869 71.9257 139.079 72.4605C139.806 74.3131 140.787 76.813 142.026 77.3086C143.303 77.8195 144.155 77.0957 144.421 76.6699C144.421 76.6699 146.126 85.0799 147.774 89.9226C148.404 91.7752 149.53 95.5107 149.53 95.5107C149.53 95.5107 149.924 106.687 147.774 113.553C144.421 124.256 118.236 133.191 106.101 136.864C93.9667 140.536 89.6557 145.645 85.8237 152.511C83.0263 157.523 81.9307 169.938 80.9219 181.37C80.5488 185.597 80.1875 189.691 79.7565 193.225C79.1075 198.547 78.353 208.09 77.5894 217.746C76.4745 231.846 75.3404 246.19 74.4875 247.991C73.915 249.199 73.4692 251.346 72.9482 253.854C72.1615 257.642 71.2032 262.255 69.3782 265.713C66.9513 270.312 61.395 301.159 59 315.689Z"
                      fill="white"
                    />
                    <path
                      id="Vector 19"
                      d="M149.422 58.7621C152.093 63.4516 158.055 67.9288 158.055 67.9288C158.055 67.9288 149.548 69.5751 145.739 66.8414C140.792 63.2903 142.529 39.6283 142.529 39.6283C142.529 39.6283 145.214 51.3758 149.422 58.7621Z"
                      fill="url(#paint0_linear_128_3681)"
                    />
                    <path
                      id="Vector 17"
                      d="M60.7565 306.269L82.1516 231.705L96.8408 268.747L94.2862 292.697L86.3029 316.008L59.1599 315.37L60.7565 306.269Z"
                      fill="url(#paint1_linear_128_3681)"
                    />
                    <path
                      id="Vector 18"
                      d="M111.051 305.47L137.236 231.705L226.649 299.882L233.833 317.286L106.58 316.966L111.051 305.47Z"
                      fill="url(#paint2_linear_128_3681)"
                    />
                    <path
                      id="Vector 14"
                      d="M185.184 193.227C185.184 193.227 185.87 197.559 186.749 200.915C187.517 203.848 188.542 207.076 188.542 207.076"
                      stroke="black"
                      stroke-width="0.51691"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 15"
                      d="M152.458 193.227C152.458 193.227 151.772 197.559 150.893 200.915C150.124 203.848 149.099 207.076 149.099 207.076"
                      stroke="black"
                      stroke-width="0.51691"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 20"
                      d="M170.201 263.638L170.201 274.134"
                      stroke="black"
                      stroke-width="0.51691"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 16"
                      d="M110.413 200.73C110.413 200.73 110.129 202.677 109.934 203.923C109.76 205.034 109.472 206.765 109.472 206.765"
                      stroke="black"
                      stroke-width="0.51691"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 17_2"
                      d="M229.666 200.57C229.666 200.57 229.95 202.518 230.145 203.764C230.319 204.875 230.8 206.318 230.8 206.318"
                      stroke="black"
                      stroke-width="0.51691"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 12"
                      d="M141.387 57.3508C141.387 57.3508 141.903 60.6128 142.565 63.1387C143.144 65.347 143.915 67.7772 143.915 67.7772"
                      stroke="black"
                      stroke-width="0.51691"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 13"
                      d="M198.892 57.1914C198.892 57.1914 198.375 60.4534 197.713 62.9793C197.135 65.1876 196.363 67.6177 196.363 67.6177"
                      stroke="black"
                      stroke-width="0.51691"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </g>
                  <path
                    id="Union_2"
                    d="M59.1597 317.445H86.1431C86.1431 317.445 94.6272 293.176 96.202 287.428C99.3953 275.772 99.5549 270.982 99.3953 264.276C99.2675 258.912 106.048 223.402 109.454 206.318C110.572 209.192 112.839 215.323 112.967 216.856C113.095 218.389 114.83 228.032 115.681 232.663C116.746 238.198 119.002 249.939 119.513 252.621C120.024 255.303 120.471 267.363 120.631 273.058C120.471 275.559 119.545 282.127 117.118 288.386C114.691 294.645 107.657 313.322 105.474 319.815L168.051 319.202L234.791 320C232.609 313.507 225.563 294.645 223.136 288.386C220.709 282.127 219.783 275.56 219.623 273.058C219.783 267.364 220.23 255.304 220.741 252.621C221.252 249.939 223.508 238.198 224.573 232.663C225.424 228.033 227.159 218.389 227.287 216.856C227.415 215.323 229.682 209.192 230.8 206.318C234.206 223.402 240.986 258.912 240.859 264.277C240.699 270.983 240.859 275.773 244.052 287.428C245.627 293.176 254.111 319.202 254.111 319.202L282.691 318.244C280.296 303.714 273.302 270.312 270.876 265.714C269.051 262.256 268.092 257.642 267.306 253.854C266.785 251.346 266.339 249.199 265.766 247.991C264.913 246.19 263.779 231.847 262.664 217.746C261.901 208.09 261.146 198.547 260.497 193.226C260.066 189.691 259.705 185.598 259.332 181.37C258.323 169.938 257.227 157.523 254.43 152.511C250.598 145.645 246.287 140.536 234.153 136.864C222.018 133.192 195.833 124.256 192.48 113.553C190.329 106.687 190.883 95.5107 190.883 95.5107C190.883 95.5107 191.876 92.1105 192.48 89.9226C193.9 84.7764 195.833 76.6702 195.833 76.6702C196.099 77.0959 196.951 77.8197 198.228 77.3088C199.467 76.8132 200.448 74.3133 201.174 72.4607C201.384 71.9259 201.573 71.445 201.741 71.0819C202.699 69.0063 207.01 55.754 202.858 56.0734C199.605 56.3236 198.742 57.2072 198.708 57.6434C198.92 52.2558 199.121 40.5153 198.228 36.4346C197.11 31.3253 194.903 24.8231 188.329 19.9891C182.9 15.9975 176.354 14.0815 171.085 14.0815C164.219 13.4425 157.545 16.6679 151.925 19.9889C144.9 24.1402 143.143 31.3251 142.026 36.4344C141.133 40.5151 141.333 52.2556 141.546 57.6432C141.512 57.2069 140.649 56.3234 137.396 56.0731C133.244 55.7538 137.555 69.0061 138.513 71.0816C138.681 71.4448 138.869 71.9257 139.079 72.4605C139.806 74.3131 140.787 76.813 142.026 77.3086C143.303 77.8195 144.155 77.0957 144.421 76.6699C144.421 76.6699 146.126 85.0799 147.774 89.9226C148.404 91.7752 149.53 95.5107 149.53 95.5107C149.53 95.5107 149.924 106.687 147.774 113.553C144.421 124.256 118.236 133.191 106.101 136.864C93.9667 140.536 89.6557 145.645 85.8237 152.511C83.0263 157.523 81.9307 169.938 80.9219 181.37C80.5488 185.597 80.1875 189.691 79.7565 193.225C79.1075 198.547 78.353 208.09 77.5894 217.746C76.4745 231.846 75.3404 246.19 74.4875 247.991C73.915 249.199 73.4692 251.346 72.9482 253.854C72.1615 257.642 71.2032 262.255 69.3782 265.713C66.9513 270.312 61.5546 302.916 59.1597 317.445Z"
                    stroke="black"
                    stroke-width="0.63866"
                  />
                  <path
                    id="Vector 12_2"
                    d="M141.387 57.3508C141.387 57.3508 141.903 60.6128 142.565 63.1387C143.144 65.347 143.915 67.7772 143.915 67.7772"
                    stroke="black"
                    stroke-width="0.51691"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </g>
                {findMeasure('shoulder_length') && (
                  <g id="shoulder_length">
                    <g id="Group 264">
                      <g id="Group 262">
                        <path
                          id="Vector 19_2"
                          d="M98.5989 150.307C98.5989 150.307 119.192 143.9 131.966 141.74C153.68 138.068 170.445 138.068 170.445 138.068"
                          stroke="#E55959"
                          stroke-width="2.55464"
                          stroke-linecap="square"
                        />
                        <path
                          id="Vector 27"
                          d="M100.566 146.438L96.4827 150.921L101.455 153.046"
                          stroke="#E55959"
                          stroke-width="2.06764"
                        />
                      </g>
                    </g>
                    <g id="Group 265">
                      <g id="Group 262_2">
                        <path
                          id="Vector 19_3"
                          d="M241.764 150.306C241.764 150.306 221.17 143.899 208.397 141.739C186.683 138.067 169.918 138.067 169.918 138.067"
                          stroke="#E55959"
                          stroke-width="2.55464"
                          stroke-linecap="square"
                        />
                        <path
                          id="Vector 27_2"
                          d="M240.547 146.437L244.631 150.92L239.659 153.045"
                          stroke="#E55959"
                          stroke-width="2.06764"
                        />
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_3681"
              x1="183.872"
              y1="-19.0246"
              x2="127.845"
              y2="92.1726"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_3681"
              x1="73.2739"
              y1="286.831"
              x2="65.7242"
              y2="330.275"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_3681"
              x1="128.358"
              y1="286.831"
              x2="120.809"
              y2="330.275"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
