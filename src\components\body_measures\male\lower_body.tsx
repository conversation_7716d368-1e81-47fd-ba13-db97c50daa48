import { useDevice } from '@/hooks/use-device';
import { BodyMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface LowerBodyProps {
  measure: BodyMeasure;
  className?: string;
}

export function LowerBody({ measure, className }: LowerBodyProps) {
  const { measures } = measure;
  const mappedMeasures = measures.map((item) => item.measure);
  const { isMobile } = useDevice();

  const findMeasure = (measure: string) => {
    const foundMeasure = mappedMeasures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full flex justify-center', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_lower_body">
            <mask id="mask0_128_1266" maskUnits="userSpaceOnUse" x="0" y="0" width="331" height="193">
              <rect id="rect" width="331" height="193" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_1266)">
              <g id="group_lower_body_mask">
                <g id="male">
                  <g id="Group 20">
                    <path
                      id="Vector 46"
                      d="M187.395 96.4655C188.117 93.457 190.253 81.7238 190.854 75.406L180.475 69.2385H173.856L167.388 74.6538C167.689 81.5734 168.291 91.6519 168.892 96.4655C169.118 98.2706 170.241 105.146 170.397 109.552C170.726 118.89 169.644 123.392 170.397 131.063C170.812 135.298 171.6 141.443 172.502 145.203C173.405 148.964 175.361 160.246 175.661 162.803C175.962 165.36 175.361 167.767 175.06 169.121C174.759 170.475 175.661 172.881 176.113 173.784C176.564 174.686 176.263 177.545 176.564 179.35C176.805 180.794 177.466 181.456 177.767 181.606C177.868 181.756 178.068 182.298 178.068 183.261C178.068 184.464 179.422 185.517 179.572 185.968C179.723 186.42 181.077 187.473 183.032 187.924C184.597 188.285 184.887 187.272 184.837 186.72C184.887 186.821 185.198 187.142 186.041 187.623C186.883 188.104 187.695 187.623 187.996 187.322C188.097 187.422 188.538 187.593 189.5 187.473C190.463 187.352 190.604 187.021 190.553 186.871C190.654 186.871 191.005 186.841 191.606 186.72C192.208 186.6 192.359 186.169 192.359 185.968C192.459 186.019 192.75 185.938 193.111 185.216C193.562 184.314 191.456 181.606 191.155 181.004C190.854 180.403 186.041 174.536 185.89 174.386C185.74 174.235 185.138 173.032 185.138 171.828C185.138 170.625 183.784 167.316 183.784 162.803C183.784 158.29 187.395 139.337 188.448 133.169C189.501 127.002 187.395 118.578 186.492 114.366C185.589 110.154 186.492 100.226 187.395 96.4655Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.30085"
                    />
                    <g id="Group 223">
                      <path
                        id="Vector 12"
                        d="M183.333 106.544C183.333 106.544 182.588 107.594 181.911 108.293C181.319 108.905 180.625 109.553 180.625 109.553"
                        stroke="black"
                        stroke-width="0.266982"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        id="Vector 41"
                        d="M173.104 106.544C173.104 106.544 173.849 107.594 174.526 108.293C175.117 108.905 175.812 109.553 175.812 109.553"
                        stroke="black"
                        stroke-width="0.266982"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </g>
                    <path
                      id="Vector 43"
                      d="M143.922 96.4655C143.2 93.457 141.064 81.7238 140.462 75.406L150.841 69.2385H157.46L163.928 74.6538C163.628 81.5734 163.026 91.6519 162.424 96.4655C162.199 98.2706 161.075 105.146 160.92 109.552C160.591 118.89 161.672 123.392 160.92 131.063C160.505 135.298 159.716 141.443 158.814 145.203C157.911 148.964 155.956 160.246 155.655 162.803C155.354 165.36 155.956 167.767 156.257 169.121C156.558 170.475 155.655 172.881 155.204 173.784C154.752 174.686 155.053 177.545 154.752 179.35C154.512 180.794 153.85 181.456 153.549 181.606C153.449 181.756 153.248 182.298 153.248 183.261C153.248 184.464 151.894 185.517 151.744 185.968C151.594 186.42 150.24 187.473 148.284 187.924C146.72 188.285 146.429 187.272 146.479 186.72C146.429 186.821 146.118 187.142 145.276 187.623C144.433 188.104 143.621 187.623 143.32 187.322C143.22 187.422 142.779 187.593 141.816 187.473C140.853 187.352 140.713 187.021 140.763 186.871C140.663 186.871 140.312 186.841 139.71 186.72C139.108 186.6 138.958 186.169 138.958 185.968C138.858 186.019 138.567 185.938 138.206 185.216C137.754 184.314 139.86 181.606 140.161 181.004C140.462 180.403 145.276 174.536 145.426 174.386C145.577 174.235 146.178 173.032 146.178 171.828C146.178 170.625 147.532 167.316 147.532 162.803C147.532 158.29 143.922 139.337 142.869 133.169C141.816 127.002 143.922 118.578 144.824 114.366C145.727 110.154 144.824 100.226 143.922 96.4655Z"
                      fill="#F4F2F2"
                      stroke="black"
                      stroke-width="0.30085"
                    />
                    <path
                      id="Union"
                      d="M157.152 -57.4427V-48.9961L153.129 -46.9851L149.778 -45.5103L144.683 -43.097L141.331 -41.354L137.845 -39.343L134.762 -37.8682L133.019 -36.6615L131.41 -35.1867L130.069 -33.3097L128.594 -30.8964L127.388 -27.6787L126.985 -24.5951L126.717 -22.0477V-18.4278L127.119 -15.8804L125.377 -4.08209L125.108 -0.328079L123.097 6.77773L122.695 10.1295L121.623 13.8835L120.818 19.2464L120.282 22.0619L120.148 27.2907V32.7877L120.282 36.6757L120.148 40.832L120.416 45.7926L120.684 47.2674L120.014 54.5073L120.148 56.3843L120.55 58.2613L121.22 63.6242L123.231 66.8419L125.913 70.3278L126.851 70.9981L127.254 70.73L127.119 69.7915C127.075 69.7021 127.012 69.4965 127.119 69.3893C127.254 69.2552 127.656 68.7189 127.656 68.5848V67.7804L125.242 63.6242L124.706 62.4175V58.9317L125.377 55.3117L127.656 53.0325L129.533 58.1272L130.739 60.1383H131.276L132.616 58.9317C131.901 56.7418 130.444 52.2281 130.337 51.6918C130.314 51.5768 130.291 51.4657 130.269 51.3579C130.161 50.8374 130.069 50.3931 130.069 49.9488C130.069 49.4126 129.399 46.597 129.265 46.3289C129.157 46.1144 128.416 44.8094 128.058 44.1838L127.119 43.1112C127.075 42.3961 126.985 40.9392 126.985 40.832C126.985 40.7247 127.164 38.9103 127.254 38.0165L128.192 32.7877L129.533 28.8996L132.616 21.5256L133.689 17.9057C134.136 15.4924 134.627 10.639 134.627 10.5317C134.627 10.4245 134.896 8.3419 135.03 7.31401L135.298 4.90072L138.113 -7.70203L138.903 -10.7857L140.124 -6.62945L141.331 -1.93694L142.806 2.75557L143.476 6.10737L143.744 8.7888L143.208 12.945L142.538 20.319L142.672 22.8663L142.538 25.95L141.599 35.2009L139.99 51.5577L139.588 58.6635L139.722 62.1494L140.393 75.4225L163.778 75.1053L165.062 58.9317H166.134L167.538 75.2557L190.804 75.4225L191.474 62.1494L191.608 58.6635L191.206 51.5577L189.597 35.2009L188.658 25.95L188.524 22.8663L188.658 20.319L187.988 12.945L187.452 8.7888L187.72 6.10737L188.39 2.75557L189.865 -1.93694L191.072 -6.62945L192.293 -10.7857L193.083 -7.70203L195.898 4.90072L196.166 7.31401C196.301 8.3419 196.569 10.4245 196.569 10.5317C196.569 10.639 197.06 15.4924 197.507 17.9057L198.58 21.5256L201.663 28.8996L203.004 32.7877L203.943 38.0165C204.032 38.9103 204.211 40.7247 204.211 40.832C204.211 40.9392 204.121 42.3961 204.077 43.1112L203.138 44.1838C202.781 44.8094 202.039 46.1144 201.932 46.3289C201.797 46.597 201.127 49.4126 201.127 49.9488C201.127 50.3931 201.035 50.8374 200.927 51.3579C200.905 51.4657 200.882 51.5768 200.859 51.6918C200.752 52.2281 199.295 56.7418 198.58 58.9317L199.92 60.1383H200.457L201.663 58.1272L203.54 53.0325L205.82 55.3117L206.49 58.9317V62.4175L205.954 63.6242L203.54 67.7804V68.5848C203.54 68.7189 203.943 69.2552 204.077 69.3893C204.184 69.4965 204.121 69.7021 204.077 69.7915L203.943 70.73L204.345 70.9981L205.283 70.3278L207.965 66.8419L209.976 63.6242L210.646 58.2613L211.048 56.3843L211.182 54.5073L210.512 47.2674L210.78 45.7926L211.048 40.832L210.914 36.6757L211.048 32.7877V27.2907L210.914 22.0619L210.378 19.2464L209.574 13.8835L208.501 10.1295L208.099 6.77773L206.088 -0.328079L205.82 -4.08209L204.077 -15.8804L204.479 -18.4278V-22.0477L204.211 -24.5951L203.809 -27.6787L202.602 -30.8964L201.127 -33.3097L199.786 -35.1867L198.178 -36.6615L196.435 -37.8682L193.351 -39.343L189.865 -41.354L186.513 -43.097L181.419 -45.5103L178.067 -46.9851L174.045 -48.9961V-57.4427L167.341 -67.4098V-72.5928L165.598 -70.0013L163.855 -72.5928V-67.4098L157.152 -57.4427Z"
                      fill="white"
                    />
                    <path
                      id="Vector 20"
                      d="M135.418 4.36437C135.418 4.36437 134.659 7.85023 135.15 6.77766L134.614 11.8724C134.48 13.1684 133.944 16.4308 133.944 16.4308C133.944 16.4308 133.586 18.6207 133.139 20.0508L130.592 26.3521C130.592 26.3521 128.715 30.7765 128.715 30.9106C128.715 31.2898 127.776 35.5137 127.374 36.5416L126.972 40.4297L127.106 43.1111L128.045 44.4518C128.179 44.8987 128.983 45.9266 128.983 45.9266C128.983 45.9266 130.056 49.4125 130.056 49.6806C130.056 49.8951 130.324 51.066 130.324 51.6917L130.994 53.5687C131.53 55.982 132.683 58.6366 132.469 59.0657C132.201 59.6019 131.709 59.9595 131.53 60.0042L130.86 60.2723L130.19 59.4679L129.653 58.5294C129.296 56.6971 127.642 52.8983 127.642 52.8983L125.363 55.0435L124.559 58.5294V62.2834L126.033 64.9648C126.659 65.9927 127.776 67.968 127.776 68.1825C127.776 68.397 127.106 69.2551 127.106 69.2551L126.972 70.864H126.704L124.559 68.5847L122.548 65.9033L121.341 63.6241L120.402 57.5909L120.134 55.7139V52.7643L120.402 49.2784L120.671 47.2673L120.134 42.0385V38.4186V31.1787C120 28.7654 120.402 21.9814 120.402 21.6596C120.402 21.3379 121.118 15.6264 121.743 13.3472C122.011 12.3193 124.559 1.14665 124.559 1.14665L126.168 -9.17688C127.195 -4.75251 135.311 3.15772 135.418 3.69401C135.553 4.36437 135.418 4.36437 135.418 4.36437Z"
                      fill="url(#paint0_linear_128_1266)"
                    />
                    <path
                      id="Vector 17"
                      d="M161.295 55.848L163.842 58.5295L165.183 59.0657L164.244 68.7189L163.929 75.2557L140.379 75.5566L139.709 63.4901L139.575 57.8591L139.843 53.5688L140.379 46.9993L141.184 38.0165L142.524 27.0226L158.613 50.0829L159.954 52.6303C161.429 55.9374 161.295 55.848 161.295 55.848Z"
                      fill="url(#paint1_linear_128_1266)"
                    />
                    <g id="Group 12">
                      <path
                        id="Vector 35"
                        d="M174.045 -57.845V-48.8622C178.246 -46.9852 187.667 -42.6412 191.742 -40.2816C196.837 -37.332 200.457 -36.3935 203.004 -29.958C205.042 -24.8097 204.613 -18.0256 204.211 -15.2101L206.222 -0.194098C207.071 2.04043 207.972 7.133 208.501 9.325C209.44 13.2131 210.512 16.4308 211.049 25.1455C211.457 31.7863 210.781 36.4969 211.049 38.5526C211.049 39.4018 211.049 41.3413 211.049 42.3067C211.049 43.272 210.691 45.9266 210.512 47.1332C210.87 49.77 211.478 55.3116 211.049 56.3842C210.512 57.7249 210.647 62.1493 209.574 64.2944C208.501 66.4396 204.613 71.5343 204.211 70.864C203.889 70.3277 204.077 69.5679 204.211 69.2551C203.943 69.1657 203.433 68.8261 203.541 68.1825C203.648 67.539 205.641 64.071 206.624 62.4174V58.9316L205.82 55.1776L203.541 52.8983C202.781 55.2669 201.074 60.031 200.323 60.1382C199.572 60.2455 198.938 59.3785 198.714 58.9316C199.474 56.2948 201.02 50.9141 201.127 50.485C201.261 49.9488 200.993 49.2784 201.932 46.731C202.683 44.6931 203.764 43.4686 204.211 43.1111C204.3 41.5022 204.184 37.2387 203.004 33.0557C201.53 27.8269 199.653 24.7433 198.044 19.6485C196.757 15.5727 196.211 8.07367 195.899 4.90063L192.374 -11.0539L192.949 -14.2716C192.636 -11.6349 191.287 -6.01281 189.463 -0.864455C187.64 4.2839 187.452 8.07367 187.586 9.325C187.988 12.5427 188.766 19.4876 188.659 21.5255C188.551 23.5634 188.614 25.503 188.659 26.2181L189.463 33.0557C189.999 38.0164 191.152 49.3857 191.474 55.1776C191.796 60.9695 191.212 71.2215 190.855 75.5565L167.388 74.8043L166.135 58.9316H165.33"
                        stroke="black"
                        stroke-width="0.268144"
                      />
                      <path
                        id="Vector 27"
                        d="M157.151 -57.845V-48.8622C152.951 -46.9852 143.53 -42.6412 139.454 -40.2816C134.359 -37.332 130.739 -36.3935 128.192 -29.958C126.154 -24.8097 126.583 -18.0256 126.985 -15.2101L124.974 -0.194098C124.125 2.04043 123.224 7.133 122.695 9.325C121.756 13.2131 120.684 16.4308 120.148 25.1455C119.739 31.7863 120.416 36.4969 120.148 38.5526C120.148 39.4018 120.148 41.3413 120.148 42.3067C120.148 43.272 120.505 45.9266 120.684 47.1332C120.326 49.77 119.719 55.3116 120.148 56.3842C120.684 57.7249 120.55 62.1493 121.622 64.2944C122.695 66.4396 126.583 71.5343 126.985 70.864C127.307 70.3277 127.119 69.5679 126.985 69.2551C127.253 69.1657 127.763 68.8261 127.656 68.1825C127.548 67.539 125.555 64.071 124.572 62.4174V58.9316L125.376 55.1776L127.656 52.8983C128.415 55.2669 130.122 60.031 130.873 60.1382C131.624 60.2455 132.259 59.3785 132.482 58.9316C131.722 56.2948 130.176 50.9141 130.069 50.485C129.935 49.9488 130.203 49.2784 129.264 46.731C128.514 44.6931 127.432 43.4686 126.985 43.1111C126.896 41.5022 127.012 37.2387 128.192 33.0557C129.667 27.8269 131.544 24.7433 133.153 19.6485C134.44 15.5727 134.985 8.07367 135.298 4.90063L138.822 -11.0539L138.247 -14.2716C138.56 -11.6349 139.91 -6.01281 141.733 -0.864455C143.556 4.2839 143.744 8.07367 143.61 9.325C143.208 12.5427 142.43 19.4876 142.538 21.5255C142.645 23.5634 142.582 25.503 142.538 26.2181L141.733 33.0557C141.197 38.0164 140.044 49.3857 139.722 55.1776C139.4 60.9695 140.104 71.2215 140.462 75.5565L163.877 75.4224L165.062 58.9316H165.866"
                        stroke="black"
                        stroke-width="0.268144"
                      />
                      <path
                        id="Vector 44"
                        d="M163.627 77.0608L140.913 77.9633C140.913 77.9633 140.763 77.2112 140.612 76.0078C140.547 75.4832 140.462 73.7514 140.462 73.7514L141.515 72.8489L163.778 74.9548L163.627 77.0608Z"
                        fill="#F4F2F2"
                      />
                    </g>
                    <g id="Group 19">
                      <g id="Group 18">
                        <g id="Group 15">
                          <path
                            id="Vector 15"
                            d="M131.276 2.35315C131.276 2.35315 131.41 3.55979 131.812 4.09608C132.214 4.63237 132.751 4.76644 132.751 4.76644"
                            stroke="black"
                            stroke-width="0.268144"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <path
                            id="Vector 33"
                            d="M200.859 2.35327C200.859 2.35327 200.725 3.55992 200.323 4.0962C199.92 4.63249 199.384 4.76656 199.384 4.76656"
                            stroke="black"
                            stroke-width="0.268144"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </g>
                        <g id="Group 16">
                          <path
                            id="Vector 11"
                            d="M165.712 17.6739C165.732 17.6027 165.691 17.5286 165.62 17.5084C165.549 17.4883 165.474 17.5297 165.454 17.601L165.712 17.6739ZM165.454 17.601C165.404 17.7776 165.41 18.0352 165.439 18.314C165.469 18.5985 165.526 18.9251 165.591 19.2505C165.657 19.5763 165.733 19.9035 165.8 20.1895C165.868 20.477 165.926 20.72 165.959 20.8822L166.222 20.8282C166.188 20.6612 166.128 20.4122 166.061 20.1282C165.994 19.8427 165.919 19.5188 165.854 19.1973C165.789 18.8753 165.734 18.5583 165.706 18.2862C165.677 18.0084 165.678 17.7965 165.712 17.6739L165.454 17.601ZM165.959 20.8822C166.092 21.5253 166.124 22.7164 165.96 23.4427L166.222 23.5019C166.396 22.732 166.361 21.5017 166.222 20.8282L165.959 20.8822ZM165.96 23.4427C165.921 23.6155 165.867 23.6561 165.846 23.6653C165.825 23.6746 165.784 23.6754 165.712 23.6355C165.643 23.5977 165.574 23.5378 165.52 23.4829C165.493 23.4562 165.471 23.4322 165.457 23.415C165.449 23.4064 165.443 23.3996 165.44 23.3951C165.438 23.3928 165.436 23.3912 165.436 23.3901C165.435 23.3896 165.435 23.3893 165.435 23.3891C165.435 23.389 165.435 23.389 165.435 23.389C165.435 23.389 165.435 23.389 165.435 23.389C165.435 23.389 165.435 23.389 165.435 23.389C165.435 23.3891 165.435 23.3891 165.33 23.4723C165.224 23.5555 165.224 23.5555 165.225 23.5556C165.225 23.5556 165.225 23.5556 165.225 23.5556C165.225 23.5557 165.225 23.5558 165.225 23.5559C165.225 23.5561 165.225 23.5563 165.225 23.5565C165.226 23.5571 165.226 23.5578 165.227 23.5587C165.228 23.5604 165.23 23.5629 165.233 23.5659C165.238 23.572 165.245 23.5805 165.254 23.5909C165.272 23.6117 165.298 23.6403 165.33 23.672C165.391 23.7341 165.481 23.8144 165.582 23.8701C165.679 23.9237 165.817 23.9713 165.955 23.9105C166.093 23.8495 166.176 23.7031 166.222 23.5019L165.96 23.4427Z"
                            fill="black"
                          />
                          <path
                            id="Vector 38"
                            d="M157.419 48.2058C157.419 48.2058 158.983 50.1668 160.369 53.5687C162.591 59.0216 165.218 59.4762 166.939 58.7975C168.659 58.1188 170.809 54.9511 171.497 53.2166"
                            stroke="black"
                            stroke-width="0.268144"
                            stroke-linecap="round"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                  <path
                    id="Vector 44_2"
                    d="M167.99 78.8659L190.403 77.9633C190.403 77.9633 190.554 77.2112 190.704 76.0078C190.77 75.4832 190.854 73.7514 190.854 73.7514L189.651 72.8489L167.388 72.9993C167.388 72.9993 167.412 74.5009 167.539 75.707C167.839 78.565 167.99 78.8659 167.99 78.8659Z"
                    fill="white"
                  />
                  <g id="Group 223_2">
                    <path
                      id="Vector 12_2"
                      d="M148.435 106.544C148.435 106.544 149.179 107.594 149.856 108.293C150.448 108.905 151.142 109.553 151.142 109.553"
                      stroke="black"
                      stroke-width="0.266982"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 41_2"
                      d="M158.664 106.544C158.664 106.544 157.919 107.594 157.242 108.293C156.65 108.905 155.956 109.553 155.956 109.553"
                      stroke="black"
                      stroke-width="0.266982"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </g>
                </g>
                {findMeasure('lower_length') && (
                  <g id="lower_length">
                    <path
                      id="Vector 19"
                      d="M195.015 130.733L195.015 15.2338"
                      stroke="#E55959"
                      stroke-width="1.50167"
                      stroke-linecap="square"
                    />
                    <path
                      id="Vector 29"
                      d="M139 14.8696L195.26 14.8696"
                      stroke="#E55959"
                      stroke-width="1.00112"
                      stroke-linecap="square"
                      stroke-dasharray="3.34 3.34"
                    />
                    <path
                      id="Vector 28"
                      d="M197.865 128.918L194.999 131.73L192.513 128.834"
                      stroke="#E55959"
                      stroke-width="1.50167"
                    />
                  </g>
                )}
                {findMeasure('insideLeg') && (
                  <g id="inside_leg">
                    <path
                      id="Vector 29_2"
                      d="M139 15L195 15"
                      stroke="#E55959"
                      stroke-linecap="square"
                      stroke-dasharray="3.39 3.39"
                    />
                    <g id="Group 225">
                      <path
                        id="Vector 19_2"
                        d="M165.314 148.621L165.314 15.5085"
                        stroke="#E55959"
                        stroke-width="1.5"
                        stroke-linecap="square"
                      />
                      <path
                        id="Vector 28_2"
                        d="M168.205 146.779L165.297 149.633L162.774 146.694"
                        stroke="#E55959"
                        stroke-width="1.5"
                      />
                    </g>
                  </g>
                )}
                {findMeasure('thigh') && (
                  <g id="thigh">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M191.5 69.5C191.5 69.5 190 69.5 178.884 70.3996M167 69.5793C167 69.5793 170.026 69.9499 175.784 70.3996"
                            stroke="#E55959"
                          />
                          <path
                            id="Vector 27_2"
                            d="M180.226 69.0153L178.279 70.3239L179.952 71.5802"
                            stroke="#E55959"
                          />
                          <path
                            id="Vector 28_3"
                            d="M174.091 69.0153L176.038 70.3239L174.366 71.5802"
                            stroke="#E55959"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('hip') && (
                  <g id="hip">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M191 49C191 49 183.526 51.6316 163.337 52.4885M140 49C140 49 144.158 50.7895 160.696 52.4885"
                            stroke="#E55959"
                          />
                          <path id="Vector 27_3" d="M164 51L162.822 52.4238L164 54" stroke="#E55959" />
                          <path id="Vector 28_4" d="M159.5 51.0001L160.912 52.4239L159.5 54" stroke="#E55959" />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_1266"
              x1="133.944"
              y1="-8.50652"
              x2="124.425"
              y2="68.1825"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_1266"
              x1="153.787"
              y1="36.6758"
              x2="143.463"
              y2="96.2036"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className=" h-full m-auto "
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_lower_body">
            <mask id="mask0_128_2746" maskUnits="userSpaceOnUse" x="0" y="0" width="342" height="291">
              <rect id="rect" width="342" height="291" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_2746)">
              <g id="group_lower_body_mask">
                <g id="male">
                  <g id="Group 20">
                    <path
                      id="Vector 46"
                      d="M203.359 145.037C204.424 140.601 207.573 123.301 208.46 113.985L193.157 104.892H183.398L173.86 112.876C174.304 123.079 175.191 137.939 176.078 145.037C176.411 147.698 178.067 157.835 178.296 164.333C178.781 178.101 177.187 184.738 178.296 196.049C178.908 202.293 180.071 211.353 181.401 216.898C182.732 222.443 185.616 239.078 186.059 242.848C186.503 246.619 185.616 250.167 185.172 252.163C184.728 254.16 186.059 257.708 186.725 259.039C187.39 260.37 186.946 264.584 187.39 267.245C187.745 269.375 188.721 270.351 189.164 270.572C189.312 270.794 189.608 271.593 189.608 273.012C189.608 274.786 191.604 276.339 191.826 277.004C192.048 277.67 194.044 279.222 196.927 279.888C199.234 280.42 199.663 278.927 199.589 278.113C199.663 278.261 200.121 278.734 201.363 279.444C202.605 280.154 203.803 279.444 204.246 279.001C204.394 279.148 205.045 279.4 206.464 279.222C207.884 279.045 208.091 278.557 208.017 278.335C208.165 278.335 208.682 278.291 209.569 278.113C210.457 277.936 210.678 277.3 210.678 277.004C210.826 277.078 211.255 276.96 211.787 275.895C212.453 274.565 209.348 270.572 208.904 269.685C208.46 268.798 201.363 260.148 201.141 259.926C200.919 259.704 200.032 257.93 200.032 256.156C200.032 254.381 198.036 249.502 198.036 242.848C198.036 236.194 203.359 208.248 204.912 199.155C206.464 190.061 203.359 177.64 202.028 171.43C200.698 165.22 202.028 150.582 203.359 145.037Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.443589"
                    />
                    <g id="Group 223">
                      <path
                        id="Vector 12"
                        d="M197.37 159.897C197.37 159.897 196.272 161.444 195.274 162.476C194.402 163.378 193.378 164.333 193.378 164.333"
                        stroke="black"
                        stroke-width="0.393653"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        id="Vector 41"
                        d="M182.289 159.897C182.289 159.897 183.386 161.444 184.385 162.476C185.257 163.378 186.281 164.333 186.281 164.333"
                        stroke="black"
                        stroke-width="0.393653"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </g>
                    <path
                      id="Vector 43"
                      d="M139.261 145.037C138.196 140.601 135.047 123.301 134.16 113.985L149.464 104.892H159.223L168.76 112.876C168.316 123.079 167.429 137.939 166.542 145.037C166.209 147.698 164.553 157.835 164.324 164.333C163.839 178.101 165.433 184.738 164.324 196.049C163.712 202.293 162.549 211.353 161.219 216.898C159.888 222.443 157.005 239.078 156.561 242.848C156.117 246.619 157.005 250.167 157.448 252.163C157.892 254.16 156.561 257.708 155.896 259.039C155.23 260.37 155.674 264.584 155.23 267.245C154.875 269.375 153.899 270.351 153.456 270.572C153.308 270.794 153.012 271.593 153.012 273.012C153.012 274.786 151.016 276.339 150.794 277.004C150.573 277.67 148.576 279.222 145.693 279.888C143.386 280.42 142.958 278.927 143.032 278.113C142.958 278.261 142.499 278.734 141.257 279.444C140.015 280.154 138.817 279.444 138.374 279.001C138.226 279.148 137.575 279.4 136.156 279.222C134.736 279.045 134.529 278.557 134.603 278.335C134.455 278.335 133.938 278.291 133.051 278.113C132.164 277.936 131.942 277.3 131.942 277.004C131.794 277.078 131.365 276.96 130.833 275.895C130.167 274.565 133.273 270.572 133.716 269.685C134.16 268.798 141.257 260.148 141.479 259.926C141.701 259.704 142.588 257.93 142.588 256.156C142.588 254.381 144.584 249.502 144.584 242.848C144.584 236.194 139.261 208.248 137.708 199.155C136.156 190.061 139.261 177.64 140.592 171.43C141.923 165.22 140.592 150.582 139.261 145.037Z"
                      fill="#F4F2F2"
                      stroke="black"
                      stroke-width="0.443589"
                    />
                    <path
                      id="Union"
                      d="M158.768 -81.8936V-69.4396L152.837 -66.4743L147.895 -64.2998L140.383 -60.7415L135.441 -58.1717L130.302 -55.2064L125.755 -53.0319L123.185 -51.2528L120.813 -49.0783L118.836 -46.3107L116.661 -42.7524L114.882 -38.0081L114.289 -33.4614L113.894 -29.7054V-24.368L114.487 -20.612L111.917 -3.21594L111.522 2.31917L108.556 12.7963L107.963 17.7384L106.382 23.2735L105.196 31.1808L104.405 35.3322L104.207 43.0418V51.1467L104.405 56.8795L104.207 63.0077L104.603 70.3219L104.998 72.4965L104.01 83.1713L104.207 85.9389L104.8 88.7064L105.789 96.6137L108.754 101.358L112.708 106.498L114.092 107.486L114.685 107.091L114.487 105.707C114.421 105.575 114.329 105.272 114.487 105.114C114.685 104.916 115.278 104.126 115.278 103.928V102.742L111.719 96.6137L110.929 94.8346V89.6948L111.917 84.3574L115.278 80.9968L118.045 88.5087L119.824 91.474H120.615L122.592 89.6948C121.538 86.466 119.389 79.8107 119.231 79.02C119.197 78.8504 119.163 78.6866 119.131 78.5277C118.972 77.7602 118.836 77.1052 118.836 76.4501C118.836 75.6594 117.848 71.508 117.65 71.1127C117.492 70.7964 116.398 68.8723 115.871 67.9498L114.487 66.3683C114.421 65.314 114.289 63.1658 114.289 63.0077C114.289 62.8495 114.553 60.1742 114.685 58.8564L116.068 51.1467L118.045 45.4139L122.592 34.5414L124.173 29.204C124.832 25.6457 125.557 18.4896 125.557 18.3315C125.557 18.1733 125.953 15.1026 126.15 13.5871L126.546 10.0288L130.697 -8.55337L131.862 -13.1001L133.662 -6.97191L135.441 -0.0530243L137.616 6.86586L138.604 11.8079L139 15.7616L138.209 21.8897L137.22 32.7623L137.418 36.5182L137.22 41.0649L135.837 54.705L133.464 78.8223L132.871 89.2995L133.069 94.4392L134.057 114.01L168.538 113.542L170.431 89.6948H172.013L174.083 113.764L208.386 114.01L209.375 94.4392L209.572 89.2995L208.979 78.8223L206.607 54.705L205.223 41.0649L205.025 36.5182L205.223 32.7623L204.235 21.8897L203.444 15.7616L203.839 11.8079L204.828 6.86586L207.002 -0.0530243L208.781 -6.97191L210.582 -13.1001L211.747 -8.55337L215.898 10.0288L216.293 13.5871C216.491 15.1026 216.886 18.1733 216.886 18.3315C216.886 18.4896 217.611 25.6457 218.27 29.204L219.852 34.5414L224.398 45.4139L226.375 51.1467L227.759 58.8564C227.891 60.1742 228.154 62.8495 228.154 63.0077C228.154 63.1658 228.023 65.314 227.957 66.3683L226.573 67.9498C226.046 68.8723 224.952 70.7964 224.794 71.1127C224.596 71.508 223.608 75.6594 223.608 76.4501C223.608 77.1052 223.472 77.7602 223.313 78.5277C223.28 78.6866 223.246 78.8504 223.212 79.02C223.054 79.8107 220.906 86.466 219.852 89.6948L221.829 91.474H222.619L224.398 88.5087L227.166 80.9968L230.527 84.3574L231.515 89.6948V94.8346L230.724 96.6137L227.166 102.742V103.928C227.166 104.126 227.759 104.916 227.957 105.114C228.115 105.272 228.023 105.575 227.957 105.707L227.759 107.091L228.352 107.486L229.736 106.498L233.689 101.358L236.655 96.6137L237.643 88.7064L238.236 85.9389L238.434 83.1713L237.445 72.4965L237.841 70.3219L238.236 63.0077L238.038 56.8795L238.236 51.1467V43.0418L238.038 35.3322L237.248 31.1808L236.062 23.2735L234.48 17.7384L233.887 12.7963L230.922 2.31917L230.527 -3.21594L227.957 -20.612L228.55 -24.368V-29.7054L228.154 -33.4614L227.561 -38.0081L225.782 -42.7524L223.608 -46.3107L221.631 -49.0783L219.259 -51.2528L216.689 -53.0319L212.142 -55.2064L207.002 -58.1717L202.06 -60.7415L194.548 -64.2998L189.606 -66.4743L183.676 -69.4396V-81.8936L173.792 -96.5897V-104.232L171.222 -100.411L168.652 -104.232V-96.5897L158.768 -81.8936Z"
                      fill="white"
                    />
                    <path
                      id="Vector 20"
                      d="M126.723 9.23802C126.723 9.23802 125.603 14.3778 126.328 12.7963L125.537 20.3082C125.339 22.2192 124.549 27.0294 124.549 27.0294C124.549 27.0294 124.022 30.2583 123.363 32.3669L119.607 41.6579C119.607 41.6579 116.839 48.1815 116.839 48.3791C116.839 48.9383 115.455 55.1662 114.862 56.6818L114.269 62.4146L114.467 66.3682L115.851 68.3451C116.048 69.004 117.234 70.5196 117.234 70.5196C117.234 70.5196 118.816 75.6593 118.816 76.0547C118.816 76.371 119.211 78.0974 119.211 79.0199L120.2 81.7875C120.99 85.3458 122.69 89.2599 122.374 89.8925C121.979 90.6832 121.254 91.2103 120.99 91.2762L120.002 91.6716L119.014 90.4855L118.223 89.1017C117.696 86.4001 115.258 80.7991 115.258 80.7991L111.897 83.962L110.711 89.1017V94.6368L112.885 98.5905C113.808 100.106 115.455 103.019 115.455 103.335C115.455 103.651 114.467 104.916 114.467 104.916L114.269 107.289H113.874L110.711 103.928L107.746 99.9743L105.967 96.6137L104.583 87.7179L104.187 84.9504V80.6014L104.583 75.4616L104.978 72.4964L104.187 64.7868V59.4494V48.7745C103.99 45.2162 104.583 35.2135 104.583 34.7391C104.583 34.2646 105.637 25.8433 106.56 22.4827C106.955 20.9672 110.711 4.49364 110.711 4.49364L113.083 -10.7279C114.599 -4.20438 126.565 7.45888 126.723 8.24961C126.921 9.23802 126.723 9.23802 126.723 9.23802Z"
                      fill="url(#paint0_linear_128_2746)"
                    />
                    <path
                      id="Vector 17"
                      d="M164.876 85.1482L168.632 89.1019L170.608 89.8926L169.225 104.126L168.759 113.764L134.037 114.208L133.049 96.4161L132.851 88.1134L133.246 81.7876L134.037 72.1012L135.223 58.8564L137.2 42.6465L160.922 76.6479L162.899 80.4038C165.073 85.28 164.876 85.1482 164.876 85.1482Z"
                      fill="url(#paint1_linear_128_2746)"
                    />
                    <g id="Group 12">
                      <path
                        id="Vector 35"
                        d="M183.676 -82.4868V-69.2421C189.87 -66.4745 203.761 -60.0696 209.77 -56.5904C217.282 -52.2414 222.62 -50.8576 226.376 -41.3689C229.38 -33.7779 228.748 -23.7751 228.155 -19.6238L231.12 2.51665C232.372 5.81136 233.7 13.3201 234.481 16.5521C235.864 22.2849 237.446 27.0293 238.237 39.8786C238.839 49.6703 237.841 56.6158 238.237 59.6469C238.237 60.8989 238.237 63.7587 238.237 65.182C238.237 66.6053 237.709 70.5194 237.446 72.2986C237.973 76.1863 238.869 84.3572 238.237 85.9387C237.446 87.9155 237.643 94.439 236.062 97.6019C234.481 100.765 228.748 108.277 228.155 107.288C227.68 106.498 227.957 105.377 228.155 104.916C227.759 104.784 227.008 104.284 227.166 103.335C227.324 102.386 230.263 97.2725 231.713 94.8344V89.6946L230.527 84.1595L227.166 80.7989C226.046 84.2913 223.529 91.3156 222.422 91.4738C221.315 91.6319 220.379 90.3536 220.05 89.6946C221.17 85.8069 223.45 77.8732 223.608 77.2406C223.806 76.4499 223.41 75.4615 224.794 71.7055C225.901 68.7007 227.496 66.8953 228.155 66.3681C228.287 63.9959 228.115 57.7096 226.376 51.5419C224.201 43.8323 221.434 39.2856 219.061 31.7737C217.164 25.7641 216.36 14.7071 215.898 10.0286L210.702 -13.4956L211.549 -18.24C211.088 -14.3523 209.098 -6.06277 206.41 1.52824C203.721 9.11925 203.444 14.7071 203.642 16.5521C204.235 21.2965 205.382 31.5364 205.224 34.5412C205.065 37.546 205.158 40.4058 205.224 41.4601L206.41 51.5419C207.2 58.8562 208.9 75.6196 209.375 84.1595C209.849 92.6994 208.989 107.816 208.461 114.207L173.861 113.098L172.013 89.6946H170.827"
                        stroke="black"
                        stroke-width="0.395365"
                      />
                      <path
                        id="Vector 27"
                        d="M158.767 -82.4868V-69.2421C152.573 -66.4745 138.683 -60.0696 132.673 -56.5904C125.161 -52.2414 119.824 -50.8576 116.068 -41.3689C113.063 -33.7779 113.696 -23.7751 114.289 -19.6238L111.323 2.51665C110.071 5.81136 108.743 13.3201 107.963 16.5521C106.579 22.2849 104.998 27.0293 104.207 39.8786C103.604 49.6703 104.602 56.6158 104.207 59.6469C104.207 60.8989 104.207 63.7587 104.207 65.182C104.207 66.6053 104.734 70.5194 104.998 72.2986C104.47 76.1863 103.574 84.3572 104.207 85.9387C104.998 87.9155 104.8 94.439 106.381 97.6019C107.963 100.765 113.696 108.277 114.289 107.288C114.763 106.498 114.486 105.377 114.289 104.916C114.684 104.784 115.435 104.284 115.277 103.335C115.119 102.386 112.18 97.2725 110.73 94.8344V89.6946L111.916 84.1595L115.277 80.7989C116.397 84.2913 118.914 91.3156 120.021 91.4738C121.128 91.6319 122.064 90.3536 122.394 89.6946C121.273 85.8069 118.993 77.8732 118.835 77.2406C118.638 76.4499 119.033 75.4615 117.649 71.7055C116.542 68.7007 114.948 66.8953 114.289 66.3681C114.157 63.9959 114.328 57.7096 116.068 51.5419C118.242 43.8323 121.01 39.2856 123.382 31.7737C125.28 25.7641 126.084 14.7071 126.545 10.0286L131.741 -13.4956L130.894 -18.24C131.355 -14.3523 133.345 -6.06277 136.034 1.52824C138.722 9.11925 138.999 14.7071 138.801 16.5521C138.208 21.2965 137.062 31.5364 137.22 34.5412C137.378 37.546 137.286 40.4058 137.22 41.4601L136.034 51.5419C135.243 58.8562 133.543 75.6196 133.068 84.1595C132.594 92.6994 133.632 107.816 134.159 114.207L168.683 114.01L170.43 89.6946H171.617"
                        stroke="black"
                        stroke-width="0.395365"
                      />
                      <path
                        id="Vector 44"
                        d="M168.316 116.425L134.825 117.756C134.825 117.756 134.603 116.647 134.381 114.873C134.285 114.099 134.16 111.546 134.16 111.546L135.712 110.215L168.538 113.32L168.316 116.425Z"
                        fill="#F4F2F2"
                      />
                    </g>
                    <g id="Group 19">
                      <g id="Group 18">
                        <g id="Group 15">
                          <path
                            id="Vector 15"
                            d="M120.616 6.27246C120.616 6.27246 120.813 8.0516 121.406 8.84233C122 9.63306 122.79 9.83075 122.79 9.83075"
                            stroke="black"
                            stroke-width="0.395365"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <path
                            id="Vector 33"
                            d="M223.213 6.27271C223.213 6.27271 223.015 8.05185 222.422 8.84258C221.829 9.63331 221.038 9.83099 221.038 9.83099"
                            stroke="black"
                            stroke-width="0.395365"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </g>
                        <g id="Group 16">
                          <path
                            id="Vector 11"
                            d="M171.39 28.8622C171.42 28.7572 171.359 28.6479 171.254 28.6182C171.148 28.5885 171.039 28.6496 171.01 28.7547L171.39 28.8622ZM171.01 28.7547C170.936 29.0151 170.944 29.395 170.987 29.8061C171.031 30.2255 171.115 30.707 171.212 31.1868C171.309 31.6672 171.42 32.1497 171.519 32.5714C171.619 32.9953 171.705 33.3536 171.754 33.5927L172.142 33.513C172.091 33.2668 172.003 32.8997 171.904 32.4809C171.805 32.0599 171.695 31.5824 171.599 31.1083C171.503 30.6336 171.422 30.1662 171.38 29.765C171.337 29.3555 171.339 29.043 171.39 28.8622L171.01 28.7547ZM171.754 33.5927C171.949 34.5409 171.997 36.2972 171.755 37.3681L172.141 37.4553C172.397 36.3202 172.346 34.5061 172.142 33.513L171.754 33.5927ZM171.755 37.3681C171.698 37.6228 171.618 37.6827 171.587 37.6962C171.556 37.71 171.495 37.7111 171.389 37.6523C171.288 37.5965 171.187 37.5082 171.106 37.4273C171.067 37.388 171.035 37.3525 171.013 37.3272C171.002 37.3145 170.993 37.3045 170.988 37.2978C170.985 37.2945 170.983 37.292 170.982 37.2905C170.981 37.2898 170.981 37.2893 170.981 37.289C170.981 37.2889 170.981 37.2888 170.981 37.2888C170.981 37.2888 170.981 37.2888 170.981 37.2888C170.981 37.2889 170.981 37.2889 170.981 37.2889C170.981 37.2889 170.981 37.289 170.826 37.4117C170.671 37.5343 170.671 37.5344 170.671 37.5344C170.671 37.5345 170.671 37.5345 170.671 37.5346C170.671 37.5347 170.671 37.5348 170.671 37.5349C170.671 37.5352 170.672 37.5355 170.672 37.5359C170.673 37.5367 170.673 37.5377 170.674 37.539C170.677 37.5416 170.679 37.5452 170.683 37.5497C170.691 37.5587 170.701 37.5713 170.715 37.5866C170.741 37.6173 170.779 37.6594 170.826 37.7062C170.917 37.7977 171.049 37.9161 171.198 37.9983C171.341 38.0773 171.545 38.1474 171.747 38.0578C171.951 37.9678 172.074 37.752 172.141 37.4553L171.755 37.3681Z"
                            fill="black"
                          />
                          <path
                            id="Vector 38"
                            d="M159.163 73.88C159.163 73.88 161.468 76.7713 163.512 81.7873C166.787 89.8274 170.662 90.4977 173.198 89.4969C175.735 88.4962 178.905 83.8256 179.919 81.2682"
                            stroke="black"
                            stroke-width="0.395365"
                            stroke-linecap="round"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                  <path
                    id="Vector 44_2"
                    d="M174.748 119.087L207.796 117.756C207.796 117.756 208.017 116.647 208.239 114.873C208.336 114.099 208.461 111.546 208.461 111.546L206.687 110.215L173.861 110.437C173.861 110.437 173.896 112.651 174.083 114.429C174.526 118.643 174.748 119.087 174.748 119.087Z"
                    fill="white"
                  />
                  <g id="Group 223_2">
                    <path
                      id="Vector 12_2"
                      d="M145.915 159.897C145.915 159.897 147.013 161.444 148.011 162.476C148.884 163.378 149.907 164.333 149.907 164.333"
                      stroke="black"
                      stroke-width="0.393653"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 41_2"
                      d="M160.997 159.897C160.997 159.897 159.899 161.444 158.901 162.476C158.029 163.378 157.005 164.333 157.005 164.333"
                      stroke="black"
                      stroke-width="0.393653"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </g>
                </g>
                {findMeasure('lower_length') && (
                  <g id="lower_length">
                    <path
                      id="Vector 19"
                      d="M214.596 195.562L214.596 25.2644"
                      stroke="#E55959"
                      stroke-width="2.21415"
                      stroke-linecap="square"
                    />
                    <path
                      id="Vector 29"
                      d="M132.004 24.7274L214.957 24.7274"
                      stroke="#E55959"
                      stroke-width="1.4761"
                      stroke-linecap="square"
                      stroke-dasharray="4.92 4.92"
                    />
                    <path
                      id="Vector 28"
                      d="M218.797 192.886L214.572 197.033L210.906 192.762"
                      stroke="#E55959"
                      stroke-width="2.21415"
                    />
                  </g>
                )}
                {findMeasure('insideLeg') && (
                  <g id="inside_leg">
                    <path
                      id="Vector 29_2"
                      d="M132.004 24.9197L214.573 24.9197"
                      stroke="#E55959"
                      stroke-width="1.47445"
                      stroke-linecap="square"
                      stroke-dasharray="4.99 4.99"
                    />
                    <g id="Group 225">
                      <path
                        id="Vector 19_2"
                        d="M170.803 221.937L170.803 25.6695"
                        stroke="#E55959"
                        stroke-width="2.21168"
                        stroke-linecap="square"
                      />
                      <path
                        id="Vector 28_2"
                        d="M175.065 219.222L170.778 223.43L167.058 219.096"
                        stroke="#E55959"
                        stroke-width="2.21168"
                      />
                    </g>
                  </g>
                )}
                {findMeasure('thigh') && (
                  <g id="thigh">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M209.413 105.277C209.413 105.277 207.201 105.277 190.81 106.604M173.289 105.394C173.289 105.394 177.751 105.941 186.24 106.604"
                            stroke="#E55959"
                            stroke-width="1.47445"
                          />
                          <path
                            id="Vector 27_2"
                            d="M192.79 104.563L189.919 106.492L192.385 108.344"
                            stroke="#E55959"
                            stroke-width="1.47445"
                          />
                          <path
                            id="Vector 28_3"
                            d="M183.745 104.563L186.616 106.492L184.149 108.344"
                            stroke="#E55959"
                            stroke-width="1.47445"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('hip') && (
                  <g id="hip">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M208.676 75.051C208.676 75.051 197.656 78.9312 167.888 80.1946M133.479 75.051C133.479 75.051 139.609 77.6895 163.993 80.1946"
                            stroke="#E55959"
                            stroke-width="1.47445"
                          />
                          <path
                            id="Vector 27_3"
                            d="M168.866 78L167.129 80.0992L168.865 82.4233"
                            stroke="#E55959"
                            stroke-width="1.47445"
                          />
                          <path
                            id="Vector 28_4"
                            d="M162.23 78.0002L164.312 80.0994L162.23 82.4234"
                            stroke="#E55959"
                            stroke-width="1.47445"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_2746"
              x1="124.549"
              y1="-9.73949"
              x2="110.513"
              y2="103.335"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_2746"
              x1="153.805"
              y1="56.8796"
              x2="138.584"
              y2="144.651"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
