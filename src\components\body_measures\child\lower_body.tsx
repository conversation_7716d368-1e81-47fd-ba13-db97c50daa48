import { useDevice } from '@/hooks/use-device';
import { BodyMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface LowerBodyProps {
  measure: BodyMeasure;
  className?: string;
}

export function LowerBody({ measure, className }: LowerBodyProps) {
  const { measures } = measure;
  const mappedMeasures = measures.map((item) => item.measure);
  const { isMobile } = useDevice();

  const findMeasure = (measure: string) => {
    const foundMeasure = mappedMeasures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full flex justify-center', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_lower_body">
            <mask id="mask0_128_1202" maskUnits="userSpaceOnUse" x="0" y="0" width="331" height="193">
              <rect id="rect" width="331" height="193" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_1202)">
              <g id="group_lower_body_mask">
                <g id="child">
                  <g id="Group 38">
                    <g id="Group 37">
                      <path
                        id="Vector"
                        d="M154.794 -80.9902C156.139 -85.0255 156.261 -90.7115 156.261 -90.7115H156.995H173.136H173.869C173.869 -90.7115 173.992 -85.0255 175.337 -80.9902C176.315 -78.0554 180.289 -77.5051 186.526 -75.4875C192.621 -73.5154 196.614 -74.2036 199.182 -67.2335C201.75 -60.2635 208.903 -38.4362 210.004 -33.6672C210.884 -29.8521 212.082 -24.3739 212.572 -22.1116C213.55 -20.522 215.947 -14.2979 217.707 -2.11862C219.468 10.0606 219.786 24.7222 219.725 30.5305C220.031 34.0767 220.086 42.4554 220.086 42.4554L219.725 45.835C219.725 45.835 218.808 48.8361 216.607 51.6241C213.856 55.1091 209.27 59.6947 208.72 58.4107C208.279 57.3835 208.903 56.0262 209.27 55.476C208.659 56.0262 207.399 56.9066 207.252 56.0262C207.069 54.9257 205.968 55.1091 208.72 52.5412C211.471 49.9733 212.572 47.7722 212.572 44.8375C212.572 42.4897 210.37 38.6011 209.27 36.9503C208.659 37.3172 207.069 38.5277 205.602 40.4353C203.767 42.8198 203.034 45.0209 201.933 43.9203C200.833 42.8198 200.833 42.0861 202.85 38.7845C204.868 35.4829 207.619 29.6134 209.27 28.5129C210.591 27.6325 211.41 27.0455 211.654 26.8621C211.349 24.5387 210.591 19.2317 210.004 16.5904C209.27 13.2888 206.335 8.8867 204.318 1.73324C202.703 -3.98953 200.955 -14.9582 200.282 -19.7271C198.02 -25.7189 193.422 -38.0694 193.129 -39.5368C192.835 -41.0041 191.417 -46.6291 190.744 -49.2581L189.644 -46.5068V-15.1416C190.011 -13.0017 190.634 -7.47456 190.194 -2.48547C189.754 2.50362 190.011 4.60686 190.194 5.03484C191.05 8.27531 192.762 15.7467 192.762 19.7086V29.7968C194.046 34.5047 196.614 45.1309 196.614 49.9733C196.614 56.0262 196.43 67.5818 196.064 72.7176C195.77 76.8263 195.085 81.7665 194.78 83.723H173.686C172.402 75.041 169.724 56.6499 169.284 52.5412C168.734 47.4054 168.917 45.0209 168.367 43.9203C167.817 42.8198 166.899 41.7193 165.799 41.7193H164.332C163.231 41.7193 162.314 42.8198 161.764 43.9203C161.213 45.0209 161.397 47.4054 160.846 52.5412C160.406 56.6499 157.728 75.041 156.444 83.723H135.351C135.045 81.7665 134.36 76.8263 134.067 72.7176C133.7 67.5818 133.517 56.0262 133.517 49.9733C133.517 45.1309 136.084 34.5047 137.368 29.7968V19.7086C137.368 15.7467 139.08 8.27531 139.936 5.03484C140.12 4.60686 140.377 2.50362 139.936 -2.48547C139.496 -7.47456 140.12 -13.0017 140.487 -15.1416V-46.5068L139.386 -49.2581C138.714 -46.6291 137.295 -41.0041 137.002 -39.5368C136.708 -38.0694 132.11 -25.7189 129.848 -19.7271C129.176 -14.9582 127.427 -3.98953 125.813 1.73324C123.795 8.8867 120.86 13.2888 120.127 16.5904C119.54 19.2317 118.782 24.5387 118.476 26.8621C118.721 27.0455 119.54 27.6325 120.86 28.5129C122.511 29.6134 125.263 35.4829 127.28 38.7845C129.298 42.0861 129.298 42.8198 128.197 43.9203C127.097 45.0209 126.363 42.8198 124.529 40.4353C123.062 38.5277 121.472 37.3172 120.86 36.9503C119.76 38.6011 117.559 42.4897 117.559 44.8375C117.559 47.7722 118.659 49.9733 121.411 52.5412C124.162 55.1091 123.062 54.9257 122.878 56.0262C122.731 56.9066 121.472 56.0262 120.86 55.476C121.227 56.0262 121.851 57.3835 121.411 58.4107C120.86 59.6947 116.275 55.1091 113.524 51.6241C111.322 48.8361 110.245 45.8157 110.551 45.2043C110.428 44.5929 109.962 44.4706 110.184 42.8198C110.385 41.3225 109.878 34.0767 110.184 30.5305C110.123 24.7222 110.662 10.0606 112.423 -2.11862C114.184 -14.2979 116.581 -20.522 117.559 -22.1116C118.048 -24.3739 119.246 -29.8521 120.127 -33.6672C121.227 -38.4362 128.381 -60.2635 130.949 -67.2335C133.517 -74.2036 137.509 -73.5154 143.605 -75.4875C149.841 -77.5051 153.815 -78.0554 154.794 -80.9902Z"
                        fill="white"
                      />
                      <g id="Group 36">
                        <g id="Group 35">
                          <g id="Group 34">
                            <path
                              id="Vector 20"
                              d="M125.407 -18.4433C126.843 -17.8959 129.994 -19.4273 130.36 -20.8278L129.443 -17.1593C129.558 -15.5655 128.709 -12.5738 128.709 -12.5738C128.709 -12.5738 127.844 -7.7933 127.608 -5.97059L124.857 4.48452C124.244 6.37424 121.394 12.7672 121.556 12.7385C121.556 12.7385 120.172 15.9966 119.905 17.3241L118.996 23.1936L118.437 25.7615C118.695 26.2724 118.392 26.4195 118.437 26.6786C118.495 27.0024 121.694 29.2465 121.694 29.2465C121.543 29.4621 123.573 32.3647 123.573 32.3647L125.591 35.8497L128.526 41.1689L129.076 42.8198L127.425 44.1037L126.508 43.1866L123.94 39.7016L121.007 36.9502C121.007 36.9502 118.743 41.0036 118.437 41.169L117.337 44.8374L117.887 47.3332L119.873 51.0738L123.192 54.7422L122.906 56.2096L122.238 56.2096L121.007 55.4759C121.053 55.7349 121.589 57.0833 121.694 57.677L121.33 58.7775L119.873 57.8412L117.337 55.8427L114.843 53.0545L112.751 50.3401L111.467 47.7722L110.917 46.6177L110.367 43.9203L110 40.0684L110.367 36.2165L110.367 24.1107L111.467 5.58505L112.751 -3.58606C112.951 -7.18362 115.319 -16.2422 115.319 -16.2422C115.319 -16.2422 116.42 -19.7272 117.704 -22.662C117.808 -23.9607 118.71 -29.0656 118.804 -28.5315L119.905 -32.5668C120.455 -27.2475 120.088 -27.431 120.638 -24.3824C121.228 -21.1181 123.613 -19.1275 125.407 -18.4433Z"
                              fill="url(#paint0_linear_128_1202)"
                            />
                            <path
                              id="Vector 18"
                              d="M156.773 37.5002C159.264 40.2624 161.542 44.4703 161.542 44.4703L160.808 53.8248L158.974 66.8478L157.139 78.9536L156.589 83.5392L135.49 83.5448L134.162 74.0269L133.845 65.5638L133.661 60.0611V51.8071V46.8547L137.513 29.7965V17.6907L153.471 34.0152C153.471 34.0152 153.095 33.4235 156.773 37.5002Z"
                              fill="url(#paint1_linear_128_1202)"
                            />
                            <g id="Vector 47">
                              <path
                                d="M135.135 91.0157C135.562 88.5965 135.431 84.9085 135.313 83.3669L141.538 80.343L156.48 83.3669V86.9245C156.48 87.9918 156.124 93.6838 155.057 97.5971C154.203 100.728 151.618 109.456 150.432 113.428C150.254 114.555 149.827 117.804 149.543 121.788C149.187 126.769 147.052 136.73 145.452 141.533C143.851 146.336 142.606 154.696 142.428 156.475C142.25 158.253 142.783 161.277 142.783 163.056C142.783 164.835 142.961 167.503 142.428 168.926C141.894 170.349 142.428 174.796 142.428 176.397C142.428 177.998 141.538 178.354 141.183 180.132C140.827 181.911 141.538 182.267 141.538 183.334C141.538 184.401 141.36 184.224 141.183 185.113C141.005 186.002 140.293 187.603 137.981 187.959C136.235 188.228 135.936 187.144 136.008 186.473C135.913 186.638 135.521 186.918 134.423 187.247C133 187.674 132.644 186.832 132.644 186.358C132.585 186.477 132.217 186.821 131.221 187.247C130.225 187.674 129.976 186.832 129.976 186.358C129.917 186.477 129.656 186.714 129.087 186.714C128.518 186.714 128.257 186.239 128.197 186.002C128.197 186.121 127.948 186.429 126.952 186.714C125.707 187.07 125.351 184.579 125.529 183.334C125.707 182.089 127.486 178.176 128.375 176.931C129.265 175.685 130.866 168.926 131.043 168.037C131.221 167.147 130.688 163.945 131.577 157.898C132.466 151.85 130.51 144.023 130.688 129.259C130.866 114.496 134.601 94.0396 135.135 91.0157Z"
                                fill="#F5F3F3"
                              />
                              <path
                                d="M135.313 83.3669C135.431 84.9085 135.562 88.5965 135.135 91.0157C134.601 94.0396 130.866 114.496 130.688 129.259C130.51 144.023 132.466 151.85 131.577 157.898C130.688 163.945 131.221 167.147 131.043 168.037C130.866 168.926 129.265 175.685 128.375 176.931C127.486 178.176 125.707 182.089 125.529 183.334C125.351 184.579 125.707 187.07 126.952 186.714C127.948 186.429 128.197 186.121 128.197 186.002C128.257 186.239 128.518 186.714 129.087 186.714C129.656 186.714 129.917 186.477 129.976 186.358C129.976 186.832 130.225 187.674 131.221 187.247C132.217 186.821 132.585 186.477 132.644 186.358C132.644 186.832 133 187.674 134.423 187.247C135.846 186.821 136.083 186.477 136.024 186.358C135.905 187.01 136.131 188.244 137.981 187.959C140.293 187.603 141.005 186.002 141.183 185.113C141.36 184.224 141.538 184.401 141.538 183.334C141.538 182.267 140.827 181.911 141.183 180.132C141.538 178.354 142.428 177.998 142.428 176.397C142.428 174.796 141.894 170.349 142.428 168.926C142.961 167.503 142.783 164.835 142.783 163.056C142.783 161.277 142.25 158.253 142.428 156.475C142.606 154.696 143.851 146.336 145.452 141.533C147.052 136.73 149.187 126.769 149.543 121.788C149.827 117.804 150.254 114.555 150.432 113.428C151.618 109.456 154.203 100.728 155.057 97.5971C156.124 93.6838 156.48 87.9918 156.48 86.9245C156.48 86.0707 156.48 84.197 156.48 83.3669L141.538 80.343L135.313 83.3669Z"
                                stroke="black"
                                stroke-width="0.355754"
                              />
                            </g>
                          </g>
                        </g>
                        <g id="Group 33">
                          <path
                            id="Vector 11"
                            d="M165.503 4.76177C165.555 4.6748 165.527 4.56219 165.44 4.51025C165.353 4.45831 165.24 4.48671 165.188 4.57368L165.503 4.76177ZM165.188 4.57368C165.14 4.65495 165.124 4.74694 165.122 4.83223C165.12 4.91847 165.133 5.0102 165.153 5.10143C165.193 5.28358 165.268 5.48728 165.352 5.68351C165.437 5.88108 165.533 6.0786 165.618 6.24905C165.704 6.42309 165.775 6.56254 165.815 6.65476L166.152 6.50863C166.107 6.40507 166.029 6.25188 165.946 6.08596C165.862 5.91645 165.77 5.72669 165.69 5.53921C165.609 5.3504 165.544 5.17127 165.511 5.02236C165.494 4.94805 165.487 4.88719 165.488 4.83999C165.49 4.79182 165.499 4.76949 165.503 4.76177L165.188 4.57368ZM165.815 6.65476C165.888 6.82286 165.939 7.07573 165.944 7.34345C165.949 7.61176 165.908 7.87172 165.818 8.05934L166.149 8.21748C166.272 7.96014 166.316 7.63752 166.31 7.3366C166.305 7.03511 166.249 6.7321 166.152 6.50863L165.815 6.65476ZM165.818 8.05934C165.792 8.11327 165.757 8.13609 165.707 8.1466C165.647 8.15912 165.566 8.15239 165.472 8.12782C165.381 8.10394 165.292 8.06725 165.225 8.03535C165.192 8.01961 165.165 8.00552 165.147 7.99555C165.138 7.99058 165.131 7.98667 165.126 7.98413C165.124 7.98286 165.123 7.98194 165.122 7.9814C165.121 7.98113 165.121 7.98096 165.121 7.98089C165.121 7.98085 165.121 7.98085 165.121 7.98086C165.121 7.98087 165.121 7.98089 165.121 7.98091C165.121 7.98092 165.121 7.98094 165.121 7.98094C165.121 7.98097 165.121 7.98099 165.027 8.13841C164.933 8.29583 164.933 8.29586 164.933 8.29588C164.933 8.2959 164.933 8.29593 164.933 8.29595C164.933 8.296 164.933 8.29606 164.933 8.29612C164.933 8.29624 164.934 8.29639 164.934 8.29657C164.935 8.29692 164.935 8.29738 164.936 8.29794C164.938 8.29906 164.941 8.30059 164.944 8.30247C164.951 8.30623 164.96 8.31144 164.972 8.31777C164.995 8.3304 165.028 8.34759 165.068 8.36662C165.147 8.40424 165.258 8.45098 165.379 8.48272C165.498 8.51377 165.642 8.53485 165.782 8.50566C165.931 8.47445 166.068 8.38603 166.149 8.21748L165.818 8.05934Z"
                            fill="black"
                          />
                          <path
                            id="Vector 12"
                            d="M152.554 32.5479C152.554 32.5479 153.765 34.2114 154.865 35.3205C155.827 36.2901 156.956 37.3168 156.956 37.3168"
                            stroke="black"
                            stroke-width="0.366844"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                  <path
                    id="Vector_2"
                    d="M165.799 41.7193H164.331C163.231 41.7193 162.314 42.8198 161.763 43.9204C161.213 45.0209 161.397 47.4054 160.846 52.5412C160.406 56.6499 157.728 75.041 156.444 83.723H135.351C135.045 81.7665 134.36 76.8263 134.067 72.7176C133.7 67.5818 133.516 56.0262 133.516 49.9733C133.516 45.1309 136.084 34.5047 137.368 29.7968V19.7086C137.368 15.7467 139.08 8.27531 139.936 5.03484C140.12 4.60686 140.376 2.50362 139.936 -2.48547C139.496 -7.47455 140.12 -13.0017 140.486 -15.1416V-46.5068L139.386 -49.2581C138.713 -46.6291 137.295 -41.0041 137.001 -39.5368C136.708 -38.0694 132.11 -25.7189 129.848 -19.7272C129.175 -14.9582 127.427 -3.98953 125.813 1.73324C123.795 8.88671 120.86 13.2888 120.127 16.5904C119.54 19.2317 118.781 24.5387 118.476 26.8621C118.72 27.0455 119.54 27.6325 120.86 28.5129C122.511 29.6134 125.262 35.4829 127.28 38.7845C129.298 42.0861 129.298 42.8198 128.197 43.9204C127.097 45.0209 126.363 42.8198 124.529 40.4353C123.061 38.5277 121.472 37.3172 120.86 36.9503C119.76 38.6011 117.559 42.4897 117.559 44.8375C117.559 47.7722 118.659 49.9733 121.41 52.5412C124.162 55.1091 123.061 54.9257 122.878 56.0262C122.731 56.9066 121.472 56.0262 120.86 55.476C121.227 56.0262 121.851 57.3835 121.41 58.4107C120.86 59.6947 116.275 55.1091 113.523 51.6241C111.322 48.8361 110.734 46.3048 110.734 46.3048C110.734 46.3048 110.329 44.8375 110.183 42.8198C110.075 41.313 110.099 34.0767 110.405 30.5305C110.344 24.7222 110.662 10.0606 112.423 -2.11862C114.184 -14.2979 116.58 -20.522 117.559 -22.1116C118.048 -24.3739 119.246 -29.8521 120.127 -33.6672C121.227 -38.4362 128.381 -60.2635 130.948 -67.2335C133.516 -74.2035 137.509 -73.5154 143.605 -75.4875C149.841 -77.5051 153.815 -78.0554 154.793 -80.9902C156.138 -85.0255 156.261 -90.7115 156.261 -90.7115H173.136M165.799 41.7193C166.899 41.7193 167.816 42.8198 168.367 43.9204C168.917 45.0209 168.733 47.4054 169.284 52.5412C169.724 56.6499 172.272 74.6852 173.556 83.3672L194.723 83.5451C195.029 81.5886 195.77 76.8263 196.063 72.7176C196.43 67.5818 196.614 56.0262 196.614 49.9733C196.614 45.1309 194.046 34.5047 192.762 29.7968V19.7086C192.762 15.7467 191.05 8.27531 190.194 5.03484C190.01 4.60686 189.754 2.50362 190.194 -2.48547C190.634 -7.47455 190.01 -13.0017 189.644 -15.1416V-46.5068L190.744 -49.2581C191.417 -46.6291 192.835 -41.0041 193.129 -39.5368C193.422 -38.0694 198.02 -25.7189 200.282 -19.7272C200.955 -14.9582 202.703 -3.98953 204.317 1.73324C206.335 8.88671 209.27 13.2888 210.003 16.5904C210.59 19.2317 211.349 24.5387 211.654 26.8621C211.41 27.0455 210.59 27.6325 209.27 28.5129C207.619 29.6134 204.868 35.4829 202.85 38.7845C200.832 42.0861 200.832 42.8198 201.933 43.9204C203.033 45.0209 203.767 42.8198 205.601 40.4353C207.069 38.5277 208.658 37.3172 209.27 36.9503C210.37 38.6011 212.571 42.4897 212.571 44.8375C212.571 47.7722 211.471 49.9733 208.719 52.5412C205.968 55.1091 207.069 54.9257 207.252 56.0262C207.399 56.9066 208.658 56.0262 209.27 55.476C208.903 56.0262 208.279 57.3835 208.719 58.4107C209.27 59.6947 213.855 55.1091 216.607 51.6241C218.808 48.8361 219.541 46.3048 219.725 45.3877C220.42 43.0032 220.03 34.0767 219.725 30.5305C219.786 24.7222 219.468 10.0606 217.707 -2.11862C215.946 -14.2979 213.55 -20.522 212.571 -22.1116C212.082 -24.3739 210.884 -29.8521 210.003 -33.6672C208.903 -38.4362 201.749 -60.2635 199.181 -67.2335C196.614 -74.2035 192.621 -73.5154 186.525 -75.4875C180.289 -77.5051 176.315 -78.0554 175.337 -80.9902C173.992 -85.0255 173.869 -90.7115 173.869 -90.7115H156.994M165.799 41.7193H164.148"
                    stroke="black"
                    stroke-width="0.366844"
                    stroke-linecap="round"
                  />
                  <path
                    id="Vector 48"
                    d="M135.668 85.3234C135.49 84.0782 135.451 83.3025 135.312 82.1216L138.692 79.2755L144.206 76.9631L156.658 81.0543L156.302 83.9004L135.668 85.3234Z"
                    fill="#F5F3F3"
                  />
                  <g id="Vector 47_2">
                    <path
                      d="M194.901 91.0157C194.475 88.5965 194.605 84.9085 194.724 83.3669L188.498 80.343L173.556 83.3669V86.9245C173.556 87.9918 173.912 93.6838 174.979 97.5971C175.833 100.728 178.418 109.456 179.604 113.428C179.782 114.555 180.209 117.804 180.493 121.788C180.849 126.769 182.984 136.73 184.585 141.533C186.185 146.336 187.431 154.696 187.608 156.475C187.786 158.253 187.253 161.277 187.253 163.056C187.253 164.835 187.075 167.503 187.608 168.926C188.142 170.349 187.608 174.796 187.608 176.397C187.608 177.998 188.498 178.354 188.854 180.132C189.209 181.911 188.498 182.267 188.498 183.334C188.498 184.401 188.676 184.224 188.854 185.113C189.032 186.002 189.743 187.603 192.055 187.959C193.801 188.228 194.1 187.144 194.029 186.473C194.123 186.638 194.516 186.918 195.613 187.247C197.036 187.674 197.392 186.832 197.392 186.358C197.451 186.477 197.819 186.821 198.815 187.247C199.811 187.674 200.06 186.832 200.06 186.358C200.119 186.477 200.38 186.714 200.949 186.714C201.519 186.714 201.779 186.239 201.839 186.002C201.839 186.121 202.088 186.429 203.084 186.714C204.329 187.07 204.685 184.579 204.507 183.334C204.329 182.089 202.55 178.176 201.661 176.931C200.771 175.685 199.171 168.926 198.993 168.037C198.815 167.147 199.348 163.945 198.459 157.898C197.57 151.85 199.526 144.023 199.348 129.259C199.171 114.496 195.435 94.0396 194.901 91.0157Z"
                      fill="white"
                    />
                    <path
                      d="M194.724 83.3669C194.605 84.9085 194.475 88.5965 194.901 91.0157C195.435 94.0396 199.171 114.496 199.348 129.259C199.526 144.023 197.57 151.85 198.459 157.898C199.348 163.945 198.815 167.147 198.993 168.037C199.171 168.926 200.771 175.685 201.661 176.931C202.55 178.176 204.329 182.089 204.507 183.334C204.685 184.579 204.329 187.07 203.084 186.714C202.088 186.429 201.839 186.121 201.839 186.002C201.779 186.239 201.519 186.714 200.949 186.714C200.38 186.714 200.119 186.477 200.06 186.358C200.06 186.832 199.811 187.674 198.815 187.247C197.819 186.821 197.451 186.477 197.392 186.358C197.392 186.832 197.036 187.674 195.613 187.247C194.19 186.821 193.953 186.477 194.012 186.358C194.131 187.01 193.905 188.244 192.055 187.959C189.743 187.603 189.032 186.002 188.854 185.113C188.676 184.224 188.498 184.401 188.498 183.334C188.498 182.267 189.209 181.911 188.854 180.132C188.498 178.354 187.608 177.998 187.608 176.397C187.608 174.796 188.142 170.349 187.608 168.926C187.075 167.503 187.253 164.835 187.253 163.056C187.253 161.277 187.786 158.253 187.608 156.475C187.431 154.696 186.185 146.336 184.585 141.533C182.984 136.73 180.849 126.769 180.493 121.788C180.209 117.804 179.782 114.555 179.604 113.428C178.418 109.456 175.833 100.728 174.979 97.5971C173.912 93.6838 173.556 87.9918 173.556 86.9245C173.556 86.0707 173.556 84.197 173.556 83.3669L188.498 80.343L194.724 83.3669Z"
                      stroke="black"
                      stroke-width="0.355754"
                    />
                  </g>
                  <path
                    id="Vector 49"
                    d="M194.367 85.3234C194.545 84.0782 194.584 83.3025 194.723 82.1216L191.343 79.2755L185.829 76.9631L173.378 80.1649L173.733 83.9004L194.367 85.3234Z"
                    fill="white"
                  />
                  <g id="Group 223">
                    <path
                      id="Vector 12_2"
                      d="M138.966 93.2672C138.966 93.2672 139.716 94.591 140.428 95.4951C141.051 96.2856 141.791 97.1307 141.791 97.1307"
                      stroke="black"
                      stroke-width="0.315706"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 41"
                      d="M151 94.4921C151 94.4921 149.998 95.6375 149.118 96.3796C148.348 97.0283 147.454 97.7071 147.454 97.7071"
                      stroke="black"
                      stroke-width="0.315706"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </g>
                  <g id="Group 224">
                    <path
                      id="Vector 12_3"
                      d="M191.104 93.2672C191.104 93.2672 190.353 94.591 189.641 95.4951C189.018 96.2856 188.278 97.1307 188.278 97.1307"
                      stroke="black"
                      stroke-width="0.315706"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 41_2"
                      d="M179.07 94.4919C179.07 94.4919 180.071 95.6374 180.952 96.3794C181.721 97.0282 182.615 97.707 182.615 97.707"
                      stroke="black"
                      stroke-width="0.315706"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </g>
                </g>
                {findMeasure('lower_length') && (
                  <g id="lower_length">
                    <path
                      id="Vector 19"
                      d="M199.945 130.734L199.945 15.3637"
                      stroke="#E55959"
                      stroke-width="1.5"
                      stroke-linecap="square"
                    />
                    <path
                      id="Vector 29"
                      d="M135 15L200.201 15"
                      stroke="#E55959"
                      stroke-linecap="square"
                      stroke-dasharray="3.33 3.33"
                    />
                    <path
                      id="Vector 28"
                      d="M202.791 128.921L199.929 131.731L197.445 128.837"
                      stroke="#E55959"
                      stroke-width="1.5"
                    />
                  </g>
                )}
                {findMeasure('insideLeg') && (
                  <g id="inside_leg">
                    <path
                      id="Vector 29_2"
                      d="M135 15L200.252 15"
                      stroke="#E55959"
                      stroke-linecap="square"
                      stroke-dasharray="3.39 3.39"
                    />
                    <g id="Group 225">
                      <path
                        id="Vector 19_2"
                        d="M165.315 148.621L165.315 15.5085"
                        stroke="#E55959"
                        stroke-width="1.5"
                        stroke-linecap="square"
                      />
                      <path
                        id="Vector 28_2"
                        d="M168.205 146.779L165.297 149.633L162.774 146.694"
                        stroke="#E55959"
                        stroke-width="1.5"
                      />
                    </g>
                  </g>
                )}
                {findMeasure('thigh') && (
                  <g id="thigh">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M196.8 53.5C196.8 53.5 191.976 54.1352 180.884 54.3996M169.5 54C169.5 54 172.026 53.9499 177.784 54.3996"
                            stroke="#E55959"
                          />
                          <path id="Vector 27" d="M182.226 53.0153L180.279 54.3239L181.952 55.5802" stroke="#E55959" />
                          <path
                            id="Vector 28_3"
                            d="M176.091 53.0153L178.038 54.3239L176.366 55.5802"
                            stroke="#E55959"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('hip') && (
                  <g id="hip">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M194 34C194 34 189.416 36.169 165.103 37.2009M136.5 33C136.5 33 142.007 35.1549 161.923 37.2009"
                            stroke="#E55959"
                          />
                          <path id="Vector 27_2" d="M166.481 35.7802L164.483 37.1231L166.2 38.4122" stroke="#E55959" />
                          <path
                            id="Vector 28_4"
                            d="M160.186 35.7802L162.183 37.1231L160.467 38.4122"
                            stroke="#E55959"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_1202"
              x1="112.783"
              y1="-38.9197"
              x2="117.707"
              y2="55.726"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_1202"
              x1="145.719"
              y1="53.2253"
              x2="137.709"
              y2="99.6508"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className=" h-full m-auto "
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_lower_body">
            <mask id="mask0_128_2283" maskUnits="userSpaceOnUse" x="0" y="0" width="342" height="291">
              <rect id="rect" width="342" height="291" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_2283)">
              <g id="group_lower_body_mask">
                <g id="child">
                  <g id="Group 38">
                    <g id="Group 37">
                      <path
                        id="Vector"
                        d="M155.521 -114.461C157.488 -120.364 157.667 -128.681 157.667 -128.681H158.741H182.351H183.424C183.424 -128.681 183.603 -120.364 185.57 -114.461C187.001 -110.169 192.814 -109.364 201.937 -106.413C210.853 -103.528 216.693 -104.534 220.449 -94.3391C224.205 -84.1438 234.669 -52.2164 236.279 -45.2406C237.566 -39.66 239.319 -31.6469 240.035 -28.3379C241.466 -26.0126 244.971 -16.9084 247.547 0.906586C250.123 18.7216 250.588 40.1675 250.498 48.6636C250.946 53.8507 251.026 66.1065 251.026 66.1065L250.498 71.05C250.498 71.05 249.157 75.4397 245.937 79.5179C241.913 84.6155 235.205 91.323 234.401 89.4449C233.757 87.9424 234.669 85.957 235.205 85.1521C234.311 85.957 232.469 87.2448 232.254 85.957C231.986 84.3472 230.376 84.6155 234.401 80.8594C238.425 77.1032 240.035 73.8836 240.035 69.5909C240.035 66.1566 236.815 60.4687 235.205 58.054C234.311 58.5906 231.986 60.3614 229.84 63.1517C227.157 66.6396 226.083 69.8591 224.474 68.2494C222.864 66.6396 222.864 65.5664 225.815 60.737C228.766 55.9077 232.791 47.3221 235.205 45.7123C237.137 44.4245 238.336 43.5659 238.693 43.2977C238.246 39.8992 237.137 32.1365 236.279 28.273C235.205 23.4436 230.913 17.0045 227.961 6.54085C225.6 -1.83006 223.043 -17.8743 222.059 -24.85C218.75 -33.6144 212.025 -51.6798 211.595 -53.8262C211.166 -55.9726 209.091 -64.2004 208.107 -68.046L206.498 -64.0215V-18.1426C207.034 -15.0124 207.946 -6.92771 207.302 0.369995C206.659 7.66769 207.034 10.7442 207.302 11.3702C208.555 16.1101 211.059 27.0388 211.059 32.834V47.5904C212.937 54.4767 216.693 70.0201 216.693 77.1032C216.693 85.957 216.425 102.86 215.888 110.372C215.459 116.382 214.457 123.608 214.01 126.47H183.156C181.278 113.771 177.36 86.8692 176.717 80.8594C175.912 73.347 176.18 69.8591 175.375 68.2494C174.57 66.6396 173.229 65.0298 171.619 65.0298H169.472C167.863 65.0298 166.521 66.6396 165.716 68.2494C164.911 69.8591 165.18 73.347 164.375 80.8594C163.731 86.8692 159.814 113.771 157.936 126.47H127.081C126.634 123.608 125.633 116.382 125.203 110.372C124.667 102.86 124.398 85.957 124.398 77.1032C124.398 70.0201 128.155 54.4767 130.033 47.5904V32.834C130.033 27.0388 132.537 16.1101 133.789 11.3702C134.057 10.7442 134.433 7.66769 133.789 0.369995C133.145 -6.92771 134.057 -15.0124 134.594 -18.1426V-64.0215L132.984 -68.046C132 -64.2004 129.925 -55.9726 129.496 -53.8262C129.067 -51.6798 122.341 -33.6144 119.032 -24.85C118.049 -17.8743 115.491 -1.83006 113.13 6.54085C110.179 17.0045 105.886 23.4436 104.813 28.273C103.954 32.1365 102.845 39.8992 102.398 43.2977C102.756 43.5659 103.954 44.4245 105.886 45.7123C108.301 47.3221 112.325 55.9077 115.276 60.737C118.228 65.5664 118.228 66.6396 116.618 68.2494C115.008 69.8591 113.935 66.6396 111.252 63.1517C109.105 60.3614 106.78 58.5906 105.886 58.054C104.276 60.4687 101.057 66.1566 101.057 69.5909C101.057 73.8836 102.666 77.1032 106.691 80.8594C110.715 84.6155 109.105 84.3472 108.837 85.957C108.623 87.2448 106.78 85.957 105.886 85.1521C106.422 85.957 107.335 87.9424 106.691 89.4449C105.886 91.323 99.1784 84.6155 95.154 79.5179C91.9344 75.4397 90.3581 71.0218 90.8052 70.1274C90.6264 69.2331 89.9445 69.0542 90.2687 66.6396C90.5628 64.4494 89.8215 53.8507 90.2687 48.6636C90.1793 40.1675 90.9685 18.7216 93.5442 0.906586C96.1198 -16.9084 99.6256 -26.0126 101.057 -28.3379C101.772 -31.6469 103.525 -39.66 104.813 -45.2406C106.422 -52.2164 116.886 -84.1438 120.642 -94.3391C124.398 -104.534 130.239 -103.528 139.155 -106.413C148.277 -109.364 154.09 -110.169 155.521 -114.461Z"
                        fill="white"
                      />
                      <g id="Group 36">
                        <g id="Group 35">
                          <g id="Group 34">
                            <path
                              id="Vector 20"
                              d="M112.537 -22.9721C114.637 -22.1714 119.246 -24.4114 119.781 -26.46L118.44 -21.094C118.607 -18.7627 117.366 -14.3866 117.366 -14.3866C117.366 -14.3866 116.1 -7.39399 115.757 -4.72785L111.732 10.5652C110.835 13.3294 106.666 22.6805 106.903 22.6386C106.903 22.6386 104.879 27.4044 104.488 29.346L103.159 37.9316L102.342 41.6877C102.719 42.4351 102.275 42.6503 102.342 43.0292C102.426 43.5029 107.106 46.7854 107.106 46.7854C106.885 47.1008 109.854 51.3465 109.854 51.3465L112.805 56.4441L117.098 64.2248L117.903 66.6394L115.488 68.5175L114.147 67.176L110.391 62.0784L106.1 58.0539C106.1 58.0539 102.788 63.9829 102.342 64.2248L100.732 69.5907L101.537 73.2414L104.442 78.7128L109.297 84.0788L108.878 86.2252L107.901 86.2252L106.1 85.152C106.167 85.5309 106.952 87.5032 107.106 88.3716L106.572 89.9814L104.442 88.6118L100.732 85.6886L97.0838 81.6102L94.0245 77.6396L92.1464 73.8835L91.3415 72.1949L90.5366 68.2492L90 62.615L90.5366 56.9806L90.5366 39.2731L92.1464 12.175L94.0245 -1.23991C94.3166 -6.50218 97.7806 -19.7525 97.7806 -19.7525C97.7806 -19.7525 99.3904 -24.8501 101.268 -29.1429C101.422 -31.0426 102.74 -38.5097 102.878 -37.7285L104.488 -43.631C105.293 -35.8503 104.756 -36.1187 105.561 -31.6594C106.423 -26.8846 109.912 -23.9729 112.537 -22.9721Z"
                              fill="url(#paint0_linear_128_2283)"
                            />
                            <path
                              id="Vector 18"
                              d="M158.416 58.8585C162.06 62.8988 165.392 69.0538 165.392 69.0538L164.318 82.737L161.635 101.786L158.952 119.494L158.148 126.201L127.285 126.209L125.342 112.287L124.879 99.908L124.61 91.859V79.7856V72.5416L130.245 47.59V29.8823L153.586 53.7608C153.586 53.7608 153.037 52.8953 158.416 58.8585Z"
                              fill="url(#paint1_linear_128_2283)"
                            />
                            <path
                              id="Vector 47"
                              d="M126.765 137.137C127.39 133.599 127.199 128.204 127.025 125.949L136.132 121.526L157.988 125.949V131.153C157.988 132.714 157.467 141.04 155.906 146.764C154.657 151.344 150.876 164.11 149.141 169.921C148.881 171.569 148.257 176.322 147.84 182.15C147.32 189.435 144.198 204.006 141.856 211.031C139.514 218.056 137.693 230.285 137.433 232.886C137.173 235.488 137.953 239.911 137.953 242.513C137.953 245.115 138.214 249.018 137.433 251.1C136.652 253.181 137.433 259.686 137.433 262.027C137.433 264.369 136.132 264.889 135.612 267.491C135.091 270.093 136.132 270.614 136.132 272.175C136.132 273.736 135.872 273.476 135.612 274.777C135.351 276.078 134.311 278.419 130.928 278.94C128.374 279.333 127.937 277.748 128.042 276.766C127.903 277.008 127.33 277.417 125.725 277.899C123.643 278.523 123.123 277.292 123.123 276.598C123.036 276.771 122.498 277.274 121.041 277.899C119.584 278.523 119.22 277.292 119.22 276.598C119.133 276.771 118.752 277.118 117.919 277.118C117.086 277.118 116.705 276.424 116.618 276.078C116.618 276.251 116.254 276.702 114.797 277.118C112.975 277.639 112.455 273.996 112.715 272.175C112.975 270.353 115.577 264.629 116.878 262.808C118.179 260.987 120.521 251.1 120.781 249.799C121.041 248.498 120.261 243.814 121.562 234.968C122.862 226.122 120 214.673 120.261 193.078C120.521 171.482 125.985 141.561 126.765 137.137Z"
                              fill="#F5F3F3"
                              stroke="black"
                              stroke-width="0.520374"
                            />
                          </g>
                        </g>
                        <g id="Group 33">
                          <path
                            id="Vector 11"
                            d="M171.187 10.9708C171.262 10.8436 171.221 10.6789 171.094 10.6029C170.967 10.5269 170.802 10.5685 170.726 10.6957L171.187 10.9708ZM170.726 10.6957C170.655 10.8146 170.631 10.9491 170.629 11.0739C170.626 11.2 170.644 11.3342 170.674 11.4676C170.733 11.7341 170.843 12.032 170.966 12.3191C171.09 12.6081 171.231 12.897 171.354 13.1463C171.481 13.4009 171.584 13.6049 171.643 13.7398L172.135 13.526C172.069 13.3745 171.955 13.1504 171.835 12.9077C171.712 12.6598 171.577 12.3822 171.459 12.108C171.341 11.8318 171.246 11.5698 171.198 11.352C171.174 11.2433 171.164 11.1543 171.165 11.0852C171.166 11.0148 171.18 10.9821 171.187 10.9708L170.726 10.6957ZM171.643 13.7398C171.75 13.9856 171.823 14.3555 171.831 14.7471C171.838 15.1396 171.778 15.5198 171.647 15.7943L172.131 16.0256C172.311 15.6492 172.375 15.1773 172.367 14.7371C172.359 14.2961 172.277 13.8529 172.135 13.526L171.643 13.7398ZM171.647 15.7943C171.609 15.8732 171.558 15.9065 171.484 15.9219C171.397 15.9402 171.278 15.9304 171.141 15.8945C171.007 15.8595 170.878 15.8059 170.78 15.7592C170.732 15.7362 170.692 15.7156 170.665 15.701C170.652 15.6937 170.642 15.688 170.635 15.6843C170.632 15.6824 170.63 15.6811 170.628 15.6803C170.628 15.6799 170.627 15.6796 170.627 15.6795C170.627 15.6795 170.627 15.6795 170.627 15.6795C170.627 15.6795 170.627 15.6795 170.627 15.6796C170.627 15.6796 170.627 15.6796 170.627 15.6796C170.627 15.6796 170.627 15.6797 170.49 15.9099C170.352 16.1402 170.352 16.1402 170.352 16.1403C170.352 16.1403 170.352 16.1403 170.352 16.1404C170.352 16.1405 170.353 16.1405 170.353 16.1406C170.353 16.1408 170.353 16.141 170.354 16.1413C170.355 16.1418 170.356 16.1425 170.357 16.1433C170.36 16.1449 170.364 16.1472 170.369 16.1499C170.378 16.1554 170.392 16.163 170.409 16.1723C170.443 16.1908 170.491 16.2159 170.549 16.2437C170.665 16.2988 170.827 16.3671 171.005 16.4136C171.178 16.459 171.39 16.4898 171.594 16.4471C171.812 16.4015 172.013 16.2721 172.131 16.0256L171.647 15.7943Z"
                            fill="black"
                          />
                          <path
                            id="Vector 12"
                            d="M152.245 51.6145C152.245 51.6145 154.016 54.0479 155.626 55.6702C157.033 57.0885 158.684 58.5902 158.684 58.5902"
                            stroke="black"
                            stroke-width="0.536595"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                  <path
                    id="Vector_2"
                    d="M171.619 65.0298H169.472C167.863 65.0298 166.521 66.6396 165.716 68.2494C164.911 69.8592 165.18 73.347 164.375 80.8594C163.731 86.8692 159.814 113.771 157.935 126.47H127.081C126.634 123.608 125.632 116.382 125.203 110.372C124.667 102.86 124.398 85.957 124.398 77.1032C124.398 70.0201 128.154 54.4767 130.033 47.5904V32.834C130.033 27.0388 132.537 16.1101 133.789 11.3702C134.057 10.7442 134.433 7.6677 133.789 0.369996C133.145 -6.92771 134.057 -15.0124 134.594 -18.1426V-64.0215L132.984 -68.046C132 -64.2004 129.925 -55.9726 129.496 -53.8262C129.067 -51.6798 122.341 -33.6144 119.032 -24.85C118.049 -17.8743 115.491 -1.83005 113.13 6.54084C110.178 17.0045 105.886 23.4436 104.813 28.273C103.954 32.1365 102.845 39.8992 102.398 43.2977C102.756 43.566 103.954 44.4245 105.886 45.7123C108.3 47.3221 112.325 55.9077 115.276 60.737C118.227 65.5664 118.227 66.6396 116.618 68.2494C115.008 69.8592 113.935 66.6396 111.252 63.1517C109.105 60.3614 106.78 58.5906 105.886 58.054C104.276 60.4687 101.056 66.1566 101.056 69.5908C101.056 73.8836 102.666 77.1032 106.691 80.8594C110.715 84.6155 109.105 84.3472 108.837 85.957C108.622 87.2448 106.78 85.957 105.886 85.1521C106.422 85.957 107.335 87.9424 106.691 89.4449C105.886 91.323 99.1783 84.6155 95.1538 79.5179C91.9342 75.4397 91.0733 71.7372 91.0733 71.7372C91.0733 71.7372 90.4808 69.5908 90.2684 66.6396C90.1099 64.4354 90.1456 53.8507 90.5928 48.6636C90.5033 40.1675 90.9684 18.7216 93.544 0.906593C96.1197 -16.9084 99.6255 -26.0126 101.056 -28.3379C101.772 -31.6469 103.525 -39.66 104.813 -45.2406C106.422 -52.2164 116.886 -84.1438 120.642 -94.3391C124.398 -104.534 130.238 -103.528 139.155 -106.413C148.277 -109.364 154.09 -110.169 155.521 -114.461C157.488 -120.364 157.667 -128.681 157.667 -128.681H182.351M171.619 65.0298C173.228 65.0298 174.57 66.6396 175.375 68.2494C176.18 69.8592 175.911 73.347 176.716 80.8594C177.36 86.8692 181.087 113.25 182.966 125.95L213.928 126.21C214.375 123.348 215.459 116.382 215.888 110.372C216.424 102.86 216.693 85.957 216.693 77.1032C216.693 70.0201 212.937 54.4767 211.058 47.5904V32.834C211.058 27.0388 208.554 16.1101 207.302 11.3702C207.034 10.7442 206.658 7.6677 207.302 0.369996C207.946 -6.92771 207.034 -15.0124 206.497 -18.1426V-64.0215L208.107 -68.046C209.091 -64.2004 211.166 -55.9726 211.595 -53.8262C212.024 -51.6798 218.75 -33.6144 222.059 -24.85C223.042 -17.8743 225.6 -1.83005 227.961 6.54084C230.913 17.0045 235.205 23.4436 236.278 28.273C237.137 32.1365 238.246 39.8992 238.693 43.2977C238.335 43.566 237.137 44.4245 235.205 45.7123C232.791 47.3221 228.766 55.9077 225.815 60.737C222.864 65.5664 222.864 66.6396 224.473 68.2494C226.083 69.8592 227.156 66.6396 229.839 63.1517C231.986 60.3614 234.311 58.5906 235.205 58.054C236.815 60.4687 240.035 66.1566 240.035 69.5908C240.035 73.8836 238.425 77.1032 234.4 80.8594C230.376 84.6155 231.986 84.3472 232.254 85.957C232.469 87.2448 234.311 85.957 235.205 85.1521C234.669 85.957 233.757 87.9424 234.4 89.4449C235.205 91.323 241.913 84.6155 245.937 79.5179C249.157 75.4397 250.23 71.7372 250.498 70.3957C251.515 66.9079 250.945 53.8507 250.498 48.6636C250.588 40.1675 250.123 18.7216 247.547 0.906593C244.971 -16.9084 241.466 -26.0126 240.035 -28.3379C239.319 -31.6469 237.566 -39.66 236.278 -45.2406C234.669 -52.2164 224.205 -84.1438 220.449 -94.3391C216.693 -104.534 210.853 -103.528 201.936 -106.413C192.814 -109.364 187.001 -110.169 185.57 -114.461C183.603 -120.364 183.424 -128.681 183.424 -128.681H158.74M171.619 65.0298H169.204"
                    stroke="black"
                    stroke-width="0.536595"
                    stroke-linecap="round"
                  />
                  <path
                    id="Vector 48"
                    d="M127.545 128.811C127.285 126.99 127.228 125.855 127.025 124.128L131.968 119.965L140.034 116.582L158.247 122.566L157.727 126.729L127.545 128.811Z"
                    fill="#F5F3F3"
                  />
                  <path
                    id="Vector 47_2"
                    d="M214.188 137.137C213.564 133.599 213.754 128.204 213.928 125.949L204.821 121.526L182.966 125.949V131.153C182.966 132.714 183.486 141.04 185.047 146.764C186.296 151.344 190.077 164.11 191.812 169.921C192.072 171.569 192.697 176.322 193.113 182.15C193.633 189.435 196.756 204.006 199.097 211.031C201.439 218.056 203.26 230.285 203.52 232.886C203.781 235.488 203 239.911 203 242.513C203 245.115 202.74 249.018 203.52 251.1C204.301 253.181 203.52 259.686 203.52 262.027C203.52 264.369 204.821 264.889 205.342 267.491C205.862 270.093 204.821 270.614 204.821 272.175C204.821 273.736 205.082 273.476 205.342 274.777C205.602 276.078 206.643 278.419 210.025 278.94C212.579 279.333 213.016 277.748 212.911 276.766C213.05 277.008 213.624 277.417 215.229 277.899C217.31 278.523 217.831 277.292 217.831 276.598C217.917 276.771 218.455 277.274 219.912 277.899C221.369 278.523 221.734 277.292 221.734 276.598C221.82 276.771 222.202 277.118 223.034 277.118C223.867 277.118 224.249 276.424 224.335 276.078C224.335 276.251 224.7 276.702 226.157 277.118C227.978 277.639 228.498 273.996 228.238 272.175C227.978 270.353 225.376 264.629 224.075 262.808C222.774 260.987 220.433 251.1 220.172 249.799C219.912 248.498 220.693 243.814 219.392 234.968C218.091 226.122 220.953 214.673 220.693 193.078C220.433 171.482 214.969 141.561 214.188 137.137Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.520374"
                  />
                  <path
                    id="Vector 49"
                    d="M213.407 128.811C213.667 126.99 213.724 125.855 213.927 124.128L208.983 119.965L200.918 116.582L182.704 121.266L183.225 126.729L213.407 128.811Z"
                    fill="white"
                  />
                  <g id="Group 223">
                    <path
                      id="Vector 12_2"
                      d="M132.369 140.431C132.369 140.431 133.466 142.367 134.509 143.69C135.42 144.846 136.501 146.082 136.501 146.082"
                      stroke="black"
                      stroke-width="0.461794"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 41"
                      d="M149.971 142.222C149.971 142.222 148.506 143.898 147.219 144.983C146.093 145.932 144.785 146.925 144.785 146.925"
                      stroke="black"
                      stroke-width="0.461794"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </g>
                  <g id="Group 224">
                    <path
                      id="Vector 12_3"
                      d="M208.633 140.431C208.633 140.431 207.535 142.367 206.493 143.69C205.582 144.846 204.5 146.082 204.5 146.082"
                      stroke="black"
                      stroke-width="0.461794"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 41_2"
                      d="M191.031 142.222C191.031 142.222 192.496 143.898 193.783 144.983C194.909 145.932 196.217 146.925 196.217 146.925"
                      stroke="black"
                      stroke-width="0.461794"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </g>
                </g>
                {findMeasure('lower_length') && (
                  <g id="lower_length">
                    <path
                      id="Vector 19"
                      d="M221.565 195.235L221.565 26.4786"
                      stroke="#E55959"
                      stroke-width="2.1941"
                      stroke-linecap="square"
                    />
                    <path
                      id="Vector 29"
                      d="M126.568 25.9467L221.939 25.9467"
                      stroke="#E55959"
                      stroke-width="1.46273"
                      stroke-linecap="square"
                      stroke-dasharray="4.88 4.88"
                    />
                    <path
                      id="Vector 28"
                      d="M225.729 192.583L221.542 196.692L217.909 192.46"
                      stroke="#E55959"
                      stroke-width="2.1941"
                    />
                  </g>
                )}
                {findMeasure('insideLeg') && (
                  <g id="inside_leg">
                    <path
                      id="Vector 29_2"
                      d="M126.568 25.9467L222.015 25.9466"
                      stroke="#E55959"
                      stroke-width="1.46273"
                      stroke-linecap="square"
                      stroke-dasharray="4.95 4.95"
                    />
                    <g id="Group 225">
                      <path
                        id="Vector 19_2"
                        d="M170.911 221.398L170.911 26.6904"
                        stroke="#E55959"
                        stroke-width="2.1941"
                        stroke-linecap="square"
                      />
                      <path
                        id="Vector 28_2"
                        d="M175.138 218.704L170.886 222.879L167.195 218.58"
                        stroke="#E55959"
                        stroke-width="2.1941"
                      />
                    </g>
                  </g>
                )}
                {findMeasure('thigh') && (
                  <g id="thigh">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M216.965 82.2618C216.965 82.2618 209.91 83.191 193.684 83.5778M177.033 82.9932C177.033 82.9932 180.728 82.92 189.15 83.5778"
                            stroke="#E55959"
                            stroke-width="1.46273"
                          />
                          <path
                            id="Vector 27"
                            d="M195.647 81.5529L192.8 83.467L195.246 85.3046"
                            stroke="#E55959"
                            stroke-width="1.46273"
                          />
                          <path
                            id="Vector 28_3"
                            d="M186.674 81.5529L189.522 83.467L187.075 85.3046"
                            stroke="#E55959"
                            stroke-width="1.46273"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('hip') && (
                  <g id="hip">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M212.87 53.7385C212.87 53.7385 206.164 56.9112 170.601 58.4206M128.762 52.2758C128.762 52.2758 136.818 55.4279 165.949 58.4206"
                            stroke="#E55959"
                            stroke-width="1.46273"
                          />
                          <path
                            id="Vector 27_2"
                            d="M172.617 56.3426L169.695 58.3069L172.205 60.1925"
                            stroke="#E55959"
                            stroke-width="1.46273"
                          />
                          <path
                            id="Vector 28_4"
                            d="M163.408 56.3426L166.33 58.3069L163.82 60.1925"
                            stroke="#E55959"
                            stroke-width="1.46273"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_2283"
              x1="94.0715"
              y1="-52.9237"
              x2="101.273"
              y2="85.5179"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_2283"
              x1="142.248"
              y1="81.86"
              x2="130.531"
              y2="149.768"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
