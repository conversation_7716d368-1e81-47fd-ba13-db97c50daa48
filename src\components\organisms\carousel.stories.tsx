import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Carousel, CarouselItem } from './carousel';

const meta = {
  title: 'Organisms/Carousel',
  component: Carousel,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Carousel>;

export default meta;
type Story = StoryObj<typeof meta>;

const items: CarouselItem[] = [
  { id: '006', content: '006' },
  { id: '007', content: '007' },
  { id: '008', content: '008' },
  { id: '009', content: '009' },
  { id: '010', content: '010' },
];

const SelectionDemo = () => {
  const [selectedItem, setSelectedItem] = useState<CarouselItem>(items[3]);
  return <Carousel items={items} selectedItem={selectedItem} onSelectItem={setSelectedItem} />;
};

export const Default: Story = {
  args: {
    items,
  },
};

export const WithSelection: Story = {
  args: {
    items,
  },
  render: () => <SelectionDemo />,
};
