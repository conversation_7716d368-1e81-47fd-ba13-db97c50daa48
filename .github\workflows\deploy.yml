name: Deploy measurements-table to AWS Cloudfront

on:
  push:
    branches:
      - main
      - develop

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: measurements-table-${{ github.ref_name }}

    steps:
      - name: Repository Checkout
        uses: actions/checkout@v3

      - name: configure Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22.13.1'

      - name: install pnpm
        run: npm install -g pnpm

      - name: install dependencies with pnpm
        run: pnpm install

      - name: Execute Build with pnpm
        env:
          VITE_API_URL: ${{ vars.VITE_API_URL }}
          VITE_S3_URL: ${{ vars.VITE_S3_URL }}
        run: pnpm run build

      - name: Configure AWS CLI
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Sincronizar arquivos com o Bucket S3
        run: |
          aws s3 sync ./dist s3://${{ vars.AWS_BUCKET_NAME }} --delete

      - name: CloudFront Cache Invalidation
        run: |
          aws cloudfront create-invalidation --distribution-id ${{ vars.CLOUDFRONT_DISTRIBUTION_ID }} --paths "/*"
