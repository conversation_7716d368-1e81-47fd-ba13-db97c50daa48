import { useEffect, useRef, useState } from 'react';
import { Container } from '../atoms/container';
import { Button } from '../atoms/button';
import { Text } from '../atoms/text';
import { cn } from '@/lib/utils';
import { useDevice } from '@/hooks/use-device';

export type Tab = {
  id: number;
  label: string;
  content: React.ReactNode;
  customClass?: string;
};

interface TabContainerProps {
  tabs: Tab[];
  activeTab: number;
  onTabChange: (index: number) => void;
  className?: string;
}

export const TabContainer = ({ tabs, activeTab, onTabChange, className }: TabContainerProps) => {
  const [selectedElement, setSelectedElement] = useState<HTMLButtonElement | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const { isMobile } = useDevice();
  const showMeasurementTabs = tabs.length > 1 || isMobile;

  const handleTabClick = ({ currentTarget }: React.MouseEvent<HTMLButtonElement>) => {
    setSelectedElement(currentTarget);
    onTabChange(Number(currentTarget.dataset.tabIndex) || 0);
  };

  useEffect(() => {
    if (containerRef.current && selectedElement) {
      selectedElement.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'nearest' });
    }
  }, [selectedElement]);

  return (
    <Container className={cn('w-full flex flex-col p-0', className)}>
      {showMeasurementTabs && (
        <div
          ref={containerRef}
          className={cn('flex w-full overflow-auto hide-scrollbar flex-none', {
            'justify-center': tabs.length <= 2,
            'justify-between': tabs.length > 2 && !isMobile,
          })}
        >
          {tabs.map((tab, index) => (
            <Button
              variant={activeTab === index ? 'primary' : 'secondary'}
              key={tab.id}
              className={cn(
                'flex-1 px-4 py-2 font-medium transition-colors cursor-pointer rounded-full mb-4 mx-1',
                activeTab === index ? 'text-[#262626] tab__active' : 'text-[#8a8a8d] tab__inactive',
                `tab__button__${tab.customClass}`
              )}
              data-tab-index={index}
              onClick={handleTabClick}
            >
              <Text
                variant="body"
                className={cn(
                  'text-nowrap text-[11px] xsm:text-[12px]',
                  activeTab === index ? 'text-[#FFFFFF]' : 'text-[#4F4F4F]'
                )}
              >
                {tab.label}
              </Text>
            </Button>
          ))}
        </div>
      )}
      <div
        className={cn(
          'overflow-y-scroll flex-auto px-3',
          {
            'p-0 custom-scroll': !isMobile,
            'hide-scrollbar': isMobile,
          },
          `tab__content__${tabs[activeTab].customClass}`
        )}
      >
        {tabs[activeTab].content}
      </div>
    </Container>
  );
};
