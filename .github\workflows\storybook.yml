name: Deploy storybook

on:
  push:
    branches:
      - develop

jobs:
  storybook:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '22.13.1'
      
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10

      - name: Install Dependencies
        run: pnpm install

      - name: Build Storybook
        run: pnpm build-storybook

      - name: Deploy Storybook
        uses: keithweaver/aws-s3-github-action@v1.0.0
        with:
          command: cp
          source: storybook-static/
          destination: s3://szb-docs/measurements-table
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws_region: us-east-1
          flags: --recursive
