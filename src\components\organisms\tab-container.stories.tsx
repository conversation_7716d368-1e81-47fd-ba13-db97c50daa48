import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { TabContainer } from './tab-container';

const meta = {
  title: 'Organisms/Tab-Container',
  component: TabContainer,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    className: { control: 'text' },
    tabs: { control: 'object' },
    activeTab: { control: 'number' },
    onTabChange: { action: 'changed' },
  },
} satisfies Meta<typeof TabContainer>;

export default meta;
type Story = StoryObj<typeof meta>;

const InteractiveTabContainer = () => {
  const [activeTab, setActiveTab] = useState(0);
  const handleTabChange = (index: number) => {
    setActiveTab(index);
  };
  return (
    <div className="w-lg">
      <TabContainer
        tabs={[
          { id: 1, label: 'Body measurements', content: 'Tab 1 content' },
          { id: 2, label: 'Garment measurements', content: 'Tab 2 content' },
          { id: 3, label: 'Size conversion', content: 'Tab 3 content' },
        ]}
        activeTab={activeTab}
        onTabChange={handleTabChange}
      />
    </div>
  );
};

export const Default: Story = {
  args: {} as Story['args'],
  render: () => <InteractiveTabContainer />,
};
