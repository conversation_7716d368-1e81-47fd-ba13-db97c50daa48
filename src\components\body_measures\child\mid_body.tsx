import { useDevice } from '@/hooks/use-device';
import { BodyMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface MidBodyProps {
  measure: BodyMeasure;
  className?: string;
}

export function MidBody({ measure, className }: MidBodyProps) {
  const { measures } = measure;
  const mappedMeasures = measures.map((item) => item.measure);
  const { isMobile } = useDevice();

  const findMeasure = (measure: string) => {
    const foundMeasure = mappedMeasures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full flex justify-center', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_mid_body">
            <mask id="mask0_128_1517" maskUnits="userSpaceOnUse" x="0" y="0" width="331" height="193">
              <rect id="rect" width="331" height="193" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_1517)">
              <g id="group_mid_body_mask">
                <g id="child">
                  <g id="Group 38">
                    <g id="Group 37">
                      <path
                        id="Vector"
                        d="M151.096 -2.89418C152.926 -8.38318 153.092 -16.1177 153.092 -16.1177H154.09H176.046H177.044C177.044 -16.1177 177.211 -8.38318 179.04 -2.89418C180.371 1.09782 185.777 1.84632 194.26 4.59082C202.551 7.27337 207.982 6.33732 211.475 15.8183C214.968 25.2993 224.699 54.9898 226.196 61.4768C227.393 66.6664 229.023 74.1181 229.689 77.1953C231.019 79.3576 234.28 87.824 236.675 104.391C239.07 120.958 239.502 140.901 239.419 148.802C239.835 153.625 239.91 165.023 239.91 165.023L239.419 169.62C239.419 169.62 238.172 173.702 235.178 177.494C231.435 182.235 225.198 188.472 224.449 186.726C223.851 185.329 224.699 183.482 225.198 182.734C224.366 183.482 222.653 184.68 222.453 183.482C222.204 181.985 220.707 182.235 224.449 178.742C228.192 175.249 229.689 172.255 229.689 168.263C229.689 165.069 226.695 159.78 225.198 157.534C224.366 158.033 222.204 159.68 220.208 162.275C217.713 165.518 216.715 168.512 215.218 167.015C213.721 165.518 213.721 164.52 216.465 160.029C219.21 155.538 222.952 147.554 225.198 146.057C226.994 144.86 228.109 144.061 228.441 143.812C228.025 140.651 226.994 133.433 226.196 129.84C225.198 125.349 221.206 119.361 218.461 109.63C216.266 101.846 213.887 86.9258 212.972 80.4388C209.895 72.2885 203.641 55.4888 203.242 53.4928C202.843 51.4968 200.913 43.8455 199.998 40.2693L198.501 44.0118V86.6763C199 89.5871 199.849 97.1054 199.25 103.892C198.651 110.678 199 113.539 199.25 114.121C200.414 118.529 202.743 128.692 202.743 134.081V147.804C204.489 154.208 207.982 168.662 207.982 175.249C207.982 183.482 207.733 199.201 207.234 206.187C206.835 211.776 205.903 218.495 205.487 221.157H176.795C175.048 209.347 171.406 184.331 170.807 178.742C170.058 171.756 170.308 168.512 169.559 167.015C168.811 165.518 167.563 164.021 166.066 164.021H164.07C162.573 164.021 161.326 165.518 160.577 167.015C159.829 168.512 160.078 171.756 159.33 178.742C158.731 184.331 155.088 209.347 153.342 221.157H124.649C124.233 218.495 123.302 211.776 122.903 206.187C122.404 199.201 122.154 183.482 122.154 175.249C122.154 168.662 125.647 154.208 127.394 147.804V134.081C127.394 128.692 129.722 118.529 130.887 114.121C131.136 113.539 131.486 110.678 130.887 103.892C130.288 97.1054 131.136 89.5871 131.635 86.6763V44.0118L130.138 40.2693C129.223 43.8455 127.294 51.4968 126.895 53.4928C126.496 55.4888 120.241 72.2885 117.164 80.4388C116.249 86.9258 113.871 101.846 111.675 109.63C108.931 119.361 104.939 125.349 103.941 129.84C103.142 133.433 102.111 140.651 101.695 143.812C102.028 144.061 103.142 144.86 104.939 146.057C107.184 147.554 110.927 155.538 113.671 160.029C116.416 164.52 116.416 165.518 114.919 167.015C113.422 168.512 112.424 165.518 109.929 162.275C107.933 159.68 105.77 158.033 104.939 157.534C103.442 159.78 100.448 165.069 100.448 168.263C100.448 172.255 101.945 175.249 105.687 178.742C109.43 182.235 107.933 181.985 107.683 183.482C107.484 184.68 105.77 183.482 104.939 182.734C105.438 183.482 106.286 185.329 105.687 186.726C104.939 188.472 98.7013 182.235 94.9588 177.494C91.9648 173.702 90.499 169.593 90.9148 168.762C90.7485 167.93 90.1143 167.764 90.4158 165.518C90.6893 163.482 90 153.625 90.4158 148.802C90.3327 140.901 91.0666 120.958 93.4618 104.391C95.857 87.824 99.1172 79.3576 100.448 77.1953C101.113 74.1181 102.743 66.6664 103.941 61.4768C105.438 54.9898 115.168 25.2993 118.661 15.8183C122.154 6.33732 127.585 7.27337 135.877 4.59082C144.36 1.84632 149.766 1.09782 151.096 -2.89418Z"
                        fill="white"
                      />
                      <g id="Group 36">
                        <g id="Group 35">
                          <g id="Group 34">
                            <path
                              id="Vector 20"
                              d="M111.124 82.1851C113.076 82.9297 117.363 80.8467 117.86 78.9416L116.613 83.9316C116.769 86.0996 115.615 90.1691 115.615 90.1691C115.615 90.1691 114.437 96.6717 114.118 99.1511L110.375 113.373C109.541 115.943 105.664 124.639 105.884 124.6C105.884 124.6 104.002 129.032 103.639 130.838L102.402 138.822L101.643 142.315C101.993 143.01 101.58 143.21 101.643 143.562C101.721 144.003 106.073 147.055 106.073 147.055C105.868 147.348 108.629 151.297 108.629 151.297L111.373 156.037L115.365 163.273L116.114 165.518L113.868 167.265L112.621 166.017L109.128 161.277L105.138 157.534C105.138 157.534 102.058 163.048 101.643 163.273L100.146 168.263L100.894 171.658L103.596 176.746L108.111 181.736L107.721 183.732L106.813 183.732L105.138 182.734C105.2 183.086 105.93 184.92 106.073 185.728L105.577 187.225L103.596 185.951L100.146 183.233L96.7532 179.44L93.9083 175.748L92.1618 172.255L91.4133 170.684L90.6648 167.015L90.1658 161.776L90.6648 156.536L90.6648 140.069L92.1618 114.87L93.9083 102.395C94.18 97.5011 97.4013 85.1791 97.4013 85.1791C97.4013 85.1791 98.8983 80.4386 100.645 76.4466C100.787 74.6801 102.013 67.7361 102.142 68.4626L103.639 62.9736C104.387 70.2091 103.888 69.9596 104.637 74.1064C105.438 78.5467 108.683 81.2544 111.124 82.1851Z"
                              fill="url(#paint0_linear_128_1517)"
                            />
                            <path
                              id="Vector 18"
                              d="M153.788 158.282C157.177 162.04 160.275 167.763 160.275 167.763L159.277 180.488L156.782 198.202L154.287 214.669L153.539 220.907L124.839 220.914L123.032 207.968L122.601 196.456L122.351 188.971V177.743V171.007L127.591 147.803V131.336L149.297 153.542C149.297 153.542 148.786 152.737 153.788 158.282Z"
                              fill="url(#paint1_linear_128_1517)"
                            />
                          </g>
                        </g>
                        <g id="Group 33">
                          <path
                            id="Vector 11"
                            d="M165.664 113.75C165.735 113.631 165.696 113.478 165.578 113.407C165.459 113.337 165.306 113.375 165.236 113.494L165.664 113.75ZM165.236 113.494C165.17 113.604 165.148 113.729 165.145 113.845C165.143 113.963 165.16 114.087 165.187 114.212C165.242 114.459 165.345 114.736 165.459 115.003C165.574 115.272 165.705 115.541 165.82 115.773C165.938 116.009 166.034 116.199 166.088 116.324L166.546 116.126C166.485 115.985 166.379 115.776 166.267 115.551C166.153 115.32 166.027 115.062 165.918 114.807C165.808 114.55 165.719 114.307 165.675 114.104C165.652 114.003 165.643 113.92 165.644 113.856C165.645 113.79 165.658 113.76 165.664 113.75L165.236 113.494ZM166.088 116.324C166.188 116.553 166.256 116.897 166.263 117.261C166.27 117.626 166.214 117.98 166.092 118.235L166.542 118.45C166.71 118.1 166.77 117.661 166.762 117.252C166.754 116.842 166.678 116.43 166.546 116.126L166.088 116.324ZM166.092 118.235C166.057 118.308 166.009 118.339 165.941 118.354C165.86 118.371 165.749 118.362 165.621 118.328C165.497 118.296 165.377 118.246 165.286 118.202C165.241 118.181 165.204 118.162 165.179 118.148C165.167 118.141 165.158 118.136 165.152 118.133C165.149 118.131 165.146 118.13 165.145 118.129C165.144 118.129 165.144 118.128 165.144 118.128C165.144 118.128 165.144 118.128 165.144 118.128C165.144 118.128 165.144 118.128 165.144 118.128C165.144 118.128 165.144 118.128 165.144 118.128C165.144 118.128 165.144 118.128 165.016 118.343C164.888 118.557 164.888 118.557 164.888 118.557C164.888 118.557 164.888 118.557 164.888 118.557C164.888 118.557 164.889 118.557 164.889 118.557C164.889 118.557 164.889 118.557 164.89 118.558C164.891 118.558 164.892 118.559 164.893 118.56C164.896 118.561 164.899 118.563 164.904 118.566C164.912 118.571 164.925 118.578 164.941 118.587C164.972 118.604 165.017 118.627 165.072 118.653C165.179 118.704 165.33 118.768 165.495 118.811C165.657 118.853 165.853 118.882 166.043 118.842C166.246 118.8 166.433 118.679 166.542 118.45L166.092 118.235Z"
                            fill="black"
                          />
                          <path
                            id="Vector 17"
                            d="M163.674 44.7605C163.674 44.7605 163.423 46.3582 163.418 47.5864C163.414 48.6602 163.47 49.8584 163.47 49.8584"
                            stroke="black"
                            stroke-width="0.498999"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <path
                            id="Vector 12"
                            d="M148.05 151.546C148.05 151.546 149.697 153.809 151.194 155.317C152.502 156.636 154.038 158.033 154.038 158.033"
                            stroke="black"
                            stroke-width="0.498999"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <g id="Group 32">
                            <path
                              id="Vector 15"
                              d="M109.128 76.696C109.128 76.696 109.378 78.9415 110.126 79.9395C110.875 80.9375 111.873 81.187 111.873 81.187"
                              stroke="black"
                              stroke-width="0.498999"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              id="Vector 16"
                              d="M222.152 76.696C222.152 76.696 221.902 78.9415 221.154 79.9395C220.405 80.9375 219.407 81.187 219.407 81.187"
                              stroke="black"
                              stroke-width="0.498999"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                          <g id="Group 27">
                            <path
                              id="Vector 15_2"
                              d="M162.521 12.8242C162.521 12.8242 161.988 11.3087 160.525 10.4216C159.061 9.53452 154.37 9.49756 152.042 7.83423"
                              stroke="black"
                              stroke-width="0.498999"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              id="Vector 14"
                              d="M166.512 12.8242C166.512 12.8242 167.045 11.3087 168.508 10.4216C169.972 9.53452 174.663 9.49756 176.991 7.83423"
                              stroke="black"
                              stroke-width="0.498999"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                  </g>
                  <path
                    id="Vector_2"
                    d="M166.066 164.021H164.07C162.573 164.021 161.325 165.518 160.577 167.015C159.828 168.512 160.078 171.756 159.329 178.742C158.731 184.331 155.088 209.347 153.341 221.157H124.649C124.233 218.495 123.302 211.776 122.902 206.187C122.403 199.201 122.154 183.482 122.154 175.249C122.154 168.662 125.647 154.208 127.393 147.804V134.081C127.393 128.692 129.722 118.529 130.886 114.121C131.136 113.539 131.485 110.678 130.886 103.892C130.288 97.1054 131.136 89.5871 131.635 86.6763V44.0118L130.138 40.2693C129.223 43.8455 127.294 51.4968 126.894 53.4928C126.495 55.4888 120.241 72.2885 117.164 80.4388C116.249 86.9258 113.87 101.846 111.675 109.63C108.93 119.361 104.938 125.349 103.94 129.84C103.142 133.433 102.111 140.651 101.695 143.812C102.028 144.061 103.142 144.86 104.938 146.057C107.184 147.554 110.926 155.538 113.671 160.029C116.415 164.52 116.415 165.518 114.918 167.015C113.421 168.512 112.423 165.518 109.928 162.275C107.932 159.68 105.77 158.033 104.938 157.534C103.441 159.78 100.447 165.069 100.447 168.263C100.447 172.255 101.944 175.249 105.687 178.742C109.429 182.235 107.932 181.985 107.683 183.482C107.483 184.68 105.77 183.482 104.938 182.734C105.437 183.482 106.286 185.329 105.687 186.726C104.938 188.472 98.7008 182.235 94.9583 177.494C91.9643 173.702 91.1638 170.259 91.1638 170.259C91.1638 170.259 90.6127 168.263 90.4153 165.518C90.2678 163.469 90.301 153.625 90.7169 148.802C90.6337 140.901 91.0662 120.958 93.4614 104.391C95.8566 87.824 99.1167 79.3576 100.447 77.1953C101.113 74.1181 102.743 66.6664 103.94 61.4768C105.437 54.9898 115.168 25.2993 118.661 15.8183C122.154 6.33732 127.585 7.27337 135.876 4.59082C144.359 1.84632 149.765 1.09782 151.096 -2.89418C152.925 -8.38318 153.092 -16.1177 153.092 -16.1177H176.046M166.066 164.021C167.563 164.021 168.81 165.518 169.559 167.015C170.307 168.512 170.058 171.756 170.806 178.742C171.405 184.331 174.871 208.863 176.618 220.673L205.411 220.915C205.826 218.253 206.834 211.776 207.233 206.187C207.732 199.201 207.982 183.482 207.982 175.249C207.982 168.662 204.489 154.208 202.742 147.804V134.081C202.742 128.692 200.414 118.529 199.249 114.121C199 113.539 198.651 110.678 199.249 103.892C199.848 97.1054 199 89.5871 198.501 86.6763V44.0118L199.998 40.2693C200.913 43.8455 202.842 51.4968 203.241 53.4928C203.641 55.4888 209.895 72.2885 212.972 80.4388C213.887 86.9258 216.265 101.846 218.461 109.63C221.205 119.361 225.197 125.349 226.195 129.84C226.994 133.433 228.025 140.651 228.441 143.812C228.108 144.061 226.994 144.86 225.197 146.057C222.952 147.554 219.209 155.538 216.465 160.029C213.72 164.52 213.72 165.518 215.217 167.015C216.714 168.512 217.712 165.518 220.207 162.275C222.203 159.68 224.366 158.033 225.197 157.534C226.694 159.78 229.688 165.069 229.688 168.263C229.688 172.255 228.191 175.249 224.449 178.742C220.706 182.235 222.203 181.985 222.453 183.482C222.652 184.68 224.366 183.482 225.197 182.734C224.698 183.482 223.85 185.329 224.449 186.726C225.197 188.472 231.435 182.235 235.177 177.494C238.171 173.702 239.169 170.259 239.419 169.011C240.365 165.768 239.835 153.625 239.419 148.802C239.502 140.901 239.07 120.958 236.674 104.391C234.279 87.824 231.019 79.3576 229.688 77.1953C229.023 74.1181 227.393 66.6664 226.195 61.4768C224.698 54.9898 214.968 25.2993 211.475 15.8183C207.982 6.33732 202.551 7.27337 194.259 4.59082C185.776 1.84632 180.37 1.09782 179.04 -2.89418C177.21 -8.38318 177.044 -16.1177 177.044 -16.1177H154.09M166.066 164.021H163.82"
                    stroke="black"
                    stroke-width="0.498999"
                    stroke-linecap="round"
                  />
                </g>
                {findMeasure('chest') && (
                  <g id="chest">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M198.5 44.5C198.5 44.5 179.11 48.3886 156.958 47.1347M132 44.5C132 44.5 137.941 47.1347 150.479 47.1347"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 27"
                            d="M159.763 44.2411L155.694 46.9719L159.19 49.5935"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 28"
                            d="M146.941 44.2411L151.01 46.9719L147.514 49.5935"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('waistLower') && (
                  <g id="lower_waist">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M202.5 129C202.5 129 186.301 135.215 156.683 133.552M128 129C128 129 135.718 132.339 150.124 133.552"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 27_2"
                            d="M159.524 130.482L155.404 133.247L158.943 135.901"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 28_2"
                            d="M146.542 130.482L150.662 133.247L147.122 135.901"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('waist') && (
                  <g id="waist">
                    <g id="Group 221_3">
                      <g id="Group 217_3">
                        <g id="Group 220_3">
                          <path
                            id="Ellipse 23_3"
                            d="M199.5 97.5C199.5 97.5 179.129 102.186 156.941 100.93M130.5 98.5C130.5 98.5 137.893 100.93 150.452 100.93"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 27_3"
                            d="M159.752 97.9445L155.676 100.68L159.178 103.306"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 28_3"
                            d="M146.908 97.9445L150.984 100.68L147.482 103.306"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('upper_waist') && (
                  <g id="upper_waist">
                    <g id="Group 221_4">
                      <g id="Group 217_4">
                        <g id="Group 220_4">
                          <path
                            id="Ellipse 23_4"
                            d="M198.5 65.5C198.5 65.5 178.964 70.1136 157.018 68.8713M131.5 66.5C131.5 66.5 139.061 68.068 150.6 68.8713"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 27_4"
                            d="M159.798 65.9691L155.767 68.6745L159.23 71.2718"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 28_4"
                            d="M147.094 65.9691L151.126 68.6745L147.662 71.2718"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('body_length') && (
                  <g id="body_length">
                    <path
                      id="Vector 19"
                      d="M189.527 117.26L189.527 27.9999"
                      stroke="#E55959"
                      stroke-width="2"
                      stroke-linecap="square"
                    />
                    <path
                      id="Vector 29"
                      d="M124 121L206.553 121"
                      stroke="#E55959"
                      stroke-linecap="square"
                      stroke-dasharray="2.58 2.58"
                    />
                    <path
                      id="Vector 28_5"
                      d="M192.762 115.411L189.444 118.668L186.564 115.314"
                      stroke="#E55959"
                      stroke-width="2"
                    />
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_1517"
              x1="93.952"
              y1="54.332"
              x2="100.649"
              y2="183.074"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_1517"
              x1="138.753"
              y1="179.672"
              x2="127.857"
              y2="242.823"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className=" h-full m-auto "
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_mid_body">
            <mask id="mask0_128_2347" maskUnits="userSpaceOnUse" x="0" y="0" width="342" height="291">
              <rect id="rect" width="342" height="291" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_2347)">
              <g id="group_mid_body_mask">
                <g id="child">
                  <g id="Group 38">
                    <g id="Group 37">
                      <path
                        id="Vector"
                        d="M150.97 -1.26191C153.552 -9.00664 153.787 -19.9197 153.787 -19.9197H155.195H186.174H187.582C187.582 -19.9197 187.816 -9.00664 190.398 -1.26191C192.276 4.37062 199.903 5.42673 211.872 9.29909C223.571 13.0841 231.234 11.7633 236.162 25.1406C241.091 38.5179 254.82 80.4098 256.932 89.5627C258.622 96.885 260.922 107.399 261.861 111.741C263.738 114.792 268.338 126.737 271.718 150.112C275.097 173.487 275.707 201.627 275.59 212.774C276.177 219.58 276.283 235.661 276.283 235.661L275.59 242.148C275.59 242.148 273.83 247.907 269.606 253.258C264.325 259.947 255.524 268.748 254.468 266.283C253.623 264.312 254.82 261.707 255.524 260.651C254.351 261.707 251.933 263.397 251.652 261.707C251.3 259.595 249.188 259.947 254.468 255.018C259.749 250.09 261.861 245.866 261.861 240.233C261.861 235.727 257.636 228.264 255.524 225.096C254.351 225.8 251.3 228.123 248.484 231.784C244.963 236.361 243.555 240.585 241.443 238.473C239.331 236.361 239.331 234.952 243.203 228.616C247.075 222.279 252.356 211.014 255.524 208.902C258.059 207.212 259.631 206.086 260.101 205.734C259.514 201.275 258.059 191.089 256.932 186.02C255.524 179.683 249.892 171.234 246.019 157.505C242.921 146.522 239.565 125.47 238.275 116.317C233.933 104.817 225.109 81.1139 224.545 78.2976C223.982 75.4814 221.26 64.6857 219.969 59.6399L217.857 64.9204V125.118C218.561 129.225 219.758 139.833 218.913 149.408C218.068 158.984 218.561 163.02 218.913 163.842C220.556 170.061 223.841 184.4 223.841 192.004V211.366C226.305 220.402 231.234 240.796 231.234 250.09C231.234 261.707 230.882 283.885 230.178 293.742C229.615 301.628 228.3 311.109 227.714 314.864H187.23C184.765 298.201 179.626 262.904 178.781 255.018C177.725 245.161 178.077 240.585 177.021 238.473C175.965 236.361 174.204 234.248 172.092 234.248H169.276C167.164 234.248 165.404 236.361 164.348 238.473C163.291 240.585 163.643 245.161 162.587 255.018C161.742 262.904 156.603 298.201 154.139 314.864H113.655C113.068 311.109 111.754 301.628 111.19 293.742C110.486 283.885 110.134 261.707 110.134 250.09C110.134 240.796 115.063 220.402 117.527 211.366V192.004C117.527 184.4 120.813 170.061 122.456 163.842C122.808 163.02 123.3 158.984 122.456 149.408C121.611 139.833 122.808 129.225 123.512 125.118V64.9204L121.399 59.6399C120.109 64.6857 117.386 75.4814 116.823 78.2976C116.26 81.1139 107.435 104.817 103.094 116.317C101.803 125.47 98.4468 146.522 95.349 157.505C91.4766 171.234 85.8441 179.683 84.4359 186.02C83.3095 191.089 81.8544 201.275 81.2676 205.734C81.737 206.086 83.3094 207.212 85.8441 208.902C89.0124 211.014 94.2929 222.279 98.1652 228.616C102.038 234.952 102.038 236.361 99.9254 238.473C97.8132 240.585 96.4051 236.361 92.8847 231.784C90.0685 228.123 87.0175 225.8 85.8441 225.096C83.7319 228.264 79.5075 235.727 79.5075 240.233C79.5075 245.866 81.6197 250.09 86.9002 255.018C92.1807 259.947 90.0685 259.595 89.7164 261.707C89.4348 263.397 87.0175 261.707 85.8441 260.651C86.5481 261.707 87.745 264.312 86.9002 266.283C85.8441 268.748 77.0432 259.947 71.7627 253.258C67.5383 247.907 65.4701 242.11 66.0568 240.937C65.8221 239.764 64.9274 239.529 65.3528 236.361C65.7386 233.487 64.7661 219.58 65.3528 212.774C65.2354 201.627 66.271 173.487 69.6505 150.112C73.0301 126.737 77.63 114.792 79.5075 111.741C80.4462 107.399 82.7462 96.885 84.4359 89.5627C86.5481 80.4098 100.277 38.5179 105.206 25.1406C110.134 11.7633 117.797 13.0841 129.496 9.29909C141.465 5.42673 149.093 4.37062 150.97 -1.26191Z"
                        fill="white"
                      />
                      <g id="Group 36">
                        <g id="Group 35">
                          <g id="Group 34">
                            <path
                              id="Vector 20"
                              d="M94.5707 118.781C97.3257 119.832 103.374 116.893 104.076 114.205L102.315 121.246C102.536 124.304 100.907 130.046 100.907 130.046C100.907 130.046 99.2463 139.221 98.7951 142.72L93.5147 162.786C92.337 166.412 86.8674 178.682 87.1781 178.627C87.1781 178.627 84.5222 184.88 84.0098 187.428L82.2654 198.693L81.1936 203.621C81.6883 204.602 81.1055 204.884 81.1936 205.382C81.3036 206.003 87.4442 210.31 87.4442 210.31C87.1549 210.724 91.0505 216.295 91.0505 216.295L94.9229 222.983L100.555 233.192L101.611 236.361L98.4432 238.825L96.683 237.065L91.7546 230.376L86.125 225.095C86.125 225.095 81.7796 232.875 81.1936 233.192L79.0814 240.233L80.1375 245.023L83.9494 252.202L90.3193 259.243L89.7702 262.059L88.4883 262.059L86.125 260.651C86.213 261.148 87.2425 263.736 87.4442 264.875L86.7447 266.987L83.9494 265.191L79.0814 261.355L74.2946 256.004L70.2805 250.794L67.8163 245.865L66.7602 243.65L65.7041 238.473L65 231.08L65.7041 223.687L65.7041 200.453L67.8162 164.898L70.2805 147.296C70.6639 140.391 75.2089 123.006 75.2089 123.006C75.2089 123.006 77.3211 116.317 79.7854 110.685C79.9864 108.192 81.716 98.3944 81.8975 99.4195L84.0098 91.6748C85.0659 101.884 84.3617 101.532 85.4178 107.383C86.5486 113.648 91.1268 117.468 94.5707 118.781Z"
                              fill="url(#paint0_linear_128_2347)"
                            />
                            <path
                              id="Vector 18"
                              d="M154.768 226.151C159.55 231.453 163.921 239.528 163.921 239.528L162.513 257.482L158.993 282.476L155.472 305.711L154.416 314.511L113.922 314.522L111.372 296.255L110.764 280.012L110.412 269.451V253.61V244.105L117.805 211.366V188.132L148.432 219.463C148.432 219.463 147.711 218.327 154.768 226.151Z"
                              fill="url(#paint1_linear_128_2347)"
                            />
                          </g>
                        </g>
                        <g id="Group 33">
                          <path
                            id="Vector 11"
                            d="M171.525 163.317C171.624 163.15 171.57 162.934 171.403 162.834C171.236 162.735 171.02 162.789 170.92 162.956L171.525 163.317ZM170.92 162.956C170.827 163.112 170.796 163.289 170.793 163.452C170.789 163.618 170.813 163.794 170.852 163.969C170.929 164.319 171.074 164.71 171.236 165.086C171.398 165.465 171.583 165.845 171.745 166.172C171.911 166.506 172.047 166.773 172.124 166.95L172.769 166.67C172.683 166.471 172.534 166.177 172.376 165.859C172.214 165.533 172.037 165.169 171.883 164.809C171.728 164.447 171.603 164.103 171.54 163.817C171.508 163.675 171.495 163.558 171.497 163.467C171.499 163.375 171.516 163.332 171.525 163.317L170.92 162.956ZM172.124 166.95C172.264 167.273 172.36 167.758 172.37 168.272C172.38 168.787 172.301 169.286 172.129 169.646L172.764 169.95C173 169.456 173.085 168.837 173.074 168.259C173.063 167.68 172.956 167.099 172.769 166.67L172.124 166.95ZM172.129 169.646C172.079 169.75 172.012 169.793 171.915 169.814C171.801 169.838 171.645 169.825 171.465 169.778C171.29 169.732 171.12 169.661 170.991 169.6C170.928 169.57 170.876 169.543 170.841 169.524C170.823 169.514 170.81 169.507 170.802 169.502C170.797 169.499 170.794 169.498 170.793 169.496C170.792 169.496 170.791 169.496 170.791 169.496C170.791 169.495 170.791 169.495 170.791 169.495C170.791 169.495 170.791 169.496 170.791 169.496C170.791 169.496 170.791 169.496 170.791 169.496C170.791 169.496 170.791 169.496 170.611 169.798C170.43 170.1 170.43 170.1 170.43 170.1C170.43 170.1 170.43 170.1 170.43 170.1C170.43 170.1 170.431 170.1 170.431 170.101C170.431 170.101 170.432 170.101 170.432 170.101C170.433 170.102 170.435 170.103 170.437 170.104C170.44 170.106 170.445 170.109 170.452 170.113C170.464 170.12 170.482 170.13 170.504 170.142C170.549 170.166 170.612 170.199 170.689 170.236C170.841 170.308 171.054 170.398 171.286 170.459C171.514 170.518 171.792 170.559 172.06 170.503C172.346 170.443 172.61 170.273 172.764 169.95L172.129 169.646Z"
                            fill="black"
                          />
                          <path
                            id="Vector 17"
                            d="M168.717 65.9768C168.717 65.9768 168.362 68.231 168.355 69.964C168.349 71.4791 168.43 73.1697 168.43 73.1697"
                            stroke="black"
                            stroke-width="0.704066"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <path
                            id="Vector 12"
                            d="M146.672 216.646C146.672 216.646 148.995 219.839 151.107 221.968C152.954 223.829 155.121 225.799 155.121 225.799"
                            stroke="black"
                            stroke-width="0.704066"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <g id="Group 32">
                            <path
                              id="Vector 15"
                              d="M91.7554 111.036C91.7554 111.036 92.1074 114.205 93.1635 115.613C94.2196 117.021 95.6277 117.373 95.6277 117.373"
                              stroke="black"
                              stroke-width="0.704066"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              id="Vector 16"
                              d="M251.226 111.036C251.226 111.036 250.874 114.205 249.818 115.613C248.762 117.021 247.354 117.373 247.354 117.373"
                              stroke="black"
                              stroke-width="0.704066"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                          <g id="Group 27">
                            <path
                              id="Vector 15_2"
                              d="M167.09 20.9162C167.09 20.9162 166.339 18.7779 164.273 17.5262C162.208 16.2745 155.59 16.2224 152.304 13.8755"
                              stroke="black"
                              stroke-width="0.704066"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              id="Vector 14"
                              d="M172.722 20.9162C172.722 20.9162 173.473 18.7779 175.538 17.5262C177.603 16.2745 184.221 16.2224 187.507 13.8755"
                              stroke="black"
                              stroke-width="0.704066"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                  </g>
                  <path
                    id="Vector_2"
                    d="M172.092 234.248H169.275C167.163 234.248 165.403 236.361 164.347 238.473C163.291 240.585 163.643 245.161 162.587 255.018C161.742 262.904 156.602 298.201 154.138 314.864H113.654C113.067 311.109 111.753 301.628 111.19 293.742C110.486 283.885 110.134 261.707 110.134 250.09C110.134 240.796 115.062 220.402 117.527 211.366V192.004C117.527 184.4 120.812 170.061 122.455 163.842C122.807 163.02 123.3 158.984 122.455 149.408C121.61 139.833 122.807 129.225 123.511 125.118V64.9204L121.399 59.6399C120.108 64.6857 117.386 75.4814 116.822 78.2976C116.259 81.1139 107.435 104.817 103.093 116.317C101.802 125.47 98.4463 146.522 95.3484 157.505C91.4761 171.234 85.8435 179.683 84.4354 186.02C83.3089 191.089 81.8538 201.275 81.2671 205.734C81.7365 206.086 83.3089 207.212 85.8435 208.902C89.0118 211.014 94.2923 222.279 98.1647 228.616C102.037 234.952 102.037 236.361 99.9249 238.473C97.8127 240.585 96.4045 236.361 92.8842 231.784C90.0679 228.123 87.017 225.8 85.8435 225.096C83.7313 228.264 79.5069 235.727 79.5069 240.233C79.5069 245.866 81.6191 250.09 86.8996 255.018C92.1801 259.947 90.0679 259.595 89.7159 261.707C89.4343 263.397 87.017 261.707 85.8435 260.651C86.5476 261.707 87.7445 264.312 86.8996 266.283C85.8435 268.748 77.0427 259.947 71.7622 253.258C67.5378 247.907 66.4082 243.049 66.4082 243.049C66.4082 243.049 65.6307 240.233 65.3521 236.361C65.1441 233.469 65.1909 219.58 65.7776 212.774C65.6603 201.627 66.2705 173.487 69.65 150.112C73.0295 126.737 77.6294 114.792 79.5069 111.741C80.4457 107.399 82.7457 96.885 84.4354 89.5627C86.5476 80.4098 100.277 38.5179 105.205 25.1406C110.134 11.7633 117.797 13.0841 129.496 9.29909C141.465 5.42672 149.092 4.37063 150.97 -1.26191C153.551 -9.00664 153.786 -19.9197 153.786 -19.9197H186.173M172.092 234.248C174.204 234.248 175.964 236.361 177.02 238.473C178.076 240.585 177.724 245.161 178.78 255.018C179.625 262.904 184.516 297.518 186.98 314.181L227.606 314.523C228.192 310.768 229.614 301.628 230.177 293.742C230.881 283.885 231.233 261.707 231.233 250.09C231.233 240.796 226.305 220.402 223.841 211.366V192.004C223.841 184.4 220.555 170.061 218.912 163.842C218.56 163.02 218.067 158.984 218.912 149.408C219.757 139.833 218.56 129.225 217.856 125.118V64.9204L219.968 59.6399C221.259 64.6857 223.981 75.4814 224.545 78.2976C225.108 81.1139 233.932 104.817 238.274 116.317C239.565 125.47 242.921 146.522 246.019 157.505C249.891 171.234 255.524 179.683 256.932 186.02C258.058 191.089 259.513 201.275 260.1 205.734C259.631 206.086 258.058 207.212 255.524 208.902C252.355 211.014 247.075 222.279 243.202 228.616C239.33 234.952 239.33 236.361 241.442 238.473C243.554 240.585 244.963 236.361 248.483 231.784C251.299 228.123 254.35 225.8 255.524 225.096C257.636 228.264 261.86 235.727 261.86 240.233C261.86 245.866 259.748 250.09 254.468 255.018C249.187 259.947 251.299 259.595 251.651 261.707C251.933 263.397 254.35 261.707 255.524 260.651C254.82 261.707 253.623 264.312 254.468 266.283C255.524 268.748 264.324 259.947 269.605 253.258C273.829 247.907 275.238 243.049 275.59 241.289C276.924 236.713 276.176 219.58 275.59 212.774C275.707 201.627 275.097 173.487 271.717 150.112C268.338 126.737 263.738 114.792 261.86 111.741C260.921 107.399 258.622 96.885 256.932 89.5627C254.82 80.4098 241.09 38.5179 236.162 25.1406C231.233 11.7633 223.57 13.0841 211.871 9.29909C199.902 5.42672 192.275 4.37063 190.397 -1.26191C187.816 -9.00664 187.581 -19.9197 187.581 -19.9197H155.194M172.092 234.248H168.923"
                    stroke="black"
                    stroke-width="0.704066"
                    stroke-linecap="round"
                  />
                </g>
                {findMeasure('chest') && (
                  <g id="chest">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M217.855 65.6094C217.855 65.6094 190.496 71.096 159.24 69.3268M124.026 65.6094C124.026 65.6094 132.408 69.3268 150.1 69.3268"
                            stroke="#E55959"
                            stroke-width="2.82191"
                          />
                          <path
                            id="Vector 27"
                            d="M163.199 65.2437L157.457 69.0968L162.39 72.7957"
                            stroke="#E55959"
                            stroke-width="2.82191"
                          />
                          <path
                            id="Vector 28"
                            d="M145.107 65.2437L150.849 69.0968L145.916 72.7957"
                            stroke="#E55959"
                            stroke-width="2.82191"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('waistLower') && (
                  <g id="waistLower">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M223.499 184.835C223.499 184.835 200.643 193.604 158.853 191.258M118.383 184.835C118.383 184.835 129.272 189.547 149.599 191.258"
                            stroke="#E55959"
                            stroke-width="2.82191"
                          />
                          <path
                            id="Vector 27_2"
                            d="M162.861 186.926L157.048 190.827L162.042 194.572"
                            stroke="#E55959"
                            stroke-width="2.82191"
                          />
                          <path
                            id="Vector 28_2"
                            d="M144.544 186.926L150.357 190.827L145.363 194.572"
                            stroke="#E55959"
                            stroke-width="2.82191"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('waist') && (
                  <g id="waist">
                    <g id="Group 221_3">
                      <g id="Group 217_3">
                        <g id="Group 220_3">
                          <path
                            id="Ellipse 23_3"
                            d="M219.266 140.39C219.266 140.39 190.523 147.002 159.217 145.23M121.91 141.801C121.91 141.801 132.341 145.23 150.061 145.23"
                            stroke="#E55959"
                            stroke-width="2.82191"
                          />
                          <path
                            id="Vector 27_3"
                            d="M163.183 141.017L157.432 144.877L162.373 148.582"
                            stroke="#E55959"
                            stroke-width="2.82191"
                          />
                          <path
                            id="Vector 28_3"
                            d="M145.06 141.017L150.811 144.877L145.871 148.582"
                            stroke="#E55959"
                            stroke-width="2.82191"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('upper_waist') && (
                  <g id="upper_waist">
                    <g id="Group 221_4">
                      <g id="Group 217_4">
                        <g id="Group 220_4">
                          <path
                            id="Ellipse 23_4"
                            d="M217.855 95.2393C217.855 95.2393 190.291 101.749 159.326 99.9961M123.321 96.6502C123.321 96.6502 133.989 98.8626 150.27 99.9961"
                            stroke="#E55959"
                            stroke-width="2.82191"
                          />
                          <path
                            id="Vector 27_4"
                            d="M163.248 95.9012L157.56 99.7185L162.447 103.383"
                            stroke="#E55959"
                            stroke-width="2.82191"
                          />
                          <path
                            id="Vector 28_4"
                            d="M145.324 95.9012L151.012 99.7185L146.125 103.383"
                            stroke="#E55959"
                            stroke-width="2.82191"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('body_length') && (
                  <g id="body_length">
                    <path
                      id="Vector 19"
                      d="M205.195 168.271L205.195 42.3283"
                      stroke="#E55959"
                      stroke-width="2.82191"
                      stroke-linecap="square"
                    />
                    <path
                      id="Vector 29"
                      d="M112.739 173.548L229.218 173.548"
                      stroke="#E55959"
                      stroke-width="1.41096"
                      stroke-linecap="square"
                      stroke-dasharray="3.64 3.64"
                    />
                    <path
                      id="Vector 28_5"
                      d="M209.759 165.661L205.077 170.257L201.014 165.524"
                      stroke="#E55959"
                      stroke-width="2.82191"
                    />
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_2347"
              x1="70.3422"
              y1="79.4819"
              x2="79.7909"
              y2="261.131"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_2347"
              x1="133.554"
              y1="256.331"
              x2="118.18"
              y2="345.434"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
