# Observation Component

O componente `Observation` replica o comportamento do componente original mostrado na imagem, fornecendo suporte completo para internacionalização e processamento de texto de observação com múltiplos idiomas.

## Características

- ✅ Suporte a múltiplos idiomas através de texto embutido ou array de traduções
- ✅ Processamento automático de idioma baseado na configuração atual
- ✅ Suporte a conteúdo HTML
- ✅ Fallback inteligente para idiomas não encontrados
- ✅ Renderização condicional (não renderiza se não houver conteúdo)
- ✅ Compatível com o sistema de internacionalização existente

## Uso Básico

### 1. Componente Observation

```tsx
import { Observation } from '@/components/molecules';

// Uso simples com texto
<Observation observationText="This product runs small, consider sizing up." />

// Uso com texto multi-idioma embutido
<Observation 
  observationText="[en]This product runs small.[pt-BR]Este produto veste pequeno.[es]Este producto es pequeño." 
/>

// Uso com array de traduções
<Observation 
  observationText=""
  observationTranslations={[
    {
      id: '1',
      modelingId: '123',
      language: 'en',
      observation: 'This garment fits true to size.',
    },
    {
      id: '2',
      modelingId: '123',
      language: 'pt-BR',
      observation: 'Esta peça veste no tamanho.',
    },
  ]}
/>
```

### 2. Hook useObservation

```tsx
import { useObservation, useProductObservation } from '@/hooks/use-observation';

function MyComponent() {
  // Uso com dados customizados
  const observation = useObservation(observationText, observationTranslations);
  
  // Uso com dados do produto
  const productObservation = useProductObservation();

  return (
    <div>
      {observation.hasObservation && (
        <div dangerouslySetInnerHTML={{ __html: observation.observationText }} />
      )}
    </div>
  );
}
```

## API

### Componente Observation

#### Props

| Prop | Tipo | Obrigatório | Descrição |
|------|------|-------------|-----------|
| `observationText` | `string` | ✅ | Texto da observação (pode conter traduções embutidas) |
| `observationTranslations` | `ObservationTranslations` | ❌ | Array de traduções da observação |
| `className` | `string` | ❌ | Classes CSS adicionais |

#### Tipos

```tsx
type ObservationTranslations = {
  id: string;
  modelingId: string;
  language: string;
  observation: string;
}[];
```

### Hook useObservation

#### Parâmetros

| Parâmetro | Tipo | Obrigatório | Descrição |
|-----------|------|-------------|-----------|
| `observationText` | `string` | ✅ | Texto da observação |
| `observationTranslations` | `ObservationTranslations` | ❌ | Array de traduções |

#### Retorno

```tsx
{
  observationText: string;    // Texto processado baseado no idioma atual
  hasObservation: boolean;    // Se há conteúdo para exibir
  currentLanguage: string;    // Idioma atual
}
```

## Formatos de Texto Suportados

### 1. Texto Simples
```
"This product runs small, consider sizing up."
```

### 2. Texto Multi-idioma Embutido
```
"[en]This product runs small.[pt-BR]Este produto veste pequeno.[es]Este producto es pequeño."
```

### 3. Array de Traduções
```tsx
[
  {
    id: '1',
    modelingId: '123',
    language: 'en',
    observation: 'This garment fits true to size.',
  },
  {
    id: '2',
    modelingId: '123',
    language: 'pt-BR',
    observation: 'Esta peça veste no tamanho.',
  },
]
```

### 4. Conteúdo HTML
```tsx
{
  id: '1',
  modelingId: '123',
  language: 'en',
  observation: '<strong>Important:</strong> This item runs <em>large</em>.',
}
```

## Mapeamento de Idiomas

O componente suporta os seguintes mapeamentos de idioma:

- `esAR` → Espanhol Argentina
- `pt-BR`, `ptBR`, `ptBr`, `br` → Português Brasil
- `pt`, `pt-PT`, `ptPT`, `PT` → Português Portugal
- `cs`, `cz` → Tcheco
- `gr`, `el` → Grego
- `dk`, `da` → Dinamarquês

## Integração com o Projeto Existente

### Substituindo o Notification atual

```tsx
// Antes
{!image && observationAdvice && (
  <Notification content={observationAdvice}/>
)}

// Depois
{!image && (
  <Observation 
    observationText={observationAdvice || ''}
    observationTranslations={modelingObservationTranslations}
  />
)}
```

### Usando com dados do produto

```tsx
import { useProductObservation } from '@/hooks/use-observation';

function ProductComponent() {
  const observation = useProductObservation();

  return (
    <div>
      {observation.hasObservation && (
        <div 
          className="observation-container"
          dangerouslySetInnerHTML={{ __html: observation.observationText }}
        />
      )}
    </div>
  );
}
```

## Exemplos Completos

Veja os arquivos de exemplo:
- `src/components/molecules/observation-example.tsx` - Exemplos do componente
- `src/components/molecules/observation-hook-example.tsx` - Exemplos do hook
- `src/components/molecules/observation.stories.tsx` - Stories do Storybook

## Comportamento

1. **Prioridade de Idioma**: O componente primeiro tenta encontrar uma tradução exata para o idioma atual, depois usa a primeira tradução disponível como fallback.

2. **Processamento de Texto**: Textos com traduções embutidas são processados para extrair apenas o conteúdo do idioma atual.

3. **Renderização Condicional**: O componente não renderiza nada se não houver conteúdo de observação.

4. **Suporte HTML**: O componente suporta conteúdo HTML através de `dangerouslySetInnerHTML`.

5. **Compatibilidade**: Totalmente compatível com o sistema de internacionalização existente do projeto.
