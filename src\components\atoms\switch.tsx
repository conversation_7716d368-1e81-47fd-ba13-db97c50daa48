import { forwardRef, useId } from 'react';
import { cn } from '@/lib/utils';

type SwitchProps = React.InputHTMLAttributes<HTMLInputElement>;

export const Switch = forwardRef<HTMLInputElement, SwitchProps>(({ className, ...props }, ref) => {
  const id = useId();

  return (
    <div className="flex items-center">
      <input type="checkbox" id={id} className="sr-only" ref={ref} {...props} />
      <label
        htmlFor={id}
        className={cn(
          'relative inline-flex h-6 w-11 items-center rounded-full transition-colors cursor-pointer bg-[#272727]',
          className
        )}
      >
        <span
          className={cn(
            'inline-block h-5 w-5 transform rounded-full bg-white transition-transform absolute',
            props.checked ? 'right-[2px]' : 'left-[2px]'
          )}
        />
      </label>
    </div>
  );
});

Switch.displayName = 'Switch';
