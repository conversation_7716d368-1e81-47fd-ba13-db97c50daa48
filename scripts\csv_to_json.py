# import csv
# import json

# csv_file = "tabela_de_medidas.csv"

# assets_dict = {}

# def format_label(label):
#     label = label.replace("_", " ")  # Substitui underscores por espaços
#     label = label.replace("product ", "")  # Remove a palavra "product"
#     return label.title()  # Capitaliza cada palavra

# def format_asset_address(address):
#     return address.replace(".svg", "")  # Remove a extensão .svg

# temp_dict = {}
# with open(csv_file, mode='r', encoding='utf-8') as file:
#     reader = csv.DictReader(file)  # Detecta automaticamente o delimitador
#     for row in reader:
#         asset_address = format_asset_address(row["Endereço do asset"].strip())
#         label_en = row["Label EN"].strip()
#         explanation_en = row["Explicação PTBR"].strip()
#         label_ptbr = row["Label PTBR"].strip()

#         if asset_address not in assets_dict:
#             assets_dict[asset_address] = {}
        
#         assets_dict[asset_address][label_en] = {
#             "title": format_label(label_ptbr),
#             "content": explanation_en,
#         }

# json_output = json.dumps(assets_dict, indent=2, ensure_ascii=False)

# with open("ptBR.json", "w", encoding="utf-8") as json_file:
#     json_file.write(json_output)

import csv
import json

# Nome do arquivo CSV de entrada e JSON de saída
csv_file = "medidas_corpo.csv"
json_file = "dados.json"

# Dicionário para armazenar os dados estruturados
data = {}

# Ler o arquivo CSV
target = "faixa etária/gênero"
with open(csv_file, mode="r", encoding="utf-8") as file:
    reader = csv.DictReader(file)
    for row in reader:
        category = row[target].strip()  # child, male, female
        section = row["svg"].replace("group_", "").strip()  # fullbody, lower_body, etc.
        measure = row["medida (ptbr)"].strip()  # height, fullbody_length, etc.
        
        # Criar estrutura se não existir
        if category not in data:
            data[category] = {}
        if section not in data[category]:
            data[category][section] = []
        
        # Adicionar a medida
        data[category][section].append(measure)

# Salvar o JSON formatado
with open(json_file, mode="w", encoding="utf-8") as file:
    json.dump(data, file, ensure_ascii=False, indent=2)

print(f"JSON gerado com sucesso: {json_file}")