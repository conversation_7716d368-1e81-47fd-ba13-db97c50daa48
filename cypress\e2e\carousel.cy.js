import sizeChartMock from '../mocks/size-chart.json';

describe('template spec', () => {
  beforeEach(() => {
    cy.intercept('GET', '/plugin/size-chart/*', {
      statusCode: 200,
      body: sizeChartMock,
    });
  });

  it('Should be possible to view header title', () => {
    cy.visit('/');

    const header = cy.get("[data-test-id='header']");
    const title = header.children('h1');

    title.should('have.text', 'Size guide');
  });

  it('Should be possible to view carousel', () => {
    cy.visit('/');

    const carousel = cy.get("[data-test-id='carousel']");

    carousel.should('be.visible');
  });

  it('Should be possible to view 3 carousel items', () => {
    cy.visit('/');

    const carousel = cy.get("[data-test-id='carousel']");

    const carouselItems = carousel.children("[data-test-id='carousel-items']").children();

    carouselItems.its('length').should('eq', 3);
  });

  it('Should be possible to select a carousel item', () => {
    cy.visit('/');

    const carousel = cy.get("[data-test-id='carousel']");
    const carouselItems = carousel.children("[data-test-id='carousel-items']").children();

    const firstItem = carouselItems.first();

    firstItem.should('have.text', '23');

    firstItem.click();

    firstItem.should('have.text', '22');
  });

  it('Should be possible to navigate through the carousel with the arrow buttons', () => {
    cy.visit('/');
    const carouselTestId = "[data-test-id='carousel']";

    const getSelectedItem = () => {
      return cy.get("[data-test-id='carousel-item-1']");
    };

    const previousButton = cy.get(carouselTestId).children().first().as('previous-button');
    const nextButton = cy.get(carouselTestId).children().last().as('next-button');

    getSelectedItem().should('have.text', '24');

    previousButton.click();

    getSelectedItem().should('have.text', '23');

    nextButton.click();
    nextButton.click();

    getSelectedItem().should('have.text', '25');
  });
});
