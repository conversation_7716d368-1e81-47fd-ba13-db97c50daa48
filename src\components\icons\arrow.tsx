type colors = '#B0B0B0' | '#6D6D6D' | '#272727' | '#FFFFFF';

interface ArrowProps {
  className?: string;
  color?: colors;
}

function Arrow({ className, color = '#B0B0B0' }: ArrowProps) {
  return (
    <svg
      width="15"
      height="15"
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M11.25 5.625L7.5 9.375L3.75 5.625"
        stroke={color}
        stroke-width="1.25"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
}

export default Arrow;
