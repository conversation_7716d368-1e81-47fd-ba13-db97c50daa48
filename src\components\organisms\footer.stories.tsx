import type { Meta, StoryObj } from '@storybook/react';
import { Footer } from './footer';

const meta = {
  title: 'Organisms/Footer',
  component: Footer,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Footer>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    unit: 'cm',
    onUnitChange: (unit) => console.log('Unit changed to:', unit),
  },
};

export const InchesSelected: Story = {
  args: {
    unit: 'in',
    onUnitChange: (unit) => console.log('Unit changed to:', unit),
  },
};
