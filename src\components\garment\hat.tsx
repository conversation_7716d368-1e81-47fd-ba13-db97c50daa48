import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface HatProps {
  measure: GarmentMeasure;
  className?: string;
}

export function Hat({ measure, className }: HatProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width={isMobile ? '331' : '342'}
          height={isMobile ? '193' : '291'}
          viewBox={isMobile ? '0 0 331 193' : '0 0 342 291'}
          fill="none"
        >
          <g xmlns="http://www.w3.org/2000/svg" id="group_hat">
            <g id="hat">
              <path
                id="Vector 370"
                d="M67.048 136.564C66.4874 130.958 71.0194 126.986 71.0194 126.986L87.3722 119.744L189.46 111.101H240.154C245.994 115.306 254.031 120.258 255.339 121.379C256.974 122.781 263 128.388 263 136.564C263 141.166 262.192 148.632 251.787 154.552C238.237 162.261 179.834 170.982 140.168 166.466C119.167 164.076 99.9872 160.393 87.3722 154.552C77.7537 150.099 67.7489 143.573 67.048 136.564Z"
                fill="white"
                stroke="black"
                stroke-width="0.467223"
              />
              <path
                id="Vector 369"
                d="M82.7 117.641C89.0854 111.801 100.688 101.055 100.688 101.055C124.516 95.6817 173.528 87.0381 178.948 87.0381C184.368 87.0381 217.961 93.7349 234.08 97.0834L239.453 103.624C243.347 107.284 253.377 118.669 254.872 120.912C256.741 123.715 261.179 130.957 258.843 138.199C256.507 145.441 254.059 147.857 248.33 150.58C234.08 157.355 216.806 159.841 196.002 162.027C176.313 164.096 164.998 164.078 145.308 162.027C121.932 159.592 100.688 155.953 86.6714 148.478C77.557 143.617 67.4672 133.293 70.9714 126.986L82.7 117.641Z"
                fill="white"
                stroke="black"
                stroke-width="0.467223"
              />
              <path
                id="Vector 368"
                d="M110.266 37.9797C106.342 41.1569 100.922 83.7678 100.221 101.756C99.2864 104.793 100.688 105.961 107.696 109.932C116.264 114.787 139.22 120.861 160.259 122.314C186.62 124.134 213.637 120.493 227.305 113.203C237.818 107.596 238.519 104.092 236.884 100.821C235.248 97.5509 235.918 59.8431 230.342 42.4186C228.473 36.5781 225.437 34.9428 210.252 31.9058C188.254 27.5063 177.313 27 153.017 27C128.721 27 115.172 34.0083 110.266 37.9797Z"
                fill="white"
                stroke="black"
                stroke-width="0.467223"
              />
              <path
                id="Vector 377"
                d="M83.868 117.174C83.868 123.248 95.0814 128.667 110.033 134.461C128.721 141.703 148.61 143.678 166.333 144.74C193.617 146.375 221.647 137.695 230.81 134.461C238.752 131.658 245.761 128.154 248.33 121.846C250.143 117.396 249.966 115.071 249.966 115.071"
                stroke="black"
                stroke-width="0.467223"
                stroke-dasharray="0.93 0.93"
              />
              <path
                id="Vector 378"
                d="M78.9142 120.678C78.9142 127.687 91.4413 133.882 107.146 139.708C126.776 146.991 148.126 150.113 167.034 150.113C204.364 150.113 224.383 142.96 234.008 139.708C242.351 136.889 249.712 132.665 252.411 126.322C254.315 121.847 253.229 118.58 252.411 117.875"
                stroke="black"
                stroke-width="0.467223"
                stroke-dasharray="0.93 0.93"
              />
              <path
                id="Vector 380"
                d="M71.9058 126.519C71.9058 135.863 88.0175 146.01 101.813 150.065C124.235 156.654 145.314 159.458 165.818 159.458C185.606 159.458 220.48 155.086 236.602 150.065C250.852 145.626 256.401 140.569 259.31 132.592"
                stroke="black"
                stroke-width="0.467223"
                stroke-dasharray="0.93 0.93"
              />
              <path
                id="Vector 376"
                d="M88.0731 113.436C89.7083 119.51 99.1462 125.724 113.537 130.957C131.525 137.498 150.271 139.367 176.378 139.367C195.486 139.367 236.183 130.957 242.49 121.379C246.761 114.893 247.162 112.034 247.162 112.034"
                stroke="black"
                stroke-width="0.467223"
                stroke-dasharray="0.93 0.93"
              />
              <path
                id="Vector 379"
                d="M74.0564 124.975C75.9434 132.462 87.4487 138.569 104.249 144.268C124.235 151.048 165.174 156.362 185.932 153.77C206.69 151.179 240.573 149.412 252.249 134.766C258.036 127.506 256.974 124.649 256.974 124.649"
                stroke="black"
                stroke-width="0.467223"
                stroke-dasharray="0.93 0.93"
              />
              <path
                id="Vector 374"
                d="M96.4831 105.633C100.5 109.512 111.134 118.012 121.531 120.982C134.527 124.696 156.454 130.19 182.919 127.219C209.385 124.248 226.137 122.08 234.954 114.545C240.388 109.902 240.388 104.325 240.388 104.325"
                stroke="black"
                stroke-width="0.467223"
                stroke-dasharray="0.93 0.93"
              />
              <path
                id="Vector 373"
                d="M100.922 101.055C101.155 102.456 109.429 109.268 120.779 113.202C138.3 119.276 170.771 122.547 190.862 119.744C208.946 117.22 222.867 115.539 232.211 108.53C234.904 106.511 235.56 103.235 236.884 101.055"
                stroke="black"
                stroke-width="0.467223"
                stroke-dasharray="0.93 0.93"
              />
              <path
                id="Vector 372"
                d="M212.121 43.8203C212.822 44.5211 221.699 50.595 220.064 63.2101C218.755 73.3021 219.518 90.4647 220.064 97.7846L223.334 114.605M227.773 112.969C226.215 110.711 223.334 103.672 223.334 95.4484C223.334 85.1695 222.4 69.7512 223.334 60.1731C223.704 56.3793 221.154 50.1278 220.064 47.3245L216.793 43.8203"
                stroke="black"
                stroke-width="0.467223"
                stroke-dasharray="0.93 0.93"
              />
              <path
                id="Vector 371"
                d="M111.434 37.2793L125.451 40.5499L140.168 43.1196C144.373 41.5622 153.718 38.5408 157.456 38.9146C162.128 39.3818 167.501 43.1196 170.304 43.1196C172.547 43.1196 180.583 44.3655 184.321 44.9885C188.448 44.9885 196.843 44.9885 197.403 44.9885C198.104 44.9885 206.748 43.1196 209.084 43.1196C210.953 43.1196 216.715 43.1196 219.363 43.1196H227.539L229.174 40.0826"
                stroke="black"
                stroke-width="0.467223"
              />
              <path
                id="Vector 375"
                d="M92.0444 110.166C93.9133 119.978 124.813 129.654 138.066 131.191C154.185 133.06 185.723 138.9 228.941 123.949C242.844 119.139 243.191 111.957 243.658 109.231"
                stroke="black"
                stroke-width="0.467223"
                stroke-dasharray="0.93 0.93"
              />
            </g>
            {findMeasure('head_circumference') && (
              <g id="head_circumference">
                <g id="Group 256">
                  <g id="Group 258">
                    <g id="Group 220">
                      <path
                        id="Ellipse 23"
                        d="M160.912 114.621C120.977 112.589 100.874 98.7188 100.874 98.7188M235.901 99.6537C235.901 99.6537 218.192 116.008 177.732 115.072"
                        stroke="#E55959"
                        stroke-width="2.33611"
                      />
                      <path
                        id="Vector 27"
                        d="M181.33 112.068L176.975 115.119L180.707 118.06"
                        stroke="#E55959"
                        stroke-width="1.86889"
                      />
                      <path
                        id="Vector 28"
                        d="M157.818 111.606L162.163 114.671L158.421 117.6"
                        stroke="#E55959"
                        stroke-width="1.86889"
                      />
                    </g>
                  </g>
                </g>
              </g>
            )}
            {findMeasure('circumference') && (
              <g id="circumference">
                <g id="Group 256_2">
                  <g id="Group 258_2">
                    <g id="Group 220_2">
                      <path
                        id="Ellipse 23_2"
                        d="M160.912 114.621C120.977 112.589 100.874 98.7188 100.874 98.7188M235.901 99.6537C235.901 99.6537 218.192 116.008 177.732 115.072"
                        stroke="#E55959"
                        stroke-width="2.33611"
                      />
                      <path
                        id="Vector 27_2"
                        d="M181.33 112.068L176.975 115.119L180.707 118.06"
                        stroke="#E55959"
                        stroke-width="1.86889"
                      />
                      <path
                        id="Vector 28_2"
                        d="M157.818 111.606L162.163 114.671L158.421 117.6"
                        stroke="#E55959"
                        stroke-width="1.86889"
                      />
                    </g>
                  </g>
                </g>
              </g>
            )}
          </g>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_hat">
            <g id="hat">
              <path
                id="Vector 370"
                d="M38.065 199.135C37.3069 191.554 43.4344 186.185 43.4344 186.185L65.544 176.394L203.571 164.707H272.111C280.008 170.392 290.873 177.088 292.642 178.605C294.853 180.5 303 188.08 303 199.135C303 205.357 301.907 215.451 287.839 223.456C269.52 233.879 190.557 245.669 136.927 239.564C108.532 236.332 82.6001 231.352 65.544 223.456C52.5394 217.435 39.0125 208.61 38.065 199.135Z"
                fill="white"
                stroke="black"
                stroke-width="0.631704"
              />
              <path
                id="Vector 369"
                d="M59.227 173.55C67.8603 165.654 83.5477 151.125 83.5477 151.125C115.765 143.86 182.03 132.174 189.358 132.174C196.686 132.174 242.105 141.228 263.899 145.755L271.164 154.599C276.428 159.548 289.989 174.94 292.01 177.972C294.537 181.763 300.538 191.554 297.38 201.345C294.221 211.137 290.912 214.403 283.166 218.086C263.899 227.245 240.544 230.607 212.415 233.562C185.795 236.36 170.498 236.335 143.875 233.562C112.27 230.27 83.5477 225.35 64.5965 215.243C52.2735 208.671 38.6317 194.713 43.3695 186.185L59.227 173.55Z"
                fill="white"
                stroke="black"
                stroke-width="0.631704"
              />
              <path
                id="Vector 368"
                d="M96.4976 65.8451C91.1913 70.1406 83.8635 127.752 82.916 152.073C81.6526 156.179 83.5477 157.758 93.0233 163.128C104.607 169.692 135.645 177.904 164.09 179.868C199.731 182.329 236.259 177.406 254.74 167.55C268.953 159.969 269.9 155.232 267.689 150.81C265.479 146.388 266.384 95.4053 258.846 71.8465C256.319 63.9499 252.213 61.739 231.682 57.6329C201.94 51.6845 187.147 51 154.299 51C121.45 51 103.131 60.4756 96.4976 65.8451Z"
                fill="white"
                stroke="black"
                stroke-width="0.631704"
              />
              <path
                id="Vector 377"
                d="M60.8063 172.919C60.8063 181.131 75.9672 188.459 96.1817 196.292C121.45 206.083 148.34 208.753 172.302 210.189C209.192 212.4 247.089 200.664 259.477 196.292C270.216 192.502 279.692 187.764 283.166 179.236C285.617 173.22 285.377 170.076 285.377 170.076"
                stroke="black"
                stroke-width="0.631704"
                stroke-dasharray="1.26 1.26"
              />
              <path
                id="Vector 378"
                d="M54.1085 177.656C54.1085 187.132 71.0457 195.509 92.2786 203.386C118.82 213.232 147.686 217.454 173.25 217.454C223.721 217.454 250.789 207.783 263.801 203.386C275.081 199.574 285.034 193.863 288.684 185.287C291.258 179.236 289.79 174.819 288.684 173.866"
                stroke="black"
                stroke-width="0.631704"
                stroke-dasharray="1.26 1.26"
              />
              <path
                id="Vector 380"
                d="M44.6329 185.553C44.6329 198.187 66.4165 211.906 85.0681 217.388C115.384 226.298 143.883 230.088 171.605 230.088C198.361 230.088 245.511 224.177 267.309 217.388C286.576 211.387 294.077 204.55 298.011 193.765"
                stroke="black"
                stroke-width="0.631704"
                stroke-dasharray="1.26 1.26"
              />
              <path
                id="Vector 376"
                d="M66.4916 167.865C68.7026 176.077 81.463 184.479 100.92 191.554C125.24 200.398 150.585 202.924 185.884 202.924C211.719 202.924 266.742 191.554 275.27 178.604C281.045 169.835 281.587 165.97 281.587 165.97"
                stroke="black"
                stroke-width="0.631704"
                stroke-dasharray="1.26 1.26"
              />
              <path
                id="Vector 379"
                d="M47.5405 183.467C50.0919 193.59 65.6475 201.846 88.3626 209.552C115.384 218.718 170.736 225.903 198.801 222.399C226.866 218.895 272.678 216.507 288.464 196.704C296.289 186.888 294.853 183.026 294.853 183.026"
                stroke="black"
                stroke-width="0.631704"
                stroke-dasharray="1.26 1.26"
              />
              <path
                id="Vector 374"
                d="M77.8623 157.315C83.2935 162.559 97.6703 174.051 111.728 178.068C129.299 183.089 158.945 190.517 194.728 186.5C230.51 182.484 253.16 179.552 265.08 169.365C272.427 163.086 272.427 155.547 272.427 155.547"
                stroke="black"
                stroke-width="0.631704"
                stroke-dasharray="1.26 1.26"
              />
              <path
                id="Vector 373"
                d="M83.8635 151.125C84.1793 153.02 95.3658 162.23 110.711 167.549C134.4 175.761 178.303 180.183 205.467 176.393C229.917 172.982 248.738 170.708 261.372 161.232C265.013 158.502 265.9 154.073 267.689 151.125"
                stroke="black"
                stroke-width="0.631704"
                stroke-dasharray="1.26 1.26"
              />
              <path
                id="Vector 372"
                d="M234.209 73.7412C235.157 74.6888 247.159 82.9009 244.948 99.9569C243.179 113.602 244.211 136.806 244.948 146.703L249.37 169.444M255.371 167.233C253.266 164.18 249.37 154.663 249.37 143.545C249.37 129.647 248.107 108.801 249.37 95.8509C249.87 90.7215 246.422 82.2692 244.948 78.479L240.526 73.7412"
                stroke="black"
                stroke-width="0.631704"
                stroke-dasharray="1.26 1.26"
              />
              <path
                id="Vector 371"
                d="M98.0768 64.8975L117.028 69.3194L136.927 72.7938C142.612 70.6881 155.246 66.6031 160.3 67.1084C166.617 67.7401 173.881 72.7938 177.672 72.7938C180.704 72.7938 191.569 74.4783 196.623 75.3206C202.203 75.3206 213.552 75.3206 214.31 75.3206C215.258 75.3206 226.945 72.7938 230.103 72.7938C232.63 72.7938 240.421 72.7938 244.001 72.7938H255.055L257.266 68.6877"
                stroke="black"
                stroke-width="0.631704"
              />
              <path
                id="Vector 375"
                d="M71.8611 163.443C74.3879 176.709 116.166 189.792 134.084 191.87C155.878 194.397 198.518 202.293 256.951 182.078C275.749 175.575 276.217 165.865 276.849 162.18"
                stroke="black"
                stroke-width="0.631704"
                stroke-dasharray="1.26 1.26"
              />
            </g>
            {findMeasure('head_circumference') && (
              <g id="head_circumference">
                <g id="Group 256">
                  <g id="Group 258">
                    <g id="Group 220">
                      <path
                        id="Ellipse 23"
                        d="M164.973 169.468C110.979 166.72 83.7986 147.968 83.7986 147.968M266.361 149.232C266.361 149.232 242.417 171.344 187.714 170.078"
                        stroke="#E55959"
                        stroke-width="3.15852"
                      />
                      <path
                        id="Vector 27"
                        d="M192.579 166.015L186.691 170.14L191.737 174.117"
                        stroke="#E55959"
                        stroke-width="2.52682"
                      />
                      <path
                        id="Vector 28"
                        d="M160.79 165.392L166.665 169.536L161.605 173.496"
                        stroke="#E55959"
                        stroke-width="2.52682"
                      />
                    </g>
                  </g>
                </g>
              </g>
            )}
            {findMeasure('circumference') && (
              <g id="circumference">
                <g id="Group 256_2">
                  <g id="Group 258_2">
                    <g id="Group 220_2">
                      <path
                        id="Ellipse 23_2"
                        d="M164.973 169.468C110.979 166.72 83.7986 147.968 83.7986 147.968M266.361 149.232C266.361 149.232 242.417 171.344 187.714 170.078"
                        stroke="#E55959"
                        stroke-width="3.15852"
                      />
                      <path
                        id="Vector 27_2"
                        d="M192.579 166.015L186.691 170.14L191.737 174.117"
                        stroke="#E55959"
                        stroke-width="2.52682"
                      />
                      <path
                        id="Vector 28_2"
                        d="M160.79 165.392L166.665 169.536L161.605 173.496"
                        stroke="#E55959"
                        stroke-width="2.52682"
                      />
                    </g>
                  </g>
                </g>
              </g>
            )}
          </g>
        </svg>
      )}
    </div>
  );
}
