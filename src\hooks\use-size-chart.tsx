import { useMemo } from 'react';
import { measure, Sizes, Display } from '@/types/size-chart.types';
import { CarouselItem } from '@/components/organisms';
import { useAppContext } from '@/store';
import { defaultDisplayMeasurements } from '@/constants';

type CarouselResponse = {
  content: CarouselItem[];
  selected: CarouselItem;
};

export type MeasurementLabel = {
  measure: string;
  label: {
    initialValue: number;
    finalValue: number;
    textValue?: string;
  };
  position?: number;
  hidden?: boolean;
  type?: string;
};

interface ComposeGarmentProps {
  sizes: Sizes;
  template: string;
  display?: Display;
}

export type GarmentMeasure = {
  sizeName: string;
  garmentMeasurements: MeasurementLabel[];
  template: string;
};

export type BodyMeasure = {
  sizeName: string;
  measures: MeasurementLabel[];
};

interface ComposeBodyProps {
  sizes: Sizes;
  display?: Display;
}

interface ComposeSizeSystemProps {
  sizes: Sizes;
}

export interface SizeSystemResponse {
  sizeName: string;
  sizeSystem: {
    [key: string]: string;
  };
}

type SizesMap = Map<string, Sizes>;

export function composeCarouselItems(data: SizesMap): CarouselResponse {
  let carouselItems = [] as CarouselItem[];

  for (const [key, _] of data) {
    carouselItems = [
      ...carouselItems,
      {
        id: key,
        content: key,
      },
    ];
  }

  const middleIndex = Math.floor(carouselItems.length / 2);

  return {
    content: carouselItems,
    selected: carouselItems[middleIndex],
  };
}

const BASIC_TYPES = ['MEASURE', 'GARMENT'];
const ALL_TYPES = [...BASIC_TYPES, 'TEXT', 'LABEL'];

function isValidMeasure(value: unknown, hasAdditionalValues?: boolean): value is measure {
  if (!(typeof value === 'object' && value !== null)) return false;

  const { type, initialValue } = value as measure;

  const validTypes = hasAdditionalValues ? ALL_TYPES : BASIC_TYPES;

  return validTypes.includes(type) && initialValue !== -1;
}

export const composeGarmentMeasures = ({ sizes, template, display }: ComposeGarmentProps): GarmentMeasure[] => {
  return Object.entries(sizes).map(([_, item]) => {
    const validGarmentMeasures = Object.entries(item.garmentMeasurements)
      .filter(([_, value]) => {
        return isValidMeasure(value);
      })
      .map(([key, value]) => {
        const measure = value as unknown as measure;
        const garmentMeasurement = display?.garmentMeasurement[key];
        return {
          measure: key,
          label: {
            initialValue: measure.initialValue,
            finalValue: measure.finalValue,
            position: garmentMeasurement?.position,
            hidden: garmentMeasurement?.hidden,
          },
        };
      })
      .filter((item) => !item.label.hidden)
      .sort((a, b) => (a.label.position ?? 0) - (b.label.position ?? 0));

    return {
      sizeName: item.sizeName,
      garmentMeasurements: validGarmentMeasures,
      template,
    };
  });
};

export const composeBodyMeasures = ({ sizes, display }: ComposeBodyProps): BodyMeasure[] => {
  return Object.entries(sizes).map(([_, item]) => {
    const validMeasures = Object.entries(item)
      .filter(([key, value]) => {
        return key !== 'equivalence' && isValidMeasure(value, true);
      })
      .map(([key, value]) => {
        const measure = value as unknown as measure;
        const bodyMeasurement = display?.bodyMeasurement[key];

        return {
          measure: key,
          label: {
            initialValue: measure.initialValue,
            finalValue: measure.finalValue,
            textValue: measure?.text,
          },
          position: bodyMeasurement?.position,
          hidden: bodyMeasurement?.hidden,
          type: measure?.type,
        };
      })
      .filter((measureItem) => !measureItem.hidden);

    return {
      sizeName: item.sizeName,
      measures: validMeasures,
    };
  });
};

function getNormalizedList(measures: Sizes): SizesMap {
  const measureValues = Object.entries(measures);

  const result = new Map(
    measureValues.sort((a, b) => {
      const arrayA = Object.entries(a[1]) as unknown as [string, measure][];
      const arrayB = Object.entries(b[1]) as unknown as [string, measure][];

      const equivalenceIndex = arrayA.findIndex((measure) => measure[0] === 'equivalence');
      if (arrayA[equivalenceIndex][1].initialValue !== -1) {
        return arrayA[equivalenceIndex][1].initialValue - arrayB[equivalenceIndex][1].initialValue;
      }

      const firstValidMeasureIndex = arrayA.findIndex((measure) => isValidMeasure(measure[1]));

      return arrayA[firstValidMeasureIndex][1].initialValue - arrayB[firstValidMeasureIndex][1].initialValue;
    })
  );

  return result as unknown as SizesMap;
}

const composeSizeSystem = ({ sizes }: ComposeSizeSystemProps): SizeSystemResponse[] => {
  const allSizes = Object.entries(sizes).map(([_, item]) => ({
    sizeName: item.sizeName,
    sizeSystem: item.sizeSystemsGrid,
  }));

  return allSizes;
};

export function useSizeChart() {
  const { app, product, carousel: carouselState } = useAppContext();
  const modelingSizes = product?.modelingInfo?.sizes || {};
  const modelingTemplate = product?.modelingInfo?.template || '';
  const display = product?.modelingInfo?.display || defaultDisplayMeasurements;

  const sortedMeasures = getNormalizedList(modelingSizes);
  const carousel = composeCarouselItems(sortedMeasures);

  const composedMeasures = useMemo(() => {
    const measures = Object.values(sortedMeasures).map((size) => {
      return size.composedMeasureValue;
    });

    const measuresArr = measures.map((measure) => {
      return { label: measure, value: measure };
    });

    const uniqueMeasures = measuresArr.filter(
      (measure, index, self) => self.findIndex((t) => t.value === measure.value) === index
    );

    return uniqueMeasures;
  }, [sortedMeasures]);

  const garmentMeasures = composeGarmentMeasures({
    sizes: modelingSizes,
    template: modelingTemplate,
    display,
  });

  const bodyMeasures = composeBodyMeasures({ sizes: modelingSizes, display });

  const selectedGarmentMeasure = !app.loading
    ? (garmentMeasures.find((item) => item.sizeName === carouselState.selected.content) as GarmentMeasure)
    : null;

  const selectedBodyMeasure = !app.loading
    ? (bodyMeasures.find((item) => item.sizeName === carouselState.selected.content) as BodyMeasure)
    : null;

  const sizeSystem = composeSizeSystem({ sizes: modelingSizes });

  const selectedSizeSystem = !app.loading
    ? (sizeSystem.find((item) => item.sizeName === carouselState.selected.content) as SizeSystemResponse)
    : null;

  return {
    carousel,
    garmentMeasures,
    bodyMeasures,
    selectedGarmentMeasure,
    selectedBodyMeasure,
    sizeSystem,
    selectedSizeSystem,
    sortedMeasures,
    composedMeasures,
  };
}
