import { useDevice } from '@/hooks/use-device';
import { BodyMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface ShoulderLengthProps {
  measure: BodyMeasure;
  className?: string;
}

export function ShoulderLength({ measure, className }: ShoulderLengthProps) {
  const { measures } = measure;
  const mappedMeasures = measures.map((item) => item.measure);
  const { isMobile } = useDevice();

  const findMeasure = (measure: string) => {
    const foundMeasure = mappedMeasures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full flex justify-center', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_shoulder_length">
            <mask id="mask0_128_1937" maskUnits="userSpaceOnUse" x="0" y="0" width="331" height="193">
              <rect id="rect" width="331" height="193" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_1937)">
              <g id="group_shoulder_length_mask">
                <g id="male">
                  <path
                    id="Union"
                    d="M208 196.85C207.769 192.199 206.813 181.868 206.021 177.751C205.229 173.634 206.219 166.007 206.813 162.709L209.979 153.505C210.507 158.585 211.622 169.418 211.86 172.11C212.097 174.802 211.959 178.51 211.86 180.027L215.125 196.85H236.402C235.281 192.661 233.037 183.431 233.037 180.027C233.037 175.772 232.741 168.448 231.85 165.579C231.137 163.283 230.365 158.948 230.069 157.068V143.015C230.069 137.315 229.475 133.713 229.178 132.624C230.629 129.689 232.84 121.323 230.069 111.348C229.682 109.957 229.344 108.702 229.036 107.56C226.585 98.464 226.074 96.5669 218.688 90.7639C215.185 88.0115 208.765 85.3469 202.36 82.6888C193.566 79.0392 185.14 76.7096 182.567 71.5654C180.984 66.9124 181.142 64.5018 181.281 60.282C181.281 60.282 183.656 53.4537 183.656 50.0908H186.229C188.175 46.7921 191.496 38.2659 189.494 37.0269C187.416 35.7404 185.239 36.6311 185.239 36.6311C185.833 34.256 185.952 26.556 185.239 23.0726C184.348 18.7173 181.9 14.5266 177.619 12.3855C172.676 9.91319 166.801 9.98197 165.948 10.0062C164.713 10.006 159.736 10.1537 154.759 12.3853C150.391 14.3437 148.03 19.3119 147.139 23.6672C146.427 27.1506 146.545 34.4531 147.139 36.8282C147.139 36.8282 144.962 35.7403 142.884 37.0268C140.882 38.2658 144.203 46.792 146.149 50.0907H148.722C149.014 52.281 151.493 60.282 151.493 60.282C151.689 64.8041 151.79 67.9021 149.415 71.5654C146.149 75.8189 138.812 79.0391 130.019 82.6887C123.614 85.3468 117.193 88.0113 113.69 90.7638C106.305 96.5667 105.793 98.4638 103.342 107.56C103.034 108.702 102.696 109.957 102.31 111.348C99.5387 121.323 101.749 129.689 103.2 132.624C102.903 133.713 102.31 137.315 102.31 143.015V157.068C102.013 158.948 101.241 163.283 100.528 165.578C99.6376 168.448 99.3408 175.771 99.3408 180.027C99.3408 183.431 97.0976 192.661 95.9761 196.85H117.253L120.518 180.027C120.42 178.509 120.281 174.802 120.518 172.11C120.756 169.418 121.871 158.585 122.399 153.505L125.565 162.709C126.159 166.007 127.149 173.634 126.357 177.751C125.565 181.867 124.609 192.199 124.378 196.85H163.006L162.973 199.026L208 196.85Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.395845"
                  />
                  <path
                    id="Vector 17"
                    d="M97.7573 190.416L111.018 144.201L120.123 167.16V179.926L117.154 196.552H96.2729L97.7573 190.416Z"
                    fill="url(#paint0_linear_128_1937)"
                  />
                  <path
                    id="Vector 18"
                    d="M125.862 181.906L143.477 143.41L206.615 182.895L207.208 189.13L207.802 196.255L166.337 211L124.477 196.255L125.862 181.906Z"
                    fill="url(#paint1_linear_128_1937)"
                  />
                  <path
                    id="Vector 14"
                    d="M179.529 120.648C179.529 120.648 179.954 123.334 180.499 125.413C180.976 127.232 181.611 129.232 181.611 129.232"
                    stroke="black"
                    stroke-width="0.320384"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector 15"
                    d="M153.11 120.648C153.11 120.648 152.685 123.334 152.14 125.413C151.663 127.232 151.028 129.232 151.028 129.232"
                    stroke="black"
                    stroke-width="0.320384"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector 16"
                    d="M122.597 147.665C122.597 147.665 122.498 151.128 122.399 152.514C122.327 153.52 122.3 154.097 122.3 154.097"
                    stroke="black"
                    stroke-width="0.320384"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector 19"
                    d="M209.722 147.665C209.722 147.665 209.821 151.128 209.92 152.514C209.992 153.52 210.019 154.097 210.019 154.097"
                    stroke="black"
                    stroke-width="0.320384"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector 20"
                    d="M166.285 163.3L166.285 178.243"
                    stroke="black"
                    stroke-width="0.320384"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector 12"
                    d="M147.139 36.6301C147.139 36.6301 147.617 38.7509 148.027 40.3164C148.386 41.6852 148.864 43.1914 148.864 43.1914"
                    stroke="black"
                    stroke-width="0.320384"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector 21"
                    d="M185.218 36.7292C185.218 36.7292 184.74 38.85 184.33 40.4156C183.971 41.7843 183.493 43.2905 183.493 43.2905"
                    stroke="black"
                    stroke-width="0.320384"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector 22"
                    d="M151.822 37.9005C153.478 40.807 157.173 43.582 157.173 43.582C157.173 43.582 151.692 44.6964 149.613 42.6668C146.223 39.3577 147.04 26.4373 147.04 26.4373C147.04 26.4373 149.214 33.3224 151.822 37.9005Z"
                    fill="url(#paint2_linear_128_1937)"
                  />
                </g>
                {findMeasure('shoulder_length') && (
                  <g id="shoulder_length">
                    <g id="male_body_shoulder_length">
                      <g id="Group 265">
                        <g id="Group 262">
                          <path
                            id="Vector 19_2"
                            d="M214.532 97.3921C214.532 97.3921 203.844 91.6525 186.229 88.6835C179.101 87.4821 165.942 87.5944 165.942 87.5944"
                            stroke="#E55959"
                            stroke-width="1.58338"
                            stroke-linecap="square"
                          />
                          <path
                            id="Vector 27"
                            d="M213.251 94.9713L215.782 97.7498L212.7 99.0674"
                            stroke="#E55959"
                            stroke-width="1.28153"
                          />
                        </g>
                      </g>
                    </g>
                    <g id="male_body_shoulder_length_2">
                      <g id="Group 265_2">
                        <g id="Group 262_2">
                          <path
                            id="Vector 19_3"
                            d="M117.524 97.3926C117.524 97.3926 128.212 91.653 145.827 88.684C152.956 87.4826 165.942 87.5948 165.942 87.5948"
                            stroke="#E55959"
                            stroke-width="1.58338"
                            stroke-linecap="square"
                          />
                          <path
                            id="Vector 27_2"
                            d="M118.806 94.9713L116.275 97.7498L119.356 99.0674"
                            stroke="#E55959"
                            stroke-width="1.28153"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_1937"
              x1="105.516"
              y1="178.368"
              x2="100.836"
              y2="205.295"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_1937"
              x1="137.975"
              y1="177.577"
              x2="133.296"
              y2="204.504"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_1937"
              x1="173.174"
              y1="-10.3121"
              x2="138.449"
              y2="58.6085"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className=" h-full m-auto "
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_shoulder_length">
            <mask id="mask0_128_3710" maskUnits="userSpaceOnUse" x="0" y="0" width="342" height="291">
              <rect id="rect" width="342" height="291" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_3710)">
              <g id="group_shoulder_length_mask">
                <g id="male">
                  <path
                    id="Union"
                    d="M235.102 299.388C234.749 292.284 233.288 276.504 232.079 270.216C230.869 263.929 232.381 252.28 233.288 247.242L238.125 233.185C238.931 240.944 240.634 257.49 240.996 261.601C241.359 265.712 241.148 271.375 240.996 273.693L245.984 299.388H278.482C276.769 292.99 273.342 278.892 273.342 273.693C273.342 267.193 272.889 256.008 271.529 251.625C270.44 248.118 269.261 241.498 268.808 238.626V217.163C268.808 208.457 267.901 202.955 267.448 201.292C269.664 196.808 273.04 184.031 268.808 168.795C268.218 166.671 267.701 164.754 267.231 163.01C263.487 149.117 262.706 146.219 251.426 137.356C246.075 133.152 236.269 129.082 226.486 125.022C213.055 119.448 200.186 115.89 196.256 108.033C193.838 100.926 194.08 97.244 194.291 90.7989C194.291 90.7989 197.919 80.3695 197.919 75.2332H201.849C204.821 70.1949 209.893 57.1723 206.837 55.28C203.662 53.315 200.337 54.6754 200.337 54.6754C201.244 51.0477 201.425 39.2871 200.337 33.9665C198.977 27.3145 195.237 20.9137 188.699 17.6435C181.149 13.8674 172.176 13.9725 170.873 14.0095C168.987 14.0092 161.385 14.2347 153.783 17.6433C147.112 20.6344 143.505 28.2227 142.144 34.8747C141.056 40.1953 141.238 51.3487 142.144 54.9764C142.144 54.9764 138.819 53.3148 135.645 55.2798C132.588 57.1721 137.66 70.1947 140.633 75.233H144.563C145.009 78.5784 148.795 90.7989 148.795 90.7989C149.093 97.7057 149.248 102.438 145.621 108.033C140.633 114.529 129.427 119.448 115.995 125.022C106.213 129.082 96.4063 133.152 91.0558 137.356C79.7753 146.219 78.9944 149.116 75.2503 163.009C74.7803 164.753 74.2636 166.671 73.6736 168.795C69.4414 184.031 72.8171 196.808 75.0339 201.292C74.5805 202.955 73.6736 208.456 73.6736 217.163V238.626C73.2201 241.498 72.0412 248.118 70.9529 251.625C69.5925 256.008 69.1391 267.193 69.1391 273.693C69.1391 278.892 65.713 292.989 64 299.388H96.4972L101.485 273.693C101.334 271.375 101.122 265.712 101.485 261.601C101.848 257.489 103.551 240.944 104.357 233.185L109.194 247.241C110.101 252.28 111.612 263.928 110.403 270.216C109.194 276.504 107.733 292.284 107.38 299.388H166.379L166.328 302.711L235.102 299.388Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.604599"
                  />
                  <path
                    id="Vector 17"
                    d="M66.7205 289.561L86.9746 218.974L100.88 254.041V273.539L96.3459 298.932H64.4533L66.7205 289.561Z"
                    fill="url(#paint0_linear_128_3710)"
                  />
                  <path
                    id="Vector 18"
                    d="M109.647 276.562L136.552 217.765L232.986 278.074L233.892 287.596L234.799 298.479L171.468 321L107.531 298.479L109.647 276.562Z"
                    fill="url(#paint1_linear_128_3710)"
                  />
                  <path
                    id="Vector 14"
                    d="M191.616 183C191.616 183 192.266 187.102 193.098 190.278C193.825 193.055 194.796 196.111 194.796 196.111"
                    stroke="black"
                    stroke-width="0.489342"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector 15"
                    d="M151.264 183C151.264 183 150.615 187.102 149.783 190.278C149.055 193.055 148.085 196.111 148.085 196.111"
                    stroke="black"
                    stroke-width="0.489342"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector 16"
                    d="M104.66 224.264C104.66 224.264 104.509 229.554 104.358 231.67C104.248 233.208 104.207 234.088 104.207 234.088"
                    stroke="black"
                    stroke-width="0.489342"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector 19"
                    d="M237.731 224.264C237.731 224.264 237.883 229.554 238.034 231.67C238.144 233.208 238.185 234.088 238.185 234.088"
                    stroke="black"
                    stroke-width="0.489342"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector 20"
                    d="M171.387 248.145L171.387 270.969"
                    stroke="black"
                    stroke-width="0.489342"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector 12"
                    d="M142.145 54.6738C142.145 54.6738 142.875 57.913 143.501 60.3042C144.049 62.3947 144.78 64.6953 144.78 64.6953"
                    stroke="black"
                    stroke-width="0.489342"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector 21"
                    d="M200.305 54.8252C200.305 54.8252 199.575 58.0643 198.948 60.4555C198.4 62.5461 197.67 64.8466 197.67 64.8466"
                    stroke="black"
                    stroke-width="0.489342"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector 22"
                    d="M149.297 56.614C151.826 61.0533 157.47 65.2918 157.47 65.2918C157.47 65.2918 149.099 66.9939 145.923 63.8939C140.745 58.8398 141.993 39.1056 141.993 39.1056C141.993 39.1056 145.314 49.6216 149.297 56.614Z"
                    fill="url(#paint2_linear_128_3710)"
                  />
                </g>
                {findMeasure('shoulder_length') && (
                  <g id="shoulder_length">
                    <g id="male_body_shoulder_length">
                      <g id="Group 265">
                        <g id="Group 262">
                          <path
                            id="Vector 19_2"
                            d="M245.077 147.48C245.077 147.48 228.753 138.713 201.849 134.179C190.961 132.344 170.863 132.515 170.863 132.515"
                            stroke="#E55959"
                            stroke-width="2.4184"
                            stroke-linecap="square"
                          />
                          <path
                            id="Vector 27"
                            d="M243.121 143.783L246.987 148.026L242.28 150.039"
                            stroke="#E55959"
                            stroke-width="1.95737"
                          />
                        </g>
                      </g>
                    </g>
                    <g id="male_body_shoulder_length_2">
                      <g id="Group 265_2">
                        <g id="Group 262_2">
                          <path
                            id="Vector 19_3"
                            d="M96.9121 147.48C96.9121 147.48 113.236 138.714 140.141 134.179C151.028 132.344 170.863 132.515 170.863 132.515"
                            stroke="#E55959"
                            stroke-width="2.4184"
                            stroke-linecap="square"
                          />
                          <path
                            id="Vector 27_2"
                            d="M98.8685 143.782L95.0026 148.026L99.7095 150.038"
                            stroke="#E55959"
                            stroke-width="1.95737"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_3710"
              x1="78.5704"
              y1="271.16"
              x2="71.4233"
              y2="312.287"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_3710"
              x1="128.148"
              y1="269.951"
              x2="121.001"
              y2="311.078"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_3710"
              x1="181.91"
              y1="-17.0241"
              x2="128.871"
              y2="88.2427"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
