import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface LongDressProps {
  measure: GarmentMeasure;
  className?: string;
}

export function LongDress({ measure, className }: LongDressProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_long_dress">
            <g id="long_dress">
              <path
                id="Vector"
                d="M148.516 20.3908C149.695 19.8011 154.708 17.8846 157.214 17C157.214 17 160.96 21.7791 165.765 21.7791C169.604 21.7791 174.021 17.1474 174.021 17.1474C176.527 18.032 181.539 19.8011 182.719 20.3908C184.193 21.128 186.11 22.8971 186.847 26.2879C187.437 29.0006 187.338 31.0179 187.731 37.259L180.213 38.8807C179.525 42.6155 177.176 55.6554 177.411 56.9527C177.706 58.5744 180.95 65.9458 182.571 75.2337C183.869 82.664 183.603 91.0083 183.309 94.2517L180.065 124.769C180.409 125.752 181.097 128.278 181.097 130.519C181.097 132.76 180.901 134.499 180.802 135.089C181.49 137.988 182.896 144.347 183.014 146.588C183.132 148.829 183.358 152.24 183.456 153.665L185.52 171.503V171.551C184.833 171.77 183.287 172.208 181.539 172.535C179.181 172.978 179.475 174.01 176.379 175.484C173.284 176.958 168.271 174.894 166.207 174.01C164.143 173.125 161.932 174.599 159.425 175.189C156.919 175.779 155.74 175.484 153.971 174.894C152.555 174.423 151.022 173.42 150.432 172.978L148.958 172.535L145.715 171.651L147.779 153.665C147.877 152.24 148.103 148.829 148.221 146.588C148.339 144.347 149.744 137.988 150.432 135.089C150.334 134.499 150.138 132.76 150.138 130.519C150.138 128.278 150.825 125.752 151.169 124.769L147.926 94.2517C147.631 91.0083 147.366 82.664 148.663 75.2337C150.285 65.9458 153.528 58.5744 153.823 56.9527C154.059 55.6554 152.742 47.8614 152.054 44.1266L151.022 38.8807L142.914 37.259C142.914 37.259 144.14 27.4289 144.388 26.2879C145.125 22.8971 147.042 21.128 148.516 20.3908Z"
                fill="white"
              />
              <g id="Group 176">
                <path
                  id="Vector 294"
                  d="M175.568 123.29C177.381 121.78 180.89 116.213 181.097 115.924C181.073 116.628 180.604 120.358 180.604 120.358C180.509 120.568 178.72 122.725 176.074 124.156C173.958 125.301 170.705 126.005 169.806 125.88C170.809 125.713 173.921 124.661 175.568 123.29Z"
                  fill="url(#paint0_linear_208_4973)"
                  fill-opacity="0.87"
                />
                <path
                  id="Vector 290"
                  d="M153.765 57.5421C153.765 57.5421 158.995 53.9533 159.197 53.8193C159.081 54.2366 158.073 56.357 158.073 56.357C157.971 56.4655 153.676 57.9844 153.676 57.9844L153.765 57.5421Z"
                  fill="url(#paint1_linear_208_4973)"
                  fill-opacity="0.87"
                />
                <path
                  id="Vector 295"
                  d="M165.47 144.672C163.701 138.185 162.816 170.914 162.964 172.978H168.861C168.91 167.572 166.885 149.861 165.47 144.672Z"
                  fill="url(#paint2_linear_208_4973)"
                />
                <path
                  id="Vector 296"
                  d="M156.023 156.279C154.254 149.793 150.584 169.808 150.731 171.872H156.629C156.678 166.466 157.438 161.469 156.023 156.279Z"
                  fill="url(#paint3_linear_208_4973)"
                />
                <path
                  id="Vector 297"
                  d="M177.411 158.677C175.642 152.191 175.352 170.988 175.499 173.052H181.396C181.445 167.646 178.827 163.867 177.411 158.677Z"
                  fill="url(#paint4_linear_208_4973)"
                />
                <path
                  id="Vector 285"
                  d="M145.862 170.324C145.862 170.324 149.793 170.619 150.875 171.651C151.956 172.683 154.56 174.393 156.329 174.157C158.541 173.862 162.374 171.209 165.765 172.093C169.156 172.978 172.399 175.042 175.571 174.157C178.314 173.393 178.296 171.504 181.245 171.209C183.603 170.973 185.373 170.324 185.373 170.324"
                  stroke="black"
                  stroke-width="0.294854"
                  stroke-dasharray="0.59 0.59"
                />
                <path
                  id="Vector 298"
                  d="M151.427 124.582C151.427 124.582 155.847 121.55 156.016 121.437C155.919 121.789 155.067 123.581 155.067 123.581C154.98 123.673 151.351 124.956 151.351 124.956L151.427 124.582Z"
                  fill="url(#paint5_linear_208_4973)"
                  fill-opacity="0.87"
                />
                <path
                  id="Vector 41"
                  d="M167.606 42.7205C170.103 41.0923 174.911 35.2888 175.188 34.9863C175.204 35.6906 174.558 39.4658 174.558 39.4658C174.438 39.6825 172.095 41.9462 168.396 43.551C165.436 44.8349 160.812 45.7551 159.511 45.6901C160.94 45.4559 165.338 44.1989 167.606 42.7205Z"
                  fill="url(#paint6_linear_208_4973)"
                  fill-opacity="0.87"
                />
                <path
                  id="Vector 289"
                  d="M167.606 42.7205C170.103 41.0923 174.911 35.2888 175.188 34.9863C175.204 35.6906 174.558 39.4658 174.558 39.4658C174.438 39.6825 172.095 41.9462 168.396 43.551C165.436 44.8349 160.812 45.7551 159.511 45.6901C160.94 45.4559 165.338 44.1989 167.606 42.7205Z"
                  fill="url(#paint7_linear_208_4973)"
                  fill-opacity="0.87"
                />
                <path
                  id="Vector 291"
                  d="M166.721 81.3465C169.219 79.7183 174.026 73.9148 174.303 73.6123C174.319 74.3166 173.674 78.0918 173.674 78.0918C173.553 78.3085 171.211 80.5722 167.511 82.177C164.552 83.4609 159.927 84.381 158.626 84.3161C160.056 84.0819 164.453 82.8249 166.721 81.3465Z"
                  fill="url(#paint8_linear_208_4973)"
                  fill-opacity="0.87"
                />
              </g>
              <path
                id="Vector_2"
                d="M185.667 171.503C185.078 171.7 183.427 172.182 181.539 172.535C179.181 172.978 179.475 174.01 176.38 175.484C173.284 176.958 168.271 174.894 166.207 174.01C164.143 173.125 161.932 174.599 159.425 175.189C156.919 175.779 155.74 175.484 153.971 174.894C152.555 174.423 151.022 173.42 150.432 172.978L148.958 172.535L145.715 171.651L147.779 153.665C147.877 152.24 148.103 148.829 148.221 146.588C148.339 144.347 149.744 137.988 150.432 135.089C150.334 134.499 150.138 132.76 150.138 130.519C150.138 128.278 150.825 125.752 151.169 124.769L147.926 94.2517C147.631 91.0083 147.366 82.664 148.663 75.2337C150.285 65.9458 153.528 58.5744 153.823 56.9527C154.059 55.6554 152.742 47.3331 152.054 43.5983L150.875 38.7332L142.914 37.259C143.307 31.0179 143.798 29.0006 144.388 26.2879C145.125 22.8971 147.042 21.128 148.516 20.3908C149.695 19.8011 154.708 17.8846 157.214 17C157.214 17 159.868 21.3369 165.765 21.6317C170.04 21.8455 174.021 17.1474 174.021 17.1474C176.527 18.032 181.539 19.8011 182.719 20.3908C184.193 21.128 186.11 22.8971 186.847 26.2879C187.437 29.0006 187.338 31.0179 187.731 37.259L180.213 39.0281C179.525 42.7629 177.176 55.6554 177.412 56.9527C177.706 58.5744 180.95 65.9458 182.571 75.2337C183.869 82.664 183.603 91.0083 183.309 94.2517L180.065 124.769C180.409 125.752 181.097 128.278 181.097 130.519C181.097 132.76 180.901 134.499 180.802 135.089C181.49 137.988 182.896 144.347 183.014 146.588C183.132 148.829 183.358 152.24 183.456 153.665L185.52 171.503V171.651"
                stroke="black"
                stroke-width="0.294854"
              />
              <g id="Group 178">
                <path
                  id="Vector 284"
                  d="M149.105 24.666C149.548 28.9414 149.622 31.2533 150.432 36.1653C150.987 39.5299 151.661 41.4359 152.054 43.5981"
                  stroke="black"
                  stroke-width="0.294854"
                />
                <path
                  id="Vector 288"
                  d="M181.834 24.666C181.392 28.9414 181.318 32.4327 180.507 37.3447C179.952 40.7093 179.574 42.6153 179.181 44.7776"
                  stroke="black"
                  stroke-width="0.294854"
                />
              </g>
            </g>
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 21"
                  d="M175.712 57.1055L156.051 57.1055"
                  stroke="#E55959"
                  stroke-width="1.32788"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M157.554 55.0654L155.13 57.146L157.258 59.3137"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
                <path
                  id="Vector 27"
                  d="M174.241 59.3219L176.443 57.0069L174.107 55.0654"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
              </g>
            )}
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 21_2"
                  d="M178.899 37.9834L152.864 37.9834"
                  stroke="#E55959"
                  stroke-width="1.32788"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M154.367 35.9443L151.943 38.0249L154.071 40.1926"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
                <path
                  id="Vector 27_2"
                  d="M177.428 40.2008L179.63 37.8858L177.294 35.9443"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 21_3"
                  d="M181.289 79.6797L149.943 79.6797"
                  stroke="#E55959"
                  stroke-width="1.32788"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M151.446 77.6396L149.022 79.7202L151.149 81.8879"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
                <path
                  id="Vector 27_3"
                  d="M180.084 81.8961L182.285 79.5812L179.95 77.6396"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
              </g>
            )}
            {findMeasure('product_biceps') && (
              <g id="product_biceps">
                <path
                  id="Vector 21_4"
                  d="M149.819 36.3426L143.577 35.4131"
                  stroke="#E55959"
                  stroke-width="1.32788"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M145.222 33.3991L142.712 35.3753L144.746 37.631"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
                <path
                  id="Vector 27_4"
                  d="M147.939 38.0839L150.567 36.2673L148.677 33.8897"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
              </g>
            )}
            {findMeasure('product_sleeve') && (
              <g id="product_sleeve">
                <path
                  id="Vector 21_5"
                  d="M142.864 22.4112L138.947 35.8066"
                  stroke="#E55959"
                  stroke-width="1.32788"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_5"
                  d="M137.206 33.6786L138.645 36.5307L141.26 34.9848"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
                <path
                  id="Vector 27_5"
                  d="M144.414 24.474L143.153 21.4204L140.406 23.0296"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 21_6"
                  d="M181.024 21.7842L150.209 21.7842"
                  stroke="#EDA7A7"
                  stroke-width="1.32788"
                  stroke-linecap="square"
                  stroke-dasharray="2.66 2.66"
                />
                <path
                  id="Vector 26_6"
                  d="M151.711 19.7441L149.287 21.8247L151.415 23.9924"
                  stroke="#EDA7A7"
                  stroke-width="1.0623"
                />
                <path
                  id="Vector 27_6"
                  d="M179.819 24.0016L182.02 21.6866L179.684 19.7451"
                  stroke="#EDA7A7"
                  stroke-width="1.0623"
                />
              </g>
            )}
            {findMeasure('product_fullbody_length') && (
              <g id="product_fullbody_length">
                <path
                  id="Vector 21_7"
                  d="M174.252 19.0801C174.252 66.1661 174.268 127.488 174.268 174.574"
                  stroke="#E55959"
                  stroke-width="1.32788"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_7"
                  d="M172.078 172.731L174.239 175.084L176.334 172.886"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
                <path
                  id="Vector 27_7"
                  d="M176.389 20.31L174.16 18.0217L172.13 20.2814"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 21_8"
                  d="M178.899 37.9834L152.864 37.9834"
                  stroke="#E55959"
                  stroke-width="1.32788"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_8"
                  d="M154.367 35.9443L151.943 38.0249L154.071 40.1926"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
                <path
                  id="Vector 27_8"
                  d="M177.428 40.2008L179.63 37.8858L177.294 35.9443"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 21_9"
                  d="M175.712 57.1055L156.051 57.1055"
                  stroke="#E55959"
                  stroke-width="1.32788"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_9"
                  d="M157.554 55.0654L155.13 57.146L157.258 59.3137"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
                <path
                  id="Vector 27_9"
                  d="M174.241 59.3219L176.443 57.0069L174.107 55.0654"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 21_10"
                  d="M181.289 79.6797L149.943 79.6797"
                  stroke="#E55959"
                  stroke-width="1.32788"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_10"
                  d="M151.446 77.6396L149.022 79.7202L151.149 81.8879"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
                <path
                  id="Vector 27_10"
                  d="M180.084 81.8961L182.285 79.5812L179.95 77.6396"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 21_11"
                  d="M174.252 19.0801C174.252 66.1661 174.268 127.488 174.268 174.574"
                  stroke="#E55959"
                  stroke-width="1.32788"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_11"
                  d="M172.078 172.731L174.239 175.084L176.334 172.886"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
                <path
                  id="Vector 27_11"
                  d="M176.389 20.31L174.16 18.0217L172.13 20.2814"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 21_12"
                  d="M174.252 19.0801C174.252 66.1661 174.268 127.488 174.268 174.574"
                  stroke="#EDA7A7"
                  stroke-width="1.32788"
                  stroke-linecap="square"
                  stroke-dasharray="2.66 2.66"
                />
                <path
                  id="Vector 26_12"
                  d="M172.078 172.731L174.239 175.084L176.334 172.886"
                  stroke="#EDA7A7"
                  stroke-width="1.0623"
                />
                <path
                  id="Vector 27_12"
                  d="M176.389 20.31L174.16 18.0217L172.13 20.2814"
                  stroke="#EDA7A7"
                  stroke-width="1.0623"
                />
              </g>
            )}
            {findMeasure('product_high_waist_circumference') && (
              <g id="product_high_waist_circumference">
                <path
                  id="Vector 21_13"
                  d="M175.712 57.1055L156.051 57.1055"
                  stroke="#E55959"
                  stroke-width="1.32788"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_13"
                  d="M157.554 55.0654L155.13 57.146L157.258 59.3137"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
                <path
                  id="Vector 27_13"
                  d="M174.241 59.3219L176.443 57.0069L174.107 55.0654"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
              </g>
            )}
            {findMeasure('product_high_waist_width') && (
              <g id="product_high_waist_width">
                <path
                  id="Vector 21_14"
                  d="M175.712 57.1055L156.051 57.1055"
                  stroke="#E55959"
                  stroke-width="1.32788"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_14"
                  d="M157.554 55.0654L155.13 57.146L157.258 59.3137"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
                <path
                  id="Vector 27_14"
                  d="M174.241 59.3219L176.443 57.0069L174.107 55.0654"
                  stroke="#E55959"
                  stroke-width="1.0623"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_208_4973"
              x1="180.303"
              y1="119.627"
              x2="177.909"
              y2="127.704"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_208_4973"
              x1="157.981"
              y1="55.8697"
              x2="155.639"
              y2="60.5691"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_208_4973"
              x1="166.278"
              y1="148.351"
              x2="166.278"
              y2="172.093"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_208_4973"
              x1="154.046"
              y1="147.245"
              x2="154.046"
              y2="170.987"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_208_4973"
              x1="178.814"
              y1="148.425"
              x2="178.814"
              y2="172.167"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint5_linear_208_4973"
              x1="154.989"
              y1="123.169"
              x2="153.01"
              y2="127.14"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint6_linear_208_4973"
              x1="174.148"
              y1="38.7491"
              x2="172.818"
              y2="47.2106"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint7_linear_208_4973"
              x1="174.148"
              y1="38.7491"
              x2="172.818"
              y2="47.2106"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint8_linear_208_4973"
              x1="173.264"
              y1="77.3751"
              x2="171.934"
              y2="85.8366"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_long_dress">
            <g id="long_dress">
              <path
                id="Vector"
                d="M145.136 28.2462C146.961 27.3338 154.716 24.3686 158.594 23C158.594 23 164.389 30.3941 171.823 30.3941C177.763 30.3941 184.596 23.2281 184.596 23.2281C188.474 24.5967 196.229 27.3338 198.054 28.2462C200.335 29.3866 203.3 32.1238 204.441 37.37C205.353 41.5669 205.201 44.6881 205.809 54.3441L194.176 56.8531C193.112 62.6315 189.478 82.8064 189.843 84.8136C190.299 87.3227 195.317 98.7274 197.826 113.097C199.833 124.593 199.423 137.503 198.966 142.522L193.948 189.737C194.481 191.258 195.545 195.166 195.545 198.633C195.545 202.1 195.241 204.791 195.089 205.704C196.153 210.19 198.328 220.028 198.51 223.495C198.693 226.962 199.042 232.239 199.195 234.444L202.388 262.043V262.117C201.325 262.456 198.934 263.133 196.229 263.64C192.58 264.324 193.036 265.921 188.246 268.202C183.456 270.483 175.701 267.289 172.507 265.921C169.314 264.552 165.893 266.833 162.015 267.745C158.137 268.658 156.313 268.202 153.576 267.289C151.386 266.559 149.014 265.008 148.101 264.324L145.82 263.64L140.802 262.271L143.996 234.444C144.148 232.239 144.497 226.962 144.68 223.495C144.862 220.028 147.037 210.19 148.101 205.704C147.949 204.791 147.645 202.1 147.645 198.633C147.645 195.166 148.71 191.258 149.242 189.737L144.224 142.522C143.768 137.503 143.357 124.593 145.364 113.097C147.873 98.7274 152.891 87.3227 153.348 84.8136C153.712 82.8064 151.675 70.7478 150.61 64.9694L149.014 56.8531L136.469 54.3441C136.469 54.3441 138.366 39.1353 138.749 37.37C139.89 32.1238 142.855 29.3866 145.136 28.2462Z"
                fill="white"
              />
              <g id="Group 176">
                <path
                  id="Vector 294"
                  d="M186.99 187.447C189.795 185.111 195.224 176.498 195.545 176.051C195.508 177.14 194.782 182.911 194.782 182.911C194.636 183.237 191.867 186.573 187.774 188.787C184.499 190.558 179.466 191.649 178.075 191.455C179.627 191.196 184.443 189.569 186.99 187.447Z"
                  fill="url(#paint0_linear_208_4974)"
                  fill-opacity="0.87"
                />
                <path
                  id="Vector 290"
                  d="M153.258 85.7256C153.258 85.7256 161.35 80.1731 161.661 79.9658C161.483 80.6114 159.923 83.892 159.923 83.892C159.764 84.0598 153.12 86.4099 153.12 86.4099L153.258 85.7256Z"
                  fill="url(#paint1_linear_208_4974)"
                  fill-opacity="0.87"
                />
                <path
                  id="Vector 295"
                  d="M171.367 220.53C168.63 210.494 167.261 261.131 167.489 264.324H176.613C176.689 255.961 173.557 228.559 171.367 220.53Z"
                  fill="url(#paint2_linear_208_4974)"
                />
                <path
                  id="Vector 296"
                  d="M156.751 238.49C154.014 228.454 148.336 259.421 148.564 262.614H157.688C157.764 254.25 158.941 246.519 156.751 238.49Z"
                  fill="url(#paint3_linear_208_4974)"
                />
                <path
                  id="Vector 297"
                  d="M189.843 242.199C187.105 232.163 186.656 261.245 186.884 264.438H196.008C196.084 256.075 192.032 250.228 189.843 242.199Z"
                  fill="url(#paint4_linear_208_4974)"
                />
                <path
                  id="Vector 285"
                  d="M141.03 260.219C141.03 260.219 147.113 260.675 148.786 262.272C150.458 263.868 154.488 266.514 157.225 266.149C160.647 265.693 166.577 261.587 171.823 262.956C177.069 264.324 182.087 267.518 186.995 266.149C191.239 264.966 191.211 262.044 195.773 261.587C199.423 261.222 202.16 260.219 202.16 260.219"
                  stroke="black"
                  stroke-width="0.456189"
                  stroke-dasharray="0.91 0.91"
                />
                <path
                  id="Vector 298"
                  d="M149.64 189.448C149.64 189.448 156.478 184.756 156.741 184.581C156.59 185.127 155.272 187.899 155.272 187.899C155.138 188.041 149.523 190.027 149.523 190.027L149.64 189.448Z"
                  fill="url(#paint5_linear_208_4974)"
                  fill-opacity="0.87"
                />
                <path
                  id="Vector 41"
                  d="M174.671 62.7943C178.535 60.2751 185.973 51.2961 186.403 50.8281C186.427 51.9178 185.428 57.7586 185.428 57.7586C185.242 58.0939 181.618 61.5962 175.894 64.0792C171.315 66.0656 164.16 67.4892 162.147 67.3887C164.359 67.0264 171.163 65.0815 174.671 62.7943Z"
                  fill="url(#paint6_linear_208_4974)"
                  fill-opacity="0.87"
                />
                <path
                  id="Vector 289"
                  d="M174.671 62.7943C178.535 60.2751 185.973 51.2961 186.403 50.8281C186.427 51.9178 185.428 57.7586 185.428 57.7586C185.242 58.0939 181.618 61.5962 175.894 64.0792C171.315 66.0656 164.16 67.4892 162.147 67.3887C164.359 67.0264 171.163 65.0815 174.671 62.7943Z"
                  fill="url(#paint7_linear_208_4974)"
                  fill-opacity="0.87"
                />
                <path
                  id="Vector 291"
                  d="M173.303 122.555C177.167 120.036 184.605 111.057 185.034 110.589C185.059 111.679 184.06 117.519 184.06 117.519C183.873 117.855 180.249 121.357 174.525 123.84C169.946 125.826 162.792 127.25 160.779 127.149C162.991 126.787 169.794 124.842 173.303 122.555Z"
                  fill="url(#paint8_linear_208_4974)"
                  fill-opacity="0.87"
                />
              </g>
              <path
                id="Vector_2"
                d="M202.616 262.043C201.704 262.347 199.149 263.092 196.229 263.64C192.58 264.324 193.036 265.921 188.246 268.202C183.456 270.483 175.701 267.289 172.507 265.921C169.314 264.552 165.893 266.833 162.015 267.745C158.138 268.658 156.313 268.202 153.576 267.289C151.386 266.559 149.014 265.008 148.101 264.324L145.82 263.64L140.802 262.271L143.996 234.444C144.148 232.239 144.497 226.962 144.68 223.495C144.862 220.028 147.037 210.19 148.101 205.704C147.949 204.791 147.645 202.1 147.645 198.633C147.645 195.166 148.71 191.258 149.242 189.737L144.224 142.522C143.768 137.503 143.357 124.593 145.364 113.097C147.873 98.7274 152.891 87.3227 153.348 84.8136C153.712 82.8064 151.675 69.9305 150.61 64.1521L148.786 56.625L136.469 54.3441C137.077 44.6881 137.837 41.5669 138.749 37.37C139.89 32.1238 142.855 29.3866 145.136 28.2462C146.961 27.3338 154.716 24.3686 158.594 23C158.594 23 162.699 29.7099 171.823 30.166C178.438 30.4968 184.596 23.2281 184.596 23.2281C188.474 24.5967 196.229 27.3338 198.054 28.2462C200.335 29.3866 203.3 32.1238 204.441 37.37C205.353 41.5669 205.201 44.6881 205.809 54.3441L194.176 57.0812C193.112 62.8596 189.478 82.8064 189.843 84.8136C190.299 87.3227 195.317 98.7274 197.826 113.097C199.833 124.593 199.423 137.503 198.966 142.522L193.948 189.737C194.481 191.258 195.545 195.166 195.545 198.633C195.545 202.1 195.241 204.791 195.089 205.704C196.153 210.19 198.328 220.028 198.51 223.495C198.693 226.962 199.042 232.239 199.195 234.444L202.388 262.043V262.271"
                stroke="black"
                stroke-width="0.456189"
              />
              <g id="Group 178">
                <path
                  id="Vector 284"
                  d="M146.048 34.8604C146.733 41.4751 146.848 45.052 148.101 52.6517C148.96 57.8572 150.002 60.8062 150.61 64.1516"
                  stroke="black"
                  stroke-width="0.456189"
                />
                <path
                  id="Vector 288"
                  d="M196.686 34.8604C196.002 41.4751 195.887 46.8768 194.633 54.4765C193.774 59.682 193.188 62.6309 192.58 65.9763"
                  stroke="black"
                  stroke-width="0.456189"
                />
              </g>
            </g>
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 21"
                  d="M187.213 85.0498L156.795 85.0498"
                  stroke="#E55959"
                  stroke-width="2.05445"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M159.12 81.8945L155.369 85.1135L158.661 88.4673"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
                <path
                  id="Vector 27"
                  d="M184.938 88.48L188.343 84.8984L184.73 81.8945"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
              </g>
            )}
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 21_2"
                  d="M192.144 55.4658L151.864 55.4658"
                  stroke="#E55959"
                  stroke-width="2.05445"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M154.189 52.3105L150.439 55.5295L153.73 58.8833"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
                <path
                  id="Vector 27_2"
                  d="M189.869 58.896L193.274 55.3144L189.66 52.3105"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 21_3"
                  d="M195.842 119.976L147.344 119.976"
                  stroke="#E55959"
                  stroke-width="2.05445"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M149.669 116.821L145.919 120.04L149.211 123.394"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
                <path
                  id="Vector 27_3"
                  d="M193.978 123.406L197.384 119.824L193.77 116.82"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
              </g>
            )}
            {findMeasure('product_biceps') && (
              <g id="product_biceps">
                <path
                  id="Vector 21_4"
                  d="M147.152 52.9274L137.496 51.4893"
                  stroke="#E55959"
                  stroke-width="2.05445"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M140.04 48.3726L136.157 51.4302L139.304 54.9201"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
                <path
                  id="Vector 27_4"
                  d="M144.244 55.6207L148.309 52.8102L145.385 49.1316"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
              </g>
            )}
            {findMeasure('product_sleeve') && (
              <g id="product_sleeve">
                <path
                  id="Vector 21_5"
                  d="M136.392 31.3707L130.331 52.0957"
                  stroke="#E55959"
                  stroke-width="2.05445"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_5"
                  d="M127.638 48.8048L129.865 53.2174L133.91 50.8256"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
                <path
                  id="Vector 27_5"
                  d="M138.789 34.5621L136.839 29.8377L132.589 32.3275"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 21_6"
                  d="M195.431 30.4014L147.755 30.4014"
                  stroke="#EDA7A7"
                  stroke-width="2.05445"
                  stroke-linecap="square"
                  stroke-dasharray="4.11 4.11"
                />
                <path
                  id="Vector 26_6"
                  d="M150.08 27.2461L146.329 30.465L149.621 33.8189"
                  stroke="#EDA7A7"
                  stroke-width="1.64356"
                />
                <path
                  id="Vector 27_6"
                  d="M193.567 33.8316L196.972 30.2499L193.358 27.2461"
                  stroke="#EDA7A7"
                  stroke-width="1.64356"
                />
              </g>
            )}
            {findMeasure('product_full_body_length') && (
              <g id="product_fullbody_length">
                <path
                  id="Vector 21_7"
                  d="M184.954 26.2188C184.954 99.0688 184.979 193.945 184.978 266.795"
                  stroke="#E55959"
                  stroke-width="2.05445"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_7"
                  d="M181.591 263.943L184.934 267.583L188.176 264.181"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
                <path
                  id="Vector 27_7"
                  d="M188.261 28.1206L184.812 24.5802L181.672 28.0764"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 21_8"
                  d="M192.144 55.4658L151.864 55.4658"
                  stroke="#E55959"
                  stroke-width="2.05445"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_8"
                  d="M154.189 52.3105L150.439 55.5295L153.73 58.8833"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
                <path
                  id="Vector 27_8"
                  d="M189.869 58.896L193.274 55.3144L189.66 52.3105"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 21_9"
                  d="M187.213 85.0498L156.795 85.0498"
                  stroke="#E55959"
                  stroke-width="2.05445"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_9"
                  d="M159.12 81.8945L155.369 85.1135L158.661 88.4673"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
                <path
                  id="Vector 27_9"
                  d="M184.938 88.48L188.343 84.8984L184.73 81.8945"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 21_10"
                  d="M195.842 119.976L147.344 119.976"
                  stroke="#E55959"
                  stroke-width="2.05445"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_10"
                  d="M149.669 116.821L145.919 120.04L149.211 123.394"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
                <path
                  id="Vector 27_10"
                  d="M193.978 123.406L197.384 119.824L193.77 116.82"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 21_11"
                  d="M184.954 26.2188C184.954 99.0688 184.979 193.945 184.978 266.795"
                  stroke="#E55959"
                  stroke-width="2.05445"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_11"
                  d="M181.591 263.943L184.934 267.583L188.176 264.181"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
                <path
                  id="Vector 27_11"
                  d="M188.261 28.1206L184.812 24.5802L181.672 28.0764"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 21_12"
                  d="M184.954 26.2188C184.954 99.0688 184.979 193.945 184.978 266.795"
                  stroke="#EDA7A7"
                  stroke-width="2.05445"
                  stroke-linecap="square"
                  stroke-dasharray="4.11 4.11"
                />
                <path
                  id="Vector 26_12"
                  d="M181.591 263.943L184.934 267.583L188.176 264.181"
                  stroke="#EDA7A7"
                  stroke-width="1.64356"
                />
                <path
                  id="Vector 27_12"
                  d="M188.261 28.1206L184.812 24.5802L181.672 28.0764"
                  stroke="#EDA7A7"
                  stroke-width="1.64356"
                />
              </g>
            )}
            {findMeasure('product_high_waist_circumference') && (
              <g id="product_high_waist_circumference">
                <path
                  id="Vector 21_13"
                  d="M187.213 85.0498L156.795 85.0498"
                  stroke="#E55959"
                  stroke-width="2.05445"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_13"
                  d="M159.12 81.8945L155.369 85.1135L158.661 88.4673"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
                <path
                  id="Vector 27_13"
                  d="M184.938 88.48L188.343 84.8984L184.73 81.8945"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
              </g>
            )}
            {findMeasure('product_high_waist_width') && (
              <g id="product_high_waist_width">
                <path
                  id="Vector 21_14"
                  d="M187.213 85.0498L156.795 85.0498"
                  stroke="#E55959"
                  stroke-width="2.05445"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_14"
                  d="M159.12 81.8945L155.369 85.1135L158.661 88.4673"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
                <path
                  id="Vector 27_14"
                  d="M184.938 88.48L188.343 84.8984L184.73 81.8945"
                  stroke="#E55959"
                  stroke-width="1.64356"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_208_4974"
              x1="194.317"
              y1="181.78"
              x2="190.612"
              y2="194.277"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_208_4974"
              x1="159.78"
              y1="83.1381"
              x2="156.157"
              y2="90.4088"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_208_4974"
              x1="172.617"
              y1="226.223"
              x2="172.617"
              y2="262.956"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_208_4974"
              x1="153.692"
              y1="224.512"
              x2="153.692"
              y2="261.245"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_208_4974"
              x1="192.012"
              y1="226.337"
              x2="192.012"
              y2="263.07"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint5_linear_208_4974"
              x1="155.151"
              y1="187.262"
              x2="152.09"
              y2="193.406"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint6_linear_208_4974"
              x1="184.794"
              y1="56.6498"
              x2="182.736"
              y2="69.7412"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint7_linear_208_4974"
              x1="184.794"
              y1="56.6498"
              x2="182.736"
              y2="69.7412"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint8_linear_208_4974"
              x1="183.425"
              y1="116.411"
              x2="181.368"
              y2="129.502"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
