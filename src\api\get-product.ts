import api from '.';
import { Product } from '@/types/size-chart.types';
import getQueryParams from '@/lib/get-query-params';
import sanatizeEachSize from '@/lib/decode-base64';

interface params {
  id: string;
  sizeSystem?: string;
}

const qp = getQueryParams<{ id: string }>();

export async function getProduct({ id, sizeSystem }: params) {
  const sizes = sanatizeEachSize();
  const url = `/plugin/size-chart/${id || qp.id}`;
  const res = await api.get(url, { params: { sizeSystem, sizes } });
  return res.data as Product;
}
