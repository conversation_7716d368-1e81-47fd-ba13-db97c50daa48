export enum mappedLanguages {
  ar = 'ar',
  bg = 'bg_BG',
  cz = 'cs_CZ',
  dk = 'da_DK',
  de = 'de_DE',
  gr = 'el_GR',
  en = 'en',
  esAR = 'es_AR',
  esCT = 'es_CT',
  mx = 'es_MX',
  es = 'es_ES',
  et = 'et_EE',
  fi = 'fi_FI',
  fr = 'fr_FR',
  he = 'he_IL',
  hr = 'hr_HR',
  hu = 'hu_HU',
  id = 'id_ID',
  it = 'it_IT',
  jp = 'ja_JP',
  ko = 'ko',
  lt = 'lt_LT',
  lv = 'lv_LV',
  nl = 'nl_NL',
  no = 'no_NO',
  pl = 'pl_PL',
  br = 'pt_BR',
  pt = 'pt_PT',
  ro = 'ro_RO',
  ru = 'ru_RU',
  sk = 'sk_SK',
  sl = 'sl_SI',
  sr = 'sr_RS',
  sv = 'sv_SE',
  th = 'th_TH',
  tr = 'tr_TR',
  ukr = 'uk_UA',
  vn = 'vi_VN',
  cn = 'zh-CN',
}

export const defaultDisplayMeasurements = {
  bodyMeasurement: {
    chest: {
      position: 1,
      hidden: false,
    },
    waist: {
      position: 2,
      hidden: false,
    },
    hip: {
      position: 3,
      hidden: false,
    },
  },
};
