import { useDevice } from '@/hooks/use-device';
import { BodyMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface MidBodyProps {
  measure: BodyMeasure;
  className?: string;
}

export function MidBody({ measure, className }: MidBodyProps) {
  const { measures } = measure;
  const mappedMeasures = measures.map((item) => item.measure);
  const { isMobile } = useDevice();

  const findMeasure = (measure: string) => {
    const foundMeasure = mappedMeasures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full flex justify-center', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_mid_body">
            <mask id="mask0_128_1590" maskUnits="userSpaceOnUse" x="0" y="0" width="331" height="193">
              <rect id="rect" width="331" height="193" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_1590)">
              <g id="group_mid_body_mask">
                <g id="male">
                  <g id="Group 20">
                    <path
                      id="Vector 46"
                      d="M198.974 222.692C200.081 218.08 203.355 200.095 204.278 190.411L188.368 180.957H178.222L168.307 189.258C168.768 199.864 169.691 215.313 170.613 222.692C170.959 225.459 172.681 235.997 172.919 242.752C173.423 257.065 171.766 263.965 172.919 275.725C173.555 282.215 174.764 291.635 176.147 297.399C177.53 303.164 180.528 320.457 180.989 324.377C181.45 328.297 180.528 331.986 180.067 334.061C179.606 336.136 180.989 339.826 181.681 341.209C182.373 342.593 181.911 346.974 182.373 349.741C182.742 351.954 183.756 352.969 184.217 353.199C184.371 353.43 184.678 354.26 184.678 355.736C184.678 357.58 186.754 359.194 186.984 359.886C187.215 360.578 189.29 362.192 192.287 362.884C194.685 363.437 195.131 361.884 195.054 361.039C195.131 361.193 195.608 361.685 196.899 362.422C198.19 363.16 199.435 362.422 199.897 361.961C200.05 362.115 200.727 362.376 202.202 362.192C203.678 362.007 203.893 361.5 203.816 361.269C203.97 361.269 204.508 361.223 205.43 361.039C206.353 360.854 206.583 360.193 206.583 359.886C206.737 359.963 207.183 359.84 207.736 358.733C208.428 357.35 205.2 353.199 204.739 352.277C204.278 351.355 196.899 342.362 196.668 342.131C196.438 341.901 195.516 340.056 195.516 338.212C195.516 336.367 193.44 331.294 193.44 324.377C193.44 317.46 198.974 288.407 200.588 278.953C202.202 269.499 198.974 256.587 197.591 250.131C196.207 243.674 197.591 228.456 198.974 222.692Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.461157"
                    />
                    <path
                      id="Vector 43"
                      d="M132.337 222.692C131.23 218.08 127.956 200.095 127.033 190.411L142.943 180.957H153.089L163.004 189.258C162.543 199.864 161.62 215.313 160.698 222.692C160.352 225.459 158.63 235.997 158.392 242.752C157.888 257.065 159.545 263.965 158.392 275.725C157.756 282.215 156.548 291.635 155.164 297.399C153.781 303.164 150.783 320.457 150.322 324.377C149.861 328.297 150.783 331.986 151.244 334.061C151.705 336.136 150.322 339.826 149.63 341.209C148.938 342.593 149.4 346.974 148.938 349.741C148.57 351.954 147.555 352.969 147.094 353.199C146.94 353.43 146.633 354.26 146.633 355.736C146.633 357.58 144.557 359.194 144.327 359.886C144.096 360.578 142.021 362.192 139.024 362.884C136.626 363.437 136.18 361.884 136.257 361.039C136.18 361.193 135.703 361.685 134.412 362.422C133.121 363.16 131.876 362.422 131.414 361.961C131.261 362.115 130.584 362.376 129.109 362.192C127.633 362.007 127.418 361.5 127.495 361.269C127.341 361.269 126.803 361.223 125.881 361.039C124.958 360.854 124.728 360.193 124.728 359.886C124.574 359.963 124.128 359.84 123.575 358.733C122.883 357.35 126.111 353.199 126.572 352.277C127.033 351.355 134.412 342.362 134.643 342.131C134.873 341.901 135.795 340.056 135.795 338.212C135.795 336.367 137.871 331.294 137.871 324.377C137.871 317.46 132.337 288.407 130.723 278.953C129.109 269.499 132.337 256.587 133.72 250.131C135.104 243.674 133.72 228.456 132.337 222.692Z"
                      fill="#F4F2F2"
                      stroke="black"
                      stroke-width="0.461157"
                    />
                    <path
                      id="Union"
                      d="M152.617 -13.2259V-0.27871L146.451 2.80396L141.313 5.06459L133.504 8.7638L128.366 11.4354L123.023 14.5181L118.296 16.7787L115.625 18.6283L113.158 20.889L111.103 23.7661L108.843 27.4653L106.993 32.3976L106.377 37.1244L105.966 41.0291V46.5779L106.582 50.4826L103.91 68.5676L103.499 74.322L100.417 85.2141L99.8002 90.3519L98.1561 96.1062L96.923 104.327L96.101 108.642L95.8955 116.657V125.083L96.101 131.043L95.8955 137.414L96.3065 145.018L96.7175 147.279L95.6899 158.376L95.8955 161.253L96.512 164.13L97.5395 172.351L100.622 177.283L104.732 182.627L106.171 183.654L106.788 183.243L106.582 181.804C106.514 181.667 106.418 181.352 106.582 181.188C106.788 180.982 107.404 180.16 107.404 179.955V178.722L103.705 172.351L102.883 170.501V165.158L103.91 159.609L107.404 156.116L110.281 163.925L112.131 167.008H112.953L115.008 165.158C113.912 161.801 111.679 154.882 111.514 154.06C111.479 153.884 111.444 153.714 111.41 153.549C111.244 152.751 111.103 152.07 111.103 151.389C111.103 150.567 110.076 146.251 109.87 145.84C109.706 145.511 108.569 143.511 108.021 142.552L106.582 140.908C106.514 139.812 106.377 137.578 106.377 137.414C106.377 137.25 106.651 134.468 106.788 133.098L108.226 125.083L110.281 119.123L115.008 107.82L116.652 102.272C117.337 98.5723 118.091 91.1328 118.091 90.9684C118.091 90.804 118.502 87.6117 118.707 86.0361L119.118 82.3369L123.434 63.0188L124.645 58.2921L126.517 64.6629L128.366 71.8558L130.627 79.0487L131.654 84.1865L132.065 88.2967L131.243 94.6676L130.216 105.971L130.421 109.875L130.216 114.602L128.777 128.783L126.311 153.855L125.695 164.747L125.9 170.09L126.928 190.436L162.774 189.95L164.742 165.158H166.386L168.538 190.18L204.2 190.436L205.228 170.09L205.433 164.747L204.817 153.855L202.35 128.783L200.912 114.602L200.706 109.875L200.912 105.971L199.884 94.6676L199.062 88.2967L199.473 84.1865L200.501 79.0487L202.761 71.8558L204.611 64.6629L206.483 58.2921L207.694 63.0188L212.009 82.3369L212.42 86.0361C212.626 87.6117 213.037 90.804 213.037 90.9684C213.037 91.1328 213.791 98.5723 214.476 102.272L216.12 107.82L220.846 119.123L222.902 125.083L224.34 133.098C224.477 134.468 224.751 137.25 224.751 137.414C224.751 137.578 224.614 139.812 224.546 140.908L223.107 142.552C222.559 143.511 221.422 145.511 221.257 145.84C221.052 146.251 220.024 150.567 220.024 151.389C220.024 152.07 219.883 152.751 219.718 153.549C219.684 153.714 219.649 153.884 219.613 154.06C219.449 154.882 217.216 161.801 216.12 165.158L218.175 167.008H218.997L220.846 163.925L223.724 156.116L227.217 159.609L228.245 165.158V170.501L227.423 172.351L223.724 178.722V179.955C223.724 180.16 224.34 180.982 224.546 181.188C224.71 181.352 224.614 181.667 224.546 181.804L224.34 183.243L224.957 183.654L226.395 182.627L230.505 177.283L233.588 172.351L234.616 164.13L235.232 161.253L235.438 158.376L234.41 147.279L234.821 145.018L235.232 137.414L235.027 131.043L235.232 125.083V116.657L235.027 108.642L234.205 104.327L232.972 96.1062L231.328 90.3519L230.711 85.2141L227.628 74.322L227.217 68.5676L224.546 50.4826L225.162 46.5779V41.0291L224.751 37.1244L224.135 32.3976L222.285 27.4653L220.024 23.7661L217.969 20.889L215.503 18.6283L212.831 16.7787L208.105 14.5181L202.761 11.4354L197.624 8.7638L189.814 5.06459L184.676 2.80396L178.511 -0.27871V-13.2259L168.236 -28.5041V-36.4487L165.564 -32.4764L162.892 -36.4487V-28.5041L152.617 -13.2259Z"
                      fill="white"
                    />
                    <g id="Group 24">
                      <g id="Group 22">
                        <path
                          id="Vector 16"
                          d="M147.252 66.7179C155.678 65.0738 156.295 62.6077 158.35 60.1416C157.322 61.3746 150.212 60.8403 149.719 61.1691C149.225 61.4979 144.992 61.1691 142.526 60.9636C140.676 61.3061 136.607 59.525 135.949 59.525C135.292 59.525 132.661 58.0179 131.428 57.6754L126.085 54.1817L123.208 49.0439C123.824 55.0038 125.879 63.4298 125.879 63.4298C125.879 63.4298 139.084 68.3118 147.252 66.7179Z"
                          fill="url(#paint0_linear_128_1590)"
                        />
                        <path
                          id="Vector 36"
                          d="M126.722 54.5925C127.749 55.6201 131.448 58.1684 138.025 60.1413C144.601 62.1143 154.329 60.9634 158.37 60.1413"
                          stroke="black"
                          stroke-width="0.411023"
                          stroke-linecap="round"
                        />
                      </g>
                      <g id="Group 23">
                        <path
                          id="Vector 16_2"
                          d="M183.649 66.7179C175.223 65.0738 174.606 62.6077 172.551 60.1416C173.578 61.3746 180.689 60.8403 181.182 61.1691C181.676 61.4979 185.909 61.1691 188.375 60.9636C190.225 61.3061 194.294 59.525 194.952 59.525C195.609 59.525 198.24 58.0179 199.473 57.6754L204.816 54.1817L207.693 49.0439C207.467 56.4424 205.022 63.4298 205.022 63.4298C205.022 63.4298 191.817 68.3118 183.649 66.7179Z"
                          fill="url(#paint1_linear_128_1590)"
                        />
                        <path
                          id="Vector 36_2"
                          d="M204.179 54.5925C203.152 55.6201 199.452 58.1684 192.876 60.1413C186.3 62.1143 176.572 60.9634 172.53 60.1413"
                          stroke="black"
                          stroke-width="0.411023"
                          stroke-linecap="round"
                        />
                      </g>
                    </g>
                    <path
                      id="Vector 20"
                      d="M119.303 81.5147C119.303 81.5147 118.138 86.858 118.892 85.2139L118.07 93.0234C117.864 95.01 117.042 100.011 117.042 100.011C117.042 100.011 116.494 103.367 115.809 105.56L111.904 115.219C111.904 115.219 109.027 122 109.027 122.206C109.027 122.787 107.589 129.262 106.972 130.837L106.356 136.797L106.561 140.908L108 142.963C108.205 143.648 109.438 145.223 109.438 145.223C109.438 145.223 111.082 150.567 111.082 150.978C111.082 151.306 111.493 153.101 111.493 154.06L112.521 156.937C113.343 160.637 115.11 164.706 114.781 165.363C114.37 166.185 113.617 166.733 113.343 166.802L112.315 167.213L111.288 165.98L110.466 164.541C109.918 161.733 107.383 155.91 107.383 155.91L103.889 159.198L102.656 164.541V170.296L104.917 174.406C105.876 175.981 107.589 179.009 107.589 179.338C107.589 179.667 106.561 180.982 106.561 180.982L106.356 183.448H105.944L102.656 179.955L99.5736 175.844L97.724 172.351L96.2854 163.103L95.8744 160.226V155.704L96.2854 150.361L96.6965 147.278L95.8744 139.263V133.715V122.617C95.669 118.918 96.2855 108.519 96.2855 108.026C96.2855 107.532 97.3816 98.7777 98.3406 95.284C98.7517 93.7084 102.656 76.5824 102.656 76.5824L105.123 60.7581C106.698 67.5399 119.138 79.6651 119.303 80.4872C119.508 81.5147 119.303 81.5147 119.303 81.5147Z"
                      fill="url(#paint2_linear_128_1590)"
                    />
                    <path
                      id="Vector 17"
                      d="M158.966 160.431L162.871 164.541L164.926 165.363L163.488 180.16L163.004 190.18L126.907 190.641L125.879 172.145L125.674 163.514L126.085 156.937L126.907 146.867L128.14 133.098L130.195 116.246L154.856 151.594L156.911 155.499C159.172 160.568 158.966 160.431 158.966 160.431Z"
                      fill="url(#paint3_linear_128_1590)"
                    />
                    <g id="Group 12">
                      <path
                        id="Vector 35"
                        d="M178.511 -13.8425V-0.073259C184.95 2.8039 199.391 9.46247 205.638 13.0795C213.448 17.6007 218.996 19.0393 222.901 28.9039C226.025 36.7955 225.367 47.1944 224.751 51.5101L227.833 74.5274C229.135 77.9526 230.516 85.7587 231.327 89.1187C232.766 95.0786 234.41 100.011 235.232 113.369C235.858 123.549 234.821 130.769 235.232 133.92C235.232 135.222 235.232 138.195 235.232 139.675C235.232 141.154 234.684 145.223 234.41 147.073C234.958 151.115 235.889 159.609 235.232 161.253C234.41 163.308 234.615 170.09 232.971 173.378C231.327 176.667 225.367 184.476 224.751 183.449C224.258 182.626 224.545 181.462 224.751 180.982C224.34 180.845 223.559 180.325 223.723 179.338C223.888 178.352 226.943 173.036 228.45 170.501V165.158L227.217 159.404L223.723 155.91C222.559 159.541 219.942 166.843 218.791 167.008C217.64 167.172 216.667 165.843 216.325 165.158C217.489 161.116 219.86 152.868 220.024 152.211C220.229 151.389 219.818 150.361 221.257 146.456C222.408 143.333 224.066 141.456 224.751 140.908C224.888 138.441 224.71 131.906 222.901 125.494C220.641 117.479 217.763 112.753 215.297 104.943C213.324 98.6956 212.489 87.2006 212.009 82.3369L206.607 57.881L207.488 52.9487C207.008 56.9904 204.939 65.6082 202.144 73.4999C199.35 81.3915 199.062 87.2006 199.267 89.1187C199.884 94.051 201.076 104.696 200.911 107.82C200.747 110.944 200.843 113.917 200.911 115.013L202.144 125.494C202.967 133.098 204.734 150.526 205.227 159.404C205.72 168.282 204.825 183.997 204.277 190.641L168.307 189.489L166.385 165.158H165.152"
                        stroke="black"
                        stroke-width="0.411023"
                      />
                      <path
                        id="Vector 27"
                        d="M152.616 -13.8425V-0.073259C146.177 2.8039 131.736 9.46247 125.489 13.0795C117.679 17.6007 112.131 19.0393 108.226 28.9039C105.102 36.7955 105.76 47.1944 106.376 51.5101L103.294 74.5274C101.992 77.9526 100.611 85.7587 99.7998 89.1187C98.3613 95.0786 96.7172 100.011 95.8951 113.369C95.2687 123.549 96.3062 130.769 95.8951 133.92C95.8951 135.222 95.8951 138.195 95.8951 139.675C95.8951 141.154 96.4432 145.223 96.7172 147.073C96.1691 151.115 95.2375 159.609 95.8951 161.253C96.7172 163.308 96.5117 170.09 98.1558 173.378C99.7998 176.667 105.76 184.476 106.376 183.449C106.869 182.626 106.582 181.462 106.376 180.982C106.787 180.845 107.568 180.325 107.404 179.338C107.239 178.352 104.184 173.036 102.677 170.501V165.158L103.91 159.404L107.404 155.91C108.568 159.541 111.185 166.843 112.336 167.008C113.487 167.172 114.46 165.843 114.802 165.158C113.638 161.116 111.267 152.868 111.103 152.211C110.897 151.389 111.308 150.361 109.87 146.456C108.719 143.333 107.061 141.456 106.376 140.908C106.239 138.441 106.417 131.906 108.226 125.494C110.486 117.479 113.364 112.753 115.83 104.943C117.803 98.6956 118.638 87.2006 119.118 82.3369L124.52 57.881L123.639 52.9487C124.119 56.9904 126.188 65.6082 128.982 73.4999C131.777 81.3915 132.065 87.2006 131.86 89.1187C131.243 94.051 130.051 104.696 130.216 107.82C130.38 110.944 130.284 113.917 130.216 115.013L128.982 125.494C128.16 133.098 126.393 150.526 125.9 159.404C125.407 168.282 126.486 183.997 127.034 190.641L162.925 190.436L164.741 165.158H165.975"
                        stroke="black"
                        stroke-width="0.411023"
                      />
                      <path
                        id="Vector 44"
                        d="M162.543 192.947L127.725 194.331C127.725 194.331 127.495 193.178 127.264 191.333C127.164 190.529 127.034 187.874 127.034 187.874L128.648 186.491L162.773 189.719L162.543 192.947Z"
                        fill="#F4F2F2"
                      />
                    </g>
                    <g id="Group 19">
                      <g id="Group 18">
                        <g id="Group 14">
                          <path
                            id="Vector 32"
                            d="M163.509 -1.71753C163.509 -1.50223 163.912 -0.244893 164.63 0.478508C165.347 1.20191 165.825 2.33007 165.975 2.80372"
                            stroke="black"
                            stroke-width="0.411023"
                            stroke-linecap="round"
                          />
                          <g id="Group 13">
                            <path
                              id="Vector 31"
                              d="M162.892 15.751C162.892 15.751 162.234 14.0658 160.426 13.0793C158.617 12.0929 152.822 12.0518 149.945 10.2021"
                              stroke="black"
                              stroke-width="0.411023"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              id="Vector 14"
                              d="M167.413 15.751C167.413 15.751 168.071 14.0658 169.879 13.0793C171.688 12.0929 177.483 12.0518 180.36 10.2021"
                              stroke="black"
                              stroke-width="0.411023"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                        </g>
                        <g id="Group 15">
                          <path
                            id="Vector 15"
                            d="M112.953 78.4321C112.953 78.4321 113.158 80.2817 113.775 81.1038C114.391 81.9258 115.213 82.1313 115.213 82.1313"
                            stroke="black"
                            stroke-width="0.411023"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <path
                            id="Vector 33"
                            d="M219.613 78.4321C219.613 78.4321 219.408 80.2817 218.791 81.1038C218.175 81.9258 217.353 82.1313 217.353 82.1313"
                            stroke="black"
                            stroke-width="0.411023"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </g>
                        <g id="Group 16">
                          <path
                            id="Vector 11"
                            d="M165.74 101.916C165.77 101.807 165.707 101.693 165.598 101.663C165.488 101.632 165.375 101.695 165.344 101.804L165.74 101.916ZM165.344 101.804C165.267 102.075 165.276 102.47 165.32 102.897C165.366 103.334 165.453 103.834 165.554 104.333C165.655 104.832 165.771 105.334 165.874 105.772C165.978 106.213 166.067 106.585 166.118 106.834L166.521 106.751C166.468 106.495 166.376 106.114 166.274 105.678C166.171 105.241 166.057 104.744 165.957 104.251C165.857 103.758 165.773 103.272 165.729 102.855C165.685 102.429 165.686 102.104 165.74 101.916L165.344 101.804ZM166.118 106.834C166.321 107.82 166.371 109.646 166.119 110.759L166.52 110.85C166.787 109.67 166.733 107.784 166.521 106.751L166.118 106.834ZM166.119 110.759C166.059 111.024 165.976 111.086 165.945 111.1C165.912 111.114 165.849 111.116 165.738 111.054C165.634 110.996 165.528 110.905 165.444 110.821C165.404 110.78 165.37 110.743 165.347 110.716C165.336 110.703 165.327 110.693 165.321 110.686C165.319 110.682 165.317 110.68 165.315 110.678C165.315 110.678 165.314 110.677 165.314 110.677C165.314 110.677 165.314 110.677 165.314 110.677C165.314 110.677 165.314 110.677 165.314 110.677C165.314 110.677 165.314 110.677 165.314 110.677C165.314 110.677 165.314 110.677 165.153 110.804C164.992 110.932 164.992 110.932 164.992 110.932C164.992 110.932 164.992 110.932 164.992 110.932C164.992 110.932 164.992 110.932 164.992 110.932C164.992 110.933 164.993 110.933 164.993 110.933C164.994 110.934 164.995 110.935 164.996 110.937C164.998 110.939 165.001 110.943 165.005 110.948C165.012 110.957 165.023 110.97 165.037 110.986C165.065 111.018 165.105 111.062 165.153 111.11C165.248 111.206 165.385 111.329 165.54 111.414C165.688 111.496 165.9 111.569 166.111 111.476C166.322 111.382 166.45 111.158 166.52 110.85L166.119 110.759Z"
                            fill="black"
                          />
                          <path
                            id="Vector 38"
                            d="M153.027 148.717C153.027 148.717 155.424 151.723 157.549 156.938C160.954 165.296 164.982 165.993 167.619 164.952C170.256 163.912 173.551 159.057 174.606 156.398"
                            stroke="black"
                            stroke-width="0.411023"
                            stroke-linecap="round"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                  <path
                    id="Vector 44_2"
                    d="M169.23 195.714L203.586 194.331C203.586 194.331 203.817 193.178 204.047 191.333C204.148 190.529 204.278 187.874 204.278 187.874L202.433 186.491L168.308 186.722C168.308 186.722 168.344 189.023 168.538 190.872C168.999 195.253 169.23 195.714 169.23 195.714Z"
                    fill="white"
                  />
                </g>
                {findMeasure('chest') && (
                  <g id="chest">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M208 52.5C208 52.5 179.11 57.3886 156.958 56.1347M124 52.5C124 52.5 137.941 56.1347 150.479 56.1347"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 27_2"
                            d="M159.763 53.2411L155.694 55.9719L159.19 58.5935"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 28"
                            d="M146.941 53.2411L151.01 55.9719L147.514 58.5935"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('waistLower') && (
                  <g id="waistLower">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M202.5 129.5C202.5 129.5 186.301 135.215 156.683 133.552M129 129C129 129 135.718 132.339 150.124 133.552"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 27_3"
                            d="M159.524 130.482L155.404 133.247L158.943 135.901"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 28_2"
                            d="M146.542 130.482L150.662 133.247L147.122 135.901"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('waist') && (
                  <g id="waist">
                    <g id="Group 221_3">
                      <g id="Group 217_3">
                        <g id="Group 220_3">
                          <path
                            id="Ellipse 23_3"
                            d="M200 97C200 97 179.129 102.186 156.941 100.93M130.5 99C130.5 99 137.893 100.93 150.452 100.93"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 27_4"
                            d="M159.752 97.9445L155.676 100.68L159.178 103.306"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 28_3"
                            d="M146.908 97.9445L150.984 100.68L147.482 103.306"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('upper_waist') && (
                  <g id="upper_waist">
                    <g id="Group 221_4">
                      <g id="Group 217_4">
                        <g id="Group 220_4">
                          <path
                            id="Ellipse 23_4"
                            d="M202 74C202 74 178.964 79.6136 157.018 78.3713M130 76C130 76 139.061 77.568 150.6 78.3713"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 27_5"
                            d="M159.798 75.4691L155.766 78.1745L159.23 80.7718"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 28_4"
                            d="M147.094 75.4691L151.126 78.1745L147.662 80.7718"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('body_length') && (
                  <g id="body_length">
                    <path
                      id="Vector 19"
                      d="M189.527 117.26L189.527 27.9999"
                      stroke="#E55959"
                      stroke-width="2"
                      stroke-linecap="square"
                    />
                    <path
                      id="Vector 29"
                      d="M124 121L206.553 121"
                      stroke="#E55959"
                      stroke-linecap="square"
                      stroke-dasharray="2.58 2.58"
                    />
                    <path
                      id="Vector 28_5"
                      d="M192.762 115.411L189.444 118.668L186.564 115.314"
                      stroke="#E55959"
                      stroke-width="2"
                    />
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_1590"
              x1="138.621"
              y1="11.4353"
              x2="139.854"
              y2="75.5549"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_1590"
              x1="192.28"
              y1="11.4353"
              x2="191.047"
              y2="75.5549"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_1590"
              x1="117.042"
              y1="61.7856"
              x2="102.451"
              y2="179.338"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_128_1590"
              x1="147.458"
              y1="131.043"
              x2="131.633"
              y2="222.29"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className=" h-full m-auto "
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_mid_body">
            <mask id="mask0_128_2895" maskUnits="userSpaceOnUse" x="0" y="0" width="342" height="291">
              <rect id="rect" width="342" height="291" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_2895)">
              <g id="group_mid_body_mask">
                <g id="male">
                  <g id="Group 20">
                    <path
                      id="Vector 46"
                      d="M216.139 344.858C217.844 337.754 222.888 310.049 224.309 295.13L199.8 280.567H184.171L168.898 293.354C169.608 309.694 171.029 333.492 172.45 344.858C172.983 349.121 175.635 365.355 176.002 375.761C176.779 397.81 174.226 408.439 176.002 426.555C176.982 436.553 178.843 451.064 180.975 459.944C183.106 468.824 187.723 495.464 188.434 501.502C189.144 507.54 187.723 513.224 187.013 516.42C186.303 519.617 188.434 525.3 189.499 527.432C190.565 529.563 189.855 536.312 190.565 540.574C191.133 543.984 192.696 545.547 193.407 545.902C193.643 546.257 194.117 547.536 194.117 549.809C194.117 552.651 197.314 555.137 197.669 556.203C198.024 557.269 201.221 559.755 205.839 560.821C209.533 561.673 210.219 559.281 210.101 557.979C210.219 558.216 210.954 558.974 212.943 560.11C214.932 561.247 216.85 560.11 217.56 559.4C217.797 559.637 218.839 560.039 221.112 559.755C223.386 559.471 223.717 558.689 223.599 558.334C223.835 558.334 224.664 558.263 226.085 557.979C227.506 557.695 227.861 556.677 227.861 556.203C228.098 556.321 228.785 556.132 229.637 554.427C230.703 552.296 225.73 545.902 225.019 544.481C224.309 543.061 212.943 529.208 212.587 528.852C212.232 528.497 210.811 525.656 210.811 522.814C210.811 519.972 207.615 512.158 207.615 501.502C207.615 490.846 216.139 446.091 218.626 431.527C221.112 416.964 216.139 397.073 214.008 387.127C211.877 377.182 214.008 353.738 216.139 344.858Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.710402"
                    />
                    <path
                      id="Vector 43"
                      d="M113.486 344.858C111.781 337.754 106.737 310.049 105.316 295.13L129.825 280.567H145.454L160.727 293.354C160.017 309.694 158.596 333.492 157.175 344.858C156.642 349.121 153.99 365.355 153.623 375.761C152.846 397.81 155.399 408.439 153.623 426.555C152.643 436.553 150.782 451.064 148.65 459.944C146.519 468.824 141.902 495.464 141.191 501.502C140.481 507.54 141.902 513.224 142.612 516.42C143.322 519.617 141.191 525.3 140.126 527.432C139.06 529.563 139.77 536.312 139.06 540.574C138.492 543.984 136.929 545.547 136.218 545.902C135.982 546.257 135.508 547.536 135.508 549.809C135.508 552.651 132.311 555.137 131.956 556.203C131.601 557.269 128.404 559.755 123.786 560.821C120.092 561.673 119.406 559.281 119.524 557.979C119.406 558.216 118.671 558.974 116.682 560.11C114.693 561.247 112.775 560.11 112.065 559.4C111.828 559.637 110.786 560.039 108.513 559.755C106.239 559.471 105.908 558.689 106.026 558.334C105.79 558.334 104.961 558.263 103.54 557.979C102.119 557.695 101.764 556.677 101.764 556.203C101.527 556.321 100.84 556.132 99.9879 554.427C98.9223 552.296 103.895 545.902 104.606 544.481C105.316 543.061 116.682 529.208 117.038 528.852C117.393 528.497 118.814 525.656 118.814 522.814C118.814 519.972 122.01 512.158 122.01 501.502C122.01 490.846 113.486 446.091 110.999 431.527C108.513 416.964 113.486 397.073 115.617 387.127C117.748 377.182 115.617 353.738 113.486 344.858Z"
                      fill="#F4F2F2"
                      stroke="black"
                      stroke-width="0.710402"
                    />
                    <path
                      id="Union"
                      d="M144.726 -18.5671V1.37775L135.229 6.12653L127.314 9.60897L115.284 15.3075L107.369 19.4231L99.1381 24.1719L91.8566 27.6544L87.741 30.5036L83.942 33.9861L80.7761 38.4183L77.2937 44.1168L74.4444 51.7149L73.4947 58.9963L72.8615 65.0115V73.5593L73.8113 79.5744L69.6956 107.434L69.0625 116.298L64.3137 133.077L63.3639 140.992L60.8313 149.856L58.9317 162.52L57.6654 169.168L57.3488 181.515V194.495L57.6654 203.676L57.3488 213.49L57.982 225.204L58.6152 228.686L57.0322 245.782L57.3488 250.214L58.2986 254.646L59.8815 267.31L64.6303 274.908L70.962 283.139L73.1781 284.722L74.1278 284.089L73.8113 281.873C73.7057 281.662 73.558 281.176 73.8113 280.923C74.1278 280.606 75.0776 279.34 75.0776 279.023V277.124L69.3791 267.31L68.1127 264.46V256.229L69.6956 247.681L75.0776 242.299L79.5098 254.33L82.3591 259.078H83.6254L86.7913 256.229C85.1028 251.058 81.6626 240.4 81.4093 239.134C81.355 238.862 81.3007 238.6 81.248 238.345C80.9934 237.116 80.7761 236.067 80.7761 235.018C80.7761 233.752 79.1932 227.103 78.8766 226.47C78.6234 225.964 76.8716 222.882 76.0274 221.405L73.8113 218.872C73.7057 217.184 73.4947 213.743 73.4947 213.49C73.4947 213.237 73.9168 208.952 74.1278 206.842L76.3439 194.495L79.5098 185.314L86.7913 167.902L89.324 159.354C90.3792 153.655 91.5401 142.195 91.5401 141.942C91.5401 141.688 92.1732 136.771 92.4898 134.344L93.123 128.645L99.7713 98.8861L101.636 91.6046L104.52 101.419L107.369 112.499L110.852 123.58L112.435 131.494L113.068 137.826L111.802 147.64L110.219 165.052L110.535 171.068L110.219 178.349L108.003 200.193L104.203 238.817L103.254 255.596L103.57 263.827L105.153 295.169L160.373 294.42L163.405 256.229H165.938L169.253 294.775L224.189 295.169L225.772 263.827L226.089 255.596L225.139 238.817L221.34 200.193L219.124 178.349L218.807 171.068L219.124 165.052L217.541 147.64L216.275 137.826L216.908 131.494L218.491 123.58L221.973 112.499L224.823 101.419L227.706 91.6046L229.571 98.8861L236.22 128.645L236.853 134.344C237.169 136.771 237.803 141.688 237.803 141.942C237.803 142.195 238.963 153.655 240.019 159.354L242.551 167.902L249.833 185.314L252.999 194.495L255.215 206.842C255.426 208.952 255.848 213.237 255.848 213.49C255.848 213.743 255.637 217.184 255.531 218.872L253.315 221.405C252.471 222.882 250.719 225.964 250.466 226.47C250.149 227.103 248.566 233.752 248.566 235.018C248.566 236.067 248.349 237.116 248.095 238.345C248.042 238.6 247.988 238.862 247.933 239.134C247.68 240.4 244.24 251.058 242.551 256.229L245.717 259.078H246.984L249.833 254.33L254.265 242.299L259.647 247.681L261.23 256.229V264.46L259.964 267.31L254.265 277.124V279.023C254.265 279.34 255.215 280.606 255.531 280.923C255.785 281.176 255.637 281.662 255.531 281.873L255.215 284.089L256.165 284.722L258.381 283.139L264.712 274.908L269.461 267.31L271.044 254.646L271.994 250.214L272.31 245.782L270.727 228.686L271.361 225.204L271.994 213.49L271.677 203.676L271.994 194.495V181.515L271.677 169.168L270.411 162.52L268.511 149.856L265.979 140.992L265.029 133.077L260.28 116.298L259.647 107.434L255.531 79.5744L256.481 73.5593V65.0115L255.848 58.9963L254.898 51.7149L252.049 44.1168L248.566 38.4183L245.401 33.9861L241.602 30.5036L237.486 27.6544L230.205 24.1719L221.973 19.4231L214.059 15.3075L202.028 9.60897L194.114 6.12653L184.616 1.37775V-18.5671L168.787 -42.1028V-54.3413L164.671 -48.222L160.556 -54.3413V-42.1028L144.726 -18.5671Z"
                      fill="white"
                    />
                    <path
                      id="Vector 18"
                      d="M165.905 -1.47137C175.161 -5.39712 179.835 -13.8182 181.418 -16.3509V-18.567C177.619 -21.0997 169.894 -26.165 169.388 -26.165C168.881 -26.165 152.925 -21.7328 145.011 -19.5167V-3.68747C151.342 0.428146 160.029 1.02077 165.905 -1.47137Z"
                      fill="url(#paint0_linear_128_2895)"
                    />
                    <g id="Group 24">
                      <g id="Group 22">
                        <path
                          id="Vector 16"
                          d="M136.463 104.585C149.443 102.052 150.393 98.2528 153.558 94.4538C151.976 96.3533 141.022 95.5302 140.262 96.0367C139.502 96.5432 132.98 96.0367 129.181 95.7201C126.332 96.2478 120.064 93.504 119.051 93.504C118.038 93.504 113.985 91.1824 112.086 90.6547L103.855 85.2728L99.4224 77.3582C100.372 86.5391 103.538 99.5191 103.538 99.5191C103.538 99.5191 123.879 107.04 136.463 104.585Z"
                          fill="url(#paint1_linear_128_2895)"
                        />
                        <path
                          id="Vector 36"
                          d="M104.836 85.9055C106.419 87.4884 112.117 91.4141 122.248 94.4533C132.379 97.4925 147.364 95.7197 153.59 94.4533"
                          stroke="black"
                          stroke-width="0.633171"
                          stroke-linecap="round"
                        />
                      </g>
                      <g id="Group 23">
                        <path
                          id="Vector 16_2"
                          d="M192.53 104.585C179.55 102.052 178.601 98.2528 175.435 94.4538C177.018 96.3533 187.971 95.5302 188.731 96.0367C189.491 96.5432 196.013 96.0367 199.812 95.7201C202.661 96.2478 208.929 93.504 209.942 93.504C210.956 93.504 215.008 91.1824 216.907 90.6547L225.139 85.2728L229.571 77.3582C229.222 88.7552 225.455 99.5191 225.455 99.5191C225.455 99.5191 205.114 107.04 192.53 104.585Z"
                          fill="url(#paint2_linear_128_2895)"
                        />
                        <path
                          id="Vector 36_2"
                          d="M224.157 85.9055C222.574 87.4884 216.876 91.4141 206.745 94.4533C196.614 97.4925 181.629 95.7197 175.403 94.4533"
                          stroke="black"
                          stroke-width="0.633171"
                          stroke-linecap="round"
                        />
                      </g>
                    </g>
                    <path
                      id="Vector 20"
                      d="M93.407 127.379C93.407 127.379 91.6133 135.61 92.7741 133.077L91.5077 145.107C91.1911 148.168 89.9247 155.871 89.9247 155.871C89.9247 155.871 89.0805 161.042 88.0252 164.419L82.0101 179.299C82.0101 179.299 77.5779 189.746 77.5779 190.063C77.5779 190.958 75.3618 200.932 74.4121 203.359L73.4623 212.54L73.7789 218.872L75.995 222.038C76.3116 223.093 78.2111 225.52 78.2111 225.52C78.2111 225.52 80.7438 233.751 80.7438 234.385C80.7438 234.891 81.3769 237.656 81.3769 239.133L82.9599 243.566C84.2262 249.264 86.9488 255.533 86.4423 256.546C85.8091 257.812 84.6483 258.656 84.2262 258.762L82.6433 259.395L81.0604 257.495L79.794 255.279C78.9498 250.953 75.0452 241.983 75.0452 241.983L69.6633 247.048L67.7638 255.279V264.144L71.2462 270.475C72.7236 272.903 75.3618 277.567 75.3618 278.073C75.3618 278.58 73.7789 280.606 73.7789 280.606L73.4623 284.405H72.8291L67.7638 279.023L63.015 272.691L60.1657 267.31L57.9496 253.063L57.3164 248.631V241.666L57.9496 233.435L58.5828 228.686L57.3164 216.339V207.791V190.696C57 184.997 57.9497 168.978 57.9497 168.218C57.9497 167.458 59.6382 153.972 61.1156 148.59C61.7487 146.163 67.7639 119.781 67.7639 119.781L71.5629 95.4036C73.9901 105.851 93.1539 124.529 93.4072 125.796C93.7238 127.379 93.407 127.379 93.407 127.379Z"
                      fill="url(#paint3_linear_128_2895)"
                    />
                    <path
                      id="Vector 17"
                      d="M154.508 248.947L160.523 255.279L163.689 256.545L161.473 279.339L160.728 294.775L105.121 295.485L103.538 266.993L103.221 253.696L103.854 243.565L105.121 228.053L107.02 206.841L110.186 180.881L148.176 235.334L151.342 241.349C154.825 249.158 154.508 248.947 154.508 248.947Z"
                      fill="url(#paint4_linear_128_2895)"
                    />
                    <g id="Group 12">
                      <path
                        id="Vector 35"
                        d="M184.616 -19.5168V1.69439C194.535 6.12659 216.781 16.384 226.405 21.9559C238.435 28.9207 246.983 31.1368 252.998 46.333C257.81 58.4898 256.797 74.5091 255.848 81.1574L260.596 116.615C262.601 121.891 264.729 133.917 265.978 139.093C268.194 148.274 270.727 155.872 271.993 176.45C272.958 192.131 271.36 203.254 271.993 208.108C271.993 210.113 271.993 214.693 271.993 216.973C271.993 219.252 271.149 225.52 270.727 228.37C271.571 234.596 273.006 247.681 271.993 250.214C270.727 253.38 271.044 263.827 268.511 268.893C265.978 273.958 256.797 285.988 255.848 284.405C255.088 283.139 255.531 281.345 255.848 280.606C255.214 280.395 254.011 279.593 254.265 278.074C254.518 276.554 259.224 268.365 261.546 264.46V256.229L259.647 247.365L254.265 241.983C252.471 247.576 248.439 258.825 246.667 259.078C244.894 259.332 243.395 257.284 242.867 256.229C244.661 250.003 248.313 237.297 248.566 236.284C248.883 235.018 248.249 233.435 250.466 227.42C252.238 222.608 254.792 219.716 255.848 218.872C256.059 215.073 255.784 205.006 252.998 195.128C249.516 182.781 245.084 175.5 241.285 163.47C238.245 153.845 236.958 136.138 236.219 128.645L227.897 90.9715L229.254 83.3735C228.516 89.5997 225.329 102.875 221.023 115.032C216.718 127.189 216.274 136.138 216.591 139.093C217.541 146.691 219.377 163.09 219.124 167.902C218.87 172.714 219.018 177.294 219.124 178.982L221.023 195.128C222.289 206.842 225.012 233.688 225.772 247.365C226.532 261.041 225.153 285.25 224.309 295.486L168.898 293.71L165.937 256.229H164.038"
                        stroke="black"
                        stroke-width="0.633171"
                      />
                      <path
                        id="Vector 27"
                        d="M144.726 -19.5168V1.69439C134.807 6.12659 112.561 16.384 102.937 21.9559C90.9066 28.9207 82.3587 31.1368 76.3436 46.333C71.5315 58.4898 72.5445 74.5091 73.4943 81.1574L68.7455 116.615C66.7405 121.891 64.6129 133.917 63.3635 139.093C61.1475 148.274 58.6148 155.872 57.3484 176.45C56.3835 192.131 57.9816 203.254 57.3484 208.108C57.3484 210.113 57.3484 214.693 57.3484 216.973C57.3484 219.252 58.1927 225.52 58.6148 228.37C57.7705 234.596 56.3354 247.681 57.3484 250.214C58.6148 253.38 58.2982 263.827 60.8309 268.893C63.3635 273.958 72.5445 285.988 73.4943 284.405C74.2541 283.139 73.8109 281.345 73.4943 280.606C74.1275 280.395 75.3305 279.593 75.0772 278.074C74.824 276.554 70.1174 268.365 67.7958 264.46V256.229L69.6953 247.365L75.0772 241.983C76.8712 247.576 80.9024 258.825 82.6753 259.078C84.4482 259.332 85.9467 257.284 86.4743 256.229C84.6803 250.003 81.029 237.297 80.7758 236.284C80.4592 235.018 81.0923 233.435 78.8762 227.42C77.1034 222.608 74.5496 219.716 73.4943 218.872C73.2832 215.073 73.5576 205.006 76.3436 195.128C79.826 182.781 84.2582 175.5 88.0572 163.47C91.0964 153.845 92.3839 136.138 93.1226 128.645L101.445 90.9715L100.087 83.3735C100.826 89.5997 104.013 102.875 108.319 115.032C112.624 127.189 113.067 136.138 112.751 139.093C111.801 146.691 109.965 163.09 110.218 167.902C110.471 172.714 110.324 177.294 110.218 178.982L108.319 195.128C107.052 206.842 104.33 233.688 103.57 247.365C102.81 261.041 104.472 285.25 105.317 295.486L160.606 295.169L163.405 256.229H165.304"
                        stroke="black"
                        stroke-width="0.633171"
                      />
                      <path
                        id="Vector 44"
                        d="M160.017 299.038L106.382 301.169C106.382 301.169 106.027 299.393 105.672 296.552C105.517 295.313 105.316 291.223 105.316 291.223L107.803 289.092L160.373 294.065L160.017 299.038Z"
                        fill="#F4F2F2"
                      />
                    </g>
                    <g id="Group 19">
                      <g id="Group 18">
                        <g id="Group 14">
                          <path
                            id="Vector 32"
                            d="M161.505 -0.838623C161.505 -0.506962 162.127 1.42994 163.232 2.54432C164.337 3.6587 165.074 5.39661 165.304 6.12626"
                            stroke="black"
                            stroke-width="0.633171"
                            stroke-linecap="round"
                          />
                          <g id="Group 13">
                            <path
                              id="Vector 31"
                              d="M160.556 26.071C160.556 26.071 159.543 23.475 156.757 21.9554C153.971 20.4358 145.043 20.3725 140.611 17.5232"
                              stroke="black"
                              stroke-width="0.633171"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              id="Vector 14"
                              d="M167.52 26.071C167.52 26.071 168.533 23.475 171.319 21.9554C174.105 20.4358 183.033 20.3725 187.465 17.5232"
                              stroke="black"
                              stroke-width="0.633171"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                        </g>
                        <g id="Group 15">
                          <path
                            id="Vector 15"
                            d="M83.625 122.63C83.625 122.63 83.9416 125.479 84.8913 126.745C85.8411 128.012 87.1074 128.328 87.1074 128.328"
                            stroke="black"
                            stroke-width="0.633171"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <path
                            id="Vector 33"
                            d="M247.933 122.63C247.933 122.63 247.617 125.479 246.667 126.745C245.717 128.012 244.451 128.328 244.451 128.328"
                            stroke="black"
                            stroke-width="0.633171"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </g>
                        <g id="Group 16">
                          <path
                            id="Vector 11"
                            d="M164.942 158.807C164.989 158.639 164.892 158.464 164.723 158.416C164.555 158.368 164.38 158.466 164.333 158.635L164.942 158.807ZM164.333 158.635C164.215 159.052 164.227 159.66 164.296 160.318C164.366 160.99 164.501 161.761 164.656 162.53C164.812 163.299 164.99 164.072 165.149 164.747C165.309 165.426 165.447 166 165.525 166.383L166.146 166.255C166.064 165.861 165.923 165.273 165.765 164.602C165.607 163.928 165.431 163.163 165.277 162.404C165.123 161.644 164.993 160.895 164.926 160.253C164.857 159.597 164.86 159.096 164.942 158.807L164.333 158.635ZM165.525 166.383C165.837 167.901 165.914 170.714 165.527 172.429L166.144 172.568C166.555 170.751 166.472 167.845 166.146 166.255L165.525 166.383ZM165.527 172.429C165.434 172.837 165.307 172.933 165.258 172.954C165.208 172.976 165.111 172.978 164.94 172.884C164.779 172.795 164.616 172.653 164.487 172.524C164.424 172.461 164.373 172.404 164.338 172.363C164.32 172.343 164.307 172.327 164.298 172.316C164.294 172.311 164.29 172.307 164.288 172.305C164.287 172.303 164.287 172.303 164.286 172.302C164.286 172.302 164.286 172.302 164.286 172.302C164.286 172.302 164.286 172.302 164.286 172.302C164.286 172.302 164.286 172.302 164.286 172.302C164.286 172.302 164.286 172.302 164.038 172.499C163.79 172.695 163.79 172.695 163.79 172.695C163.79 172.695 163.79 172.695 163.79 172.695C163.79 172.696 163.79 172.696 163.791 172.696C163.791 172.696 163.791 172.697 163.792 172.698C163.793 172.699 163.794 172.7 163.796 172.703C163.799 172.707 163.804 172.712 163.81 172.72C163.822 172.734 163.839 172.754 163.86 172.779C163.903 172.828 163.964 172.895 164.038 172.97C164.184 173.117 164.396 173.307 164.634 173.438C164.863 173.565 165.19 173.677 165.514 173.533C165.84 173.389 166.037 173.044 166.144 172.568L165.527 172.429Z"
                            fill="black"
                          />
                          <path
                            id="Vector 38"
                            d="M145.359 230.902C145.359 230.902 149.051 235.533 152.324 243.566C157.57 256.442 163.774 257.515 167.836 255.912C171.899 254.31 176.975 246.83 178.6 242.734"
                            stroke="black"
                            stroke-width="0.633171"
                            stroke-linecap="round"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                  <path
                    id="Vector 44_2"
                    d="M170.319 303.3L223.243 301.169C223.243 301.169 223.599 299.393 223.954 296.551C224.109 295.312 224.309 291.223 224.309 291.223L221.467 289.092L168.898 289.447C168.898 289.447 168.953 292.993 169.253 295.841C169.963 302.59 170.319 303.3 170.319 303.3Z"
                    fill="white"
                  />
                </g>
                {findMeasure('chest') && (
                  <g id="chest">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M230.043 82.6821C230.043 82.6821 185.538 90.2128 151.414 88.2813M100.643 82.6821C100.643 82.6821 122.118 88.2813 141.434 88.2813"
                            stroke="#E55959"
                            stroke-width="3.08095"
                          />
                          <path
                            id="Vector 27_2"
                            d="M155.736 83.8238L149.467 88.0305L154.852 92.069"
                            stroke="#E55959"
                            stroke-width="3.08095"
                          />
                          <path
                            id="Vector 28"
                            d="M135.983 83.8238L142.251 88.0305L136.866 92.069"
                            stroke="#E55959"
                            stroke-width="3.08095"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('waistLower') && (
                  <g id="lower_waist">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M221.571 201.299C221.571 201.299 196.616 210.102 150.991 207.541M108.346 200.529C108.346 200.529 118.694 205.673 140.887 207.541"
                            stroke="#E55959"
                            stroke-width="3.08095"
                          />
                          <path
                            id="Vector 27_3"
                            d="M155.367 202.811L149.02 207.07L154.472 211.159"
                            stroke="#E55959"
                            stroke-width="3.08095"
                          />
                          <path
                            id="Vector 28_2"
                            d="M135.368 202.811L141.715 207.07L136.262 211.159"
                            stroke="#E55959"
                            stroke-width="3.08095"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('waist') && (
                  <g id="waist">
                    <g id="Group 221_3">
                      <g id="Group 217_3">
                        <g id="Group 220_3">
                          <path
                            id="Ellipse 23_3"
                            d="M217.719 151.233C217.719 151.233 185.568 159.222 151.388 157.288M110.656 154.314C110.656 154.314 122.045 157.288 141.392 157.288"
                            stroke="#E55959"
                            stroke-width="3.08095"
                          />
                          <path
                            id="Vector 27_4"
                            d="M155.718 152.688L149.439 156.902L154.833 160.947"
                            stroke="#E55959"
                            stroke-width="3.08095"
                          />
                          <path
                            id="Vector 28_3"
                            d="M135.932 152.688L142.211 156.902L136.817 160.947"
                            stroke="#E55959"
                            stroke-width="3.08095"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('upper_waist') && (
                  <g id="upper_waist">
                    <g id="Group 221_4">
                      <g id="Group 217_4">
                        <g id="Group 220_4">
                          <path
                            id="Ellipse 23_4"
                            d="M220.801 115.802C220.801 115.802 185.314 124.45 151.507 122.536M109.886 118.883C109.886 118.883 123.844 121.299 141.62 122.536"
                            stroke="#E55959"
                            stroke-width="3.08095"
                          />
                          <path
                            id="Vector 27_5"
                            d="M155.789 118.065L149.578 122.233L154.914 126.234"
                            stroke="#E55959"
                            stroke-width="3.08095"
                          />
                          <path
                            id="Vector 28_4"
                            d="M136.219 118.065L142.429 122.233L137.094 126.234"
                            stroke="#E55959"
                            stroke-width="3.08095"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('body_length') && (
                  <g id="body_length">
                    <path
                      id="Vector 19"
                      d="M201.586 182.444L201.586 44.9403"
                      stroke="#E55959"
                      stroke-width="3.08095"
                      stroke-linecap="square"
                    />
                    <path
                      id="Vector 29"
                      d="M100.643 188.205L227.814 188.205"
                      stroke="#E55959"
                      stroke-width="1.54048"
                      stroke-linecap="square"
                      stroke-dasharray="3.97 3.97"
                    />
                    <path
                      id="Vector 28_5"
                      d="M206.569 179.595L201.458 184.612L197.022 179.445"
                      stroke="#E55959"
                      stroke-width="3.08095"
                    />
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_2895"
              x1="157.357"
              y1="-26.165"
              x2="157.357"
              y2="11.2365"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_2895"
              x1="123.166"
              y1="19.423"
              x2="125.066"
              y2="118.198"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_2895"
              x1="205.827"
              y1="19.423"
              x2="203.927"
              y2="118.198"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_128_2895"
              x1="89.9247"
              y1="96.9865"
              x2="67.4471"
              y2="278.073"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_128_2895"
              x1="136.779"
              y1="203.676"
              x2="112.402"
              y2="344.24"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
