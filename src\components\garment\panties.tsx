import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface PantiesProps {
  measure: GarmentMeasure;
  className?: string;
}

export function Panties({ measure, className }: PantiesProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_panties">
            <g id="panties">
              <g id="Group 188">
                <path
                  id="Vector 308"
                  d="M69.3248 123.06C61.1091 118.004 54.4909 117.618 52.2087 118.057C64.4094 110.157 89.4427 94.0947 91.9706 93.0414C95.1305 91.7248 132.522 112.527 134.366 113.581C135.84 114.423 151.131 129.731 158.591 137.28L147.269 163.612C144.986 162.208 144.502 157.064 138.052 154.132C132.259 151.499 112.247 147.813 103.294 144.653C94.3405 141.493 79.5944 129.38 69.3248 123.06Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.526647"
                />
                <path
                  id="Vector 309"
                  d="M263.394 123.06C271.61 118.004 278.228 117.618 280.51 118.057C268.31 110.157 243.276 94.0947 240.749 93.0414C237.589 91.7248 200.197 112.527 198.353 113.581C196.879 114.423 181.588 129.731 174.128 137.28L184.924 163.875C187.206 162.471 188.217 157.064 194.667 154.132C200.46 151.499 220.473 147.813 229.426 144.653C238.379 141.493 253.125 129.38 263.394 123.06Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.526647"
                />
                <g id="Vector">
                  <path
                    d="M181.169 37.7444C214.233 36.3105 242.789 30.9258 267.606 28.0897C267.87 28.0595 268.134 28.0296 268.398 28C271.645 42.3073 275.743 62.1472 278.141 84.3513C279.568 97.5723 279.72 111.474 280.51 118.32C264.272 118.232 228.672 121.75 207.833 125.166C175.708 130.433 183.783 155.01 184.924 164.138C184.924 164.138 174.502 165.894 166.646 165.995C166.224 166.001 165.81 166.002 165.405 165.997C157.535 165.913 147.005 164.138 147.005 164.138C148.146 155.01 156.222 130.433 124.096 125.166C103.257 121.75 67.6571 118.232 51.4188 118.32C52.2088 111.474 52.3614 97.5723 53.7887 84.3513C56.1859 62.1472 60.2841 42.3073 63.5317 28C63.7789 28.0278 64.0265 28.0558 64.2744 28.0841C93.783 31.4519 128.535 38.3492 169.914 38.0063C173.723 37.9747 177.475 37.8845 181.169 37.7444Z"
                    fill="white"
                  />
                  <path
                    d="M64.2744 28.0841C91.0457 31.4196 123.618 37.7726 161.967 38.0904C203.694 38.4362 238.73 31.3354 268.398 28C271.645 42.3073 275.743 62.1472 278.14 84.3513C279.568 97.5723 279.72 111.474 280.51 118.32C264.272 118.232 228.672 121.75 207.833 125.166C175.708 130.433 183.783 155.01 184.924 164.138C184.924 164.138 172.595 166.216 164.648 165.982M267.608 28.0895C240.836 31.425 208.264 37.6885 169.914 38.0063C128.187 38.3521 93.1995 31.3354 63.5317 28C60.2841 42.3073 56.1859 62.1472 53.7887 84.3513C52.3614 97.5723 52.2088 111.474 51.4188 118.32C67.6571 118.232 103.257 121.75 124.096 125.166C156.222 130.433 148.146 155.01 147.005 164.138C147.005 164.138 159.334 166.216 167.281 165.982"
                    stroke="black"
                    stroke-width="0.526647"
                  />
                </g>
                <path
                  id="Vector 312"
                  d="M147.269 141.493C148.585 141.229 158.319 139.684 165.438 139.913C173.143 140.16 182.115 140.966 184.134 141.493"
                  stroke="black"
                  stroke-width="0.526647"
                />
                <path
                  id="Vector 314"
                  d="M63.2684 31.9498C100.66 35.8119 136.944 41.4295 166.755 41.4295C198.88 41.4295 269.187 31.6865 269.187 31.6865"
                  stroke="black"
                  stroke-width="0.526647"
                  stroke-dasharray="1.05 1.05"
                />
              </g>
              <g id="Group 187">
                <path
                  id="Vector 310"
                  d="M137.789 154.132C138.667 153.517 140.949 152.183 143.055 151.762C145.162 151.341 147.971 151.235 148.849 151.235"
                  stroke="black"
                  stroke-width="0.526647"
                />
                <path
                  id="Vector 315"
                  d="M65.375 118.847C71.8703 121.743 82.8597 129.116 89.6008 134.383C98.0271 140.966 103.557 142.546 115.143 144.916C126.729 147.286 133.89 149.578 139.369 152.815C145.162 156.239 145.776 159.047 147.532 161.505"
                  stroke="black"
                  stroke-width="0.526647"
                  stroke-dasharray="1.05 1.05"
                />
                <path
                  id="Vector 317"
                  d="M51.6821 115.687C104.874 117.266 132.523 121.48 143.845 130.169C154.458 138.314 149.639 155.8 149.375 163.875"
                  stroke="black"
                  stroke-width="0.526647"
                  stroke-dasharray="1.05 1.05"
                />
                <g id="Group 186">
                  <path
                    id="Vector 313"
                    d="M147.005 139.386C148.322 139.126 158.319 137.603 165.438 137.828C173.143 138.072 183.168 139.13 185.187 139.649"
                    stroke="black"
                    stroke-width="0.526647"
                    stroke-dasharray="1.05 1.05"
                  />
                  <g id="Group 185">
                    <g id="Group 184">
                      <path
                        id="Vector 311"
                        d="M194.403 154.395C193.526 153.781 190.98 152.183 188.874 151.762C186.767 151.341 183.958 151.235 183.08 151.235"
                        stroke="black"
                        stroke-width="0.526647"
                      />
                      <path
                        id="Vector 316"
                        d="M267.344 118.847C260.849 121.743 249.859 129.116 243.118 134.383C234.692 140.966 229.162 142.546 217.576 144.916C205.99 147.286 198.829 149.578 193.35 152.815C187.557 156.239 186.943 159.047 185.187 161.505"
                        stroke="black"
                        stroke-width="0.526647"
                        stroke-dasharray="1.05 1.05"
                      />
                    </g>
                    <path
                      id="Vector 318"
                      d="M280.512 115.687C227.32 117.266 199.671 121.48 188.348 130.169C177.736 138.314 182.555 155.8 182.819 163.875"
                      stroke="black"
                      stroke-width="0.526647"
                      stroke-dasharray="1.05 1.05"
                    />
                  </g>
                </g>
              </g>
            </g>
            {findMeasure('product_waistband_width') && (
              <g id="product_waistband_width">
                <path
                  id="Vector 21"
                  d="M266.817 42.5771L64.5849 42.5771"
                  stroke="#E55959"
                  stroke-width="2.63324"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M67.2854 38.5332L62.4784 42.659L66.6974 46.9577"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
                <path
                  id="Vector 27"
                  d="M264.691 46.974L269.056 42.3833L264.424 38.5332"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 21_2"
                  d="M269.714 59.9561L61.4251 59.9561"
                  stroke="#E55959"
                  stroke-width="2.63324"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M64.1255 55.9121L59.3185 60.0379L63.5374 64.3366"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
                <path
                  id="Vector 27_2"
                  d="M267.324 64.3529L271.69 59.7622L267.058 55.9121"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
              </g>
            )}

            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 21_3"
                  d="M275.507 96.8223L56.6853 96.8223"
                  stroke="#E55959"
                  stroke-width="2.63324"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M59.3858 92.7773L54.5787 96.9031L58.7977 101.202"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
                <path
                  id="Vector 27_3"
                  d="M272.591 101.218L276.956 96.6274L272.324 92.7773"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
              </g>
            )}

            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 21_4"
                  d="M165.717 161.768L165.717 41.4131"
                  stroke="#E55959"
                  stroke-width="2.63324"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M169.761 44.393L165.636 39.5859L161.337 43.8049"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
                <path
                  id="Vector 27_4"
                  d="M161.604 159.106L165.865 163.794L170.042 159.454"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
              </g>
            )}

            {findMeasure('product_thigh_circumference') && (
              <g id="product_thigh_circumference">
                <path
                  id="Vector 21_5"
                  d="M147.325 140.861L58.2968 114.533"
                  stroke="#E55959"
                  stroke-width="2.63324"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_5"
                  d="M62.208 111.381L56.5091 114.147L59.4949 119.378"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
                <path
                  id="Vector 27_5"
                  d="M143.347 144.475L148.737 141.146L145.237 136.244"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
              </g>
            )}

            {findMeasure('product_thigh_width') && (
              <g id="product_thigh_width">
                <path
                  id="Vector 21_6"
                  d="M147.325 140.861L58.2968 114.533"
                  stroke="#E55959"
                  stroke-width="2.63324"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_6"
                  d="M62.208 111.381L56.5091 114.147L59.4949 119.378"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
                <path
                  id="Vector 27_6"
                  d="M143.347 144.475L148.737 141.146L145.237 136.244"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
              </g>
            )}

            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 21_7"
                  d="M166.77 161.242L166.77 40.8867"
                  stroke="#E55959"
                  stroke-width="2.63324"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_7"
                  d="M170.815 43.8666L166.689 39.0596L162.39 43.2785"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
                <path
                  id="Vector 27_7"
                  d="M162.658 158.579L166.918 163.266L171.095 158.927"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
              </g>
            )}

            {findMeasure('product_waistband_circumference') && (
              <g id="product_waistband_circumference">
                <path
                  id="Vector 21_8"
                  d="M266.817 42.5771L64.5849 42.5771"
                  stroke="#E55959"
                  stroke-width="2.63324"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_8"
                  d="M67.2854 38.5332L62.4784 42.659L66.6974 46.9577"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
                <path
                  id="Vector 27_8"
                  d="M264.691 46.974L269.056 42.3833L264.424 38.5332"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
              </g>
            )}

            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 21_9"
                  d="M269.714 59.9561L61.4251 59.9561"
                  stroke="#E55959"
                  stroke-width="2.63324"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_9"
                  d="M64.1255 55.9121L59.3185 60.0379L63.5374 64.3366"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
                <path
                  id="Vector 27_9"
                  d="M267.324 64.3529L271.69 59.7622L267.058 55.9121"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
              </g>
            )}

            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 21_10"
                  d="M275.507 96.8223L56.6853 96.8223"
                  stroke="#E55959"
                  stroke-width="2.63324"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_10"
                  d="M59.3858 92.7773L54.5787 96.9031L58.7977 101.202"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
                <path
                  id="Vector 27_10"
                  d="M272.591 101.218L276.956 96.6274L272.324 92.7773"
                  stroke="#E55959"
                  stroke-width="2.10659"
                />
              </g>
            )}
          </g>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_panties">
            <g id="panties">
              <g id="Group 188">
                <path
                  id="Vector 308"
                  d="M56.2596 176.864C46.5052 170.861 38.6475 170.402 35.9379 170.923C50.4237 161.544 80.1455 142.473 83.1468 141.223C86.8985 139.659 131.294 164.358 133.482 165.609C135.233 166.609 153.387 184.784 162.245 193.746L148.802 225.01C146.092 223.343 145.517 217.236 137.859 213.755C130.981 210.629 107.22 206.252 96.5904 202.5C85.9606 198.749 68.4527 184.367 56.2596 176.864Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.625283"
                />
                <path
                  id="Vector 309"
                  d="M286.677 176.864C296.431 170.861 304.289 170.402 306.998 170.923C292.513 161.544 262.791 142.473 259.789 141.223C256.038 139.659 211.643 164.358 209.454 165.609C207.703 166.609 189.549 184.784 180.691 193.746L193.509 225.323C196.219 223.656 197.419 217.236 205.077 213.755C211.955 210.629 235.716 206.252 246.346 202.5C256.976 198.749 274.484 184.367 286.677 176.864Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.625283"
                />
                <g id="Vector">
                  <path
                    d="M189.052 75.5694C228.308 73.8669 262.212 67.4739 291.677 64.1066C291.991 64.0707 292.304 64.0352 292.617 64C296.473 80.9869 301.338 104.543 304.185 130.905C305.879 146.603 306.06 163.107 306.998 171.236C287.719 171.132 245.451 175.309 220.709 179.365C182.567 185.618 192.155 214.798 193.509 225.636C193.509 225.636 181.12 227.723 171.79 227.841C171.296 227.847 170.81 227.848 170.335 227.842C160.991 227.742 148.489 225.636 148.489 225.636C149.844 214.798 159.431 185.618 121.289 179.365C96.5468 175.309 54.2796 171.132 35 171.236C35.9379 163.107 36.1191 146.603 37.8138 130.905C40.6599 104.543 45.5256 80.9869 49.3815 64C49.675 64.033 49.9689 64.0663 50.2633 64.0999C85.2986 68.0983 126.559 76.2875 175.689 75.8804C180.211 75.8429 184.665 75.7358 189.052 75.5694Z"
                    fill="white"
                  />
                  <path
                    d="M50.2633 64.0999C82.0486 68.06 120.721 75.603 166.253 75.9803C215.795 76.3908 257.392 67.9601 292.617 64C296.473 80.9869 301.338 104.543 304.185 130.905C305.879 146.603 306.06 163.107 306.998 171.236C287.719 171.132 245.451 175.309 220.709 179.365C182.567 185.618 192.155 214.798 193.509 225.636C193.509 225.636 178.872 228.102 169.436 227.824M291.679 64.1063C259.894 68.0664 221.221 75.5031 175.689 75.8804C126.147 76.2909 84.6058 67.9601 49.3815 64C45.5256 80.9869 40.6599 104.543 37.8138 130.905C36.1191 146.603 35.9379 163.107 35 171.236C54.2796 171.132 96.5468 175.309 121.289 179.365C159.431 185.618 149.844 214.798 148.489 225.636C148.489 225.636 163.126 228.102 172.562 227.824"
                    stroke="black"
                    stroke-width="0.625283"
                  />
                </g>
                <path
                  id="Vector 312"
                  d="M148.802 198.749C150.365 198.436 161.922 196.602 170.374 196.873C179.522 197.167 190.174 198.124 192.571 198.749"
                  stroke="black"
                  stroke-width="0.625283"
                />
                <path
                  id="Vector 314"
                  d="M49.0689 68.6896C93.464 73.275 136.544 79.9447 171.937 79.9447C210.079 79.9447 293.555 68.377 293.555 68.377"
                  stroke="black"
                  stroke-width="0.625283"
                  stroke-dasharray="1.25 1.25"
                />
              </g>
              <g id="Group 187">
                <path
                  id="Vector 310"
                  d="M137.547 213.754C138.589 213.025 141.298 211.441 143.799 210.941C146.3 210.44 149.635 210.315 150.677 210.315"
                  stroke="black"
                  stroke-width="0.625283"
                />
                <path
                  id="Vector 315"
                  d="M51.57 171.861C59.2819 175.3 72.3294 184.054 80.3331 190.307C90.3376 198.123 96.9031 199.999 110.659 202.813C124.416 205.627 132.918 208.348 139.422 212.192C146.3 216.256 147.03 219.591 149.114 222.509"
                  stroke="black"
                  stroke-width="0.625283"
                  stroke-dasharray="1.25 1.25"
                />
                <path
                  id="Vector 317"
                  d="M35.3127 168.109C98.4663 169.985 131.294 174.987 144.737 185.305C157.338 194.975 151.615 215.735 151.303 225.323"
                  stroke="black"
                  stroke-width="0.625283"
                  stroke-dasharray="1.25 1.25"
                />
                <g id="Group 186">
                  <path
                    id="Vector 313"
                    d="M148.489 196.247C150.052 195.939 161.922 194.131 170.374 194.398C179.522 194.687 191.425 195.943 193.822 196.56"
                    stroke="black"
                    stroke-width="0.625283"
                    stroke-dasharray="1.25 1.25"
                  />
                  <g id="Group 185">
                    <g id="Group 184">
                      <path
                        id="Vector 311"
                        d="M204.765 214.068C203.722 213.339 200.7 211.442 198.199 210.942C195.698 210.441 192.363 210.316 191.321 210.316"
                        stroke="black"
                        stroke-width="0.625283"
                      />
                      <path
                        id="Vector 316"
                        d="M291.366 171.861C283.654 175.3 270.607 184.054 262.603 190.307C252.599 198.123 246.033 199.999 232.277 202.813C218.521 205.627 210.019 208.348 203.514 212.192C196.636 216.256 195.906 219.591 193.822 222.509"
                        stroke="black"
                        stroke-width="0.625283"
                        stroke-dasharray="1.25 1.25"
                      />
                    </g>
                    <path
                      id="Vector 318"
                      d="M307 168.109C243.846 169.985 211.019 174.987 197.575 185.305C184.975 194.975 190.697 215.735 191.01 225.323"
                      stroke="black"
                      stroke-width="0.625283"
                      stroke-dasharray="1.25 1.25"
                    />
                  </g>
                </g>
              </g>
            </g>
            {findMeasure('product_waistband_width') && (
              <g id="product_waistband_width">
                <path
                  id="Vector 21"
                  d="M290.741 81.3076L50.6321 81.3076"
                  stroke="#E55959"
                  stroke-width="3.12642"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M53.8383 76.5059L48.131 81.4044L53.1401 86.5082"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
                <path
                  id="Vector 27"
                  d="M288.216 86.5275L293.399 81.077L287.9 76.5059"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 21_2"
                  d="M294.18 101.941L46.8804 101.941"
                  stroke="#E55959"
                  stroke-width="3.12642"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M50.0866 97.1406L44.3793 102.039L49.3884 107.143"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
                <path
                  id="Vector 27_2"
                  d="M291.343 107.161L296.526 101.711L291.026 97.1396"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 21_3"
                  d="M301.058 145.712L41.2528 145.712"
                  stroke="#E55959"
                  stroke-width="3.12642"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M44.459 140.91L38.7517 145.809L43.7608 150.912"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
                <path
                  id="Vector 27_3"
                  d="M297.596 150.932L302.778 145.481L297.279 140.91"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 21_4"
                  d="M170.705 222.822L170.705 79.9258"
                  stroke="#E55959"
                  stroke-width="3.12642"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M175.507 83.4632L170.608 77.7559L165.505 82.765"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
                <path
                  id="Vector 27_4"
                  d="M165.822 219.661L170.881 225.227L175.84 220.074"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
              </g>
            )}
            {findMeasure('product_thigh_circumference') && (
              <g id="product_thigh_circumference">
                <path
                  id="Vector 21_5"
                  d="M148.869 197.999L43.1661 166.74"
                  stroke="#E55959"
                  stroke-width="3.12642"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_5"
                  d="M47.81 162.998L41.0437 166.282L44.5887 172.493"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
                <path
                  id="Vector 27_5"
                  d="M144.145 202.29L150.545 198.338L146.39 192.518"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
              </g>
            )}
            {findMeasure('product_thigh_width') && (
              <g id="product_thigh_width">
                <path
                  id="Vector 21_6"
                  d="M148.869 197.999L43.1661 166.74"
                  stroke="#E55959"
                  stroke-width="3.12642"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_6"
                  d="M47.81 162.998L41.0437 166.282L44.5887 172.493"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
                <path
                  id="Vector 27_6"
                  d="M144.145 202.29L150.545 198.338L146.39 192.518"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 21_7"
                  d="M171.956 222.196L171.956 79.2998"
                  stroke="#E55959"
                  stroke-width="3.12642"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_7"
                  d="M176.758 82.8382L171.859 77.1309L166.755 82.14"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
                <path
                  id="Vector 27_7"
                  d="M167.073 219.035L172.132 224.601L177.091 219.448"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
              </g>
            )}
            {findMeasure('product_waistband_circumference') && (
              <g id="product_waistband_circumference">
                <path
                  id="Vector 21_8"
                  d="M290.741 81.3076L50.6321 81.3076"
                  stroke="#E55959"
                  stroke-width="3.12642"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_8"
                  d="M53.8383 76.5059L48.131 81.4044L53.1401 86.5082"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
                <path
                  id="Vector 27_8"
                  d="M288.216 86.5275L293.399 81.077L287.9 76.5059"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 21_9"
                  d="M294.18 101.941L46.8804 101.941"
                  stroke="#E55959"
                  stroke-width="3.12642"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_9"
                  d="M50.0866 97.1406L44.3793 102.039L49.3884 107.143"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
                <path
                  id="Vector 27_9"
                  d="M291.343 107.161L296.526 101.711L291.026 97.1396"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 21_10"
                  d="M301.058 145.712L41.2528 145.712"
                  stroke="#E55959"
                  stroke-width="3.12642"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_10"
                  d="M44.459 140.91L38.7517 145.809L43.7608 150.912"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
                <path
                  id="Vector 27_10"
                  d="M297.596 150.932L302.778 145.481L297.279 140.91"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
              </g>
            )}
            {findMeasure('product_high_waist_circumference') && (
              <g id="product_high_waist_circumference">
                <path
                  id="Vector 21_11"
                  d="M294.18 101.941L46.8804 101.941"
                  stroke="#E55959"
                  stroke-width="3.12642"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_11"
                  d="M50.0866 97.1406L44.3793 102.039L49.3884 107.143"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
                <path
                  id="Vector 27_11"
                  d="M291.343 107.161L296.526 101.711L291.026 97.1396"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
              </g>
            )}
            {findMeasure('product_high_waist_width') && (
              <g id="product_high_waist_width">
                <path
                  id="Vector 21_12"
                  d="M294.18 101.941L46.8804 101.941"
                  stroke="#E55959"
                  stroke-width="3.12642"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_12"
                  d="M50.0866 97.1406L44.3793 102.039L49.3884 107.143"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
                <path
                  id="Vector 27_12"
                  d="M291.343 107.161L296.526 101.711L291.026 97.1396"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_circumference') && (
              <g id="product_lower_waist_circumference">
                <path
                  id="Vector 21_13"
                  d="M294.18 101.941L46.8804 101.941"
                  stroke="#E55959"
                  stroke-width="3.12642"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_13"
                  d="M50.0866 97.1406L44.3793 102.039L49.3884 107.143"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
                <path
                  id="Vector 27_13"
                  d="M291.343 107.161L296.526 101.711L291.026 97.1396"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_width') && (
              <g id="product_lower_waist_width">
                <path
                  id="Vector 21_14"
                  d="M294.18 101.941L46.8804 101.941"
                  stroke="#E55959"
                  stroke-width="3.12642"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_14"
                  d="M50.0866 97.1406L44.3793 102.039L49.3884 107.143"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
                <path
                  id="Vector 27_14"
                  d="M291.343 107.161L296.526 101.711L291.026 97.1396"
                  stroke="#E55959"
                  stroke-width="2.50113"
                />
              </g>
            )}
          </g>
        </svg>
      )}
    </div>
  );
}
