import { useDevice } from '@/hooks/use-device';
import { BodyMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface SleeveProps {
  measure: BodyMeasure;
  className?: string;
}

export function Sleeve({ measure, className }: SleeveProps) {
  const { measures } = measure;
  const mappedMeasures = measures.map((item) => item.measure);
  const { isMobile } = useDevice();

  const findMeasure = (measure: string) => {
    const foundMeasure = mappedMeasures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full flex justify-center', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_sleeve">
            <mask id="mask0_128_2482" maskUnits="userSpaceOnUse" x="0" y="0" width="331" height="193">
              <rect id="rect" width="331" height="193" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_2482)">
              <g id="group_sleeve_mask">
                <g id="child">
                  <g id="Group 38">
                    <g id="Group 37">
                      <path
                        id="Vector"
                        d="M150.52 72.5199C152.157 67.6083 152.306 60.6875 152.306 60.6875H153.199H172.845H173.738C173.738 60.6875 173.887 67.6083 175.524 72.5199C176.715 76.0919 181.552 76.7616 189.143 79.2174C196.562 81.6177 201.421 80.7802 204.547 89.2637C207.672 97.7473 216.746 126.697 218.086 132.502C219.158 137.145 218.752 137.7 218.752 137.7L218.353 141.166L218.086 143.699L216.486 145.565L213.554 147.565L209.155 148.764L206.622 149.031C203.869 141.738 197.537 124.761 197.18 122.975C196.822 121.189 195.096 114.342 194.277 111.142L192.938 114.491V152.667C193.384 155.272 194.143 161.999 193.608 168.072C193.072 174.144 193.384 176.704 193.608 177.225C194.649 181.169 196.733 190.263 196.733 195.085V207.364C198.296 213.094 201.421 226.028 201.421 231.922C201.421 239.289 201.198 253.354 200.752 259.605C200.394 264.606 199.561 270.619 199.189 273H173.515C171.952 262.433 168.693 240.048 168.157 235.047C167.487 228.796 167.71 225.894 167.041 224.554C166.371 223.215 165.255 221.875 163.915 221.875H162.129C160.79 221.875 159.673 223.215 159.004 224.554C158.334 225.894 158.557 228.796 157.887 235.047C157.351 240.048 154.092 262.433 152.529 273H126.855C126.483 270.619 125.65 264.606 125.292 259.605C124.846 253.354 124.623 239.289 124.623 231.922C124.623 226.028 127.748 213.094 129.311 207.364V195.085C129.311 190.263 131.395 181.169 132.437 177.225C132.66 176.704 132.972 174.144 132.437 168.072C131.901 161.999 132.66 155.272 133.106 152.667V114.491L131.767 111.142C130.948 114.342 129.222 121.189 128.864 122.975C128.507 124.761 122.911 139.793 120.158 147.086C119.339 152.891 117.211 166.241 115.246 173.206C112.79 181.913 109.218 187.271 108.325 191.29C107.611 194.505 106.688 200.964 106.316 203.792C106.614 204.015 107.611 204.73 109.218 205.801C111.228 207.141 114.576 214.285 117.032 218.303C119.488 222.322 119.488 223.215 118.148 224.554C116.809 225.894 115.916 223.215 113.683 220.313C111.897 217.991 109.962 216.517 109.218 216.071C107.879 218.08 105.2 222.813 105.2 225.671C105.2 229.243 106.539 231.922 109.888 235.047C113.237 238.173 111.897 237.95 111.674 239.289C111.495 240.361 109.962 239.289 109.218 238.619C109.665 239.289 110.424 240.941 109.888 242.191C109.218 243.754 103.637 238.173 100.288 233.931C97.6092 230.538 96.2976 226.861 96.6697 226.117C96.5208 225.373 95.9534 225.224 96.2232 223.215C96.4679 221.392 95.8511 212.573 96.2232 208.257C96.1488 201.187 96.8055 183.342 98.9488 168.518C101.092 153.694 104.009 146.119 105.2 144.184C105.795 141.43 107.254 134.762 108.325 130.119C109.665 124.314 118.372 97.7473 121.497 89.2637C124.623 80.7802 129.482 81.6177 136.902 79.2174C144.492 76.7616 149.329 76.0919 150.52 72.5199Z"
                        fill="white"
                      />
                      <g id="Group 36">
                        <g id="Group 35">
                          <path
                            id="Vector 91"
                            d="M201.023 132.502C198.402 132.235 193.079 131.675 192.759 131.569L187.96 136.368C188.005 137.834 188.067 140.926 187.96 141.566C187.853 142.206 191.115 145.743 192.759 147.432L206.622 149.164C207.066 147.654 207.982 144.526 208.088 144.099C208.195 143.672 203.423 136.19 201.023 132.502Z"
                            fill="white"
                          />
                          <path
                            id="Vector 21"
                            d="M164.761 71.4034C171.288 68.635 174.584 62.6966 175.701 60.9106V59.3478C173.022 57.5618 167.574 53.9897 167.217 53.9897C166.86 53.9897 155.608 57.1153 150.027 58.678L152 65.5L151.5 70C155.965 72.9023 160.618 73.1608 164.761 71.4034Z"
                            fill="url(#paint0_linear_128_2482)"
                          />
                          <g id="Group 34">
                            <path
                              id="Vector 20"
                              d="M114.753 148.648C116.5 149.315 120.336 147.451 120.781 145.746L119.664 150.211C119.804 152.151 118.771 155.792 118.771 155.792C118.771 155.792 117.718 161.611 117.432 163.829L114.083 176.555C113.336 178.855 109.868 186.636 110.065 186.601C110.065 186.601 108.38 190.567 108.055 192.183L106.949 199.327L106.269 202.452C106.583 203.074 106.214 203.253 106.269 203.568C106.339 203.962 110.233 206.694 110.233 206.694C110.05 206.956 112.52 210.489 112.52 210.489L114.976 214.731L118.548 221.205L119.218 223.215L117.209 224.777L116.092 223.661L112.967 219.419L109.397 216.07C109.397 216.07 106.641 221.004 106.269 221.205L104.93 225.67L105.6 228.708L108.017 233.261L112.057 237.726L111.708 239.512L110.896 239.512L109.397 238.619C109.453 238.934 110.105 240.575 110.233 241.298L109.79 242.637L108.017 241.498L104.93 239.065L101.894 235.672L99.3485 232.368L97.7858 229.242L97.116 227.837L96.4463 224.554L95.9998 219.866L96.4463 215.177L96.4463 200.443L97.7858 177.894L99.3485 166.732C99.5917 162.353 102.474 151.327 102.474 151.327C102.474 151.327 103.814 147.086 105.376 143.514C105.504 141.933 106.601 135.719 106.716 136.37L108.055 131.458C108.725 137.932 108.279 137.709 108.948 141.42C109.665 145.393 112.569 147.816 114.753 148.648Z"
                              fill="url(#paint1_linear_128_2482)"
                            />
                            <path
                              id="Vector 18"
                              d="M152.929 216.741C155.962 220.103 158.734 225.224 158.734 225.224L157.841 236.61L155.608 252.461L153.376 267.195L152.706 272.777H127.199L125.408 261.199L125.023 250.898L124.799 244.201V234.154V228.126L129.488 207.364V192.629L148.911 212.499C148.911 212.499 148.453 211.779 152.929 216.741Z"
                              fill="url(#paint2_linear_128_2482)"
                            />
                          </g>
                        </g>
                        <g id="Group 33">
                          <path
                            id="Vector 11"
                            d="M163.555 176.893C163.619 176.787 163.584 176.65 163.478 176.587C163.372 176.523 163.235 176.558 163.172 176.664L163.555 176.893ZM163.172 176.664C163.113 176.763 163.093 176.875 163.091 176.979C163.089 177.084 163.104 177.195 163.129 177.306C163.178 177.528 163.27 177.776 163.372 178.015C163.475 178.255 163.592 178.496 163.695 178.703C163.8 178.915 163.886 179.085 163.935 179.197L164.345 179.019C164.29 178.893 164.195 178.706 164.095 178.505C163.992 178.298 163.88 178.067 163.782 177.839C163.684 177.609 163.605 177.391 163.565 177.21C163.545 177.12 163.536 177.045 163.537 176.988C163.539 176.929 163.55 176.902 163.555 176.893L163.172 176.664ZM163.935 179.197C164.024 179.401 164.085 179.709 164.091 180.035C164.097 180.362 164.048 180.678 163.938 180.906L164.341 181.099C164.491 180.786 164.545 180.393 164.538 180.027C164.531 179.66 164.463 179.291 164.345 179.019L163.935 179.197ZM163.938 180.906C163.907 180.972 163.864 181 163.803 181.013C163.73 181.028 163.632 181.02 163.517 180.99C163.406 180.961 163.299 180.916 163.217 180.877C163.177 180.858 163.144 180.841 163.122 180.829C163.111 180.823 163.102 180.818 163.097 180.815C163.094 180.813 163.092 180.812 163.091 180.812C163.09 180.811 163.09 180.811 163.09 180.811C163.09 180.811 163.09 180.811 163.09 180.811C163.09 180.811 163.09 180.811 163.09 180.811C163.09 180.811 163.09 180.811 163.09 180.811C163.09 180.811 163.09 180.811 162.976 181.003C162.861 181.194 162.861 181.194 162.861 181.194C162.861 181.194 162.861 181.194 162.861 181.194C162.861 181.194 162.861 181.195 162.862 181.195C162.862 181.195 162.862 181.195 162.863 181.195C162.863 181.196 162.864 181.196 162.865 181.197C162.868 181.198 162.871 181.2 162.875 181.202C162.883 181.207 162.894 181.213 162.908 181.221C162.937 181.236 162.977 181.257 163.025 181.28C163.121 181.326 163.257 181.383 163.404 181.422C163.549 181.46 163.725 181.485 163.895 181.45C164.076 181.412 164.243 181.304 164.341 181.099L163.938 180.906Z"
                            fill="black"
                          />
                          <path
                            id="Vector 17"
                            d="M161.775 115.161C161.775 115.161 161.55 116.59 161.546 117.689C161.542 118.65 161.593 119.722 161.593 119.722"
                            stroke="black"
                            stroke-width="0.446504"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <g id="Group 32">
                            <path
                              id="Vector 15"
                              d="M112.967 143.737C112.967 143.737 113.19 145.746 113.86 146.639C114.53 147.532 115.423 147.756 115.423 147.756"
                              stroke="black"
                              stroke-width="0.446504"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                          <g id="Group 27">
                            <path
                              id="Vector 15_2"
                              d="M160.743 86.5847C160.743 86.5847 160.267 85.2286 158.957 84.4348C157.647 83.641 153.45 83.608 151.366 82.1196"
                              stroke="black"
                              stroke-width="0.446504"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              id="Vector 14"
                              d="M164.315 86.5847C164.315 86.5847 164.791 85.2286 166.101 84.4348C167.411 83.641 171.608 83.608 173.692 82.1196"
                              stroke="black"
                              stroke-width="0.446504"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                  </g>
                  <path
                    id="Vector 90"
                    d="M126.909 128.37C126.909 128.157 127.087 126.504 127.176 125.704L127.042 124.237C127.531 123.038 128.509 120.612 128.509 120.505C128.509 120.398 129.219 118.061 129.575 116.906C130.153 117.35 131.308 118.266 131.308 118.372C131.308 118.479 131.752 119.75 131.974 120.372L134.374 121.838L134.774 124.904C134.507 126.459 133.974 129.596 133.974 129.703C133.974 129.836 133.441 134.235 133.441 134.368C133.441 134.475 132.996 134.235 132.774 134.102C132.152 133.524 130.881 132.342 130.775 132.235C130.641 132.102 126.909 128.636 126.909 128.37Z"
                    fill="white"
                  />
                  <path
                    id="Vector_2"
                    d="M163.915 221.875H162.129C160.789 221.875 159.673 223.215 159.003 224.554C158.334 225.894 158.557 228.796 157.887 235.047C157.351 240.048 154.092 262.433 152.529 273H126.855C126.483 270.619 125.65 264.606 125.292 259.605C124.846 253.354 124.623 239.289 124.623 231.922C124.623 226.028 127.748 213.094 129.311 207.364V195.085C129.311 190.263 131.395 181.169 132.436 177.225C132.66 176.704 132.972 174.144 132.436 168.072C131.901 161.999 132.66 155.272 133.106 152.667V134.227M163.915 221.875C165.254 221.875 166.371 223.215 167.04 224.554C167.71 225.894 167.487 228.796 168.157 235.047C168.693 240.048 171.952 262.433 173.515 273H199.189C199.561 270.619 200.394 264.606 200.752 259.605C201.198 253.354 201.421 239.289 201.421 231.922C201.421 226.028 198.296 213.094 196.733 207.364V195.085C196.733 190.263 194.649 181.169 193.607 177.225C193.384 176.704 193.072 174.144 193.607 168.072C194.143 161.999 193.384 155.272 192.938 152.667V147.507M163.915 221.875H161.906M172.845 60.6875H152.306C152.306 60.6875 152.157 67.6083 150.52 72.5199C149.329 76.0919 144.492 76.7616 136.901 79.2174C129.482 81.6177 124.623 80.7802 121.497 89.2637C118.372 97.7473 109.665 124.314 108.325 130.119C107.254 134.762 105.795 141.43 105.2 144.184C104.009 146.119 101.092 153.694 98.9487 168.518C96.8054 183.342 96.4185 201.187 96.4929 208.257C96.1208 212.573 96.0911 221.381 96.223 223.215C96.3997 225.671 96.8928 227.457 96.8928 227.457C96.8928 227.457 97.6091 230.538 100.288 233.931C103.637 238.173 109.218 243.754 109.888 242.191C110.424 240.941 109.665 239.289 109.218 238.619C109.962 239.289 111.495 240.361 111.674 239.289C111.897 237.95 113.237 238.173 109.888 235.047C106.539 231.922 105.2 229.243 105.2 225.671C105.2 222.813 107.879 218.08 109.218 216.071C109.962 216.517 111.897 217.991 113.683 220.313C115.916 223.215 116.809 225.894 118.148 224.554C119.488 223.215 119.488 222.322 117.032 218.303C114.576 214.285 111.227 207.141 109.218 205.801C107.611 204.73 106.614 204.015 106.316 203.792C106.688 200.964 107.611 194.505 108.325 191.29C109.218 187.271 112.79 181.913 115.246 173.206C117.211 166.241 119.339 152.891 120.158 147.086C121.967 142.293 125.005 134.156 126.976 128.634M153.199 60.6875H173.738C173.738 60.6875 173.887 67.6083 175.524 72.5199C176.715 76.0919 181.552 76.7616 189.142 79.2174C196.562 81.6177 201.421 80.7802 204.547 89.2637C207.672 97.7473 216.213 124.831 217.553 130.636C218.624 135.28 218.752 137.834 218.752 137.834C218.841 139.834 218.379 144.339 215.82 146.365C213.26 148.392 208.71 149.076 206.755 149.165C203.33 148.805 198.027 148.196 192.938 147.507M133.313 121.172C132.741 120.826 132.196 120.497 131.767 120.239C131.658 119.883 131.441 119.092 131.441 118.772C131.441 118.503 130.929 117.75 130.248 117.165M133.313 121.172C134.902 122.133 136.694 123.226 136.773 123.305C136.906 123.438 140.639 124.771 141.705 124.638C142.771 124.504 146.504 124.771 147.837 123.704C149.17 122.638 149.436 122.771 149.036 121.172C148.637 119.572 141.972 114.373 142.371 112.374C142.691 110.774 143.749 110.286 144.238 110.241C144.638 110.73 145.624 111.841 146.37 112.374C147.304 113.04 154.368 118.906 154.902 119.306C155.435 119.705 161.7 127.97 162.633 128.237C163.566 128.503 171.697 130.636 179.695 130.636C186.094 130.636 195.958 131.702 200.09 132.236C200.09 132.236 197.537 124.761 197.179 122.975C196.822 121.189 195.096 114.342 194.277 111.142L192.938 114.491V131.439M133.313 121.172L133.106 114.491L131.767 111.142C131.382 112.646 130.797 114.955 130.248 117.165M126.976 128.634C128.405 130.291 131.881 133.052 132.374 133.702C132.981 134.502 134.907 134.635 135.173 135.035C135.387 135.355 140.328 137.034 142.771 137.834C147.348 138.412 157.221 139.487 160.1 139.167C163.699 138.767 174.23 143.433 179.695 145.166C181.987 145.892 187.333 146.748 192.938 147.507M126.976 128.634C126.646 128.252 126.426 127.928 126.376 127.703C126.162 126.744 126.02 125.615 125.976 125.171L123.576 121.972C123.71 121.394 124.269 120.239 125.442 120.239C126.615 120.239 127.709 121.038 128.108 121.438C127.709 120.15 126.935 117.519 127.042 117.306C127.175 117.039 128.038 116.106 129.175 116.506C129.546 116.637 129.917 116.881 130.248 117.165"
                    stroke="black"
                    stroke-width="0.446504"
                    stroke-linecap="round"
                  />
                  <g id="Group 31">
                    <g id="Group 2">
                      <path
                        id="Vector 4"
                        d="M139.09 36.6431C139.923 33.7359 141.519 36.0018 142.214 37.4982L143.255 41.1322L143.776 48.6141C143.689 48.6854 143.255 49.4692 142.214 48.8279C140.88 48.0066 138.048 40.2772 139.09 36.6431Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.446504"
                      />
                      <path
                        id="Vector 5"
                        d="M188.647 36.6431C187.814 33.7359 186.217 36.0018 185.523 37.4982L184.482 41.1322L183.961 48.6141C184.048 48.6854 184.482 49.4692 185.523 48.8279C186.857 48.0066 189.689 40.2772 188.647 36.6431Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.446504"
                      />
                    </g>
                    <g id="Vector_3">
                      <path
                        d="M144.962 17.9786C149.285 8.54072 161.614 8.02558 163.422 8.00113C165.23 8.02558 177.558 8.54072 181.882 17.9786C185.384 25.6234 187.387 41.763 181.882 55.7224C179.085 62.815 174.543 65.9176 171.429 67.436C168.315 68.9544 163.644 69.171 163.644 69.171C163.644 69.171 158.306 69.171 155.415 67.436C152.524 65.701 147.759 62.815 144.962 55.7224C139.457 41.763 141.459 25.6234 144.962 17.9786Z"
                        fill="white"
                      />
                      <path
                        d="M163.644 8.00036C163.644 8.00036 149.632 7.7834 144.962 17.9786C141.459 25.6234 139.457 41.763 144.962 55.7224C147.759 62.815 152.524 65.701 155.415 67.436C158.306 69.171 163.644 69.171 163.644 69.171C163.644 69.171 168.315 68.9544 171.429 67.436C174.543 65.9176 179.085 62.815 181.882 55.7224C187.387 41.763 185.384 25.6234 181.882 17.9786C177.211 7.7834 163.2 8.00036 163.2 8.00036"
                        stroke="black"
                        stroke-width="0.446504"
                        stroke-linecap="round"
                      />
                    </g>
                  </g>
                </g>
                {findMeasure('sleeve') && (
                  <g id="sleeve">
                    <g id="tÃ³rax (circunferÃªncia)">
                      <g id="Group 221">
                        <g id="Group 217">
                          <g id="Group 220">
                            <path
                              id="Ellipse 23"
                              d="M204.623 83.3142C204.623 83.3142 210.355 96.1109 216.753 118.505C221.051 133.55 224.518 143.225 218.219 148.231C207.822 156.496 186.034 147.86 163.166 144.784"
                              stroke="#E55959"
                              stroke-width="2"
                            />
                            <path
                              id="Vector 27"
                              d="M166.68 142.757L161.879 144.363L164.638 147.936"
                              stroke="#E55959"
                              stroke-width="1.72621"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                    <path
                      id="Vector 29"
                      d="M154.769 155.143L171.007 115.674"
                      stroke="#E55959"
                      stroke-width="0.533199"
                      stroke-linecap="square"
                      stroke-dasharray="2.67 2.67"
                    />
                  </g>
                )}
                {findMeasure('biceps') && (
                  <g id="biceps">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M196.169 119.75C196.169 119.75 207 118 213 114.5"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_2482"
              x1="158.734"
              y1="53.9897"
              x2="158.734"
              y2="80.3648"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_2482"
              x1="99.3876"
              y1="123.726"
              x2="105.38"
              y2="238.924"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_2482"
              x1="139.475"
              y1="235.88"
              x2="129.726"
              y2="292.387"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className=" h-full m-auto "
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_sleeve">
            <mask id="mask0_128_2527" maskUnits="userSpaceOnUse" x="0" y="0" width="342" height="291">
              <rect id="rect" width="342" height="291" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_2527)">
              <g id="group_sleeve_mask">
                <g id="child">
                  <g id="Group 38">
                    <g id="Group 37">
                      <path
                        id="Vector"
                        d="M149.744 118.571C152.043 111.676 152.252 101.961 152.252 101.961H153.505H181.084H182.338C182.338 101.961 182.547 111.676 184.845 118.571C186.516 123.585 193.306 124.526 203.962 127.973C214.377 131.343 221.199 130.167 225.586 142.076C229.974 153.985 242.711 194.624 244.592 202.772C246.096 209.291 245.527 210.07 245.527 210.07L244.966 214.935L244.592 218.49L242.346 221.11L238.23 223.917L232.055 225.601L228.499 225.975C224.634 215.738 215.745 191.906 215.244 189.398C214.743 186.891 212.319 177.28 211.17 172.788L209.29 177.489V231.08C209.916 234.736 210.982 244.18 210.23 252.704C209.478 261.229 209.916 264.822 210.23 265.553C211.692 271.09 214.617 283.856 214.617 290.625V307.862C216.811 315.906 221.199 334.062 221.199 342.335C221.199 352.677 220.885 372.421 220.258 381.196C219.757 388.216 218.587 396.657 218.065 400H182.024C179.83 385.166 175.255 353.743 174.503 346.723C173.563 337.948 173.876 333.873 172.936 331.993C171.996 330.113 170.429 328.232 168.548 328.232H166.041C164.161 328.232 162.594 330.113 161.653 331.993C160.713 333.873 161.027 337.948 160.087 346.723C159.334 353.743 154.759 385.166 152.565 400H116.525C116.002 396.657 114.832 388.216 114.331 381.196C113.704 372.421 113.391 352.677 113.391 342.335C113.391 334.062 117.778 315.906 119.972 307.862V290.625C119.972 283.856 122.897 271.09 124.359 265.553C124.673 264.822 125.112 261.229 124.359 252.704C123.607 244.18 124.673 234.736 125.3 231.08V177.489L123.419 172.788C122.27 177.28 119.847 186.891 119.345 189.398C118.844 191.906 110.988 213.007 107.123 223.245C105.974 231.393 102.986 250.134 100.228 259.912C96.7807 272.135 91.7664 279.656 90.5128 285.297C89.51 289.81 88.2146 298.878 87.6922 302.847C88.1101 303.161 89.5099 304.164 91.7664 305.668C94.5869 307.548 99.2879 317.577 102.735 323.218C106.183 328.859 106.183 330.113 104.302 331.993C102.422 333.873 101.168 330.113 98.0343 326.039C95.5271 322.779 92.811 320.711 91.7664 320.084C89.886 322.905 86.1253 329.549 86.1253 333.56C86.1253 338.574 88.0056 342.335 92.7066 346.723C97.4075 351.11 95.5271 350.797 95.2137 352.677C94.963 354.181 92.811 352.677 91.7664 351.737C92.3932 352.677 93.4587 354.996 92.7066 356.751C91.7664 358.945 83.9315 351.11 79.2306 345.156C75.4698 340.392 73.6286 335.232 74.1509 334.187C73.942 333.142 73.1455 332.933 73.5242 330.113C73.8677 327.554 73.0019 315.174 73.5242 309.115C73.4197 299.191 74.3416 274.14 77.3502 253.331C80.3588 232.522 84.4539 221.887 86.1253 219.171C86.961 215.306 89.0085 205.946 90.5128 199.427C92.3932 191.279 104.616 153.985 109.003 142.076C113.391 130.167 120.212 131.343 130.627 127.973C141.283 124.526 148.073 123.585 149.744 118.571Z"
                        fill="white"
                      />
                      <g id="Group 36">
                        <g id="Group 35">
                          <path
                            id="Vector 91"
                            d="M220.64 202.773C216.96 202.398 209.487 201.612 209.038 201.463L202.302 208.199C202.364 210.257 202.451 214.599 202.302 215.497C202.152 216.395 206.73 221.36 209.038 223.73L228.499 226.163C229.123 224.042 230.408 219.651 230.557 219.052C230.707 218.453 224.008 207.95 220.64 202.773Z"
                            fill="white"
                          />
                          <path
                            id="Vector 21"
                            d="M169.736 117.004C178.899 113.118 183.525 104.782 185.092 102.275V100.081C181.332 97.5736 173.685 92.5593 173.183 92.5593C172.682 92.5593 156.887 96.9469 149.052 99.1406L151.822 108.717L151.12 115.034C157.388 119.108 163.919 119.471 169.736 117.004Z"
                            fill="url(#paint0_linear_128_2527)"
                          />
                          <g id="Group 34">
                            <path
                              id="Vector 20"
                              d="M99.5353 225.439C101.988 226.374 107.372 223.757 107.997 221.364L106.43 227.632C106.626 230.355 105.176 235.467 105.176 235.467C105.176 235.467 103.698 243.635 103.296 246.749L98.5952 264.613C97.5468 267.842 92.6775 278.765 92.9541 278.716C92.9541 278.716 90.5897 284.283 90.1336 286.551L88.5806 296.579L87.6264 300.967C88.0668 301.84 87.548 302.091 87.6264 302.534C87.7243 303.087 93.191 306.921 93.191 306.921C92.9334 307.29 96.4015 312.249 96.4015 312.249L99.8488 318.203L104.863 327.292L105.803 330.112L102.983 332.306L101.416 330.739L97.0282 324.785L92.0166 320.084C92.0166 320.084 88.1481 327.009 87.6264 327.292L85.746 333.56L86.6862 337.824L90.0797 344.215L95.7505 350.483L95.2617 352.99L94.1205 352.99L92.0166 351.737C92.0949 352.179 93.0114 354.483 93.191 355.497L92.5683 357.378L90.0797 355.778L85.746 352.364L81.4847 347.6L77.9111 342.962L75.7174 338.574L74.7772 336.602L73.837 331.993L73.2102 325.412L73.837 318.83L73.837 298.146L75.7173 266.493L77.9111 250.824C78.2524 244.677 82.2986 229.199 82.2986 229.199C82.2986 229.199 84.179 223.245 86.3728 218.23C86.5517 216.011 88.0915 207.289 88.253 208.202L90.1335 201.307C91.0737 210.396 90.4468 210.082 91.387 215.291C92.3937 220.868 96.4694 224.269 99.5353 225.439Z"
                              fill="url(#paint1_linear_128_2527)"
                            />
                            <path
                              id="Vector 18"
                              d="M153.126 321.024C157.383 325.744 161.274 332.933 161.274 332.933L160.021 348.917L156.887 371.167L153.753 391.852L152.813 399.686H117.007L114.493 383.434L113.952 368.974L113.638 359.572V345.469V337.007L120.22 307.862V287.178L147.485 315.07C147.485 315.07 146.843 314.059 153.126 321.024Z"
                              fill="url(#paint2_linear_128_2527)"
                            />
                          </g>
                        </g>
                        <g id="Group 33">
                          <path
                            id="Vector 11"
                            d="M168.043 265.087C168.132 264.938 168.083 264.746 167.935 264.657C167.786 264.568 167.594 264.617 167.505 264.766L168.043 265.087ZM167.505 264.766C167.422 264.904 167.394 265.062 167.391 265.207C167.388 265.355 167.41 265.511 167.444 265.667C167.513 265.979 167.642 266.327 167.786 266.662C167.93 266.999 168.095 267.337 168.239 267.628C168.387 267.925 168.508 268.164 168.576 268.321L169.151 268.072C169.074 267.895 168.941 267.633 168.8 267.349C168.657 267.06 168.499 266.736 168.362 266.415C168.224 266.093 168.112 265.787 168.056 265.532C168.028 265.405 168.016 265.301 168.018 265.221C168.02 265.138 168.035 265.1 168.043 265.087L167.505 264.766ZM168.576 268.321C168.701 268.609 168.787 269.041 168.796 269.498C168.804 269.956 168.734 270.401 168.581 270.721L169.146 270.991C169.357 270.552 169.432 270 169.422 269.486C169.413 268.971 169.317 268.453 169.151 268.072L168.576 268.321ZM168.581 270.721C168.537 270.813 168.477 270.852 168.391 270.87C168.289 270.892 168.15 270.88 167.99 270.838C167.834 270.797 167.683 270.735 167.568 270.68C167.512 270.653 167.466 270.629 167.434 270.612C167.419 270.604 167.407 270.597 167.399 270.593C167.396 270.591 167.393 270.589 167.391 270.588C167.391 270.588 167.39 270.587 167.39 270.587C167.39 270.587 167.39 270.587 167.39 270.587C167.39 270.587 167.39 270.587 167.39 270.587C167.39 270.587 167.39 270.587 167.39 270.587C167.39 270.587 167.39 270.587 167.229 270.856C167.068 271.125 167.068 271.125 167.069 271.125C167.069 271.125 167.069 271.125 167.069 271.125C167.069 271.126 167.069 271.126 167.069 271.126C167.07 271.126 167.07 271.126 167.071 271.127C167.072 271.127 167.073 271.128 167.075 271.129C167.078 271.131 167.082 271.133 167.088 271.137C167.099 271.143 167.115 271.152 167.135 271.163C167.174 271.184 167.231 271.214 167.299 271.246C167.434 271.31 167.624 271.39 167.831 271.445C168.034 271.498 168.281 271.534 168.519 271.484C168.774 271.43 169.009 271.279 169.146 270.991L168.581 270.721Z"
                            fill="black"
                          />
                          <path
                            id="Vector 17"
                            d="M165.544 178.429C165.544 178.429 165.228 180.436 165.222 181.979C165.217 183.328 165.288 184.833 165.288 184.833"
                            stroke="black"
                            stroke-width="0.62679"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <g id="Group 32">
                            <path
                              id="Vector 15"
                              d="M97.0286 218.544C97.0286 218.544 97.342 221.364 98.2821 222.618C99.2223 223.872 100.476 224.185 100.476 224.185"
                              stroke="black"
                              stroke-width="0.62679"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                          <g id="Group 27">
                            <path
                              id="Vector 15_2"
                              d="M164.095 138.315C164.095 138.315 163.427 136.411 161.588 135.297C159.749 134.183 153.858 134.136 150.933 132.047"
                              stroke="black"
                              stroke-width="0.62679"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              id="Vector 14"
                              d="M169.11 138.315C169.11 138.315 169.778 136.411 171.617 135.297C173.455 134.183 179.347 134.136 182.272 132.047"
                              stroke="black"
                              stroke-width="0.62679"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                  </g>
                  <path
                    id="Vector 90"
                    d="M116.6 196.972C116.6 196.673 116.849 194.352 116.974 193.229L116.787 191.171C117.473 189.487 118.845 186.081 118.845 185.932C118.845 185.782 119.843 182.501 120.342 180.879C121.153 181.503 122.775 182.788 122.775 182.938C122.775 183.087 123.398 184.871 123.71 185.745L127.078 187.803L127.64 192.107C127.266 194.29 126.517 198.693 126.517 198.843C126.517 199.03 125.769 205.205 125.769 205.392C125.769 205.542 125.145 205.205 124.833 205.018C123.96 204.207 122.176 202.548 122.026 202.399C121.839 202.211 116.6 197.346 116.6 196.972Z"
                    fill="white"
                  />
                  <path
                    id="Vector_2"
                    d="M168.548 328.232H166.041C164.16 328.232 162.593 330.113 161.653 331.993C160.713 333.873 161.026 337.948 160.086 346.723C159.334 353.743 154.759 385.166 152.565 400H116.524C116.002 396.657 114.832 388.216 114.331 381.196C113.704 372.421 113.39 352.677 113.39 342.335C113.39 334.062 117.778 315.906 119.972 307.862V290.625C119.972 283.856 122.897 271.09 124.359 265.553C124.673 264.822 125.111 261.229 124.359 252.704C123.607 244.18 124.673 234.736 125.299 231.08V205.194M168.548 328.232C170.428 328.232 171.995 330.113 172.935 331.993C173.876 333.873 173.562 337.948 174.502 346.723C175.255 353.743 179.83 385.166 182.024 400H218.064C218.587 396.657 219.757 388.216 220.258 381.196C220.885 372.421 221.198 352.677 221.198 342.335C221.198 334.062 216.811 315.906 214.617 307.862V290.625C214.617 283.856 211.692 271.09 210.229 265.553C209.916 264.822 209.477 261.229 210.229 252.704C210.982 244.18 209.916 234.736 209.289 231.08V223.836M168.548 328.232H165.727M181.084 101.961H152.251C152.251 101.961 152.042 111.676 149.744 118.571C148.073 123.585 141.283 124.526 130.627 127.973C120.212 131.343 113.39 130.167 109.003 142.076C104.615 153.985 92.393 191.279 90.5126 199.427C89.0083 205.946 86.9608 215.306 86.1251 219.171C84.4536 221.887 80.3586 232.522 77.35 253.331C74.3414 274.14 73.7982 299.191 73.9027 309.115C73.3803 315.174 73.3386 327.538 73.5238 330.113C73.7718 333.56 74.464 336.067 74.464 336.067C74.464 336.067 75.4696 340.392 79.2304 345.156C83.9313 351.11 91.7662 358.945 92.7063 356.751C93.4585 354.996 92.3929 352.677 91.7662 351.737C92.8108 352.677 94.9628 354.181 95.2135 352.677C95.5269 350.797 97.4073 351.11 92.7063 346.723C88.0054 342.335 86.1251 338.574 86.1251 333.56C86.1251 329.549 89.8858 322.905 91.7662 320.084C92.8108 320.711 95.5269 322.779 98.0341 326.039C101.168 330.113 102.422 333.873 104.302 331.993C106.182 330.113 106.182 328.859 102.735 323.218C99.2876 317.577 94.5867 307.548 91.7662 305.668C89.5097 304.164 88.1099 303.161 87.692 302.847C88.2144 298.878 89.5097 289.81 90.5126 285.297C91.7662 279.656 96.7805 272.135 100.228 259.912C102.986 250.134 105.973 231.393 107.123 223.245C109.663 216.516 113.927 205.094 116.694 197.343M153.505 101.961H182.337C182.337 101.961 182.546 111.676 184.844 118.571C186.516 123.585 193.306 124.526 203.962 127.973C214.376 131.343 221.198 130.167 225.586 142.076C229.973 153.985 241.963 192.005 243.843 200.153C245.347 206.672 245.527 210.258 245.527 210.258C245.652 213.065 245.003 219.389 241.41 222.234C237.818 225.078 231.431 226.038 228.686 226.163C223.878 225.659 216.433 224.804 209.289 223.836M125.589 186.867C124.787 186.382 124.022 185.92 123.419 185.558C123.267 185.059 122.962 183.948 122.962 183.499C122.962 183.121 122.243 182.065 121.287 181.243M125.589 186.867C127.82 188.217 130.337 189.751 130.447 189.861C130.634 190.048 135.873 191.92 137.37 191.733C138.867 191.545 144.107 191.92 145.978 190.423C147.849 188.926 148.223 189.113 147.662 186.867C147.101 184.622 137.744 177.324 138.306 174.517C138.755 172.272 140.239 171.586 140.926 171.523C141.487 172.209 142.872 173.769 143.919 174.517C145.229 175.453 155.147 183.686 155.895 184.248C156.644 184.809 165.439 196.411 166.748 196.785C168.058 197.159 179.473 200.153 190.7 200.153C199.682 200.153 213.529 201.65 219.33 202.399C219.33 202.399 215.745 191.906 215.244 189.398C214.742 186.891 212.319 177.28 211.17 172.788L209.289 177.489V201.281M125.589 186.867L125.299 177.489L123.419 172.788C122.879 174.899 122.058 178.14 121.287 181.243M116.694 197.343C118.701 199.668 123.579 203.545 124.272 204.457C125.124 205.58 127.827 205.767 128.201 206.328C128.501 206.777 135.437 209.135 138.867 210.258C145.292 211.069 159.151 212.578 163.193 212.129C168.245 211.568 183.028 218.117 190.7 220.549C193.917 221.569 201.421 222.771 209.289 223.836M116.694 197.343C116.231 196.806 115.921 196.352 115.851 196.036C115.552 194.689 115.352 193.105 115.29 192.481L111.922 187.99C112.109 187.179 112.895 185.558 114.541 185.558C116.188 185.558 117.722 186.68 118.284 187.242C117.722 185.433 116.637 181.74 116.787 181.441C116.974 181.067 118.184 179.757 119.781 180.318C120.302 180.501 120.822 180.845 121.287 181.243"
                    stroke="black"
                    stroke-width="0.62679"
                    stroke-linecap="round"
                  />
                  <g id="Group 31">
                    <g id="Group 2">
                      <path
                        id="Vector 4"
                        d="M133.699 68.2085C134.868 64.1274 137.11 67.3083 138.084 69.4088L139.546 74.5102L140.277 85.0131C140.155 85.1131 139.546 86.2134 138.084 85.3132C136.212 84.1603 132.237 73.3099 133.699 68.2085Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.62679"
                      />
                      <path
                        id="Vector 5"
                        d="M203.266 68.2085C202.097 64.1274 199.855 67.3083 198.881 69.4088L197.419 74.5102L196.688 85.0131C196.81 85.1131 197.419 86.2134 198.881 85.3132C200.753 84.1603 204.728 73.3099 203.266 68.2085Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.62679"
                      />
                    </g>
                    <g id="Vector_3">
                      <path
                        d="M141.942 42.0077C148.011 28.759 165.318 28.0359 167.856 28.0016C170.393 28.0359 187.7 28.759 193.769 42.0077C198.686 52.7392 201.497 75.3955 193.769 94.9915C189.843 104.948 183.466 109.303 179.095 111.435C174.724 113.566 168.168 113.87 168.168 113.87C168.168 113.87 160.675 113.87 156.616 111.435C152.557 108.999 145.868 104.948 141.942 94.9915C134.214 75.3955 137.025 52.7392 141.942 42.0077Z"
                        fill="white"
                      />
                      <path
                        d="M168.168 28.0005C168.168 28.0005 148.498 27.6959 141.942 42.0077C137.025 52.7392 134.214 75.3955 141.942 94.9915C145.868 104.948 152.557 108.999 156.616 111.435C160.675 113.87 168.168 113.87 168.168 113.87C168.168 113.87 174.724 113.566 179.095 111.435C183.466 109.303 189.843 104.948 193.769 94.9915C201.497 75.3955 198.686 52.7392 193.769 42.0077C187.213 27.6959 167.543 28.0005 167.543 28.0005"
                        stroke="black"
                        stroke-width="0.62679"
                        stroke-linecap="round"
                      />
                    </g>
                  </g>
                </g>
                {findMeasure('sleeve') && (
                  <g id="sleeve">
                    <g id="tÃ³rax (circunferÃªncia)">
                      <g id="Group 221">
                        <g id="Group 217">
                          <g id="Group 220">
                            <path
                              id="Ellipse 23"
                              d="M225.692 133.724C225.692 133.724 233.739 151.688 242.72 183.124C248.754 204.244 253.62 217.825 244.779 224.853C230.183 236.455 199.598 224.332 167.497 220.013"
                              stroke="#E55959"
                              stroke-width="2.80755"
                            />
                            <path
                              id="Vector 27"
                              d="M172.43 217.169L165.69 219.422L169.563 224.439"
                              stroke="#E55959"
                              stroke-width="2.42321"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                    <path
                      id="Vector 29"
                      d="M155.708 234.555L178.504 179.15"
                      stroke="#E55959"
                      stroke-width="0.748491"
                      stroke-linecap="square"
                      stroke-dasharray="3.74 3.74"
                    />
                  </g>
                )}
                {findMeasure('biceps') && (
                  <g id="biceps">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M213.825 184.872C213.825 184.872 229.029 182.415 237.451 177.502"
                            stroke="#E55959"
                            stroke-width="2.80755"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_2527"
              x1="161.274"
              y1="92.5593"
              x2="161.274"
              y2="129.584"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_2527"
              x1="77.966"
              y1="190.453"
              x2="86.3777"
              y2="352.164"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_2527"
              x1="134.24"
              y1="347.892"
              x2="120.554"
              y2="427.215"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
