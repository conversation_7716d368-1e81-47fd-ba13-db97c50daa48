import { mappedLanguages } from '@/constants';

type response = {
  key: string;
  value: string;
};

export const getMappedLanguages = (language: string): response => {
  const languages = Object.entries(mappedLanguages);
  const selectedLanguage = languages.find(([key, value]) => key === language || value === language);

  return {
    key: selectedLanguage?.[0] ?? 'en',
    value: selectedLanguage?.[1] ?? 'en',
  };
};
