@import 'tailwindcss';

@theme {
  --breakpoint-xsm: calc(375px - 20px);
}

.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.custom-scroll {
  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #e7e7e7;
    border-radius: 100vh;
  }
}

.close-btn-shadow {
  -webkit-box-shadow: inset 20px 0px 20px 0px #00000033;
  -moz-box-shadow: inset 20px 0px 20px 0px #00000033;
  box-shadow: inset 20px 0px 20px 0px #00000033;
}

.close-btn-shadow-rtl {
  -webkit-box-shadow: inset -20px 0px 20px 0px #00000033;
  -moz-box-shadow: inset -20px 0px 20px 0px #00000033;
  box-shadow: inset -20px 0px 20px 0px #00000033;
}

.language-switcher-shadow {
  -webkit-box-shadow: 0px 0px 12px 4px #00000033;
  -moz-box-shadow: 0px 0px 12px 4px #00000033;
  box-shadow: 0px 0px 12px 4px #00000033;
}
