import type { Meta, StoryObj } from '@storybook/react';
import { Container } from './container';

const meta = {
  title: 'Atoms/Container',
  component: Container,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    as: {
      control: 'select',
      options: ['div', 'section', 'main', 'article', 'aside', 'header', 'footer', 'nav'],
    },
    maxWidth: {
      control: 'select',
      options: ['sm', 'md', 'lg', 'xl', '2xl', 'full'],
    },
  },
} satisfies Meta<typeof Container>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: <div className="bg-gray-200 p-4 text-center">Container Content</div>,
    maxWidth: 'lg',
  },
};

export const AsSection: Story = {
  args: {
    as: 'section',
    children: <div className="bg-gray-200 p-4 text-center">Section Container</div>,
    maxWidth: 'md',
  },
};

export const FullWidth: Story = {
  args: {
    maxWidth: 'full',
    children: <div className="bg-gray-200 p-4 text-center">Full Width Container</div>,
  },
};

export const CustomStyle: Story = {
  args: {
    children: <div className="bg-gray-200 p-4 text-center">Custom Styled Container</div>,
    className: 'bg-blue-100 p-8 rounded-lg shadow-md',
  },
};
