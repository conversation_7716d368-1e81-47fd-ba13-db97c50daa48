import type { Meta, StoryObj } from '@storybook/react';
import { MeasurementContent, MeasurementContentProps } from './measurement-content';
import placeholder from '@/assets/clothe-storie.svg';

const meta = {
  title: 'Molecules/Measurement-Content',
  component: MeasurementContent,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    image: { control: 'text' },
    value: { control: 'text' },
  },
} satisfies Meta<typeof MeasurementContent>;

export default meta;

type Story = StoryObj<typeof meta>;

const Template = ({ image, value }: MeasurementContentProps) => (
  <div className="w-[400px] h-72">
    <MeasurementContent image={image} value={value} />
  </div>
);
export const Default: Story = {
  args: {
    image: placeholder,
    value:
      'lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua',
  },

  render: (args) => <Template {...args} />,
};
