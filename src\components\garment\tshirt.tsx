import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface TshirtProps {
  measure: GarmentMeasure;
  className?: string;
}

export function Tshirt({ measure, className }: TshirtProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_tshirt">
            <g id="tshirt">
              <g id="Group 8">
                <path
                  id="Vector 2"
                  d="M163.163 31.4945C156.056 31.4945 151.261 29.138 150.509 27.3468L148.256 34.1071C148.776 37.1117 149.643 43.2942 149.643 43.9876C149.643 44.8543 160.737 49.8812 161.43 50.5746C161.985 51.1293 174.488 43.9876 180.671 40.3474L176.684 27.1735C176.684 27.1735 171.657 31.4945 163.163 31.4945Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.346683"
                />
                <g id="Group 5">
                  <path
                    id="Vector 1"
                    d="M163.163 40.694C152.243 40.694 150.336 27 150.336 27C141.9 30.0046 124.889 36.1871 124.335 36.8805C123.641 37.7472 116.534 41.734 112.721 44.5075C109.67 46.7263 98.9691 54.3302 94 57.1615C94.52 58.0859 98.5914 62.5121 100.934 66.3486C104.204 71.7047 107.347 81.0826 107.347 81.0826L120.001 78.1358L119.135 165.5H161.43H207.192L205.805 78.1358L219.499 83.5094C220.019 82.9894 222.227 74.5914 225.046 69.4687C228.115 63.8927 234.753 56.6414 234.753 56.6414L202.512 36.8805L176.684 27C176.684 27 173.044 40.694 163.163 40.694Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.346683"
                  />
                  <path
                    id="Vector 18"
                    d="M232.327 55.2546C232.327 55.2546 226.485 61.4096 223.66 66.0018C219.984 71.9761 216.899 82.6426 216.899 82.6426"
                    stroke="black"
                    stroke-width="0.346683"
                    stroke-dasharray="0.69 0.69"
                  />
                  <path
                    id="Vector 18_2"
                    d="M97.2935 55.4282C97.2935 55.4282 102.624 61.6152 105.094 66.1754C107.807 71.1847 110.121 79.8694 110.121 79.8694"
                    stroke="black"
                    stroke-width="0.346683"
                    stroke-dasharray="0.69 0.69"
                  />
                  <path
                    id="Vector 17"
                    d="M119.308 162.9C119.308 162.9 147.043 162.9 162.643 162.9C178.242 162.9 207.192 162.9 207.192 162.9"
                    stroke="black"
                    stroke-width="0.346683"
                    stroke-dasharray="0.69 0.69"
                  />
                  <path
                    id="Vector 16"
                    d="M165.254 118.079C155.246 99.3997 137.62 41.4403 136.498 37.9204C136.498 46.2936 134.092 76.9596 136.498 81.4395C137.331 82.9909 147.455 110.079 162.207 128.159C174.009 142.622 192.354 152.318 197.485 151.198C191.819 148.798 174.341 135.038 165.254 118.079Z"
                    fill="url(#paint0_linear_207_4946)"
                    fill-opacity="0.5"
                  />
                </g>
                <g id="Group 9">
                  <g id="Group 6">
                    <path
                      id="Vector 14"
                      d="M124.508 36.8805C124.508 36.8805 127.795 50.7332 126.588 59.5883C125.499 67.5768 120.001 78.3092 120.001 78.3092"
                      stroke="black"
                      stroke-width="0.346683"
                    />
                    <path
                      id="Vector 15"
                      d="M201.565 36.5334C201.565 36.5334 198.278 50.3862 199.485 59.2412C200.574 67.2298 205.805 78.1354 205.805 78.1354"
                      stroke="black"
                      stroke-width="0.346683"
                    />
                  </g>
                  <g id="Group 4">
                    <path
                      id="Vector 3"
                      d="M147.563 28.0403C147.563 28.0403 150.163 42.7743 161.43 43.4677C176.684 44.4064 179.111 28.0403 179.111 28.0403"
                      stroke="black"
                      stroke-width="0.346683"
                    />
                    <g id="Group 3">
                      <g id="Group 1">
                        <path
                          id="Vector 4"
                          d="M147.909 29.6004L150.683 28.5603"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 5"
                          d="M148.603 31.3338L151.203 30.2937"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 6"
                          d="M149.123 33.2405L151.896 32.0271"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 7"
                          d="M149.989 35.1474L152.589 33.7606"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 8"
                          d="M151.376 37.4008L153.803 35.494"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 9"
                          d="M152.763 39.1341L154.843 37.054"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 10"
                          d="M154.496 40.8676L156.576 38.4408"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 11"
                          d="M156.576 42.081L158.136 39.6542"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 12"
                          d="M159.003 43.1211L160.043 40.1743"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 13"
                          d="M162.297 43.4677L162.47 40.5209"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                      </g>
                      <g id="Group 2">
                        <path
                          id="Vector 4_2"
                          d="M178.764 29.2539L176.164 28.3872"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 5_2"
                          d="M178.417 30.9873L175.644 29.9473"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 6_2"
                          d="M177.724 32.8939L174.951 31.5072"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 7_2"
                          d="M176.684 34.8008L173.91 33.2407"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 8_2"
                          d="M175.297 37.0545L172.87 35.1477"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 9_2"
                          d="M173.39 39.1345L171.137 36.8811"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 11_2"
                          d="M171.31 41.041L169.75 38.2676"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 12_2"
                          d="M168.884 42.2545L167.844 39.6544"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                        <path
                          id="Vector 13_2"
                          d="M165.417 43.4679L165.07 40.5211"
                          stroke="black"
                          stroke-width="0.346683"
                        />
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19"
                  d="M122.027 78.656L203.725 78.656"
                  stroke="#E55959"
                  stroke-width="1.73342"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27"
                  d="M201.667 81.6144L204.588 78.6386L201.58 76.0559"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
                <path
                  id="Vector 28"
                  d="M123.912 81.6144L120.991 78.6386L123.999 76.0559"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19_2"
                  d="M122.027 108.817L203.725 108.817"
                  stroke="#E55959"
                  stroke-width="1.73342"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M202.014 111.776L204.935 108.8L201.926 106.217"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
                <path
                  id="Vector 28_2"
                  d="M123.912 111.776L120.991 108.8L123.999 106.217"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
              </g>
            )}
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <g id="Group 219">
                  <path
                    id="Vector 19_3"
                    d="M122.027 159.086L204.939 159.086"
                    stroke="#E55959"
                    stroke-width="1.73342"
                    stroke-linecap="square"
                  />
                  <path
                    id="Vector 27_3"
                    d="M203.054 162.044L205.975 159.069L202.967 156.486"
                    stroke="#E55959"
                    stroke-width="1.38673"
                  />
                  <path
                    id="Vector 28_3"
                    d="M123.912 162.044L120.991 159.069L123.999 156.486"
                    stroke="#E55959"
                    stroke-width="1.38673"
                  />
                </g>
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_4"
                  d="M162.796 33.8794L162.796 163.94"
                  stroke="#E55959"
                  stroke-width="1.73342"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M159.837 161.708L162.813 164.63L165.395 161.621"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
                <path
                  id="Vector 28_4"
                  d="M159.837 35.7645L162.813 32.8432L165.395 35.8517"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
              </g>
            )}
            {findMeasure('product_biceps') && (
              <g id="product_biceps">
                <path
                  id="Vector 21"
                  d="M103.534 54.0292L116.188 77.0959"
                  stroke="#E55959"
                  stroke-width="1.73342"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M112.784 76.5037L116.743 77.8145L117.735 73.9757"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
                <path
                  id="Vector 27_5"
                  d="M106.794 53.9698L102.819 52.7082L101.875 56.559"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
              </g>
            )}
            {findMeasure('product_sleeve') && (
              <g id="product_sleeve">
                <path
                  id="Vector 21_2"
                  d="M202.979 44.1075L230.073 58.8948"
                  stroke="#E55959"
                  stroke-width="1.73342"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M226.923 60.5573L230.896 59.2903L229.442 55.6015"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
                <path
                  id="Vector 27_6"
                  d="M205.551 42.0253L201.594 43.3414L203.094 47.0119"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_5"
                  d="M162.796 43.2399L162.796 163.246"
                  stroke="#E55959"
                  stroke-width="1.73342"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M159.837 161.708L162.813 164.63L165.395 161.621"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
                <path
                  id="Vector 28_5"
                  d="M159.837 45.1251L162.813 42.2038L165.395 45.2123"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_6"
                  d="M122.027 78.656L203.725 78.656"
                  stroke="#E55959"
                  stroke-width="1.73342"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_8"
                  d="M201.667 81.6144L204.588 78.6386L201.58 76.0559"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
                <path
                  id="Vector 28_6"
                  d="M123.912 81.6144L120.991 78.6386L123.999 76.0559"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_7"
                  d="M122.027 108.817L203.725 108.817"
                  stroke="#E55959"
                  stroke-width="1.73342"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_9"
                  d="M202.014 111.776L204.935 108.8L201.926 106.217"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
                <path
                  id="Vector 28_7"
                  d="M123.912 111.776L120.991 108.8L123.999 106.217"
                  stroke="#E55959"
                  stroke-width="1.38673"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 19_8"
                  d="M126.415 38.9607L199.392 38.9607"
                  stroke="#EDA7A7"
                  stroke-width="1.73342"
                  stroke-linecap="square"
                  stroke-dasharray="3.47 3.47"
                />
                <path
                  id="Vector 27_10"
                  d="M197.853 41.7456L200.775 38.7698L197.766 36.1871"
                  stroke="#EDA7A7"
                  stroke-width="1.38673"
                />
                <path
                  id="Vector 28_8"
                  d="M128.419 41.7456L125.498 38.7698L128.506 36.1871"
                  stroke="#EDA7A7"
                  stroke-width="1.38673"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_9"
                  d="M162.796 33.8795L162.796 163.94"
                  stroke="#EDA7A7"
                  stroke-width="1.73342"
                  stroke-linecap="square"
                  stroke-dasharray="3.47 3.47"
                />
                <path
                  id="Vector 27_11"
                  d="M159.837 161.708L162.813 164.63L165.396 161.621"
                  stroke="#EDA7A7"
                  stroke-width="1.38673"
                />
                <path
                  id="Vector 28_9"
                  d="M159.837 35.7646L162.813 32.8434L165.396 35.8518"
                  stroke="#EDA7A7"
                  stroke-width="1.38673"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4946"
              x1="139.063"
              y1="72.6397"
              x2="182.423"
              y2="152.559"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#E6E6E6" />
              <stop offset="0.69487" stop-color="#CDCDCD" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_tshirt">
            <g id="tshirt">
              <g id="Group 8">
                <path
                  id="Vector 2"
                  d="M168.865 44.0093C157.781 44.0093 150.302 40.3342 149.13 37.5407L145.616 48.0839C146.427 52.7698 147.778 62.4118 147.778 63.4932C147.778 64.8449 165.08 72.6847 166.161 73.766C167.027 74.6311 186.527 63.4932 196.169 57.8161L189.951 37.2704C189.951 37.2704 182.111 44.0093 168.865 44.0093Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.540676"
                />
                <g id="Group 5">
                  <path
                    id="Vector 1"
                    d="M168.865 58.3567C151.834 58.3567 148.86 37 148.86 37C135.703 41.6859 109.174 51.3279 108.309 52.4093C107.228 53.761 96.1439 59.9787 90.1965 64.3041C85.4385 67.7645 68.7497 79.6233 61 84.0388C61.811 85.4806 68.1606 92.3836 71.8135 98.3667C76.9135 106.72 81.816 121.345 81.816 121.345L101.551 116.75L100.199 253H166.161H237.531L235.368 116.75L256.725 125.13C257.536 124.319 260.979 111.222 265.375 103.233C270.161 94.5366 280.514 83.2278 280.514 83.2278L230.232 52.4093L189.951 37C189.951 37 184.274 58.3567 168.865 58.3567Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.540676"
                  />
                  <path
                    id="Vector 18"
                    d="M276.73 81.0654C276.73 81.0654 267.619 90.6645 263.213 97.8264C257.48 107.144 252.67 123.779 252.67 123.779"
                    stroke="black"
                    stroke-width="0.540676"
                    stroke-dasharray="1.08 1.08"
                  />
                  <path
                    id="Vector 18_2"
                    d="M66.1363 81.3357C66.1363 81.3357 74.4499 90.9847 78.3015 98.0966C82.5325 105.909 86.1413 119.453 86.1413 119.453"
                    stroke="black"
                    stroke-width="0.540676"
                    stroke-dasharray="1.08 1.08"
                  />
                  <path
                    id="Vector 17"
                    d="M100.469 248.945C100.469 248.945 143.723 248.945 168.054 248.945C192.381 248.945 237.531 248.945 237.531 248.945"
                    stroke="black"
                    stroke-width="0.540676"
                    stroke-dasharray="1.08 1.08"
                  />
                  <path
                    id="Vector 16"
                    d="M172.126 179.043C156.518 149.912 129.029 59.5207 127.278 54.0311C127.278 67.0896 123.527 114.915 127.278 121.902C128.577 124.322 144.367 166.567 167.374 194.763C185.78 217.321 214.389 232.442 222.392 230.695C213.556 226.952 186.297 205.493 172.126 179.043Z"
                    fill="url(#paint0_linear_207_4947)"
                    fill-opacity="0.5"
                  />
                </g>
                <g id="Group 9">
                  <g id="Group 6">
                    <path
                      id="Vector 14"
                      d="M108.579 52.4097C108.579 52.4097 113.706 74.0139 111.823 87.8239C110.125 100.283 101.551 117.02 101.551 117.02"
                      stroke="black"
                      stroke-width="0.540676"
                    />
                    <path
                      id="Vector 15"
                      d="M228.755 51.8682C228.755 51.8682 223.628 73.4724 225.511 87.2824C227.209 99.7411 235.368 116.749 235.368 116.749"
                      stroke="black"
                      stroke-width="0.540676"
                    />
                  </g>
                  <g id="Group 4">
                    <path
                      id="Vector 3"
                      d="M144.534 38.6221C144.534 38.6221 148.589 61.6008 166.161 62.6821C189.951 64.1461 193.736 38.6221 193.736 38.6221"
                      stroke="black"
                      stroke-width="0.540676"
                    />
                    <g id="Group 3">
                      <g id="Group 1">
                        <path
                          id="Vector 4"
                          d="M145.075 41.0551L149.401 39.4331"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 5"
                          d="M146.157 43.7584L150.212 42.1364"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 6"
                          d="M146.968 46.7321L151.293 44.8397"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 7"
                          d="M148.319 49.7058L152.374 47.5431"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 8"
                          d="M150.482 53.2202L154.267 50.2465"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 9"
                          d="M152.645 55.9235L155.889 52.6794"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 10"
                          d="M155.348 58.6269L158.592 54.8422"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 11"
                          d="M158.592 60.5192L161.025 56.7345"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 12"
                          d="M162.377 62.1412L163.999 57.5454"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 13"
                          d="M167.513 62.6819L167.784 58.0862"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                      </g>
                      <g id="Group 2">
                        <path
                          id="Vector 4_2"
                          d="M193.195 40.5145L189.14 39.1628"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 5_2"
                          d="M192.655 43.218L188.329 41.5959"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 6_2"
                          d="M191.573 46.1916L187.248 44.0289"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 7_2"
                          d="M189.951 49.1653L185.626 46.7323"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 8_2"
                          d="M187.789 52.6796L184.004 49.7059"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 9_2"
                          d="M184.815 55.9245L181.3 52.4102"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 11_2"
                          d="M181.571 58.8974L179.138 54.572"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 12_2"
                          d="M177.786 60.7898L176.164 56.7347"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                        <path
                          id="Vector 13_2"
                          d="M172.379 62.6822L171.839 58.0864"
                          stroke="black"
                          stroke-width="0.540676"
                        />
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </g>
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <g id="Group 219">
                  <path
                    id="Vector 19"
                    d="M104.71 242.998L234.016 242.998"
                    stroke="#E55959"
                    stroke-width="2.70338"
                    stroke-linecap="square"
                  />
                  <path
                    id="Vector 27"
                    d="M231.076 247.611L235.632 242.97L230.94 238.942"
                    stroke="#E55959"
                    stroke-width="2.1627"
                  />
                  <path
                    id="Vector 28"
                    d="M107.65 247.611L103.094 242.97L107.786 238.942"
                    stroke="#E55959"
                    stroke-width="2.1627"
                  />
                </g>
              </g>
            )}
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19_2"
                  d="M104.71 117.561L232.124 117.561"
                  stroke="#E55959"
                  stroke-width="2.70338"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M228.914 122.175L233.47 117.534L228.778 113.506"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
                <path
                  id="Vector 28_2"
                  d="M107.65 122.175L103.094 117.534L107.786 113.506"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_3"
                  d="M104.71 117.561L232.124 117.561"
                  stroke="#E55959"
                  stroke-width="2.70338"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M228.914 122.175L233.47 117.534L228.778 113.506"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
                <path
                  id="Vector 28_3"
                  d="M107.65 122.175L103.094 117.534L107.786 113.506"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 19_4"
                  d="M111.553 55.6539L225.365 55.6539"
                  stroke="#EDA7A7"
                  stroke-width="2.70338"
                  stroke-linecap="square"
                  stroke-dasharray="5.41 5.41"
                />
                <path
                  id="Vector 27_4"
                  d="M222.966 59.9969L227.522 55.3559L222.83 51.3281"
                  stroke="#EDA7A7"
                  stroke-width="2.1627"
                />
                <path
                  id="Vector 28_4"
                  d="M114.679 59.9969L110.123 55.3559L114.815 51.3281"
                  stroke="#EDA7A7"
                  stroke-width="2.1627"
                />
              </g>
            )}
            {findMeasure('product_biceps') && (
              <g id="product_biceps">
                <path
                  id="Vector 21"
                  d="M75.8685 79.1535L95.6031 115.128"
                  stroke="#E55959"
                  stroke-width="2.70338"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M90.295 114.204L96.4689 116.248L98.0167 110.261"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
                <path
                  id="Vector 27_5"
                  d="M80.9529 79.0608L74.7542 77.0933L73.2808 83.0988"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_5"
                  d="M168.291 62.3269L168.291 249.485"
                  stroke="#E55959"
                  stroke-width="2.70338"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M163.677 247.087L168.318 251.642L172.346 246.951"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
                <path
                  id="Vector 28_5"
                  d="M163.677 65.2675L168.318 60.7115L172.346 65.4034"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_6"
                  d="M168.291 47.7289L168.291 250.567"
                  stroke="#E55959"
                  stroke-width="2.70338"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M163.677 247.087L168.318 251.642L172.346 246.951"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
                <path
                  id="Vector 28_6"
                  d="M163.677 50.6686L168.318 46.1127L172.346 50.8045"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
              </g>
            )}
            {findMeasure('product_sleeve') && (
              <g id="product_sleeve">
                <path
                  id="Vector 21_2"
                  d="M230.96 63.681L273.215 86.7428"
                  stroke="#E55959"
                  stroke-width="2.70338"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M268.303 89.3347L274.499 87.3587L272.232 81.6059"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
                <path
                  id="Vector 27_8"
                  d="M234.971 60.4332L228.8 62.4858L231.139 68.2101"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_7"
                  d="M168.291 47.7289L168.291 250.567"
                  stroke="#EDA7A7"
                  stroke-width="2.70338"
                  stroke-linecap="square"
                  stroke-dasharray="5.41 5.41"
                />
                <path
                  id="Vector 27_9"
                  d="M163.677 247.087L168.318 251.642L172.346 246.951"
                  stroke="#EDA7A7"
                  stroke-width="2.1627"
                />
                <path
                  id="Vector 28_7"
                  d="M163.677 50.6686L168.318 46.1127L172.346 50.8045"
                  stroke="#EDA7A7"
                  stroke-width="2.1627"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19_8"
                  d="M104.71 164.6L232.124 164.6"
                  stroke="#E55959"
                  stroke-width="2.70338"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_10"
                  d="M229.454 169.214L234.01 164.573L229.318 160.545"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
                <path
                  id="Vector 28_8"
                  d="M107.65 169.214L103.094 164.573L107.786 160.545"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_9"
                  d="M104.71 164.6L232.124 164.6"
                  stroke="#E55959"
                  stroke-width="2.70338"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_11"
                  d="M229.454 169.214L234.01 164.573L229.318 160.545"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
                <path
                  id="Vector 28_9"
                  d="M107.65 169.214L103.094 164.573L107.786 160.545"
                  stroke="#E55959"
                  stroke-width="2.1627"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4947"
              x1="131.279"
              y1="108.178"
              x2="198.902"
              y2="232.817"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#E6E6E6" />
              <stop offset="0.69487" stop-color="#CDCDCD" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
