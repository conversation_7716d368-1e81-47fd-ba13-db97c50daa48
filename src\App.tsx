import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppContext } from './store';
import { getProduct } from './api/get-product';
import getQueryParams from './lib/get-query-params';
import { useDevice } from './hooks/use-device';
import { getConfig } from './api/get-config';
import { AppStateActions } from './store/types';
import { Desktop } from './pages/desktop';
import { DesktopContainer } from './components/organisms/desktop-container';
import useRTL from './hooks/use-rtl';
import { getMappedLanguages } from './lib/get-mapped-languages';
import { Mobile } from './pages/mobile';
import setCustomTheme from './lib/set-custom-theme';
import { decodedRecommendedSize } from './lib/decode-base64';

const qp = getQueryParams<{
  id: string;
  lang?: string;
  sizeSystem?: string;
  recommendedSize: string;
  sizes: string;
}>();

function App() {
  const { setState } = useAppContext();
  const { isMobile } = useDevice();
  const { i18n } = useTranslation();
  const [isI18nInitialized, setIsI18nInitialized] = useState(false);
  useRTL();
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        const language = qp.lang || 'en';
        const mappedLanguage = getMappedLanguages(language).value;

        if (!i18n.isInitialized) {
          await i18n.init();
        }

        await i18n.changeLanguage(mappedLanguage);
        setIsI18nInitialized(true);
      } catch (error) {
        console.error('Error initializing language:', error);
        setIsI18nInitialized(true);
      }
    };

    initializeLanguage();
  }, [i18n]);

  useEffect(() => {
    const loadProduct = async () => {
      const productRequest = getProduct({ id: qp.id, sizeSystem: qp.sizeSystem });
      const configRequest = getConfig();

      const [product, config] = await Promise.all([productRequest, configRequest]);

      const { modelingInfo } = product;
      const sizes = Object.values(modelingInfo.sizes);

      const middleSize = sizes[Math.floor(sizes.length / 2)].sizeName;
      const recommendedSize = sizes.find((item) => item.sizeName === decodedRecommendedSize)?.sizeName;

      const selectedSize = recommendedSize || middleSize;

      setState({ action: AppStateActions.SET_PRODUCT, payload: product });
      setState({
        action: AppStateActions.SET_CAROUSEL_SELECTED,
        payload: {
          id: selectedSize,
          content: selectedSize,
        },
      });
      setState({ action: AppStateActions.SET_CONFIG, payload: config });
      setState({
        action: AppStateActions.SET_UNIT_SYSTEM,
        payload: !config?.general?.isMetric ? 'metric' : 'imperial',
      });

      setCustomTheme(config?.general?.theme?.css);
    };
    loadProduct();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!isI18nInitialized) {
    return null;
  }

  return (
    <>
      {isMobile ? (
        <div className="min-h-screen min-w-screen bg-white rounded-md">{<Mobile />}</div>
      ) : (
        <DesktopContainer>
          <Desktop />
        </DesktopContainer>
      )}
    </>
  );
}

export default App;
