import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { LanguageSwitcher } from './menu';
import { Button, Link, Text } from '../atoms';
import Close from '../icons/close';
import Arrow from '../icons/arrow';
import { cn } from '@/lib/utils';
import { getMappedLanguages } from '@/lib/get-mapped-languages';
import { handleCloseApp } from '@/lib/close-app';
import useRTL from '@/hooks/use-rtl';
import getQueryParams from '@/lib/get-query-params';

export function DesktopContainer({ children }: { children: React.ReactNode }) {
  const { t, i18n } = useTranslation();
  const [isLanguageOpen, setIsLanguageOpen] = useState(false);
  const isRTL = useRTL();

  const { disableCloseApp } = getQueryParams<{ disableCloseApp?: string }>();
  const shouldHideCloseButton = disableCloseApp === 'true';

  const handleCloseMenu = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!isLanguageOpen) return;

    event.stopPropagation();
    const element = event.target as HTMLElement;

    const isClickInsideMenu = element instanceof Node && Boolean(element.closest('.language-switcher-shadow'));

    if (!isClickInsideMenu) {
      setIsLanguageOpen(false);
    }
  };

  const toggleLanguage = () => {
    setIsLanguageOpen(!isLanguageOpen);
  };

  const language = getMappedLanguages(i18n.language).key;

  const selectedLanguage = t(`languages.all_langs.${language}`);

  return (
    <div
      className={cn('bg-white relative w-[900px] h-[500px] border-[1px] border-[#E1E1E1] rounded-lg')}
      onClick={handleCloseMenu}
    >
      {!shouldHideCloseButton && (
        <Button
          variant="blank"
          className={cn(
            'absolute cursor-pointer z-50 top-8 -right-12 h-12 w-12 bg-white rounded-none rounded-tr-[3px] rounded-br-[3px]',
            {
              '-left-12 right-auto rounded-none rounded-tl-[3px] rounded-bl-[3px] close-btn-shadow-rtl': isRTL,
              'close-btn-shadow': !isRTL,
            }
          )}
          onClick={handleCloseApp}
        >
          <Close />
        </Button>
      )}
      {children}
      <div className="flex w-full justify-between mt-4 absolute -bottom-8">
        <Link external href="https://sizebay.com/pt/" className="no-underline text-white text-[12px] font-normal">
          {t('footer.by-sizebay')}
        </Link>

        <Button
          variant="blank"
          className="text-[#C9C9C9] p-0 sm:px-0 text-[12px] font-normal flex items-center gap-0.5"
          onClick={toggleLanguage}
        >
          {t('generics.language')}:
          <Text variant="label" className="text-white font-bold text-[12px] flex items-center">
            {selectedLanguage} - {language.toUpperCase()}
            <Arrow className={cn('rotate-90 ml-1', isLanguageOpen ? 'rotate-180' : 'rotate-0')} color="#FFFFFF" />
          </Text>
        </Button>
        {isLanguageOpen && (
          <LanguageSwitcher
            className={cn('bg-white absolute bottom-6 right-1 rounded-lg language-switcher-shadow', {
              'left-1 right-auto': isRTL,
            })}
            onclick={toggleLanguage}
          />
        )}
      </div>
    </div>
  );
}
