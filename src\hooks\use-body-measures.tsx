import { BodyMeasure, GarmentMeasure, MeasurementLabel } from './use-size-chart';
import { bodyMeasures, dict } from '@/components/body_measures';

type templateResponse = React.ComponentType<{
  measure: BodyMeasure | GarmentMeasure;
  className?: string;
}>;

interface Props {
  gender: string;
  ageGroup: string;
  measure: MeasurementLabel;
}

const formatTemplateAndCapitalize = (template: string) => {
  return template
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
};

const getSelectedKey = (gender: string, ageGroup: string): string => {
  const keyMap: { [key: string]: string } = {
    kids: 'child',
    adultU: 'male',
    male: 'male',
    female: 'female',
  };

  if (ageGroup === 'kids' || ageGroup === 'infant' || ageGroup === 'newborn' || ageGroup === 'toddler') {
    return keyMap.kids;
  }

  if (ageGroup === 'adult' && gender === 'U') {
    return keyMap.adultU;
  }

  return gender === 'M' ? keyMap.male : keyMap.female;
};

export function getBodyMeasureTemplate({ gender, ageGroup, measure }: Props) {
  const selectedKey = getSelectedKey(gender, ageGroup);
  const measures = bodyMeasures[selectedKey as keyof typeof bodyMeasures];

  const template = Object.entries(measures).find(([_, value]) => {
    return value.includes(measure.measure);
  });

  const formattedTemplate = template?.length ? formatTemplateAndCapitalize(template[0]) : '';

  const selectedTemplate = template?.length
    ? ((dict[selectedKey as keyof typeof dict] as Record<string, unknown>)[formattedTemplate] as templateResponse)
    : undefined;

  return selectedTemplate ?? '';
}
