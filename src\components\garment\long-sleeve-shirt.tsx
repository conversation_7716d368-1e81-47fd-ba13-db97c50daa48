import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface Props {
  measure: GarmentMeasure;
  className?: string;
}

export function LongSleeveShirt({ measure, className }: Props) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_long_sleeve_shirt">
            <g id="long_sleeve_shirt">
              <g id="Group 40">
                <path
                  id="Vector 75"
                  d="M160.932 32.5993C154.994 32.9592 151.094 30.5597 149.235 29.7199C146.175 30.6797 139.265 32.9952 136.097 34.5789C132.93 36.1626 123.14 40.6377 118.641 42.6773C93.2059 59.9539 41.1842 95.2989 36.5771 98.4662C31.97 101.634 28.7787 105.785 27.7589 107.464L27.3989 108.904L22 115.563C23.2598 116.883 26.1032 119.99 27.3989 121.862C28.6947 123.733 29.8584 125.521 30.2784 126.181L38.1968 122.222H39.4565C45.8153 118.622 60.1524 110.776 66.6311 108.184C74.7295 104.945 105.323 92.3474 108.383 91.2677C110.83 90.4038 114.082 86.9485 115.401 85.3288C114.922 95.4668 113.962 116.715 113.962 120.602V163.433C113.962 163.433 138.149 163.145 160.033 163.433C181.916 163.721 201.604 162.953 209.163 163.433C209.583 157.674 209.631 144.357 210.063 133.559C210.495 122.761 208.503 97.8663 207.723 87.3084C216.541 90.1879 236.733 96.8825 246.955 100.626C257.177 104.369 281.629 113.883 292.306 118.082H293.746L302.744 121.142C302.744 120.662 303.032 119.018 304.184 116.283C305.336 113.547 308.503 109.264 309.943 107.464L302.744 102.425V100.806L205.384 41.4175C201.964 39.9178 193.902 36.4505 189.007 34.5789C184.112 32.7073 177.849 30.2598 174.07 29C171.19 30.6797 167.22 32.2182 160.932 32.5993Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.359929"
                />
                <g id="Group 39">
                  <path
                    id="Vector 80"
                    d="M114.142 158.034C114.142 158.034 143.836 158.034 160.573 158.034C177.145 158.034 209.343 157.135 209.343 157.135"
                    stroke="black"
                    stroke-width="0.359929"
                    stroke-dasharray="0.72 0.72"
                  />
                  <g id="Group 38">
                    <path
                      id="Vector 78"
                      d="M115.402 85.6887C115.546 84.9688 116.301 83.2292 116.661 82.4493L117.921 81.3696C119.001 77.1704 121.376 67.4403 122.24 62.1134C123.104 56.7864 121.04 46.3965 119.901 42.1373"
                      stroke="black"
                      stroke-width="0.359929"
                    />
                    <path
                      id="Vector 76"
                      d="M27.579 108.724C27.579 108.724 30.1944 111.424 33.1579 114.843C35.4974 117.542 38.5568 122.221 38.5568 122.221"
                      stroke="black"
                      stroke-width="0.359929"
                      stroke-dasharray="0.72 0.72"
                    />
                  </g>
                  <g id="Group 37">
                    <path
                      id="Vector 79"
                      d="M207.723 87.3083C207.579 86.5885 207.363 83.5891 207.003 82.8092L205.949 81.3695C204.869 77.1703 202.494 67.4403 201.63 62.1133C200.766 56.7864 203.344 45.3167 204.484 41.0575"
                      stroke="black"
                      stroke-width="0.359929"
                    />
                    <path
                      id="Vector 77"
                      d="M294.106 117.902C294.106 117.902 295.163 113.732 297.274 109.73C299.325 105.845 302.924 102.605 302.924 102.605"
                      stroke="black"
                      stroke-width="0.359929"
                      stroke-dasharray="0.72 0.72"
                    />
                  </g>
                  <g id="Group 36">
                    <path
                      id="Vector 81"
                      d="M144.376 31.3393C144.376 31.3393 150.675 37.0982 161.652 36.7382C172.661 36.3773 179.829 30.9794 179.829 30.9794"
                      stroke="black"
                      stroke-width="0.359929"
                    />
                    <g id="Group 35">
                      <path
                        id="Vector 82"
                        d="M146.715 32.9588L150.854 30.4393"
                        stroke="black"
                        stroke-width="0.359929"
                      />
                      <path
                        id="Vector 83"
                        d="M149.955 34.7585L153.194 31.5192"
                        stroke="black"
                        stroke-width="0.359929"
                      />
                      <path
                        id="Vector 84"
                        d="M154.274 36.0182L156.613 32.4189"
                        stroke="black"
                        stroke-width="0.359929"
                      />
                      <path
                        id="Vector 85"
                        d="M159.313 36.5582L160.033 32.7789"
                        stroke="black"
                        stroke-width="0.359929"
                      />
                      <path
                        id="Vector 86"
                        d="M164.172 36.5581L163.272 32.4189"
                        stroke="black"
                        stroke-width="0.359929"
                      />
                      <path
                        id="Vector 87"
                        d="M168.671 35.6583L166.871 31.6991"
                        stroke="black"
                        stroke-width="0.359929"
                      />
                      <path id="Vector 88" d="M172.45 34.7585L170.111 30.9792" stroke="black" stroke-width="0.359929" />
                      <path id="Vector 89" d="M176.229 32.9588L172.27 29.8994" stroke="black" stroke-width="0.359929" />
                    </g>
                  </g>
                </g>
                <path
                  id="Vector 16"
                  d="M150.744 114.535C143.837 101.606 131.671 61.4902 130.896 59.054C130.896 64.8493 129.236 86.0744 130.896 89.1751C131.471 90.2489 138.459 108.998 148.641 121.511C156.787 131.522 169.448 138.233 172.99 137.458C169.079 135.797 157.016 126.273 150.744 114.535Z"
                  fill="url(#paint0_linear_207_4948)"
                  fill-opacity="0.5"
                />
              </g>
            </g>
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 19_3"
                  d="M116.065 154.975L207.723 154.975"
                  stroke="#E55959"
                  stroke-width="1.79964"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M205.586 158.046L208.619 154.957L205.496 152.276"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
                <path
                  id="Vector 28_3"
                  d="M118.001 157.62L114.98 154.771L118.113 152.276"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
              </g>
            )}
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19_2"
                  d="M117.505 86.5886L205.204 86.5886"
                  stroke="#E55959"
                  stroke-width="1.79964"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M203.427 89.66L206.459 86.5705L203.336 83.8892"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
                <path
                  id="Vector 28_2"
                  d="M119.451 89.4476L116.424 86.4776L119.552 83.8892"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_1"
                  d="M117.505 86.5886L205.204 86.5886"
                  stroke="#E55959"
                  stroke-width="1.79964"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_1"
                  d="M203.427 89.66L206.459 86.5705L203.336 83.8892"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
                <path
                  id="Vector 28_1"
                  d="M119.451 89.4476L116.424 86.4776L119.552 83.8892"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 19_4"
                  d="M122.42 43.937L201.604 43.937"
                  stroke="#E55959"
                  stroke-width="1.79964"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M199.827 46.8285L202.86 43.739L199.737 41.0576"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
                <path
                  id="Vector 28_4"
                  d="M124.501 46.8285L121.468 43.739L124.591 41.0576"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
              </g>
            )}
            {findMeasure('product_biceps') && (
              <g id="product_biceps">
                <path
                  id="Vector 21"
                  d="M90.7527 64.6201L104.604 90.3678"
                  stroke="#E55959"
                  stroke-width="1.79964"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M100.976 89.753L105.085 91.1138L106.116 87.1284"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
                <path
                  id="Vector 27_5"
                  d="M94.1376 64.5585L90.0111 63.2487L89.0302 67.2466"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_5"
                  d="M160.551 34.7026L160.551 160.374"
                  stroke="#E55959"
                  stroke-width="1.79964"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M157.671 158.415L160.518 161.645L163.379 158.706"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
                <path
                  id="Vector 28_5"
                  d="M157.666 37.001L160.461 33.7829L163.271 36.7324"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_6"
                  d="M160.551 34.7026L160.551 160.374"
                  stroke="#E55959"
                  stroke-width="1.79964"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M157.671 158.415L160.518 161.645L163.379 158.706"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
                <path
                  id="Vector 28_6"
                  d="M157.666 37.001L160.461 33.7829L163.271 36.7324"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
              </g>
            )}
            {findMeasure('product_sleeve') && (
              <g id="product_sleeve">
                <path
                  id="Vector 21_2"
                  d="M205.092 50.4739L305.264 108.544"
                  stroke="#E55959"
                  stroke-width="1.79964"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M301.994 110.27L306.118 108.955L304.609 105.125"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
                <path
                  id="Vector 27_8"
                  d="M207.762 48.3123L203.654 49.6787L205.211 53.4894"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_7"
                  d="M160.551 34.7026L160.551 160.374"
                  stroke="#EDA7A7"
                  stroke-width="1.79964"
                  stroke-linecap="square"
                  stroke-dasharray="3.6 3.6"
                />
                <path
                  id="Vector 27_9"
                  d="M157.671 158.415L160.518 161.645L163.379 158.706"
                  stroke="#EDA7A7"
                  stroke-width="1.43971"
                />
                <path
                  id="Vector 28_7"
                  d="M157.666 37.001L160.461 33.7829L163.271 36.7324"
                  stroke="#EDA7A7"
                  stroke-width="1.43971"
                />
              </g>
            )}
            {findMeasure('product_chest_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19_8"
                  d="M116.065 117.902L207.723 117.902"
                  stroke="#E55959"
                  stroke-width="1.79964"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_10"
                  d="M205.586 120.974L208.619 117.884L205.496 115.203"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
                <path
                  id="Vector 28_8"
                  d="M118.022 120.974L114.989 117.884L118.113 115.203"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_9"
                  d="M116.065 117.902L207.723 117.902"
                  stroke="#E55959"
                  stroke-width="1.79964"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_11"
                  d="M205.586 120.974L208.619 117.884L205.496 115.203"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
                <path
                  id="Vector 28_9"
                  d="M118.022 120.974L114.989 117.884L118.113 115.203"
                  stroke="#E55959"
                  stroke-width="1.43971"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4948"
              x1="132.667"
              y1="83.0845"
              x2="162.724"
              y2="138.329"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#E6E6E6" />
              <stop offset="0.69487" stop-color="#CDCDCD" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px] scale-[0.9]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_long_sleeve_shirt">
            <g id="long_sleeve_shirt">
              <g id="Group 40">
                <path
                  id="Vector 75"
                  d="M165.68 77.8C159.41 78.18 155.293 75.6467 153.33 74.76C150.1 75.7733 142.804 78.218 139.46 79.89C136.116 81.562 125.78 86.2866 121.03 88.44C94.1765 106.68 39.2539 143.996 34.39 147.34C29.526 150.684 26.1566 155.066 25.08 156.84L24.7 158.36L19 165.39C20.33 166.783 23.332 170.064 24.7 172.04C26.068 174.016 27.2966 175.903 27.74 176.6L36.1 172.42H37.43C44.1433 168.62 59.2799 160.336 66.1199 157.6C74.6699 154.18 106.97 140.88 110.2 139.74C112.784 138.828 116.216 135.18 117.61 133.47C117.103 144.173 116.09 166.606 116.09 170.71V215.93C116.09 215.93 141.626 215.626 164.73 215.93C187.834 216.234 208.62 215.423 216.599 215.93C217.043 209.85 217.093 195.79 217.549 184.39C218.005 172.99 215.903 146.706 215.079 135.56C224.389 138.6 245.707 145.668 256.499 149.62C267.291 153.572 293.106 163.616 304.379 168.05H305.899L315.399 171.28C315.399 170.773 315.703 169.038 316.919 166.15C318.135 163.262 321.479 158.74 322.999 156.84L315.399 151.52V149.81L212.609 87.11C209 85.5266 200.488 81.866 195.32 79.89C190.152 77.914 183.54 75.33 179.55 74C176.51 75.7733 172.317 77.3977 165.68 77.8Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.379999"
                />
                <g id="Group 39">
                  <path
                    id="Vector 80"
                    d="M116.28 210.229C116.28 210.229 147.63 210.229 165.3 210.229C182.796 210.229 216.79 209.279 216.79 209.279"
                    stroke="black"
                    stroke-width="0.379999"
                    stroke-dasharray="0.76 0.76"
                  />
                  <g id="Group 38">
                    <path
                      id="Vector 78"
                      d="M117.609 133.85C117.761 133.09 118.559 131.253 118.939 130.43L120.269 129.29C121.409 124.856 123.917 114.584 124.829 108.96C125.741 103.336 123.563 92.3664 122.359 87.8698"
                      stroke="black"
                      stroke-width="0.379999"
                    />
                    <path
                      id="Vector 76"
                      d="M24.8898 158.169C24.8898 158.169 27.6511 161.019 30.7798 164.629C33.2498 167.479 36.4798 172.419 36.4798 172.419"
                      stroke="black"
                      stroke-width="0.379999"
                      stroke-dasharray="0.76 0.76"
                    />
                  </g>
                  <g id="Group 37">
                    <path
                      id="Vector 79"
                      d="M215.079 135.56C214.927 134.8 214.699 131.633 214.319 130.81L213.206 129.29C212.066 124.856 209.558 114.584 208.646 108.96C207.734 103.336 210.456 91.2265 211.659 86.7299"
                      stroke="black"
                      stroke-width="0.379999"
                    />
                    <path
                      id="Vector 77"
                      d="M306.279 167.86C306.279 167.86 307.395 163.456 309.624 159.232C311.789 155.13 315.589 151.71 315.589 151.71"
                      stroke="black"
                      stroke-width="0.379999"
                      stroke-dasharray="0.76 0.76"
                    />
                  </g>
                  <g id="Group 36">
                    <path
                      id="Vector 81"
                      d="M148.2 76.4696C148.2 76.4696 154.85 82.5496 166.44 82.1696C178.062 81.7885 185.63 76.0896 185.63 76.0896"
                      stroke="black"
                      stroke-width="0.379999"
                    />
                    <g id="Group 35">
                      <path id="Vector 82" d="M150.67 78.1796L155.04 75.5197" stroke="black" stroke-width="0.379999" />
                      <path id="Vector 83" d="M154.09 80.0797L157.51 76.6597" stroke="black" stroke-width="0.379999" />
                      <path id="Vector 84" d="M158.65 81.4097L161.12 77.6097" stroke="black" stroke-width="0.379999" />
                      <path id="Vector 85" d="M163.97 81.9796L164.73 77.9896" stroke="black" stroke-width="0.379999" />
                      <path id="Vector 86" d="M169.1 81.9797L168.15 77.6097" stroke="black" stroke-width="0.379999" />
                      <path id="Vector 87" d="M173.85 81.0296L171.95 76.8496" stroke="black" stroke-width="0.379999" />
                      <path id="Vector 88" d="M177.84 80.0795L175.37 76.0895" stroke="black" stroke-width="0.379999" />
                      <path id="Vector 89" d="M181.83 78.1797L177.65 74.9497" stroke="black" stroke-width="0.379999" />
                    </g>
                  </g>
                </g>
                <path
                  id="Vector 16"
                  d="M154.923 164.304C147.63 150.655 134.786 108.302 133.968 105.73C133.968 111.848 132.216 134.257 133.968 137.531C134.575 138.664 141.953 158.458 152.703 171.67C161.303 182.239 174.67 189.324 178.409 188.505C174.281 186.752 161.544 176.697 154.923 164.304Z"
                  fill="url(#paint0_linear_207_4949)"
                  fill-opacity="0.5"
                />
              </g>
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19"
                  d="M119.83 134.8L212.419 134.8"
                  stroke="#E55959"
                  stroke-width="1.89999"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27"
                  d="M210.543 138.043L213.745 134.781L210.448 131.95"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
                <path
                  id="Vector 28"
                  d="M121.885 137.818L118.69 134.683L121.992 131.95"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19_2"
                  d="M118.31 167.86L215.079 167.86"
                  stroke="#E55959"
                  stroke-width="1.89999"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M212.823 171.102L216.025 167.841L212.728 165.01"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
                <path
                  id="Vector 28_2"
                  d="M120.377 171.102L117.175 167.841L120.472 165.01"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
              </g>
            )}
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 19_3"
                  d="M118.31 207L215.079 207"
                  stroke="#E55959"
                  stroke-width="1.89999"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M212.823 210.242L216.025 206.981L212.728 204.15"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
                <path
                  id="Vector 28_3"
                  d="M120.354 209.793L117.165 206.784L120.472 204.15"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
              </g>
            )}
            {findMeasure('product_biceps') && (
              <g id="product_biceps">
                <path
                  id="Vector 21"
                  d="M91.5865 111.607L106.21 138.79"
                  stroke="#E55959"
                  stroke-width="1.89999"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M102.379 138.141L106.719 139.577L107.806 135.37"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
                <path
                  id="Vector 27_4"
                  d="M95.1602 111.541L90.8036 110.158L89.768 114.379"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
              </g>
            )}
            {findMeasure('product_sleeve') && (
              <g id="product_sleeve">
                <path
                  id="Vector 21_2"
                  d="M212.302 96.6713L318.059 157.98"
                  stroke="#E55959"
                  stroke-width="1.89999"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M314.607 159.802L318.961 158.413L317.368 154.37"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
                <path
                  id="Vector 27_5"
                  d="M215.121 94.3894L210.784 95.832L212.427 99.8552"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_4"
                  d="M165.277 80.0206L165.277 212.7"
                  stroke="#E55959"
                  stroke-width="1.89999"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M162.236 210.631L165.242 214.041L168.262 210.938"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
                <path
                  id="Vector 28_4"
                  d="M162.231 82.447L165.182 79.0496L168.148 82.1635"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_5"
                  d="M165.277 80.0206L165.277 212.7"
                  stroke="#E55959"
                  stroke-width="1.89999"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M162.236 210.631L165.242 214.041L168.262 210.938"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
                <path
                  id="Vector 28_5"
                  d="M162.231 82.447L165.182 79.0496L168.148 82.1635"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_6"
                  d="M119.83 134.8L212.419 134.8"
                  stroke="#E55959"
                  stroke-width="1.89999"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_8"
                  d="M210.543 138.043L213.745 134.781L210.448 131.95"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
                <path
                  id="Vector 28_6"
                  d="M121.885 137.818L118.69 134.683L121.992 131.95"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_7"
                  d="M118.31 167.86L215.079 167.86"
                  stroke="#E55959"
                  stroke-width="1.89999"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_9"
                  d="M212.823 171.102L216.025 167.841L212.728 165.01"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
                <path
                  id="Vector 28_7"
                  d="M120.377 171.102L117.175 167.841L120.472 165.01"
                  stroke="#E55959"
                  stroke-width="1.52"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_8"
                  d="M165.277 80.0206L165.277 212.7"
                  stroke="#EDA7A7"
                  stroke-width="1.89999"
                  stroke-linecap="square"
                  stroke-dasharray="3.8 3.8"
                />
                <path
                  id="Vector 27_10"
                  d="M162.236 210.631L165.242 214.041L168.262 210.938"
                  stroke="#EDA7A7"
                  stroke-width="1.52"
                />
                <path
                  id="Vector 28_8"
                  d="M162.231 82.447L165.182 79.0496L168.148 82.1635"
                  stroke="#EDA7A7"
                  stroke-width="1.52"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 19_9"
                  d="M125.02 89.7699L208.619 89.7699"
                  stroke="#EDA7A7"
                  stroke-width="1.89999"
                  stroke-linecap="square"
                  stroke-dasharray="3.8 3.8"
                />
                <path
                  id="Vector 27_11"
                  d="M206.743 92.8226L209.945 89.5608L206.648 86.73"
                  stroke="#EDA7A7"
                  stroke-width="1.52"
                />
                <path
                  id="Vector 28_9"
                  d="M127.217 92.8226L124.015 89.5608L127.312 86.73"
                  stroke="#EDA7A7"
                  stroke-width="1.52"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4949"
              x1="135.838"
              y1="131.1"
              x2="167.571"
              y2="189.425"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#E6E6E6" />
              <stop offset="0.69487" stop-color="#CDCDCD" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
