import { MeasurementLabel } from '@/hooks/use-size-chart';

export const getTablePriority = (measureObj: MeasurementLabel) => {
  const { type, measure, position } = measureObj;

  if (position) return position;
  if (measure === 'weightChart' || measure === 'weight') return 6;
  if (type === 'TEXT' || type === 'LABEL') return 5;
  if (type === 'MEASURE' || type === 'GARMENT') return 4;

  return 7;
};
