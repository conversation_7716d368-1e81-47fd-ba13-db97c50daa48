import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface ShirtProps {
  measure: GarmentMeasure;
  className?: string;
}

export function Shirt({ measure, className }: ShirtProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_shirt">
            <g id="shirt">
              <g id="Group 50">
                <path
                  id="Vector 99"
                  d="M177.671 169.805L170.82 170.185L166.633 168.853L152.93 163.334L160.923 144.302L181.097 147.728L177.671 169.805Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.38063"
                />
                <g id="Group 49">
                  <g id="Group 47">
                    <path
                      id="Vector 98"
                      d="M124.383 120.894C126.21 114.651 130.346 101.291 130.283 101.672C132.376 98.0559 136.601 90.7097 136.753 90.2529C136.905 89.7962 139.862 78.39 141.321 72.7439L138.276 51.048L126.096 40.9613C125.017 42.1032 122.898 44.9579 121.528 47.2417C119.815 50.0964 112.964 62.4669 110.109 71.6021C107.825 78.9102 106.239 84.3532 102.497 97.6752C95.0657 124.129 92.6001 151.725 92.6001 151.725L91.0776 162.953L110.68 166.95C110.68 166.95 116.96 139.735 124.383 120.894Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.38063"
                    />
                    <path
                      id="Vector 101"
                      d="M92.5089 151.933C93.6665 151.945 98.5052 152.997 103.19 153.829C106.937 154.494 111.836 154.849 113.816 154.943"
                      stroke="black"
                      stroke-width="0.38063"
                    />
                    <path
                      id="Vector 104"
                      d="M117.151 110.426C118.978 111.797 123.621 114.55 125.715 115.755C125.334 114.93 124.459 113.053 124.002 112.139C123.545 111.226 117.214 106.937 114.106 104.907C114.359 106.176 115.324 109.056 117.151 110.426Z"
                      fill="url(#paint0_linear_207_4950)"
                    />
                  </g>
                  <path
                    id="Vector 95"
                    d="M202.793 36.0132C199.291 33.8817 190.93 30.6844 187.187 29.3522L177.671 26.3071L172.342 27.6393L163.207 31.636L150.075 66.4636V96.5334V124.89L171.2 153.628C172.215 156.483 175.045 161.241 175.197 162.763C175.349 164.286 174.88 168.98 175.197 170.185L189.471 169.424L209.073 168.663V93.4884C208.819 87.3349 225.326 70.4365 221.824 59.2317C218.969 50.0965 214.021 42.4839 212.879 41.5323C210.976 40.5808 206.294 38.1447 202.793 36.0132Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.38063"
                  />
                  <path
                    id="Vector 93"
                    d="M167.394 53.7124C167.242 52.4944 168.916 49.5889 170.058 48.1933V41.1516L162.255 34.8712L153.12 28.7811C149.377 30.3036 141.13 33.539 134.85 36.2034C129.708 38.385 126.667 40.2 125.905 41.1516L127.999 43.6257C128.697 47.6858 130.092 57.3665 130.092 63.6088V102.433L130.663 168.853C134.66 170.312 170.82 170.185 170.82 170.185C170.693 169.297 170.439 166.76 170.439 163.715C170.439 160.67 170.693 155.975 170.82 154.009L167.013 141.067L166.252 116.516C166.506 96.1527 167.546 54.9304 167.394 53.7124Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.38063"
                  />
                  <g id="Group 48">
                    <path
                      id="Vector 94"
                      d="M155.594 29.9231C155.594 29.6186 157.878 28.2737 159.02 27.6393C160.035 27.449 162.103 26.9922 162.255 26.6877C162.408 26.3832 165.491 25.5458 167.013 25.1652C170.82 25.2286 178.508 25.3174 178.813 25.1652C179.117 25.0129 182.492 28.5274 184.142 30.3037L182.619 36.2035L177.861 43.2451L172.152 47.6224L168.917 48.1933L164.73 46.0999C162.953 44.4505 159.401 41.0755 159.401 40.771C159.401 40.3904 155.594 30.3037 155.594 29.9231Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.38063"
                    />
                    <path
                      id="Vector 92"
                      d="M158.259 28.5908L157.307 30.1134H155.975L156.546 25.3555L158.259 22.5008L158.83 21.9298C163.207 20.5976 174.245 20.9782 176.719 21.3589C179.194 21.7395 180.716 22.1201 180.716 22.1201L182.81 24.5942L183.951 28.2102L182.81 30.494L181.097 28.9715C178.432 29.0983 172.152 29.3521 168.346 29.3521C164.539 29.3521 160.035 28.8446 158.259 28.5908Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.38063"
                    />
                    <path
                      id="Vector 90"
                      d="M159.21 36.3938C155.594 27.4489 157.054 23.833 158.639 22.1201C156.863 23.7061 152.816 28.3625 152.359 28.9715C151.788 29.7327 153.311 35.2519 154.262 38.8679C155.023 41.7606 158.069 48.9546 160.162 51.2383L165.11 46.8611L169.107 48.1933C169.107 48.1933 162.361 44.188 159.21 36.3938Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.38063"
                    />
                    <path
                      id="Vector 91"
                      d="M180.145 36.3938C183.761 27.4489 182.302 23.833 180.716 22.1201C182.492 23.7061 186.54 28.3625 186.996 28.9715C187.567 29.7327 186.045 35.2519 185.093 38.8679C184.332 41.7606 181.287 48.9546 179.194 51.2383L174.245 46.8611L170.249 48.1933C170.249 48.1933 176.994 44.188 180.145 36.3938Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.38063"
                    />
                  </g>
                  <g id="Group 46">
                    <path
                      id="Vector 96"
                      d="M219.731 50.8578C216.533 44.92 213.958 42.1667 213.07 41.5323C212.308 42.6108 210.672 45.1103 210.215 46.4805C209.644 48.1934 207.741 54.2835 207.55 60.5639C207.398 65.5882 208.502 84.2264 209.073 92.9175L215.734 107.381C213.514 112.393 208.845 122.835 207.931 124.51C207.018 126.185 203.364 134.343 201.651 138.212L196.512 149.251C197.4 149.885 200.166 151.725 204.125 154.009C208.083 156.292 212.626 156.863 214.402 156.863C215.924 154.897 219.54 149.746 221.824 144.874C224.679 138.783 237.811 117.087 239.143 110.617C240.475 104.146 239.523 96.5334 236.478 87.7789C233.433 79.0244 223.727 58.2801 219.731 50.8578Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.38063"
                    />
                    <path
                      id="Vector 105"
                      d="M231.721 103.195C228.675 103.766 218.779 105.098 218.018 105.098C223.664 103.448 235.146 100.15 235.907 100.15C236.669 100.15 237.62 101.038 238.001 101.482C236.922 101.862 234.157 102.738 231.721 103.195Z"
                      fill="url(#paint1_linear_207_4950)"
                    />
                    <path
                      id="Vector 100"
                      d="M201.841 138.022C204.125 139.291 208.845 141.943 210.976 143.161C213.108 144.379 218.208 146.523 220.302 147.348"
                      stroke="black"
                      stroke-width="0.38063"
                    />
                  </g>
                  <g id="Group 45">
                    <path
                      id="Vector 97"
                      d="M174.245 164.286C173.636 161.241 171.073 154.009 169.868 150.773C170.947 152.169 173.522 155.455 175.197 157.434C176.872 159.414 179.955 164.222 181.287 166.379C180.843 166.569 179.726 167.064 178.813 167.521C177.899 167.978 176.022 169.234 175.197 169.995C175.133 169.234 174.854 167.331 174.245 164.286Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.38063"
                    />
                    <g id="Group 44">
                      <path
                        id="Vector 102"
                        d="M166.442 160.479C166.442 162.915 166.633 167.965 166.633 170.185H170.82C170.82 169.107 170.629 166.303 170.629 162.954C170.629 159.604 170.82 155.658 170.82 154.009L169.487 149.441L167.013 141.258C167.013 146.396 166.442 158.043 166.442 160.479Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.38063"
                      />
                      <path
                        id="Ellipse 1"
                        d="M168.115 145.053L168.819 147.34C168.818 147.34 168.818 147.34 168.817 147.34C168.746 147.354 168.632 147.366 168.455 147.366C167.824 147.366 167.313 146.855 167.313 146.224C167.313 145.922 167.491 145.581 167.721 145.333C167.834 145.211 167.949 145.125 168.042 145.08C168.073 145.065 168.097 145.057 168.115 145.053Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.38063"
                      />
                    </g>
                    <g id="Group 43">
                      <path
                        id="Vector 103"
                        d="M171.771 50.6674C172.342 48.3836 174.701 46.6483 174.701 46.6483L175.958 43.2451L174.245 45.1483L170.629 48.003L169.297 48.9546C168.853 49.2718 167.851 50.4771 167.394 52.7609C166.937 55.0447 166.696 79.5953 166.633 91.5852L166.252 116.136L166.633 139.925C166.886 141.638 170.058 150.773 170.058 150.773L171.2 152.296V138.212V106.81C171.137 95.9624 170.972 72.5917 170.82 65.8926C170.629 57.5188 171.2 52.9512 171.771 50.6674Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.38063"
                      />
                      <g id="Group 42">
                        <circle
                          id="Ellipse 2"
                          cx="169.107"
                          cy="57.709"
                          r="0.951576"
                          fill="white"
                          stroke="black"
                          stroke-width="0.38063"
                        />
                        <path id="Vector 135" d="M169.868 57.7091H170.629" stroke="black" stroke-width="0.38063" />
                        <path id="Vector 136" d="M169.868 73.6956H170.629" stroke="black" stroke-width="0.38063" />
                        <circle
                          id="Ellipse 3"
                          cx="169.107"
                          cy="73.6956"
                          r="0.951576"
                          fill="white"
                          stroke="black"
                          stroke-width="0.38063"
                        />
                        <circle
                          id="Ellipse 4"
                          cx="168.726"
                          cy="89.6821"
                          r="0.951576"
                          fill="white"
                          stroke="black"
                          stroke-width="0.38063"
                        />
                        <path id="Vector 137" d="M169.487 89.682H170.249" stroke="black" stroke-width="0.38063" />
                        <circle
                          id="Ellipse 5"
                          cx="168.726"
                          cy="105.669"
                          r="0.951576"
                          fill="white"
                          stroke="black"
                          stroke-width="0.38063"
                        />
                        <path id="Vector 138" d="M169.487 105.668H170.249" stroke="black" stroke-width="0.38063" />
                        <path id="Vector 139" d="M169.487 122.416H170.249" stroke="black" stroke-width="0.38063" />
                        <path id="Vector 140" d="M169.868 139.925H170.629" stroke="black" stroke-width="0.38063" />
                        <circle
                          id="Ellipse 6"
                          cx="168.726"
                          cy="122.416"
                          r="0.951576"
                          fill="white"
                          stroke="black"
                          stroke-width="0.38063"
                        />
                        <circle
                          id="Ellipse 7"
                          cx="169.107"
                          cy="139.925"
                          r="0.951576"
                          fill="white"
                          stroke="black"
                          stroke-width="0.38063"
                        />
                      </g>
                    </g>
                  </g>
                </g>
                <path
                  id="Vector 41"
                  d="M181.484 91.0781C184.179 88.0611 188.42 78.7576 188.677 78.2632C188.947 79.1801 189.438 84.3533 189.438 84.3533C189.357 84.6798 187.09 88.4741 182.813 91.8861C179.391 94.6157 173.66 97.4583 171.933 97.8338C173.723 97.021 179.037 93.8174 181.484 91.0781Z"
                  fill="url(#paint2_linear_207_4950)"
                  fill-opacity="0.87"
                />
              </g>
            </g>
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 19"
                  d="M132.697 164.476L206.789 164.476"
                  stroke="#E55959"
                  stroke-width="1.90315"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27"
                  d="M204.529 167.724L207.737 164.457L204.434 161.621"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
                <path
                  id="Vector 28"
                  d="M134.744 167.273L131.55 164.26L134.863 161.621"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
              </g>
            )}
            {findMeasure('product_wrist_width') && (
              <g id="product_wrist_width">
                <path
                  id="Vector 21"
                  d="M214.603 151.718L201.123 144.74"
                  stroke="#E55959"
                  stroke-width="1.90315"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M204.574 142.942L200.241 144.418L201.916 148.436"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
                <path
                  id="Vector 27_2"
                  d="M211.044 153.761L215.359 152.231L213.634 148.234"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_wrist_circumference">
                <path
                  id="Vector 21_2"
                  d="M214.603 151.718L201.123 144.74"
                  stroke="#E55959"
                  stroke-width="1.90315"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M204.574 142.942L200.241 144.418L201.916 148.436"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
                <path
                  id="Vector 27_3"
                  d="M211.044 153.761L215.359 152.231L213.634 148.234"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
              </g>
            )}
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19_2"
                  d="M131.175 79.2145L205.267 79.2145"
                  stroke="#E55959"
                  stroke-width="1.90315"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M204.149 82.4627L207.356 79.1954L204.053 76.3599"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
                <path
                  id="Vector 28_2"
                  d="M133.221 82.0122L130.027 78.9984L133.34 76.3599"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_3"
                  d="M131.175 79.2146L205.267 79.2146"
                  stroke="#E55959"
                  stroke-width="1.90315"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_5"
                  d="M204.149 82.4628L207.356 79.1956L204.053 76.36"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
                <path
                  id="Vector 28_3"
                  d="M133.221 82.0122L130.027 78.9984L133.34 76.3599"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 19_4"
                  d="M128.891 42.2936L210.596 42.2936"
                  stroke="#EDA7A7"
                  stroke-width="1.90315"
                  stroke-linecap="square"
                  stroke-dasharray="3.81 3.81"
                />
                <path
                  id="Vector 27_6"
                  d="M207.955 45.5417L211.162 42.2744L207.859 39.4389"
                  stroke="#EDA7A7"
                  stroke-width="1.52252"
                />
                <path
                  id="Vector 28_4"
                  d="M130.938 45.0911L127.743 42.0772L131.056 39.4388"
                  stroke="#EDA7A7"
                  stroke-width="1.52252"
                />
              </g>
            )}
            {findMeasure('product_biceps') && (
              <g id="product_biceps">
                <path
                  id="Vector 21_3"
                  d="M231.34 80.1664L211.371 93.3093"
                  stroke="#E55959"
                  stroke-width="1.90315"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M211.422 89.4182L210.67 93.9344L215.002 94.3613"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
                <path
                  id="Vector 27_7"
                  d="M231.365 83.8855L232.061 79.3602L227.724 78.9871"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_5"
                  d="M138.253 38.8678L138.253 166.95"
                  stroke="#E55959"
                  stroke-width="1.90315"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_8"
                  d="M135.208 165.449L138.218 168.865L141.243 165.757"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
                <path
                  id="Vector 28_5"
                  d="M135.202 40.4777L138.157 37.0746L141.129 40.1937"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_6"
                  d="M170.226 23.2026L170.226 168.473"
                  stroke="#E55959"
                  stroke-width="1.90315"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_9"
                  d="M167.181 165.83L170.191 169.245L173.216 166.137"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
                <path
                  id="Vector 28_6"
                  d="M167.175 25.6331L170.13 22.23L173.102 25.3491"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
              </g>
            )}
            {findMeasure('product_sleeve') && (
              <g id="product_sleeve">
                <path
                  id="Vector 21_4"
                  d="M128.57 49.1449L101.025 163.445"
                  stroke="#E55959"
                  stroke-width="1.90315"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M98.4141 160.443L100.657 164.434L104.301 162.053"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
                <path
                  id="Vector 27_10"
                  d="M131.029 51.8844L128.839 47.8634L125.164 50.1955"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_7"
                  d="M169.845 23.2026L169.845 168.473"
                  stroke="#EDA7A7"
                  stroke-width="1.90315"
                  stroke-linecap="square"
                  stroke-dasharray="3.81 3.81"
                />
                <path
                  id="Vector 27_11"
                  d="M166.8 165.83L169.811 169.245L172.836 166.137"
                  stroke="#EDA7A7"
                  stroke-width="1.52252"
                />
                <path
                  id="Vector 28_7"
                  d="M166.795 25.6331L169.75 22.23L172.721 25.3491"
                  stroke="#EDA7A7"
                  stroke-width="1.52252"
                />
              </g>
            )}
            {findMeasure('product_collar') && (
              <g id="product_collar">
                <path
                  id="Vector 464"
                  d="M164.33 47.0514C164.33 47.0514 152.962 33.1689 157.054 26.4056C161.147 19.6423 181.753 20.8882 183.667 27.8294C185.199 33.3825 180.221 42.2459 176.392 47.0514"
                  stroke="#E55959"
                  stroke-width="1.90315"
                />
                <path
                  id="Vector 465"
                  d="M175.872 43.1441L175.973 47.6501L180.252 47.1466"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
                <path
                  id="Vector 28_8"
                  d="M165.113 43.1441L165.011 47.6501L160.733 47.1466"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19_8"
                  d="M132.697 113.471L206.789 113.471"
                  stroke="#E55959"
                  stroke-width="1.90315"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_12"
                  d="M204.529 116.72L207.737 113.452L204.434 110.617"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
                <path
                  id="Vector 28_9"
                  d="M134.744 116.269L131.549 113.255L134.863 110.617"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_9"
                  d="M132.697 113.471L206.789 113.471"
                  stroke="#E55959"
                  stroke-width="1.90315"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_13"
                  d="M204.529 116.72L207.737 113.452L204.434 110.617"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
                <path
                  id="Vector 28_10"
                  d="M134.744 116.269L131.549 113.255L134.863 110.617"
                  stroke="#E55959"
                  stroke-width="1.52252"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4950"
              x1="135.231"
              y1="136.309"
              x2="116.58"
              y2="99.3881"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_207_4950"
              x1="211.928"
              y1="107.191"
              x2="236.478"
              y2="99.9592"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_207_4950"
              x1="175.007"
              y1="129.458"
              x2="151.917"
              y2="87.3321"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#CDCDCD" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_shirt">
            <g id="shirt">
              <g id="Group 50">
                <path
                  id="Vector 99"
                  d="M189.166 255.432L179.017 255.996L172.815 254.022L152.516 245.847L164.357 217.654L194.241 222.729L189.166 255.432Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.563847"
                />
                <g id="Group 49">
                  <g id="Group 47">
                    <path
                      id="Vector 98"
                      d="M110.228 182.978C112.934 173.731 119.061 153.94 118.967 154.503C122.069 149.147 128.327 138.265 128.553 137.588C128.778 136.911 133.158 120.015 135.319 111.651L130.808 79.5118L112.765 64.5698C111.168 66.2614 108.029 70.4902 105.999 73.8733C103.462 78.1022 93.3124 96.4272 89.0836 109.96C85.7005 120.785 83.35 128.848 77.8066 148.583C66.7989 187.77 63.1466 228.649 63.1466 228.649L60.8912 245.283L89.9293 251.203C89.9293 251.203 99.2328 210.888 110.228 182.978Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.563847"
                    />
                    <path
                      id="Vector 101"
                      d="M63.0114 228.957C64.7262 228.975 71.8941 230.534 78.8336 231.766C84.3853 232.752 91.6413 233.277 94.5754 233.416"
                      stroke="black"
                      stroke-width="0.563847"
                    />
                    <path
                      id="Vector 104"
                      d="M99.5148 167.472C102.221 169.502 109.1 173.58 112.201 175.366C111.638 174.144 110.341 171.362 109.664 170.009C108.987 168.656 99.6088 162.303 95.0041 159.296C95.38 161.176 96.8084 165.442 99.5148 167.472Z"
                      fill="url(#paint0_linear_207_4951)"
                    />
                  </g>
                  <path
                    id="Vector 95"
                    d="M226.38 57.2398C221.193 54.0823 208.807 49.3459 203.263 47.3725L189.166 42.8617L181.272 44.8352L167.74 50.7556L148.287 102.348V146.892V188.898L179.581 231.469C181.085 235.697 185.276 242.746 185.501 245.001C185.727 247.256 185.031 254.21 185.501 255.996L206.646 254.868L235.684 253.741V142.381C235.308 133.265 259.76 108.233 254.573 91.6345C250.344 78.1021 243.014 66.8252 241.322 65.4156C238.503 64.006 231.568 60.3973 226.38 57.2398Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.563847"
                  />
                  <path
                    id="Vector 93"
                    d="M173.942 83.4587C173.717 81.6544 176.198 77.3504 177.889 75.2829V64.8518L166.331 55.5483L152.798 46.5267C147.254 48.7821 135.037 53.5748 125.734 57.5218C118.116 60.7534 113.611 63.4421 112.483 64.8518L115.584 68.5168C116.618 74.5311 118.685 88.8717 118.685 98.1188V155.631L119.531 254.022C125.452 256.184 179.017 255.996 179.017 255.996C178.829 254.68 178.453 250.921 178.453 246.411C178.453 241.9 178.829 234.946 179.017 232.032L173.379 212.862L172.251 176.494C172.627 146.328 174.168 85.263 173.942 83.4587Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.563847"
                  />
                  <g id="Group 48">
                    <path
                      id="Vector 94"
                      d="M156.463 48.2181C156.463 47.7671 159.846 45.7748 161.538 44.8351C163.041 44.5531 166.105 43.8765 166.331 43.4254C166.556 42.9744 171.123 41.7339 173.379 41.1701C179.017 41.264 190.407 41.3956 190.858 41.1701C191.309 40.9445 196.308 46.1507 198.752 48.782L196.496 57.5216L189.448 67.9528L180.991 74.437L176.198 75.2828L169.996 72.1817C167.364 69.7383 162.102 64.7389 162.102 64.2878C162.102 63.7239 156.463 48.782 156.463 48.2181Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.563847"
                    />
                    <path
                      id="Vector 92"
                      d="M160.41 46.2447L159.001 48.5001H157.027L157.873 41.452L160.41 37.2231L161.256 36.3774C167.74 34.404 184.092 34.9678 187.757 35.5316C191.422 36.0955 193.677 36.6593 193.677 36.6593L196.778 40.3243L198.47 45.6809L196.778 49.064L194.241 46.8085C190.294 46.9965 180.991 47.3724 175.352 47.3724C169.714 47.3724 163.041 46.6206 160.41 46.2447Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.563847"
                    />
                    <path
                      id="Vector 90"
                      d="M161.82 57.8036C156.463 44.5532 158.625 39.1966 160.974 36.6593C158.343 39.0087 152.347 45.9064 151.671 46.8086C150.825 47.9362 153.08 56.112 154.49 61.4686C155.617 65.7538 160.128 76.4105 163.229 79.7936L170.559 73.3094L176.48 75.2828C176.48 75.2828 166.487 69.3496 161.82 57.8036Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.563847"
                    />
                    <path
                      id="Vector 91"
                      d="M192.831 57.8036C198.188 44.5532 196.027 39.1966 193.677 36.6593C196.308 39.0087 202.304 45.9064 202.981 46.8086C203.826 47.9362 201.571 56.112 200.161 61.4686C199.034 65.7538 194.523 76.4105 191.422 79.7936L184.092 73.3094L178.171 75.2828C178.171 75.2828 188.164 69.3496 192.831 57.8036Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.563847"
                    />
                  </g>
                  <g id="Group 46">
                    <path
                      id="Vector 96"
                      d="M251.471 79.2297C246.735 70.4336 242.92 66.3552 241.604 65.4154C240.476 67.013 238.052 70.7156 237.375 72.7454C236.53 75.2827 233.71 84.3043 233.428 93.6078C233.203 101.051 234.838 128.66 235.684 141.535L245.551 162.961C242.262 170.385 235.345 185.853 233.992 188.334C232.639 190.815 227.226 202.9 224.689 208.633L217.077 224.984C218.392 225.924 222.49 228.649 228.354 232.032C234.218 235.415 240.946 236.261 243.578 236.261C245.833 233.348 251.19 225.717 254.573 218.5C258.801 209.478 278.254 177.339 280.228 167.754C282.201 158.168 280.792 146.891 276.281 133.923C271.77 120.954 257.392 90.2247 251.471 79.2297Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.563847"
                    />
                    <path
                      id="Vector 105"
                      d="M269.233 156.759C264.722 157.605 250.062 159.578 248.934 159.578C257.298 157.135 274.307 152.248 275.435 152.248C276.563 152.248 277.972 153.564 278.536 154.222C276.939 154.785 272.841 156.082 269.233 156.759Z"
                      fill="url(#paint1_linear_207_4951)"
                    />
                    <path
                      id="Vector 100"
                      d="M224.971 208.351C228.354 210.23 235.345 214.158 238.503 215.963C241.661 217.767 249.216 220.943 252.317 222.165"
                      stroke="black"
                      stroke-width="0.563847"
                    />
                  </g>
                  <g id="Group 45">
                    <path
                      id="Vector 97"
                      d="M184.092 247.256C183.19 242.746 179.393 232.032 177.608 227.24C179.205 229.307 183.021 234.175 185.501 237.107C187.982 240.039 192.55 247.162 194.523 250.357C193.865 250.639 192.211 251.372 190.858 252.049C189.505 252.726 186.723 254.586 185.501 255.714C185.407 254.586 184.994 251.767 184.092 247.256Z"
                      fill="white"
                      stroke="black"
                      stroke-width="0.563847"
                    />
                    <g id="Group 44">
                      <path
                        id="Vector 102"
                        d="M172.533 241.618C172.533 245.226 172.815 252.707 172.815 255.996H179.017C179.017 254.398 178.735 250.245 178.735 245.283C178.735 240.321 179.017 234.476 179.017 232.032L177.044 225.266L173.379 213.144C173.379 220.755 172.533 238.009 172.533 241.618Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.563847"
                      />
                      <path
                        id="Ellipse 1"
                        d="M175.011 218.766L176.053 222.153C176.052 222.154 176.051 222.154 176.05 222.154C175.946 222.175 175.777 222.192 175.515 222.192C174.58 222.192 173.823 221.435 173.823 220.501C173.823 220.053 174.087 219.548 174.427 219.181C174.594 219.001 174.764 218.872 174.902 218.807C174.949 218.784 174.985 218.772 175.011 218.766Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.563847"
                      />
                    </g>
                    <g id="Group 43">
                      <path
                        id="Vector 103"
                        d="M180.427 78.9479C181.273 75.5648 184.767 72.9942 184.767 72.9942L186.629 67.9529L184.092 70.7721L178.735 75.001L176.762 76.4106C176.104 76.8805 174.619 78.666 173.943 82.0491C173.266 85.4321 172.909 121.8 172.815 139.561L172.251 175.93L172.815 211.17C173.191 213.707 177.89 227.24 177.89 227.24L179.581 229.495V208.633V162.115C179.487 146.046 179.243 111.425 179.017 101.502C178.735 89.0972 179.581 82.331 180.427 78.9479Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.563847"
                      />
                      <g id="Group 42">
                        <circle
                          id="Ellipse 2"
                          cx="176.48"
                          cy="89.3788"
                          r="1.40962"
                          fill="white"
                          stroke="black"
                          stroke-width="0.563847"
                        />
                        <path id="Vector 135" d="M177.608 89.3788H178.735" stroke="black" stroke-width="0.563847" />
                        <path id="Vector 136" d="M177.608 113.061H178.735" stroke="black" stroke-width="0.563847" />
                        <circle
                          id="Ellipse 3"
                          cx="176.48"
                          cy="113.06"
                          r="1.40962"
                          fill="white"
                          stroke="black"
                          stroke-width="0.563847"
                        />
                        <circle
                          id="Ellipse 4"
                          cx="175.916"
                          cy="136.742"
                          r="1.40962"
                          fill="white"
                          stroke="black"
                          stroke-width="0.563847"
                        />
                        <path id="Vector 137" d="M177.044 136.742H178.172" stroke="black" stroke-width="0.563847" />
                        <circle
                          id="Ellipse 5"
                          cx="175.916"
                          cy="160.424"
                          r="1.40962"
                          fill="white"
                          stroke="black"
                          stroke-width="0.563847"
                        />
                        <path id="Vector 138" d="M177.044 160.424H178.172" stroke="black" stroke-width="0.563847" />
                        <path id="Vector 139" d="M177.044 185.233H178.172" stroke="black" stroke-width="0.563847" />
                        <path id="Vector 140" d="M177.608 211.17H178.735" stroke="black" stroke-width="0.563847" />
                        <circle
                          id="Ellipse 6"
                          cx="175.916"
                          cy="185.233"
                          r="1.40962"
                          fill="white"
                          stroke="black"
                          stroke-width="0.563847"
                        />
                        <circle
                          id="Ellipse 7"
                          cx="176.48"
                          cy="211.17"
                          r="1.40962"
                          fill="white"
                          stroke="black"
                          stroke-width="0.563847"
                        />
                      </g>
                    </g>
                  </g>
                </g>
                <path
                  id="Vector 41"
                  d="M194.815 138.81C198.807 134.341 205.09 120.559 205.47 119.827C205.87 121.185 206.597 128.848 206.597 128.848C206.477 129.332 203.12 134.953 196.784 140.007C191.715 144.051 183.225 148.262 180.667 148.818C183.317 147.614 191.191 142.868 194.815 138.81Z"
                  fill="url(#paint2_linear_207_4951)"
                  fill-opacity="0.87"
                />
              </g>
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19"
                  d="M120.289 121.236L230.045 121.236"
                  stroke="#E55959"
                  stroke-width="2.81924"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27"
                  d="M228.389 126.048L233.14 121.208L228.247 117.007"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
                <path
                  id="Vector 28"
                  d="M123.321 125.381L118.589 120.916L123.497 117.007"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19_2"
                  d="M122.544 171.983L232.301 171.983"
                  stroke="#E55959"
                  stroke-width="2.81924"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M228.953 176.794L233.704 171.954L228.811 167.754"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
                <path
                  id="Vector 28_2"
                  d="M125.576 176.127L120.844 171.662L125.752 167.754"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
              </g>
            )}
            {findMeasure('product_sleeve') && (
              <g id="product_sleeve">
                <path
                  id="Vector 21"
                  d="M116.43 76.6924L75.627 246.01"
                  stroke="#E55959"
                  stroke-width="2.81924"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M71.759 241.563L75.0815 247.476L80.4801 243.949"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
                <path
                  id="Vector 27_3"
                  d="M120.073 80.7507L116.83 74.7942L111.385 78.249"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
              </g>
            )}
            {findMeasure('product_biceps') && (
              <g id="product_biceps">
                <path
                  id="Vector 21_2"
                  d="M268.669 122.646L239.088 142.116"
                  stroke="#E55959"
                  stroke-width="2.81924"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M239.163 136.351L238.049 143.042L244.467 143.674"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
                <path
                  id="Vector 27_4"
                  d="M268.706 128.156L269.737 121.452L263.312 120.899"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_3"
                  d="M178.137 38.2628L178.137 253.458"
                  stroke="#E55959"
                  stroke-width="2.81924"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_5"
                  d="M173.627 249.544L178.086 254.603L182.567 249.999"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
                <path
                  id="Vector 28_3"
                  d="M173.618 41.8633L177.996 36.8221L182.398 41.4426"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
              </g>
            )}
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 19_4"
                  d="M122.544 247.538L232.301 247.538"
                  stroke="#E55959"
                  stroke-width="2.81924"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M228.953 252.35L233.704 247.51L228.811 243.309"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
                <path
                  id="Vector 28_4"
                  d="M125.576 251.682L120.844 247.218L125.752 243.309"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
              </g>
            )}
            {findMeasure('product_collar') && (
              <g id="product_collar">
                <path
                  id="Vector 464"
                  d="M169.403 73.5915C169.403 73.5915 152.563 53.0266 158.626 43.0078C164.688 32.989 195.212 34.8345 198.049 45.117C200.318 53.343 192.944 66.4729 187.271 73.5915"
                  stroke="#E55959"
                  stroke-width="2.81924"
                />
                <path
                  id="Vector 465"
                  d="M186.501 67.8033L186.652 74.4783L192.989 73.7324"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
                <path
                  id="Vector 28_5"
                  d="M170.564 67.8033L170.413 74.4783L164.075 73.7324"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
              </g>
            )}
            {findMeasure('product_wrist_width') && (
              <g id="product_wrist_width">
                <path
                  id="Vector 21_3"
                  d="M243.876 228.64L223.907 218.302"
                  stroke="#E55959"
                  stroke-width="2.81924"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M229.02 215.639L222.6 217.826L225.082 223.778"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
                <path
                  id="Vector 27_7"
                  d="M238.603 231.665L244.996 229.399L242.44 223.478"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
              </g>
            )}
            {findMeasure('product_wrist_circumference') && (
              <g id="product_wrist_circumference">
                <path
                  id="Vector 21_4"
                  d="M243.876 228.64L223.907 218.302"
                  stroke="#E55959"
                  stroke-width="2.81924"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M229.02 215.639L222.6 217.826L225.082 223.778"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
                <path
                  id="Vector 27_8"
                  d="M238.603 231.665L244.996 229.399L242.44 223.478"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_5"
                  d="M130.774 61.4686L130.774 251.203"
                  stroke="#E55959"
                  stroke-width="2.81924"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_9"
                  d="M126.263 248.98L130.723 254.04L135.204 249.436"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
                <path
                  id="Vector 28_6"
                  d="M126.255 63.8536L130.633 58.8124L135.035 63.4328"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_6"
                  d="M120.289 121.236L230.045 121.236"
                  stroke="#E55959"
                  stroke-width="2.81924"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_10"
                  d="M228.389 126.048L233.14 121.208L228.247 117.007"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
                <path
                  id="Vector 28_7"
                  d="M123.321 125.381L118.589 120.916L123.497 117.007"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_7"
                  d="M122.544 171.983L232.301 171.983"
                  stroke="#E55959"
                  stroke-width="2.81924"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_11"
                  d="M228.953 176.794L233.704 171.954L228.811 167.754"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
                <path
                  id="Vector 28_8"
                  d="M125.576 176.127L120.844 171.662L125.752 167.754"
                  stroke="#E55959"
                  stroke-width="2.25539"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_8"
                  d="M177.573 38.2628L177.573 253.458"
                  stroke="#EDA7A7"
                  stroke-width="2.81924"
                  stroke-linecap="square"
                  stroke-dasharray="5.64 5.64"
                />
                <path
                  id="Vector 27_12"
                  d="M173.063 249.544L177.522 254.603L182.004 249.999"
                  stroke="#EDA7A7"
                  stroke-width="2.25539"
                />
                <path
                  id="Vector 28_9"
                  d="M173.055 41.8633L177.432 36.8221L181.834 41.4426"
                  stroke="#EDA7A7"
                  stroke-width="2.25539"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 19_9"
                  d="M116.906 66.5431L237.939 66.5431"
                  stroke="#EDA7A7"
                  stroke-width="2.81924"
                  stroke-linecap="square"
                  stroke-dasharray="5.64 5.64"
                />
                <path
                  id="Vector 27_13"
                  d="M234.027 71.3546L238.779 66.5147L233.886 62.3143"
                  stroke="#EDA7A7"
                  stroke-width="2.25539"
                />
                <path
                  id="Vector 28_10"
                  d="M119.938 70.6874L115.206 66.2228L120.113 62.3143"
                  stroke="#EDA7A7"
                  stroke-width="2.25539"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4951"
              x1="126.298"
              y1="205.814"
              x2="98.6691"
              y2="151.12"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_207_4951"
              x1="239.913"
              y1="162.679"
              x2="276.281"
              y2="151.966"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_207_4951"
              x1="185.22"
              y1="195.664"
              x2="151.016"
              y2="133.261"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#CDCDCD" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
