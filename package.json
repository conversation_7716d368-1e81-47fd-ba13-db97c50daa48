{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "preview:test": "vite preview --port 3000", "prepare": "husky", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "cy:open": "cypress open", "cy:run": "./scripts/clear_tests.sh && cypress run", "coverage": "nyc report --reporter=text-summary", "coverage:html": "nyc report --reporter=html", "coverage:check": "./scripts/check_minimum_coverage.sh", "format": "prettier . --write"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@tailwindcss/vite": "^4.0.4", "@tanstack/react-query": "^5.66.0", "axios": "^1.7.9", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "react": "^18.3.0", "react-dom": "^18.3.0", "react-i18next": "^15.4.1", "tailwindcss": "^4.0.4", "vite-plugin-svgr": "^4.3.0"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.4", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@cypress/code-coverage": "^3.13.11", "@eslint/js": "^9.19.0", "@storybook/addon-essentials": "^8.5.3", "@storybook/addon-interactions": "^8.5.3", "@storybook/addon-onboarding": "^8.5.3", "@storybook/blocks": "^8.5.3", "@storybook/react": "^8.5.3", "@storybook/react-vite": "^8.5.3", "@storybook/test": "^8.5.3", "@tanstack/eslint-plugin-query": "^5.66.0", "@types/node": "^22.13.5", "@types/react": "18.3.2", "@types/react-dom": "18.3.2", "@vitejs/plugin-react": "^4.3.4", "clsx": "^2.1.1", "cypress": "^14.0.2", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "eslint-plugin-storybook": "^0.11.2", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.14.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "nyc": "10.2.2-candidate.3", "prettier": "^3.4.2", "storybook": "^8.5.3", "tailwind-merge": "^3.0.1", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0", "vite-plugin-istanbul": "^6.0.2", "vite-tsconfig-paths": "^5.1.4"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "pnpm": {"onlyBuiltDependencies": ["cypress", "esbuild"]}}