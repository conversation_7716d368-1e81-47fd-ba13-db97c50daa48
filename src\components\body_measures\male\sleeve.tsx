import { useDevice } from '@/hooks/use-device';
import { BodyMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface SleeveProps {
  measure: BodyMeasure;
  className?: string;
}

export function Sleeve({ measure, className }: SleeveProps) {
  const { measures } = measure;
  const mappedMeasures = measures.map((item) => item.measure);
  const { isMobile } = useDevice();

  const findMeasure = (measure: string) => {
    const foundMeasure = mappedMeasures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full flex justify-center', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_sleeve">
            <mask id="mask0_128_1845" maskUnits="userSpaceOnUse" x="0" y="0" width="331" height="193">
              <rect id="rect" width="331" height="193" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_1845)">
              <g id="group_sleeve_mask">
                <g id="male">
                  <g id="Group 20">
                    <path
                      id="Intersect"
                      d="M229.785 150.3C224.983 152.87 219.68 145.947 215.804 144.54C213.597 143.739 212.096 144.939 211.083 145.368L210.699 145.148L208.644 135.947L207.353 130.908L205.357 137.7L203.386 145.368L200.976 153.036L199.88 158.513L199.442 162.895L200.318 169.686L201.414 181.736L201.195 185.899L201.414 190.938L202.947 206.055L205.576 232.784L206.234 244.395L206.015 250.091L204.919 271.781H166.579L164.607 244.833H162.854L160.883 271.781H122.542L121.447 250.091L121.228 244.395L121.885 232.784L124.514 206.055L126.048 190.938L126.267 185.899L126.048 181.736L127.143 169.686L128.02 162.895L127.581 158.513L126.486 153.036L124.076 145.368L122.104 137.7L120.109 130.908L118.818 135.947L114.217 156.541L113.779 160.485C113.56 162.164 113.122 165.568 113.122 165.743C113.122 165.918 112.318 173.849 111.588 177.793L109.835 183.708L104.796 195.758L102.606 202.111L101.072 210.656C100.926 212.116 100.634 215.081 100.634 215.257C100.634 215.432 100.78 217.813 100.853 218.981L102.386 220.734C102.971 221.756 104.183 223.889 104.358 224.239C104.577 224.677 105.673 229.278 105.673 230.154C105.673 230.88 105.823 231.606 105.999 232.457C106.036 232.633 106.073 232.815 106.111 233.003C106.286 233.879 108.667 241.255 109.835 244.833L107.645 246.805H106.768L104.796 243.519L101.729 235.193L98.0047 238.918L96.9093 244.833V250.53L97.7856 252.501L101.729 259.293V260.608C101.729 260.827 101.072 261.703 100.853 261.922C100.678 262.097 100.78 262.433 100.853 262.579L101.072 264.113L100.415 264.551L98.881 263.456L94.4993 257.759L91.213 252.501L90.1176 243.738L89.4603 240.671L89.2412 237.603L90.3366 225.773L89.8985 223.363L89.4603 215.257L89.6794 208.465L89.4603 202.111V193.129L89.6794 184.584L90.5557 179.984L91.8703 171.22L93.623 165.086L94.2802 159.608L97.5665 147.997L98.0047 141.862L100.853 122.583L100.196 118.42V112.505L100.634 108.342L101.291 103.303L103.263 98.045L105.673 94.1014L107.864 91.0342L110.493 88.6243L113.341 86.6525L118.38 84.2425L124.076 80.9562L129.553 78.1081L137.879 74.1645L143.356 71.7546L149.928 68.4683V54.6658L160.883 38.3784V29.9089L163.731 34.1437L166.579 29.9089V38.3784L177.533 54.6658V68.4683L184.106 71.7546L189.583 74.1645L197.908 78.1081L203.386 80.9562L209.082 84.2425L214.121 86.6525L216.969 88.6243L219.598 91.0342L221.789 94.1014L224.199 98.045L226.171 103.303L226.828 108.342L227.266 112.505V118.42L226.609 122.583L229.457 141.862L228.837 143.93L229.785 150.3Z"
                      fill="white"
                    />
                    <path
                      id="Vector 18"
                      d="M164.585 66.496C170.99 63.7793 174.225 57.9516 175.32 56.1989V54.6653C172.691 52.9126 167.346 49.4072 166.995 49.4072C166.644 49.4072 155.602 52.4744 150.125 54.0081V64.9624C154.507 67.8105 160.519 68.2206 164.585 66.496Z"
                      fill="url(#paint0_linear_128_1845)"
                    />
                    <g id="Group 24">
                      <g id="Group 22">
                        <path
                          id="Vector 16"
                          d="M144.21 139.89C153.192 138.137 153.85 135.508 156.041 132.879C154.945 134.194 147.365 133.624 146.839 133.974C146.313 134.325 141.8 133.974 139.171 133.755C137.199 134.121 132.861 132.222 132.16 132.222C131.459 132.222 128.655 130.615 127.34 130.25L121.644 126.526L118.577 121.048C119.234 127.402 121.425 136.384 121.425 136.384C121.425 136.384 135.502 141.589 144.21 139.89Z"
                          fill="url(#paint1_linear_128_1845)"
                        />
                        <path
                          id="Vector 36"
                          d="M122.323 126.963C123.419 128.059 127.362 130.775 134.373 132.879C141.384 134.982 151.754 133.755 156.063 132.879"
                          stroke="black"
                          stroke-width="0.438174"
                          stroke-linecap="round"
                        />
                      </g>
                      <g id="Group 23">
                        <path
                          id="Vector 16_2"
                          d="M183.01 139.89C174.028 138.137 173.371 135.508 171.18 132.879C172.275 134.194 179.856 133.624 180.381 133.974C180.907 134.325 185.42 133.974 188.049 133.755C190.021 134.121 194.359 132.222 195.06 132.222C195.761 132.222 198.566 130.615 199.88 130.25L205.576 126.526L208.644 121.048C208.402 128.935 205.795 136.384 205.795 136.384C205.795 136.384 191.718 141.589 183.01 139.89Z"
                          fill="url(#paint2_linear_128_1845)"
                        />
                        <path
                          id="Vector 36_2"
                          d="M204.897 126.963C203.802 128.059 199.858 130.775 192.847 132.879C185.836 134.982 175.466 133.755 171.158 132.879"
                          stroke="black"
                          stroke-width="0.438174"
                          stroke-linecap="round"
                        />
                      </g>
                    </g>
                    <path
                      id="Vector 20"
                      d="M114.414 155.664C114.414 155.664 113.173 161.361 113.976 159.608L113.1 167.933C112.881 170.051 112.004 175.382 112.004 175.382C112.004 175.382 111.42 178.961 110.69 181.298L106.527 191.595C106.527 191.595 103.46 198.825 103.46 199.044C103.46 199.663 101.926 206.566 101.269 208.245L100.612 214.599L100.831 218.981L102.365 221.171C102.584 221.902 103.898 223.581 103.898 223.581C103.898 223.581 105.651 229.278 105.651 229.716C105.651 230.066 106.089 231.98 106.089 233.002L107.185 236.069C108.061 240.013 109.945 244.351 109.594 245.052C109.156 245.928 108.353 246.512 108.061 246.586L106.965 247.024L105.87 245.709L104.994 244.176C104.409 241.181 101.707 234.974 101.707 234.974L97.9829 238.479L96.6683 244.176V250.31L99.0783 254.692C100.101 256.371 101.926 259.599 101.926 259.95C101.926 260.3 100.831 261.703 100.831 261.703L100.612 264.332H100.174L96.6683 260.607L93.382 256.225L91.4102 252.501L89.8766 242.642L89.4385 239.575V234.755L89.8766 229.059L90.3148 225.772L89.4385 217.228V211.312V199.482C89.2194 195.538 89.8767 184.452 89.8767 183.927C89.8767 183.401 91.0452 174.068 92.0676 170.343C92.5058 168.664 96.6684 150.406 96.6684 150.406L99.2975 133.537C100.977 140.766 114.239 153.693 114.414 154.569C114.633 155.664 114.414 155.664 114.414 155.664Z"
                      fill="url(#paint3_linear_128_1845)"
                    />
                    <path
                      id="Vector 17"
                      d="M156.698 239.794L160.86 244.176L163.051 245.052L161.518 260.826L161.079 272H122.52L121.425 252.282L121.206 243.08L121.644 236.07L122.52 225.334L123.835 210.656L126.026 192.69L152.316 230.373L154.507 234.536C156.917 239.94 156.698 239.794 156.698 239.794Z"
                      fill="url(#paint4_linear_128_1845)"
                    />
                    <g id="Group 12">
                      <g id="Intersect_2">
                        <path
                          d="M229.514 150.435C229.514 150.435 230.073 150.502 230.192 150.435L227.049 123.679C227.706 119.05 228.404 107.947 225.06 99.4975C222.964 94.2043 220.421 91.1585 217.347 88.8325C215.815 87.6726 214.154 86.6943 212.361 85.707C211.761 85.377 211.148 85.0464 210.52 84.7077L210.52 84.7076C209.264 84.0306 207.948 83.3211 206.562 82.5189C199.932 78.6803 184.677 71.6417 177.752 68.5448V54.0081H177.314V68.829L177.444 68.8869C184.311 71.9552 199.694 79.0489 206.343 82.8981C207.734 83.7035 209.061 84.4189 210.32 85.0978L210.32 85.0979C210.947 85.4357 211.557 85.7645 212.149 86.0908C213.937 87.0753 215.576 88.0414 217.083 89.1819C220.089 91.4567 222.585 94.4358 224.652 99.6588C227.961 108.017 227.267 119.056 226.611 123.647L226.607 123.677L229.514 150.435Z"
                          fill="black"
                        />
                        <path
                          d="M211.219 145.919L207.709 130.469C208.163 128.375 208.483 126.575 208.642 125.237L208.209 125.173L207.27 130.427C206.255 135.102 204.572 141.258 202.522 147.047C199.534 155.482 199.22 161.708 199.443 163.794L199.443 163.796L199.444 163.798C199.772 166.425 200.254 170.575 200.631 174.483C201.01 178.399 201.281 182.051 201.195 183.696C201.019 187.036 201.122 190.214 201.195 191.389L201.195 191.395L202.51 202.573L202.51 202.574C203.387 210.681 205.27 229.253 205.796 238.711C206.316 248.07 205.304 264.37 204.717 271.561H166.782L164.811 244.614H162.651L160.679 271.561H122.744C122.157 264.37 121.146 248.07 121.666 238.711C122.191 229.253 124.074 210.681 124.951 202.574L124.951 202.573L126.266 191.395L126.266 191.389C126.34 190.214 126.442 187.036 126.267 183.696C126.18 182.051 126.452 178.399 126.83 174.483C127.208 170.575 127.689 166.425 128.018 163.798L128.018 163.796L128.018 163.794C128.242 161.708 127.927 155.482 124.94 147.047C122.89 141.258 121.206 135.102 120.191 130.427L119.253 125.173L118.819 125.237C118.978 126.575 119.298 128.375 119.752 130.469L114 156.506L113.999 156.519C113.93 157.219 113.854 158.045 113.77 158.968L113.77 158.969C113.54 161.475 113.245 164.693 112.844 168.011C112.294 172.554 111.548 177.265 110.503 180.574C109.295 184.398 107.995 187.526 106.706 190.63C106.595 190.896 106.484 191.162 106.374 191.429C104.977 194.799 103.603 198.204 102.395 202.49C100.459 209.353 100.268 216.345 100.415 218.993L100.42 219.091L100.497 219.152C101.192 219.708 102.938 221.674 104.153 224.972C104.915 227.04 105.184 228.338 105.291 229.222C105.341 229.634 105.356 229.959 105.369 230.237L105.372 230.298C105.385 230.586 105.399 230.84 105.46 231.084C105.635 231.785 108.133 240.476 109.382 244.81C109.199 245.153 108.881 245.624 108.476 245.997C108.038 246.402 107.536 246.662 107.018 246.588C106.993 246.584 106.945 246.567 106.873 246.512C106.802 246.457 106.721 246.376 106.629 246.265C106.445 246.043 106.238 245.722 106.014 245.32C105.567 244.516 105.068 243.415 104.566 242.192C103.563 239.747 102.557 236.839 101.938 234.907L101.828 234.565L97.8039 238.589L96.471 244.81V250.589L96.5018 250.641C97.3043 251.991 98.5189 254.081 99.5544 255.964C100.072 256.906 100.544 257.794 100.897 258.511C101.074 258.87 101.219 259.183 101.326 259.438C101.435 259.699 101.496 259.882 101.513 259.986C101.589 260.441 101.448 260.78 101.243 261.025C101.032 261.278 100.751 261.432 100.564 261.495L100.338 261.57L100.432 261.789C100.533 262.025 100.658 262.439 100.695 262.897C100.731 263.354 100.677 263.828 100.451 264.211C100.419 264.21 100.353 264.195 100.244 264.136C100.031 264.021 99.7394 263.788 99.3846 263.451C98.6802 262.78 97.777 261.742 96.8344 260.553C94.9481 258.175 92.9324 255.231 92.0661 253.498C91.2103 251.787 90.829 249.143 90.5544 246.617C90.5034 246.148 90.456 245.683 90.4099 245.229C90.3327 244.471 90.2589 243.746 90.1774 243.094C90.0476 242.056 89.8946 241.166 89.6636 240.589C89.5883 240.4 89.5292 240.093 89.4908 239.674C89.4529 239.26 89.4365 238.753 89.4383 238.175C89.4419 237.02 89.5182 235.596 89.6348 234.087C89.8681 231.071 90.2619 227.734 90.5537 225.583L90.5578 225.552L90.5533 225.521C90.2602 223.543 89.6793 219.224 89.6793 217.666V211.546C89.863 210.118 89.7748 207.932 89.6607 205.102C89.6385 204.551 89.6154 203.977 89.593 203.379C89.4549 199.681 89.3456 195.052 89.6789 189.636C90.4346 177.356 91.8416 171.772 93.1994 166.384L93.1994 166.384C93.4137 165.534 93.6268 164.688 93.8358 163.822C94.0965 162.742 94.4128 161.23 94.7647 159.548C94.9972 158.436 95.2453 157.25 95.5031 156.066C96.1533 153.078 96.8651 150.101 97.5521 148.293L97.5611 148.27L100.855 123.677L100.851 123.647C100.195 119.056 99.5007 108.017 102.809 99.6588C104.877 94.4358 107.372 91.4567 110.378 89.1819C111.885 88.0414 113.524 87.0753 115.312 86.0908C115.905 85.7645 116.515 85.4356 117.141 85.0978C118.401 84.4189 119.728 83.7034 121.119 82.8981C127.767 79.0489 143.151 71.9552 150.018 68.8869L150.147 68.829V54.0081H149.709V68.5448C142.784 71.6417 127.529 78.6803 120.899 82.5189C119.513 83.3211 118.197 84.0306 116.942 84.7076C116.313 85.0463 115.7 85.377 115.101 85.707C113.308 86.6943 111.647 87.6726 110.114 88.8325C107.04 91.1585 104.497 94.2043 102.402 99.4975C99.0572 107.947 99.7551 119.05 100.412 123.679L97.1335 148.161C96.4358 150.007 95.7212 153.003 95.0749 155.973C94.8149 157.167 94.566 158.357 94.3333 159.47C93.9829 161.145 93.6693 162.645 93.4099 163.719C93.2012 164.584 92.9883 165.429 92.7741 166.279C91.4145 171.674 89.9998 177.288 89.2415 189.609C88.907 195.046 89.0168 199.692 89.1552 203.395C89.1778 204 89.201 204.578 89.2232 205.131C89.3376 207.974 89.4238 210.117 89.243 211.503L89.2411 211.517V217.666C89.2411 219.255 89.8226 223.575 90.1153 225.555C89.8232 227.713 89.431 231.041 89.198 234.053C89.0809 235.566 89.0038 237.003 89.0001 238.174C88.9983 238.759 89.0148 239.281 89.0545 239.714C89.0937 240.143 89.1569 240.502 89.2568 240.752C89.464 241.27 89.6122 242.105 89.7426 243.148C89.8236 243.797 89.8964 244.512 89.9731 245.265L89.9731 245.265C90.0193 245.72 90.067 246.188 90.1188 246.664C90.3918 249.176 90.7773 251.901 91.6742 253.694C92.5606 255.467 94.5981 258.439 96.491 260.825C97.438 262.019 98.356 263.076 99.0825 263.768C99.4432 264.111 99.7694 264.377 100.036 264.521C100.168 264.592 100.307 264.646 100.442 264.649C100.594 264.652 100.736 264.588 100.822 264.444C101.117 263.952 101.172 263.371 101.131 262.863C101.1 262.465 101.009 262.097 100.914 261.823C101.131 261.717 101.378 261.547 101.58 261.306C101.856 260.975 102.045 260.51 101.945 259.914C101.919 259.755 101.839 259.531 101.73 259.27C101.619 259.003 101.469 258.68 101.29 258.317C100.933 257.592 100.457 256.697 99.9383 255.753C98.9129 253.888 97.7134 251.823 96.9092 250.469V244.856L98.2053 238.808L101.631 235.382C102.258 237.311 103.21 240.039 104.161 242.358C104.665 243.587 105.171 244.707 105.631 245.533C105.861 245.945 106.083 246.292 106.291 246.544C106.395 246.67 106.501 246.778 106.606 246.859C106.71 246.939 106.828 247.003 106.956 247.022C107.665 247.123 108.295 246.761 108.774 246.319C109.255 245.874 109.621 245.314 109.812 244.931L109.85 244.854L109.827 244.772C108.584 240.459 106.059 231.673 105.885 230.977C105.837 230.783 105.823 230.571 105.809 230.277L105.806 230.216C105.794 229.937 105.778 229.598 105.726 229.169C105.614 228.245 105.335 226.914 104.564 224.82C103.365 221.566 101.65 219.554 100.847 218.873C100.712 216.192 100.919 209.335 102.816 202.609C104.018 198.349 105.383 194.963 106.779 191.596C106.889 191.33 107 191.063 107.111 190.796C108.4 187.694 109.707 184.549 110.921 180.706C111.979 177.355 112.729 172.609 113.279 168.064C113.681 164.738 113.977 161.51 114.207 159.003L114.207 159.002C114.291 158.087 114.366 157.268 114.434 156.575L119.977 131.483C121.01 136.038 122.608 141.775 124.527 147.193C127.498 155.581 127.797 161.739 127.583 163.745C127.254 166.376 126.772 170.529 126.394 174.441C126.016 178.346 125.74 182.034 125.829 183.719C126.003 187.034 125.902 190.19 125.829 191.356L124.516 202.523L124.515 202.526C123.639 210.631 121.754 229.215 121.228 238.686C120.701 248.168 121.74 264.715 122.324 271.799L122.341 272H161.086L163.058 245.052H164.403L166.375 272H205.121L205.137 271.799C205.722 264.715 206.76 248.168 206.233 238.686C205.707 229.215 203.822 210.631 202.946 202.526L201.632 191.356C201.559 190.19 201.458 187.034 201.632 183.719C201.721 182.034 201.445 178.346 201.068 174.441C200.69 170.529 200.207 166.376 199.879 163.746C199.664 161.74 199.964 155.582 202.935 147.193C204.854 141.775 206.452 136.038 207.485 131.483L210.674 145.919H211.219Z"
                          fill="black"
                        />
                      </g>
                    </g>
                    <g id="Group 19">
                      <g id="Group 11">
                        <path
                          id="Vector 4"
                          d="M143.326 34.1979C144.027 31.2183 145.371 33.5406 145.955 35.0742L146.831 38.7987L147.269 46.4668C147.196 46.5398 146.831 47.3431 145.955 46.6858C144.833 45.8441 142.449 37.9224 143.326 34.1979Z"
                          fill="white"
                          stroke="black"
                          stroke-width="0.438174"
                        />
                        <path
                          id="Vector 5"
                          d="M184.325 34.1979C183.623 31.2183 182.28 33.5406 181.696 35.0742L180.819 38.7987L180.381 46.4668C180.454 46.5398 180.819 47.3431 181.696 46.6858C182.818 45.8441 185.201 37.9224 184.325 34.1979Z"
                          fill="white"
                          stroke="black"
                          stroke-width="0.438174"
                        />
                        <path
                          id="Vector"
                          d="M147.299 18.2971C149.602 11.7719 157.596 8 163.512 8C170.01 7.99999 178.297 11.7719 180.6 18.2971C183.23 25.746 182.999 37.7555 179.724 49.1884C178.183 54.5668 176.705 58.3363 174.904 60.3618C173.152 62.3336 166.217 64.5245 163.95 64.5245C162.197 64.5245 154.748 62.3336 152.995 60.3618C151.195 58.3363 149.716 54.5668 148.176 49.1884C144.901 37.7555 144.67 25.746 147.299 18.2971Z"
                          fill="white"
                          stroke="black"
                          stroke-width="0.438174"
                        />
                      </g>
                      <g id="Group 18">
                        <g id="Group 14">
                          <path
                            id="Vector 32"
                            d="M161.54 66.9341C161.54 67.1636 161.97 68.504 162.735 69.2752C163.499 70.0464 164.009 71.2491 164.169 71.754"
                            stroke="black"
                            stroke-width="0.438174"
                            stroke-linecap="round"
                          />
                          <g id="Group 13">
                            <path
                              id="Vector 31"
                              d="M160.882 85.5567C160.882 85.5567 160.181 83.7602 158.253 82.7086C156.325 81.657 150.147 81.6131 147.08 79.6414"
                              stroke="black"
                              stroke-width="0.438174"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              id="Vector 14"
                              d="M165.702 85.5567C165.702 85.5567 166.403 83.7602 168.331 82.7086C170.259 81.657 176.437 81.6131 179.505 79.6414"
                              stroke="black"
                              stroke-width="0.438174"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                        </g>
                        <g id="Group 15">
                          <path
                            id="Vector 15"
                            d="M107.645 152.378C107.645 152.378 107.864 154.35 108.521 155.227C109.178 156.103 110.054 156.322 110.054 156.322"
                            stroke="black"
                            stroke-width="0.438174"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <path
                            id="Vector 33"
                            d="M221.351 152.378C221.351 152.378 221.132 154.35 220.474 155.227C219.817 156.103 218.941 156.322 218.941 156.322"
                            stroke="black"
                            stroke-width="0.438174"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </g>
                        <g id="Group 16">
                          <path
                            id="Vector 11"
                            d="M163.918 177.414C163.951 177.297 163.883 177.176 163.767 177.143C163.65 177.11 163.529 177.178 163.496 177.294L163.918 177.414ZM163.496 177.294C163.415 177.583 163.423 178.004 163.471 178.46C163.52 178.924 163.613 179.458 163.72 179.99C163.828 180.522 163.952 181.057 164.061 181.524C164.172 181.994 164.267 182.391 164.322 182.656L164.751 182.568C164.695 182.295 164.597 181.888 164.488 181.424C164.378 180.958 164.256 180.428 164.15 179.903C164.043 179.377 163.953 178.859 163.907 178.414C163.859 177.96 163.861 177.614 163.918 177.414L163.496 177.294ZM164.322 182.656C164.538 183.707 164.591 185.654 164.323 186.84L164.75 186.937C165.034 185.679 164.977 183.669 164.751 182.568L164.322 182.656ZM164.323 186.84C164.259 187.123 164.17 187.189 164.137 187.204C164.102 187.219 164.035 187.221 163.917 187.155C163.805 187.094 163.692 186.996 163.603 186.906C163.56 186.862 163.524 186.823 163.5 186.795C163.488 186.781 163.478 186.77 163.472 186.763C163.469 186.759 163.467 186.756 163.466 186.754C163.465 186.754 163.465 186.753 163.464 186.753C163.464 186.753 163.464 186.753 163.464 186.753C163.464 186.753 163.464 186.753 163.464 186.753C163.464 186.753 163.464 186.753 163.464 186.753C163.464 186.753 163.464 186.753 163.292 186.889C163.121 187.025 163.121 187.025 163.121 187.025C163.121 187.025 163.121 187.025 163.121 187.025C163.121 187.025 163.121 187.025 163.121 187.025C163.121 187.026 163.122 187.026 163.122 187.026C163.123 187.027 163.124 187.028 163.125 187.03C163.127 187.033 163.13 187.037 163.135 187.042C163.143 187.052 163.154 187.066 163.169 187.083C163.199 187.117 163.241 187.163 163.293 187.215C163.394 187.317 163.54 187.448 163.705 187.539C163.863 187.626 164.089 187.704 164.314 187.605C164.539 187.505 164.676 187.266 164.75 186.937L164.323 186.84Z"
                            fill="black"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                  <path
                    id="Vector 50"
                    d="M202.816 146.505C202.816 146.505 205.876 145.81 208.644 145.692C216.467 145.358 229.65 148.944 229.65 148.944C230.282 151.836 231.764 161.819 229.921 163.988C227.617 166.698 215.284 166.834 207.695 167.24C200.106 167.647 185.198 164.801 183.843 164.53C182.759 164.313 162.579 162.387 152.415 160.67L146.316 160.128L137.236 159.451H130.46L127.614 157.96L121.515 155.249C121.335 155.114 120.973 154.762 120.973 154.436C120.973 154.111 121.877 153.578 122.328 153.352L116.772 149.151V148.202L117.992 147.389L115.281 145.085C115.326 144.814 115.39 144.109 115.281 143.459C115.146 142.646 116.094 143.052 116.772 143.052C117.314 143.052 120.973 145.672 122.735 146.983L130.46 149.151L123.684 145.763L118.805 141.833C118.805 141.562 118.994 140.83 119.753 140.071C120.512 139.312 124.045 141.742 125.716 143.052L134.119 145.763H140.895C141.573 145.492 143.199 144.652 144.283 143.459C145.367 142.266 142.476 140.974 140.895 140.478C139.359 139.8 136.043 137.957 135.068 136.005C133.848 133.566 136.423 133.972 137.236 134.243C137.886 134.46 139.585 135.508 140.353 136.005L149.84 140.478C152.369 142.646 157.564 147.308 158.107 148.609C158.784 150.235 163.256 150.642 169.084 151.59C174.911 152.539 183.843 150.435 188.451 149.487C193.109 148.528 200.241 146.912 202.816 146.505Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.542094"
                  />
                  <path
                    id="Vector 51"
                    d="M202.681 146.641L211.083 145.556L210.541 142.71C210.541 142.71 218.266 141.491 218.401 141.626C218.51 141.735 227.798 142.53 228.837 144.472L229.65 150.164H221.925L220.57 149.08L202.681 146.641Z"
                    fill="white"
                  />
                  <path
                    id="Vector 12"
                    d="M216.885 146.608C216.885 146.608 213.099 145.806 209.999 145.692C207.288 145.592 202.003 146.608 202.003 146.608"
                    stroke="black"
                    stroke-width="0.438753"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </g>
                {findMeasure('sleeve') && (
                  <g id="sleeve">
                    <g id="tÃ³rax (circunferÃªncia)">
                      <g id="Group 221">
                        <g id="Group 217">
                          <g id="Group 220">
                            <path
                              id="Ellipse 23"
                              d="M227.075 96.9033C227.075 96.9033 231.412 102.596 231.412 133.514C231.412 149.422 238.493 162.711 232.09 167.801C221.519 176.203 182.563 169.013 159.314 165.885"
                              stroke="#E55959"
                              stroke-width="2.19376"
                            />
                            <path
                              id="Vector 27"
                              d="M163.157 163.825L158.276 165.457L161.08 169.09"
                              stroke="#E55959"
                              stroke-width="1.75501"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                    <path
                      id="Vector 29"
                      d="M149.691 177.384L166.201 137.256"
                      stroke="#E55959"
                      stroke-width="0.542094"
                      stroke-linecap="square"
                      stroke-dasharray="2.71 2.71"
                    />
                  </g>
                )}
                {findMeasure('biceps') && (
                  <g id="biceps">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M209 136.721C209 136.721 215 135.648 219 134.921C223 134.194 228 133.5 228 133.5"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('fist') && (
                  <g id="fist">
                    <g id="Group 221_3">
                      <g id="Group 217_3">
                        <g id="Group 220_3">
                          <path
                            id="Ellipse 23_3"
                            d="M163.5 162C163.5 162 164.5 158.5 166.5 151"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('wrist') && (
                  <g id="wrist">
                    <g id="Group 221_4">
                      <g id="Group 217_4">
                        <g id="Group 220_4">
                          <path
                            id="Ellipse 23_4"
                            d="M163.5 162C163.5 162 164.5 158.5 166.5 151"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_1845"
              x1="158.67"
              y1="49.4072"
              x2="158.67"
              y2="75.2902"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_1845"
              x1="135.008"
              y1="80.9554"
              x2="136.323"
              y2="149.311"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_1845"
              x1="192.212"
              y1="80.9554"
              x2="190.898"
              y2="149.311"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_128_1845"
              x1="112.004"
              y1="134.632"
              x2="96.4492"
              y2="259.95"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_128_1845"
              x1="144.429"
              y1="208.465"
              x2="127.559"
              y2="305.739"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className=" h-full m-auto "
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_sleeve">
            <mask id="mask0_128_3457" maskUnits="userSpaceOnUse" x="0" y="0" width="342" height="291">
              <rect id="rect" width="342" height="291" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_3457)">
              <g id="group_sleeve_mask">
                <g id="male">
                  <g id="Group 20">
                    <path
                      id="Intersect"
                      d="M266.03 221.299C258.831 225.152 250.883 214.775 245.072 212.665C241.764 211.464 239.514 213.264 237.996 213.906L237.42 213.576L234.339 199.785L232.405 192.232L229.413 202.412L226.458 213.906L222.845 225.4L221.203 233.61L220.547 240.178L221.86 250.359L223.502 268.421L223.174 274.66L223.502 282.213L225.801 304.873L229.742 344.938L230.727 362.343L230.399 370.881L228.757 403.393H171.287L168.331 363H165.704L162.748 403.393H105.278L103.636 370.881L103.308 362.343L104.293 344.938L108.234 304.873L110.533 282.213L110.861 274.66L110.533 268.421L112.175 250.359L113.488 240.178L112.832 233.61L111.19 225.4L107.577 213.906L104.622 202.412L101.631 192.232L99.6957 199.785L92.7993 230.655L92.1425 236.566C91.8141 239.084 91.1573 244.185 91.1573 244.448C91.1573 244.71 89.9532 256.598 88.8585 262.51L86.2313 271.376L78.6782 289.438L75.3942 298.962L73.0954 311.769C72.8764 313.959 72.4386 318.403 72.4386 318.666C72.4386 318.928 72.6575 322.497 72.767 324.249L75.0658 326.876C75.9415 328.408 77.7586 331.605 78.0214 332.13C78.3498 332.787 79.9918 339.683 79.9918 340.997C79.9918 342.085 80.2171 343.173 80.4812 344.448C80.5359 344.712 80.5922 344.984 80.6486 345.266C80.9113 346.58 84.4799 357.636 86.2313 363L82.9473 365.955H81.6337L78.6782 361.029L74.0806 348.55L68.4978 354.133L66.8558 363V371.538L68.1694 374.494L74.0806 384.674V386.644C74.0806 386.973 73.0954 388.286 72.767 388.615C72.5043 388.878 72.6575 389.381 72.767 389.6L73.0954 391.899L72.1102 392.556L69.8114 390.914L63.2434 382.375L58.3174 374.494L56.6754 361.358L55.6902 356.76L55.3618 352.163L57.0038 334.429L56.347 330.817L55.6902 318.666L56.0186 308.485L55.6902 298.962V285.497L56.0186 272.69L57.3322 265.794L59.3026 252.658L61.9298 243.462L62.915 235.252L67.841 217.847L68.4978 208.652L72.767 179.753L71.7818 173.513V164.647L72.4386 158.407L73.4238 150.854L76.3794 142.972L79.9918 137.061L83.2757 132.463L87.2165 128.851L91.4857 125.895L99.0389 122.283L107.577 117.357L115.787 113.088L128.266 107.177L136.476 103.564L146.328 98.6383V77.9492L162.748 53.5353V40.8401L167.018 47.1877L171.287 40.8401V53.5353L187.707 77.9492V98.6383L197.559 103.564L205.769 107.177L218.248 113.088L226.458 117.357L234.996 122.283L242.549 125.895L246.819 128.851L250.759 132.463L254.043 137.061L257.656 142.972L260.611 150.854L261.596 158.407L262.253 164.647V173.513L261.268 179.753L265.537 208.652L264.608 211.752L266.03 221.299Z"
                      fill="white"
                    />
                    <path
                      id="Vector 18"
                      d="M168.298 95.6823C177.899 91.61 182.748 82.8747 184.39 80.2475V77.9487C180.449 75.3215 172.436 70.0671 171.91 70.0671C171.385 70.0671 154.834 74.6647 146.624 76.9635V93.3835C153.192 97.6527 162.203 98.2674 168.298 95.6823Z"
                      fill="url(#paint0_linear_128_3457)"
                    />
                    <g id="Group 24">
                      <g id="Group 22">
                        <path
                          id="Vector 16"
                          d="M137.757 205.695C151.221 203.068 152.206 199.127 155.49 195.186C153.848 197.157 142.486 196.303 141.697 196.828C140.909 197.354 134.144 196.828 130.204 196.5C127.248 197.047 120.746 194.201 119.695 194.201C118.644 194.201 114.44 191.793 112.47 191.246L103.932 185.663L99.334 177.453C100.319 186.976 103.603 200.441 103.603 200.441C103.603 200.441 124.704 208.242 137.757 205.695Z"
                          fill="url(#paint1_linear_128_3457)"
                        />
                        <path
                          id="Vector 36"
                          d="M104.95 186.319C106.592 187.961 112.503 192.033 123.012 195.186C133.52 198.339 149.065 196.5 155.523 195.186"
                          stroke="black"
                          stroke-width="0.656798"
                          stroke-linecap="round"
                        />
                      </g>
                      <g id="Group 23">
                        <path
                          id="Vector 16_2"
                          d="M195.917 205.695C182.452 203.068 181.467 199.127 178.183 195.186C179.825 197.157 191.188 196.303 191.976 196.828C192.764 197.354 199.529 196.828 203.47 196.5C206.425 197.047 212.928 194.201 213.979 194.201C215.029 194.201 219.233 191.793 221.203 191.246L229.742 185.663L234.339 177.453C233.978 189.275 230.07 200.441 230.07 200.441C230.07 200.441 208.97 208.242 195.917 205.695Z"
                          fill="url(#paint2_linear_128_3457)"
                        />
                        <path
                          id="Vector 36_2"
                          d="M228.723 186.319C227.081 187.961 221.17 192.033 210.661 195.186C200.152 198.339 184.608 196.5 178.15 195.186"
                          stroke="black"
                          stroke-width="0.656798"
                          stroke-linecap="round"
                        />
                      </g>
                    </g>
                    <path
                      id="Vector 20"
                      d="M93.0946 229.34C93.0946 229.34 91.234 237.879 92.4381 235.252L91.1244 247.731C90.796 250.905 89.4824 258.896 89.4824 258.896C89.4824 258.896 88.6067 264.26 87.512 267.763L81.2724 283.198C81.2724 283.198 76.6749 294.035 76.6749 294.363C76.6749 295.292 74.3761 305.639 73.3909 308.156L72.4057 317.68L72.7341 324.248L75.0329 327.532C75.3613 328.626 77.3317 331.144 77.3317 331.144C77.3317 331.144 79.9588 339.683 79.9588 340.339C79.9588 340.865 80.6156 343.733 80.6156 345.265L82.2576 349.863C83.5712 355.774 86.3955 362.276 85.87 363.327C85.2132 364.641 84.0091 365.517 83.5712 365.626L81.9292 366.283L80.2872 364.312L78.9736 362.014C78.0979 357.526 74.0477 348.221 74.0477 348.221L68.4649 353.475L66.4945 362.014V371.209L70.1069 377.777C71.6394 380.295 74.3761 385.133 74.3761 385.658C74.3761 386.184 72.7341 388.286 72.7341 388.286L72.4057 392.226H71.7489L66.4945 386.644L61.5685 380.076L58.6129 374.493L56.3141 359.715L55.6573 355.117V347.893L56.3141 339.354L56.9709 334.428L55.6573 321.621V312.754V295.02C55.329 289.109 56.3142 272.492 56.3142 271.704C56.3142 270.916 58.0657 256.926 59.5982 251.343C60.255 248.825 66.4946 221.459 66.4946 221.459L70.4354 196.172C72.9531 207.009 92.8321 226.385 93.0948 227.698C93.4232 229.34 93.0946 229.34 93.0946 229.34Z"
                      fill="url(#paint3_linear_128_3457)"
                    />
                    <path
                      id="Vector 17"
                      d="M156.475 355.446L162.715 362.014L165.999 363.328L163.7 386.973L163.043 403.721H105.245L103.603 374.165L103.274 360.372L103.931 349.864L105.245 333.772L107.215 311.769L110.499 284.841L149.907 341.325L153.191 347.565C156.803 355.665 156.475 355.446 156.475 355.446Z"
                      fill="url(#paint4_linear_128_3457)"
                    />
                    <g id="Group 12">
                      <g id="Intersect_2">
                        <path
                          d="M265.623 221.502C265.623 221.502 266.46 221.603 266.639 221.502L261.928 181.396C262.913 174.458 263.959 157.815 258.946 145.15C255.805 137.216 251.993 132.65 247.386 129.164C245.088 127.425 242.598 125.958 239.911 124.479C239.012 123.984 238.093 123.488 237.152 122.981L237.151 122.98C235.269 121.966 233.297 120.902 231.219 119.7C221.281 113.946 198.415 103.395 188.035 98.7534V76.9636H187.378V99.1793L187.572 99.2662C197.866 103.865 220.925 114.499 230.89 120.268C232.976 121.475 234.965 122.548 236.852 123.565L236.852 123.566C237.792 124.072 238.706 124.565 239.594 125.054C242.274 126.53 244.73 127.978 246.989 129.687C251.495 133.097 255.236 137.563 258.335 145.392C263.294 157.92 262.254 174.466 261.271 181.348L261.265 181.393L265.623 221.502Z"
                          fill="black"
                        />
                        <path
                          d="M238.199 214.732L232.939 191.574C233.619 188.436 234.099 185.738 234.337 183.732L233.687 183.636L232.28 191.512C230.759 198.518 228.236 207.747 225.163 216.424C220.685 229.067 220.213 238.4 220.548 241.527L220.548 241.529L220.549 241.532C221.041 245.471 221.763 251.691 222.329 257.549C222.897 263.419 223.304 268.893 223.174 271.359C222.91 276.365 223.064 281.128 223.174 282.89L223.175 282.899L225.146 299.654L225.146 299.655C226.46 311.808 229.283 339.646 230.07 353.822C230.85 367.852 229.334 392.285 228.454 403.064H171.592L168.636 362.671H165.398L162.443 403.064H105.581C104.701 392.285 103.185 367.852 103.964 353.822C104.752 339.646 107.575 311.808 108.888 299.655L108.889 299.654L110.86 282.899L110.86 282.89C110.97 281.128 111.124 276.365 110.86 271.359C110.731 268.893 111.138 263.419 111.705 257.549C112.271 251.691 112.993 245.471 113.486 241.532L113.486 241.529L113.486 241.527C113.821 238.4 113.35 229.067 108.872 216.424C105.799 207.747 103.275 198.518 101.754 191.511L100.347 183.636L99.6976 183.732C99.9356 185.738 100.416 188.436 101.095 191.574L92.474 230.603L92.4721 230.622C92.3688 231.671 92.2552 232.909 92.1284 234.293L92.1283 234.294C91.7841 238.051 91.3421 242.874 90.7406 247.848C89.917 254.658 88.7977 261.719 87.2314 266.679C85.4215 272.411 83.4731 277.1 81.5401 281.752C81.3741 282.151 81.2083 282.55 81.0427 282.949C78.9482 288.001 76.8894 293.106 75.0777 299.529C72.176 309.817 71.8898 320.298 72.1103 324.266L72.1184 324.413L72.233 324.505C73.2748 325.338 75.8915 328.285 77.7128 333.229C78.8553 336.329 79.2585 338.275 79.4191 339.599C79.494 340.217 79.5166 340.704 79.5359 341.121L79.5402 341.212C79.5605 341.644 79.5815 342.025 79.6728 342.39C79.9357 343.441 83.6792 356.469 85.5509 362.965C85.2778 363.479 84.7999 364.185 84.1936 364.745C83.5368 365.352 82.7842 365.741 82.0082 365.63C81.9703 365.624 81.8987 365.599 81.7905 365.516C81.6847 365.435 81.5622 365.313 81.4245 365.146C81.1491 364.813 80.8388 364.333 80.5031 363.729C79.8328 362.525 79.0853 360.874 78.333 359.041C76.8295 355.376 75.3218 351.017 74.3929 348.121L74.2285 347.609L68.1966 353.641L66.1986 362.965V371.628L66.2447 371.706C67.4476 373.729 69.2683 376.861 70.8204 379.684C71.5967 381.096 72.3041 382.427 72.8332 383.502C73.0978 384.04 73.3163 384.51 73.476 384.892C73.639 385.283 73.7303 385.557 73.7563 385.713C73.87 386.395 73.6595 386.903 73.352 387.271C73.036 387.649 72.6148 387.881 72.3343 387.975L71.9958 388.087L72.1363 388.415C72.2877 388.769 72.4751 389.391 72.5295 390.077C72.5838 390.762 72.5032 391.473 72.1637 392.046C72.1167 392.045 72.0177 392.021 71.8547 391.933C71.5349 391.761 71.0977 391.413 70.566 390.906C69.51 389.901 68.1562 388.345 66.7433 386.563C63.9158 382.998 60.8945 378.586 59.596 375.988C58.3132 373.423 57.7416 369.46 57.3299 365.673C57.2535 364.97 57.1825 364.273 57.1134 363.594C56.9977 362.457 56.8871 361.371 56.7649 360.393C56.5703 358.837 56.341 357.503 55.9948 356.638C55.8818 356.355 55.7932 355.895 55.7357 355.267C55.6788 354.646 55.6543 353.886 55.657 353.02C55.6624 351.289 55.7767 349.153 55.9516 346.892C56.3012 342.371 56.8915 337.369 57.3288 334.144L57.3351 334.098L57.3283 334.052C56.889 331.087 56.0182 324.612 56.0182 322.278V313.104C56.2936 310.964 56.1615 307.687 55.9904 303.444C55.9571 302.62 55.9224 301.758 55.8889 300.862C55.6819 295.319 55.5181 288.38 56.0176 280.263C57.1504 261.855 59.2594 253.486 61.2946 245.409L61.2947 245.409C61.6159 244.134 61.9353 242.867 62.2486 241.569C62.6393 239.95 63.1135 237.683 63.641 235.162C63.9895 233.495 64.3614 231.717 64.7477 229.942C65.7223 225.465 66.7894 221.002 67.8192 218.292L67.8327 218.256L72.7697 181.393L72.7633 181.348C71.7802 174.466 70.74 157.92 75.6991 145.392C78.7981 137.563 82.5391 133.097 87.0449 129.687C89.304 127.978 91.7606 126.53 94.4404 125.054C95.3288 124.565 96.243 124.072 97.1824 123.565C99.0697 122.548 101.059 121.475 103.144 120.268C113.11 114.499 136.169 103.865 146.462 99.2662L146.657 99.1793V76.9636H146V98.7535C135.62 103.395 112.753 113.946 102.815 119.7C100.738 120.902 98.7653 121.966 96.883 122.98C95.9412 123.488 95.022 123.984 94.1236 124.479C91.4361 125.958 88.9462 127.425 86.6486 129.164C82.0413 132.65 78.2291 137.216 75.0884 145.15C70.0752 157.815 71.1213 174.458 72.1067 181.396L67.1917 218.094C66.1459 220.861 65.0746 225.352 64.106 229.803C63.7163 231.593 63.3432 233.377 62.9943 235.045C62.4692 237.556 61.999 239.804 61.6102 241.415C61.2974 242.711 60.9783 243.977 60.6572 245.251C58.6191 253.339 56.4986 261.754 55.3621 280.223C54.8606 288.372 55.0252 295.335 55.2326 300.887C55.2664 301.793 55.3013 302.66 55.3346 303.488C55.506 307.749 55.6353 310.962 55.3642 313.04L55.3614 313.061V322.278C55.3614 324.66 56.2331 331.135 56.6717 334.102C56.234 337.337 55.646 342.326 55.2967 346.841C55.1213 349.11 55.0057 351.263 55.0002 353.018C54.9975 353.895 55.0222 354.677 55.0816 355.327C55.1404 355.969 55.2352 356.507 55.3849 356.882C55.6955 357.658 55.9177 358.911 56.1131 360.474C56.2346 361.446 56.3436 362.518 56.4586 363.647L56.4586 363.648C56.5279 364.328 56.5994 365.03 56.677 365.744C57.0863 369.51 57.6641 373.593 59.0085 376.282C60.3371 378.939 63.3912 383.394 66.2287 386.971C67.6481 388.761 69.0241 390.345 70.1131 391.382C70.6537 391.897 71.1427 392.295 71.5426 392.511C71.7395 392.618 71.9486 392.699 72.1506 392.703C72.3791 392.707 72.5909 392.611 72.7198 392.396C73.1626 391.658 73.2446 390.787 73.1843 390.025C73.137 389.429 73.0007 388.877 72.859 388.467C73.1839 388.308 73.5543 388.053 73.856 387.692C74.271 387.195 74.5531 386.499 74.4041 385.605C74.3644 385.367 74.2456 385.031 74.082 384.639C73.915 384.239 73.6901 383.756 73.4225 383.212C72.887 382.124 72.1741 380.783 71.3959 379.368C69.8589 376.573 68.0608 373.477 66.8554 371.448V363.034L68.7982 353.968L73.9327 348.833C74.8723 351.725 76.2994 355.814 77.7254 359.29C78.481 361.132 79.24 362.81 79.9292 364.049C80.2731 364.667 80.6058 365.187 80.9183 365.565C81.0746 365.754 81.2324 365.915 81.3908 366.037C81.5468 366.156 81.7233 366.253 81.9153 366.28C82.9784 366.432 83.9225 365.89 84.6395 365.227C85.3616 364.56 85.909 363.721 86.1963 363.146L86.2536 363.032L86.2181 362.908C84.3554 356.444 80.5708 343.274 80.31 342.231C80.237 341.939 80.217 341.622 80.1963 341.181L80.1919 341.089C80.1726 340.671 80.149 340.163 80.0711 339.52C79.9032 338.136 79.4855 336.14 78.3291 333.001C76.5319 328.123 73.9622 325.108 72.7585 324.087C72.5559 320.068 72.8662 309.789 75.7099 299.707C77.5105 293.323 79.5568 288.248 81.6494 283.201C81.8152 282.801 81.9814 282.401 82.1477 282.001C84.0799 277.351 86.0388 272.637 87.8577 266.877C89.444 261.854 90.5688 254.739 91.3927 247.927C91.9954 242.942 92.4389 238.102 92.7833 234.345L92.7834 234.343C92.9091 232.971 93.0216 231.744 93.1238 230.706L101.432 193.094C102.981 199.921 105.376 208.522 108.253 216.643C112.706 229.216 113.155 238.447 112.833 241.454C112.341 245.397 111.618 251.622 111.051 257.486C110.486 263.34 110.072 268.868 110.205 271.393C110.466 276.362 110.314 281.092 110.205 282.841L108.236 299.58L108.236 299.583C106.922 311.733 104.097 339.589 103.308 353.786C102.519 367.998 104.075 392.802 104.951 403.419L104.976 403.721H163.053L166.009 363.328H168.026L170.981 403.721H229.059L229.083 403.419C229.959 392.802 231.516 367.998 230.726 353.786C229.937 339.589 227.112 311.733 225.799 299.583L223.829 282.841C223.72 281.092 223.568 276.362 223.83 271.393C223.963 268.868 223.549 263.34 222.983 257.486C222.416 251.622 221.694 245.397 221.201 241.454C220.88 238.447 221.329 229.217 225.782 216.643C228.658 208.522 231.054 199.921 232.602 193.094L237.382 214.732H238.199Z"
                          fill="black"
                        />
                      </g>
                    </g>
                    <g id="Group 19">
                      <g id="Group 11">
                        <path
                          id="Vector 4"
                          d="M136.432 47.2691C137.483 42.8028 139.497 46.2839 140.372 48.5827L141.686 54.1654L142.343 65.6594C142.233 65.7689 141.686 66.973 140.372 65.9878C138.69 64.7261 135.118 52.8519 136.432 47.2691Z"
                          fill="white"
                          stroke="black"
                          stroke-width="0.656798"
                        />
                        <path
                          id="Vector 5"
                          d="M197.886 47.2691C196.835 42.8028 194.821 46.2839 193.945 48.5827L192.632 54.1654L191.975 65.6594C192.084 65.7689 192.632 66.973 193.945 65.9878C195.628 64.7261 199.2 52.8519 197.886 47.2691Z"
                          fill="white"
                          stroke="black"
                          stroke-width="0.656798"
                        />
                        <path
                          id="Vector"
                          d="M142.387 23.4348C145.84 13.6538 157.822 8 166.689 8C176.43 7.99999 188.852 13.6538 192.304 23.4348C196.245 34.6003 195.9 52.6019 190.99 69.739C188.681 77.801 186.464 83.4514 183.766 86.4874C181.139 89.443 170.744 92.727 167.346 92.727C164.718 92.727 153.553 89.443 150.926 86.4874C148.227 83.4514 146.011 77.801 143.701 69.739C138.792 52.6019 138.447 34.6003 142.387 23.4348Z"
                          fill="white"
                          stroke="black"
                          stroke-width="0.656798"
                        />
                      </g>
                      <g id="Group 18">
                        <g id="Group 14">
                          <path
                            id="Vector 32"
                            d="M163.734 96.3384C163.734 96.6824 164.379 98.6916 165.525 99.8476C166.672 101.004 167.436 102.806 167.675 103.563"
                            stroke="black"
                            stroke-width="0.656798"
                            stroke-linecap="round"
                          />
                          <g id="Group 13">
                            <path
                              id="Vector 31"
                              d="M162.749 124.253C162.749 124.253 161.698 121.56 158.808 119.984C155.918 118.407 146.657 118.342 142.059 115.386"
                              stroke="black"
                              stroke-width="0.656798"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              id="Vector 14"
                              d="M169.973 124.253C169.973 124.253 171.024 121.56 173.914 119.984C176.804 118.407 186.065 118.342 190.662 115.386"
                              stroke="black"
                              stroke-width="0.656798"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </g>
                        </g>
                        <g id="Group 15">
                          <path
                            id="Vector 15"
                            d="M82.9473 224.415C82.9473 224.415 83.2757 227.371 84.2609 228.684C85.2461 229.998 86.5597 230.326 86.5597 230.326"
                            stroke="black"
                            stroke-width="0.656798"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <path
                            id="Vector 33"
                            d="M253.386 224.415C253.386 224.415 253.058 227.371 252.073 228.684C251.087 229.998 249.774 230.326 249.774 230.326"
                            stroke="black"
                            stroke-width="0.656798"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </g>
                        <g id="Group 16">
                          <path
                            id="Vector 11"
                            d="M167.298 261.941C167.347 261.766 167.246 261.585 167.071 261.536C166.897 261.486 166.715 261.588 166.666 261.762L167.298 261.941ZM166.666 261.762C166.544 262.195 166.557 262.826 166.628 263.509C166.701 264.206 166.84 265.006 167.002 265.803C167.163 266.601 167.348 267.402 167.513 268.103C167.678 268.807 167.821 269.402 167.903 269.799L168.546 269.667C168.462 269.258 168.316 268.648 168.152 267.952C167.988 267.253 167.805 266.46 167.645 265.672C167.486 264.884 167.351 264.107 167.281 263.441C167.21 262.76 167.213 262.241 167.298 261.941L166.666 261.762ZM167.903 269.799C168.227 271.374 168.307 274.292 167.904 276.071L168.545 276.216C168.971 274.33 168.886 271.317 168.546 269.667L167.903 269.799ZM167.904 276.071C167.809 276.494 167.676 276.594 167.626 276.616C167.574 276.639 167.473 276.641 167.296 276.543C167.129 276.451 166.96 276.304 166.826 276.17C166.761 276.104 166.708 276.045 166.671 276.003C166.653 275.982 166.639 275.966 166.63 275.954C166.625 275.949 166.622 275.945 166.62 275.942C166.619 275.941 166.618 275.94 166.618 275.94C166.618 275.94 166.618 275.939 166.618 275.939C166.618 275.939 166.618 275.939 166.618 275.94C166.618 275.94 166.618 275.94 166.618 275.94C166.618 275.94 166.618 275.94 166.36 276.144C166.103 276.347 166.103 276.347 166.103 276.348C166.103 276.348 166.103 276.348 166.103 276.348C166.103 276.348 166.103 276.348 166.104 276.348C166.104 276.349 166.104 276.349 166.105 276.35C166.106 276.351 166.107 276.353 166.109 276.355C166.113 276.359 166.117 276.365 166.124 276.373C166.136 276.388 166.153 276.409 166.176 276.434C166.22 276.485 166.283 276.555 166.361 276.633C166.512 276.785 166.731 276.982 166.978 277.118C167.216 277.249 167.555 277.366 167.891 277.217C168.229 277.068 168.434 276.709 168.545 276.216L167.904 276.071Z"
                            fill="black"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                  <path
                    id="Vector 50"
                    d="M225.605 215.611C225.605 215.611 230.191 214.569 234.34 214.393C246.067 213.893 265.827 219.268 265.827 219.268C266.775 223.602 268.996 238.567 266.233 241.817C262.78 245.88 244.294 246.083 232.918 246.692C221.542 247.302 199.196 243.036 197.165 242.629C195.54 242.304 165.291 239.418 150.056 236.845L140.914 236.032L127.304 235.016H117.146L112.881 232.782L103.739 228.719C103.468 228.516 102.927 227.988 102.927 227.5C102.927 227.012 104.281 226.213 104.958 225.875L96.6291 219.577V218.155L98.4574 216.937L94.3946 213.483C94.4623 213.077 94.5571 212.021 94.3946 211.046C94.1914 209.827 95.6134 210.436 96.6291 210.436C97.4417 210.436 102.927 214.363 105.567 216.327L117.146 219.577L106.989 214.499L99.6763 208.608C99.6763 208.202 99.9607 207.105 101.098 205.967C102.236 204.829 107.531 208.472 110.037 210.436L122.631 214.499H132.788C133.804 214.093 136.242 212.833 137.867 211.046C139.492 209.258 135.158 207.321 132.788 206.577C130.486 205.561 125.516 202.798 124.053 199.873C122.225 196.216 126.085 196.826 127.304 197.232C128.279 197.557 130.825 199.128 131.976 199.873L146.196 206.577C149.988 209.827 157.775 216.815 158.587 218.765C159.603 221.203 166.307 221.812 175.042 223.234C183.777 224.656 197.165 221.503 204.072 220.081C211.054 218.643 221.745 216.221 225.605 215.611Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.812569"
                  />
                  <path
                    id="Vector 51"
                    d="M225.401 215.814L237.996 214.189L237.184 209.923C237.184 209.923 248.763 208.095 248.966 208.298C249.128 208.461 263.05 209.652 264.608 212.564L265.827 221.096H254.248L252.216 219.471L225.401 215.814Z"
                    fill="white"
                  />
                  <path
                    id="Vector 12"
                    d="M246.693 215.765C246.693 215.765 241.018 214.564 236.371 214.392C232.308 214.243 224.385 215.765 224.385 215.765"
                    stroke="black"
                    stroke-width="0.657665"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </g>
                {findMeasure('sleeve') && (
                  <g id="sleeve">
                    <g id="tÃ³rax (circunferÃªncia)">
                      <g id="Group 221">
                        <g id="Group 217">
                          <g id="Group 220">
                            <path
                              id="Ellipse 23"
                              d="M261.967 141.261C261.967 141.261 268.468 149.793 268.468 196.138C268.468 219.983 279.082 239.902 269.484 247.532C253.639 260.127 195.246 249.349 160.397 244.661"
                              stroke="#E55959"
                              stroke-width="3.28833"
                            />
                            <path
                              id="Vector 27"
                              d="M166.158 241.572L158.84 244.019L163.045 249.465"
                              stroke="#E55959"
                              stroke-width="2.63066"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                    <path
                      id="Vector 29"
                      d="M145.972 261.896L170.72 201.748"
                      stroke="#E55959"
                      stroke-width="0.812569"
                      stroke-linecap="square"
                      stroke-dasharray="4.06 4.06"
                    />
                  </g>
                )}
                {findMeasure('biceps') && (
                  <g id="biceps">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M234.874 200.946C234.874 200.946 243.867 199.337 249.863 198.247C255.859 197.157 263.353 196.117 263.353 196.117"
                            stroke="#E55959"
                            stroke-width="2.99789"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('fist') && (
                  <g id="fist">
                    <g id="Group 221_3">
                      <g id="Group 217_3">
                        <g id="Group 220_3">
                          <path
                            id="Ellipse 23_3"
                            d="M166.671 238.838C166.671 238.838 168.17 233.591 171.168 222.349"
                            stroke="#E55959"
                            stroke-width="2.99789"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('wrist') && (
                  <g id="wrist">
                    <g id="Group 221_4">
                      <g id="Group 217_4">
                        <g id="Group 220_4">
                          <path
                            id="Ellipse 23_4"
                            d="M166.671 238.838C166.671 238.838 168.17 233.591 171.168 222.349"
                            stroke="#E55959"
                            stroke-width="2.99789"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_3457"
              x1="159.431"
              y1="70.0671"
              x2="159.431"
              y2="108.864"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_3457"
              x1="123.964"
              y1="117.356"
              x2="125.934"
              y2="219.816"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_3457"
              x1="209.709"
              y1="117.356"
              x2="207.739"
              y2="219.816"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_128_3457"
              x1="89.4824"
              y1="197.814"
              x2="66.1661"
              y2="385.658"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_128_3457"
              x1="138.085"
              y1="308.485"
              x2="112.798"
              y2="454.295"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
