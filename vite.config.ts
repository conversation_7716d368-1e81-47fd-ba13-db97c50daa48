import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';
import istanbul from 'vite-plugin-istanbul';
import tsconfigPaths from 'vite-tsconfig-paths';
import svgr from 'vite-plugin-svgr';

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    svgr(),
    tailwindcss(),
    istanbul({
      include: ['src/**/*'],
      exclude: ['node_modules', 'dist'],
      cypress: true,
      forceBuildInstrument: true,
      requireEnv: false,
    }),
    tsconfigPaths(),
  ],
  server: {
    port: 3000,
  },
});
