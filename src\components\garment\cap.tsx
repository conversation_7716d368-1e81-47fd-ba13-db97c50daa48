import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface CapProps {
  measure: GarmentMeasure;
  className?: string;
}

export function Cap({ measure, className }: CapProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width={isMobile ? '331' : '342'}
          height={isMobile ? '193' : '291'}
          viewBox={isMobile ? '0 0 331 193' : '0 0 342 291'}
          fill="none"
        >
          <g id="group_cap">
            <g id="cap">
              <path
                id="Vector 382"
                d="M79.0039 154.707C78.575 152.992 112.694 112.218 114.449 111.121C123.588 107.539 143.403 99.0131 146.035 97.9603C148.667 96.9074 173.161 97.5216 185.08 97.9603L218.421 117.483C217.763 119.603 215.668 125.243 212.718 130.205C207.892 138.321 203.724 142.708 193.415 156.088C183.105 169.469 169.067 175.611 157.003 174.953C143.554 174.219 117.807 160.967 109.842 158.501C100.629 155.65 96.7335 155.07 87.907 154.772C85.253 154.683 79.4426 156.462 79.0039 154.707Z"
                fill="white"
                stroke="black"
                stroke-width="0.438702"
              />
              <path
                id="Vector 423"
                d="M95.6707 139.643L115.193 117.27C118.264 119.463 126.66 123.072 135.154 124.508C150.728 127.14 176.904 129.626 193.063 130.65C180.706 138.839 154.501 152.804 143.27 152.804C132.039 152.804 106.858 144.03 95.6707 139.643Z"
                fill="url(#paint0_linear_208_4989)"
              />
              <path
                id="Vector 381"
                d="M121.248 49.2639C128.828 38.3324 159.721 23.8191 173.102 21.4062H180.473C187.858 22.7224 205.304 26.6268 216.008 31.7158C226.712 36.8047 235.625 41.1084 241.453 52.5541C244.432 58.4057 245.489 67.2068 245.84 68.7861C246.191 70.3654 248.765 88.4546 250.008 97.3018C249.35 99.2759 247.656 104.865 244.171 109.153C235.617 119.682 225.015 122.241 216.094 125.385C211.561 126.847 201.443 127.923 192.318 127.572C180.912 127.134 159.416 125.598 139.893 121.65C124.275 118.491 114.229 110.902 114.229 110.902C114.229 110.902 109.842 65.7152 121.248 49.2639Z"
                fill="white"
                stroke="black"
                stroke-width="0.438702"
              />
              <path
                id="Vector 391"
                d="M218.86 54.3095C216.666 52.3355 216.227 50.142 216.886 49.2645C217.605 48.305 219.644 49.9443 220.614 51.2386C221.931 52.9934 221.166 56.3846 218.86 54.3095Z"
                fill="white"
                stroke="black"
                stroke-width="0.438702"
              />
              <path
                id="Vector 392"
                d="M219.059 52.3633C218.438 51.7845 218.314 51.1413 218.5 50.884C218.704 50.6026 219.243 50.859 219.518 51.2385C219.89 51.753 219.711 52.9718 219.059 52.3633Z"
                fill="white"
                stroke="black"
                stroke-width="0.438702"
              />
              <path
                id="Vector 390"
                d="M173.893 25.1357C171.085 27.5925 155.906 41.5871 148.229 46.8515C140.551 52.1159 138.577 56.7223 138.577 56.7223"
                stroke="black"
                stroke-width="0.438702"
              />
              <path
                id="Vector 388"
                d="M182.009 22.9414C190.271 27.6209 206.266 49.5788 207.673 58.915C210.086 74.9276 209.501 107.392 210.086 122.527"
                stroke="black"
                stroke-width="0.438702"
                stroke-dasharray="0.88 0.88"
              />
              <path
                id="Vector 387"
                d="M179.815 23.5996C180.912 25.7931 202.847 41.3671 204.821 60.0119C206.651 77.2888 206.649 107.977 206.576 122.746"
                stroke="black"
                stroke-width="0.438702"
              />
              <path
                id="Vector 386"
                d="M176.964 25.7939C184.275 29.5229 199.601 41.8505 202.408 61.3288C205.216 80.8072 204.163 110.537 203.286 122.967"
                stroke="black"
                stroke-width="0.438702"
                stroke-dasharray="0.88 0.88"
              />
              <path
                id="Vector 384"
                d="M114.01 106.082C115.545 109.153 126.521 114.308 135.593 116.83C160.818 123.843 181.351 123.843 188.809 123.843C194.775 123.843 207.114 124.432 218.069 120.998C227.575 118.018 232.546 115.295 238.688 111.346C244.829 107.398 249.655 95.7725 249.655 95.7725"
                stroke="black"
                stroke-width="0.438702"
                stroke-dasharray="0.88 0.88"
              />
              <path
                id="Vector 383"
                d="M171.699 22.2838C171.48 21.8451 171.48 20.9678 172.357 20.0903C173.235 19.2129 174.159 19.3394 174.77 19.2129C177.041 18.7422 178.367 19.1252 179.596 19.6516C181.131 20.3097 181.789 20.7483 181.351 22.2838C180.912 23.8193 180.034 23.8193 179.157 24.258C178.28 24.6967 175.428 24.6967 173.893 24.258C173.049 24.0169 172.357 23.6 171.699 22.2838Z"
                fill="white"
                stroke="black"
                stroke-width="0.438702"
              />
              <path
                id="Vector 385"
                d="M171.919 23.8193C170.822 24.0387 152.702 28.6515 147.218 31.5031C142.831 33.7843 134.117 38.589 130.461 41.1481"
                stroke="black"
                stroke-width="0.438702"
                stroke-dasharray="0.88 0.88"
              />
              <path
                id="Vector 389"
                d="M182.447 22.9414L211.621 30.838C217.69 32.8853 232.679 41.1476 236.408 46.412"
                stroke="black"
                stroke-width="0.438702"
                stroke-dasharray="0.88 0.88"
              />
              <g id="Group 273">
                <path
                  id="Vector 424"
                  d="M121.335 100.599L122.651 67.4766C122.651 67.4766 132.96 71.8636 148.973 74.9345C164.986 78.0054 183.192 78.2248 183.192 78.2248V115.295C183.192 115.295 167.179 114.057 144.586 109.373C126.259 105.573 121.335 100.599 121.335 100.599Z"
                  stroke="black"
                  stroke-width="0.438702"
                />
                <path
                  id="Vector 425"
                  d="M181.437 79.541V111.128"
                  stroke="black"
                  stroke-width="0.438702"
                  stroke-dasharray="0.88 0.88"
                />
                <path
                  id="Vector 426"
                  d="M125.502 71.2061L124.186 99.7217"
                  stroke="black"
                  stroke-width="0.438702"
                  stroke-dasharray="0.88 0.88"
                />
              </g>
            </g>
            {findMeasure('circumference') && (
              <g id="circumference">
                <path
                  id="Ellipse 23"
                  d="M172.664 123.983C133.277 122.162 112.689 103.29 112.689 103.29M245.515 101.149C245.515 101.149 229.069 124.459 189.23 124.184"
                  stroke="#E55959"
                  stroke-width="2.19351"
                />
                <path
                  id="Vector 27"
                  d="M192.395 121.274L188.355 124.208L191.906 126.91"
                  stroke="#E55959"
                  stroke-width="1.75481"
                />
                <path
                  id="Vector 28"
                  d="M169.978 121.228L174.106 124.036L170.64 126.846"
                  stroke="#E55959"
                  stroke-width="1.75481"
                />
              </g>
            )}
            {findMeasure('head_circumference') && (
              <g id="head_circumference">
                <path
                  id="Ellipse 23_2"
                  d="M172.664 123.983C133.277 122.162 112.689 103.29 112.689 103.29M245.515 101.149C245.515 101.149 229.069 124.459 189.23 124.184"
                  stroke="#E55959"
                  stroke-width="2.19351"
                />
                <path
                  id="Vector 27_2"
                  d="M192.395 121.274L188.355 124.208L191.906 126.91"
                  stroke="#E55959"
                  stroke-width="1.75481"
                />
                <path
                  id="Vector 28_2"
                  d="M169.978 121.228L174.106 124.036L170.64 126.846"
                  stroke="#E55959"
                  stroke-width="1.75481"
                />
              </g>
            )}
            {findMeasure('brim') && (
              <g id="brim">
                <path
                  id="Vector 21"
                  d="M140.771 126.063L118.354 157.408"
                  stroke="#E55959"
                  stroke-width="2.19351"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M116.747 153.879L117.198 159.137L122.158 158.375"
                  stroke="#E55959"
                  stroke-width="1.75481"
                />
                <path
                  id="Vector 27_3"
                  d="M142.338 129.912L141.293 124.739L136.452 126.059"
                  stroke="#E55959"
                  stroke-width="1.75481"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_208_4989"
              x1="144.367"
              y1="117.27"
              x2="144.367"
              y2="152.804"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_cap">
            <g id="cap">
              <path
                id="Vector 382"
                d="M54.0053 224.293C53.4197 221.95 100.005 166.279 102.401 164.781C114.88 159.889 141.935 148.249 145.529 146.811C149.123 145.374 182.567 146.212 198.839 146.811L244.363 173.467C243.465 176.362 240.604 184.063 236.576 190.838C229.987 201.919 224.297 207.909 210.22 226.179C196.144 244.448 176.976 252.834 160.504 251.935C142.142 250.934 106.987 232.839 96.1113 229.473C83.5324 225.58 78.213 224.788 66.1615 224.382C62.5377 224.259 54.6044 226.689 54.0053 224.293Z"
                fill="white"
                stroke="black"
                stroke-width="0.598997"
              />
              <path
                id="Vector 423"
                d="M76.7619 203.725L103.417 173.176C107.61 176.171 119.074 181.099 130.672 183.059C151.936 186.653 187.676 190.048 209.739 191.445C192.868 202.626 157.087 221.695 141.753 221.695C126.419 221.695 92.0363 209.715 76.7619 203.725Z"
                fill="url(#paint0_linear_208_4990)"
              />
              <path
                id="Vector 381"
                d="M111.685 80.3215C122.034 65.3959 164.215 45.5796 182.485 42.2852H192.55C202.633 44.0821 226.453 49.4132 241.069 56.3616C255.684 63.31 267.854 69.1862 275.811 84.814C279.879 92.8037 281.321 104.82 281.801 106.977C282.28 109.133 285.794 133.832 287.491 145.912C286.593 148.607 284.28 156.238 279.523 162.093C267.842 176.469 253.366 179.964 241.187 184.256C234.997 186.253 221.182 187.722 208.723 187.243C193.149 186.644 163.798 184.547 137.143 179.156C115.818 174.843 102.101 164.481 102.101 164.481C102.101 164.481 96.1113 102.784 111.685 80.3215Z"
                fill="white"
                stroke="black"
                stroke-width="0.598997"
              />
              <path
                id="Vector 391"
                d="M244.962 87.2113C241.967 84.516 241.368 81.521 242.267 80.3228C243.249 79.0127 246.033 81.2511 247.358 83.0183C249.155 85.4142 248.111 90.0445 244.962 87.2113Z"
                fill="white"
                stroke="black"
                stroke-width="0.598997"
              />
              <path
                id="Vector 392"
                d="M245.234 84.5539C244.387 83.7636 244.217 82.8854 244.472 82.534C244.749 82.1499 245.486 82.4999 245.861 83.0181C246.369 83.7206 246.125 85.3847 245.234 84.5539Z"
                fill="white"
                stroke="black"
                stroke-width="0.598997"
              />
              <path
                id="Vector 390"
                d="M183.565 47.3779C179.731 50.7323 159.006 69.8403 148.524 77.0283C138.041 84.2163 135.346 90.5057 135.346 90.5057"
                stroke="black"
                stroke-width="0.598997"
              />
              <path
                id="Vector 388"
                d="M194.646 44.3828C205.928 50.7721 227.767 80.7531 229.688 93.5006C232.982 115.364 232.184 159.69 232.982 180.355"
                stroke="black"
                stroke-width="0.598997"
                stroke-dasharray="1.2 1.2"
              />
              <path
                id="Vector 387"
                d="M191.651 45.2803C193.149 48.2753 223.099 69.5397 225.794 94.9971C228.292 118.587 228.29 160.487 228.19 180.654"
                stroke="black"
                stroke-width="0.598997"
              />
              <path
                id="Vector 386"
                d="M187.758 48.2764C197.741 53.3678 218.666 70.1997 222.5 96.7952C226.333 123.391 224.896 163.983 223.698 180.954"
                stroke="black"
                stroke-width="0.598997"
                stroke-dasharray="1.2 1.2"
              />
              <path
                id="Vector 384"
                d="M101.802 157.901C103.898 162.094 118.884 169.133 131.271 172.576C165.713 182.151 193.748 182.151 203.931 182.151C212.077 182.151 228.924 182.955 243.882 178.267C256.862 174.198 263.649 170.48 272.035 165.089C280.421 159.698 287.01 143.824 287.01 143.824"
                stroke="black"
                stroke-width="0.598997"
                stroke-dasharray="1.2 1.2"
              />
              <path
                id="Vector 383"
                d="M180.57 43.4836C180.27 42.8847 180.27 41.6867 181.468 40.4887C182.666 39.2907 183.929 39.4635 184.763 39.2907C187.864 38.648 189.675 39.1709 191.352 39.8897C193.448 40.7882 194.347 41.3872 193.748 43.4836C193.149 45.5801 191.951 45.5801 190.753 46.1791C189.555 46.7781 185.661 46.7782 183.565 46.1791C182.413 45.85 181.468 45.2807 180.57 43.4836Z"
                fill="white"
                stroke="black"
                stroke-width="0.598997"
              />
              <path
                id="Vector 385"
                d="M180.87 45.5801C179.372 45.8796 154.632 52.1778 147.144 56.0713C141.154 59.1861 129.256 65.7463 124.264 69.2405"
                stroke="black"
                stroke-width="0.598997"
                stroke-dasharray="1.2 1.2"
              />
              <path
                id="Vector 389"
                d="M195.245 44.3828L235.079 55.1648C243.365 57.9601 263.831 69.2412 268.922 76.4292"
                stroke="black"
                stroke-width="0.598997"
                stroke-dasharray="1.2 1.2"
              />
              <g id="Group 273">
                <path
                  id="Vector 424"
                  d="M111.803 150.413L113.6 105.188C113.6 105.188 127.677 111.178 149.54 115.371C171.404 119.564 196.262 119.864 196.262 119.864V170.479C196.262 170.479 174.398 168.788 143.55 162.393C118.526 157.205 111.803 150.413 111.803 150.413Z"
                  stroke="black"
                  stroke-width="0.598997"
                />
                <path
                  id="Vector 425"
                  d="M193.866 121.661V164.789"
                  stroke="black"
                  stroke-width="0.598997"
                  stroke-dasharray="1.2 1.2"
                />
                <path
                  id="Vector 426"
                  d="M117.494 110.28L115.697 149.215"
                  stroke="black"
                  stroke-width="0.598997"
                  stroke-dasharray="1.2 1.2"
                />
              </g>
            </g>
            {findMeasure('circumference') && (
              <g id="circumference">
                <path
                  id="Ellipse 23"
                  d="M181.888 182.339C128.109 179.854 99.9985 154.086 99.9985 154.086M281.357 151.162C281.357 151.162 258.902 182.99 204.507 182.614"
                  stroke="#E55959"
                  stroke-width="2.99499"
                />
                <path
                  id="Vector 27"
                  d="M208.827 178.642L203.311 182.648L208.16 186.337"
                  stroke="#E55959"
                  stroke-width="2.39599"
                />
                <path
                  id="Vector 28"
                  d="M178.22 178.58L183.857 182.414L179.124 186.25"
                  stroke="#E55959"
                  stroke-width="2.39599"
                />
              </g>
            )}
            {findMeasure('head_circumference') && (
              <g id="head_circumference">
                <path
                  id="Ellipse 23_2"
                  d="M181.888 182.339C128.109 179.854 99.9985 154.086 99.9985 154.086M281.357 151.162C281.357 151.162 258.902 182.99 204.507 182.614"
                  stroke="#E55959"
                  stroke-width="2.99499"
                />
                <path
                  id="Vector 27_2"
                  d="M208.827 178.642L203.311 182.648L208.16 186.337"
                  stroke="#E55959"
                  stroke-width="2.39599"
                />
                <path
                  id="Vector 28_2"
                  d="M178.22 178.58L183.857 182.414L179.124 186.25"
                  stroke="#E55959"
                  stroke-width="2.39599"
                />
              </g>
            )}
            {findMeasure('brim') && (
              <g id="brim">
                <path
                  id="Vector 21"
                  d="M138.341 185.183L107.733 227.981"
                  stroke="#E55959"
                  stroke-width="2.99499"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M105.539 223.162L106.156 230.341L112.927 229.3"
                  stroke="#E55959"
                  stroke-width="2.39599"
                />
                <path
                  id="Vector 27_3"
                  d="M140.48 190.438L139.054 183.376L132.444 185.177"
                  stroke="#E55959"
                  stroke-width="2.39599"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_208_4990"
              x1="143.251"
              y1="173.176"
              x2="143.251"
              y2="221.695"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
