import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface PantsProps {
  measure: GarmentMeasure;
  className?: string;
}

export function Pants({ measure, className }: PantsProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_pants">
            <g id="pants">
              <g id="Group 11">
                <path
                  id="Vector 23"
                  d="M131.173 68.0684C134.296 44.0247 139.37 30.1424 140.931 26.3042L148.933 22.5962L197.723 22.7914C197.723 23.7021 197.918 26.3042 197.918 26.3042C197.918 26.3042 204.553 57.1394 206.31 82.7053C208.624 116.395 213.14 171.698 213.14 171.698L187.184 176.577C185.297 161.875 180.588 127.787 176.841 109.052C173.094 90.3165 168.254 80.1683 168.254 80.1683C168.254 80.1683 160.651 99.7097 159.276 106.124C158.105 111.589 155.503 118.354 153.421 128.373L143.468 175.601L118.683 171.698L120.244 148.279C122.586 131.56 128.051 92.112 131.173 68.0684Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.390319"
                />
                <path
                  id="Vector 20"
                  d="M140.736 26.6947L140.931 22.2061L197.723 22.7915L197.918 26.6947C193.559 27.9958 181.38 33.9156 169.034 33.9156C158.496 33.9156 146.591 29.0366 140.736 26.6947Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.390319"
                />
                <path
                  id="Vector 19"
                  d="M158.105 28.4513C154.109 27.6521 142.688 22.9868 140.931 22.4014L197.527 22.7917C193.754 24.418 189.37 27.2023 179.378 28.4513C172.742 29.2807 164.546 29.7394 158.105 28.4513Z"
                  fill="white"
                />
                <path
                  id="Vector 24"
                  d="M149.323 40.7462C153.695 41.0584 156.089 34.6962 156.349 31.964L140.736 26.4995C140.736 26.4995 139.314 29.7139 138.589 31.964C137.864 34.2164 137.028 37.8188 137.028 37.8188C139.11 38.4693 144.951 40.4339 149.323 40.7462Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.390319"
                />
                <path
                  id="Vector 25"
                  d="M189.136 41.7221C184.92 41.566 181.98 35.0216 181.329 32.3545C186.989 30.7932 197.918 26.6948 197.918 26.6948C197.918 26.6948 199.869 35.2818 200.455 39.7705C198.308 40.4861 193.351 41.8782 189.136 41.7221Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.390319"
                />
              </g>
              <g id="Group 12">
                <path
                  id="Vector 16"
                  d="M154.752 64.4585C151.988 61.3647 149.788 54.9142 149.526 54.4072C149.249 55.3474 148.685 58.6197 148.685 58.6197C148.768 58.9546 151.439 62.9265 155.826 66.4253C159.334 69.2244 162.775 71.001 164.546 71.3861C162.711 70.5526 157.261 67.2675 154.752 64.4585Z"
                  fill="url(#paint0_linear_207_4962)"
                  fill-opacity="0.87"
                />
                <path
                  id="Vector 41"
                  d="M198.739 107.751C201.502 104.657 205.852 95.1169 206.115 94.6099C206.392 95.5501 206.895 100.855 206.895 100.855C206.812 101.19 204.488 105.081 200.102 108.58C196.593 111.379 190.716 114.294 188.945 114.679C190.78 113.845 196.23 110.56 198.739 107.751Z"
                  fill="url(#paint1_linear_207_4962)"
                  fill-opacity="0.87"
                />
                <path
                  id="Vector 40"
                  d="M155.385 71.5876C153.465 70.7307 149.745 68.1843 149.518 68.0044C149.585 68.4967 150.004 70.1269 150.004 70.1269C150.112 70.2645 152.172 71.5465 154.927 72.2537C157.131 72.8195 160.454 72.9472 161.353 72.7576C160.331 72.7531 157.129 72.3656 155.385 71.5876Z"
                  fill="url(#paint2_linear_207_4962)"
                  fill-opacity="0.87"
                />
              </g>
              <g id="Group 13">
                <path
                  id="Vector 21"
                  d="M156.934 34.6963L157.129 27.8657L159.081 28.0609L158.886 35.0866L156.934 34.6963Z"
                  fill="white"
                />
                <path
                  id="Vector 22"
                  d="M179.378 34.6963L179.182 27.8657H180.939L181.134 34.6963H179.378Z"
                  fill="white"
                />
              </g>
              <g id="Group 23">
                <g id="Group 16">
                  <g id="Group 15">
                    <path
                      id="Vector 29"
                      d="M119.074 168.576C122.001 169.291 126.099 170.137 129.027 170.527C132.344 170.97 138.85 172.479 143.859 173.455"
                      stroke="black"
                      stroke-width="0.390319"
                    />
                    <path
                      id="Vector 30"
                      d="M212.75 169.161C212.75 169.161 203.187 170.918 199.87 171.698C196.612 172.465 191.998 173.259 186.989 174.235"
                      stroke="black"
                      stroke-width="0.390319"
                    />
                  </g>
                  <g id="Group 14">
                    <path
                      id="Vector 31"
                      d="M167.863 72.3618C167.863 72.3618 156.066 100.709 151.08 119.786C145.724 140.272 142.102 173.259 142.102 173.259"
                      stroke="black"
                      stroke-width="0.390319"
                      stroke-dasharray="0.78 0.78"
                    />
                    <path
                      id="Vector 32"
                      d="M167.864 72.5571C167.864 72.5571 174.001 83.9254 178.988 103.002C184.343 123.489 190.112 173.845 190.112 173.845"
                      stroke="black"
                      stroke-width="0.390319"
                      stroke-dasharray="0.78 0.78"
                    />
                  </g>
                </g>
                <g id="Group 22">
                  <path
                    id="Vector 37"
                    d="M141.322 22.4009L174.694 22.596L197.723 22.9864"
                    stroke="black"
                    stroke-width="0.390319"
                  />
                  <g id="Group 21">
                    <g id="Vector 28">
                      <path
                        d="M173.523 33.7202L167.278 33.9154C167.326 39.2092 167.184 48.6493 167.278 55.7732C167.278 55.7732 168.839 56.5539 171.962 52.8458C174.617 49.693 174.369 39.5099 173.523 33.7202Z"
                        fill="white"
                      />
                      <path
                        d="M168.254 79.973C168.254 79.973 167.437 67.821 167.278 55.7732M167.278 55.7732C167.184 48.6493 167.326 39.2092 167.278 33.9154L173.523 33.7202C174.369 39.5099 174.617 49.693 171.962 52.8458C168.839 56.5539 167.278 55.7732 167.278 55.7732Z"
                        stroke="black"
                        stroke-width="0.390319"
                      />
                    </g>
                    <g id="Group 20">
                      <g id="Group 19">
                        <path
                          id="Vector 26"
                          d="M156.349 31.9639C156.349 31.9639 155.734 39.0587 150.884 40.5509C148.347 41.3315 140.281 38.9246 137.028 37.8187"
                          stroke="black"
                          stroke-width="0.390319"
                        />
                        <path
                          id="Vector 27"
                          d="M181.524 32.354C181.524 32.354 182.891 39.3797 187.574 41.3313C190.697 42.6324 197.202 40.6808 200.455 39.5749"
                          stroke="black"
                          stroke-width="0.390319"
                        />
                      </g>
                      <g id="Group 18">
                        <path
                          id="Vector 33"
                          d="M140.931 22.4009L156.934 28.2557V27.8653L159.081 28.2557V28.8411C163.114 29.2315 169.425 29.4266 172.937 29.2315C175.322 29.099 178.987 28.4508 178.987 28.4508V27.6702C178.987 27.6702 180.614 27.6702 181.134 27.6702V28.0605C181.134 28.0605 187.653 27.0709 191.478 25.5234C193.975 24.5127 197.528 22.9864 197.528 22.9864"
                          stroke="black"
                          stroke-width="0.390319"
                        />
                        <path
                          id="Vector 34"
                          d="M140.736 26.499L156.934 32.1586"
                          stroke="black"
                          stroke-width="0.390319"
                        />
                        <path
                          id="Vector 35"
                          d="M197.918 26.6943C197.918 26.6943 192.862 28.7187 189.721 29.8169C186.536 30.9305 181.524 32.354 181.524 32.354"
                          stroke="black"
                          stroke-width="0.390319"
                        />
                        <path
                          id="Vector 36"
                          d="M159.081 32.7441C159.081 32.7441 165.326 34.1103 170.01 33.9151C174.114 33.7441 177.556 33.0044 179.183 32.7441"
                          stroke="black"
                          stroke-width="0.390319"
                        />
                        <g id="Group 17">
                          <path
                            id="Vector 38"
                            d="M156.934 28.0605C156.934 28.0605 156.804 32.5492 156.739 34.696L159.081 35.0863V28.4509"
                            stroke="black"
                            stroke-width="0.390319"
                          />
                          <path
                            id="Vector 39"
                            d="M178.987 28.2557C178.987 28.2557 179.248 32.5492 179.183 34.696H181.329L181.134 28.0605"
                            stroke="black"
                            stroke-width="0.390319"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </g>
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 21_2"
                  d="M142.019 171.841L121.023 168.666"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M123.749 165.752L120.06 168.656L123.049 171.971"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 27_2"
                  d="M140.008 174.614L143.66 171.664L140.63 168.386"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_thigh_width') && (
              <g id="product_thigh_width">
                <path
                  id="Vector 21_3"
                  d="M161.228 89.731L131.564 82.9004"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M133.785 79.7452L130.097 82.6497L133.086 85.9649"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 27_3"
                  d="M159.296 92.8362L162.948 89.8862L159.918 86.6083"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_thigh_circumference') && (
              <g id="product_thigh_circumference">
                <path
                  id="Vector 21_4"
                  d="M161.228 89.731L131.564 82.9004"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M133.785 79.7452L130.097 82.6497L133.086 85.9649"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 27_4"
                  d="M159.296 92.8362L162.948 89.8862L159.918 86.6083"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_lower_length') && (
              <g id="product_lower_length">
                <path
                  id="Vector 19_2"
                  d="M111.267 169.747L135.151 23.2136"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_5"
                  d="M137.603 25.8005L135.118 21.8171L131.496 24.4271"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 28_2"
                  d="M114.794 168.102L111.021 170.561L110.105 168.593L109.188 166.624"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19_3"
                  d="M140.196 29.2075L196.747 29.2075"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M194.429 32.3673L197.718 29.0168L194.331 26.1091"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 28_3"
                  d="M142.517 32.0691L138.96 29.3077L140.521 27.7979L142.081 26.2881"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 19_4"
                  d="M136.683 48.7236L199.284 48.7236"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M197.552 51.8834L200.841 48.533L197.454 45.6252"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 28_4"
                  d="M139.005 51.5848L135.447 48.8233L137.008 47.3135L138.569 45.8037"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_inside_leg') && (
              <g id="product_inside_leg">
                <path
                  id="Vector 19_5"
                  d="M173.551 76.4118L189.926 173.851"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_8"
                  d="M185.825 172.376L190.298 174.956L193.14 171.04"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 28_5"
                  d="M170.586 79.2676L173.228 75.2129L175.257 76.4529L177.285 77.6928"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_6"
                  d="M169.4 176.772L169.4 23.7065"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_9"
                  d="M172.56 26.0237L169.21 22.7347L166.302 26.1218"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 28_6"
                  d="M172.262 174.061L169.5 177.618L167.991 176.058L166.481 174.497"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_7"
                  d="M140.196 29.2075L196.747 29.2075"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_10"
                  d="M194.429 32.3673L197.718 29.0168L194.331 26.1091"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 28_7"
                  d="M142.517 32.0691L138.96 29.3077L140.521 27.7979L142.081 26.2881"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 19_8"
                  d="M136.683 48.7236L199.284 48.7236"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_11"
                  d="M197.552 51.8834L200.841 48.533L197.454 45.6252"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 28_8"
                  d="M139.005 51.5848L135.447 48.8233L137.008 47.3135L138.569 45.8037"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_back_hook') && (
              <g id="product_back_hook">
                <path
                  id="Vector 19_9"
                  d="M162.594 31.2285L162.594 80.3632"
                  stroke="#EDA7A7"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                  stroke-dasharray="3.9 3.9"
                />
                <path
                  id="Vector 27_12"
                  d="M159.434 78.4363L162.785 81.7252L165.692 78.3381"
                  stroke="#EDA7A7"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 28_9"
                  d="M159.732 33.5501L162.494 29.9924L164.004 31.5532L165.513 33.114"
                  stroke="#EDA7A7"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_hook') && (
              <g id="product_hook">
                <path
                  id="Vector 19_10"
                  d="M162.594 31.2285L162.594 80.3632"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_13"
                  d="M159.434 78.4363L162.785 81.7252L165.692 78.3381"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 28_10"
                  d="M159.732 33.5501L162.494 29.9924L164.004 31.5532L165.513 33.114"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_waistband_width') && (
              <g id="product_waistband_width">
                <path
                  id="Vector 19_11"
                  d="M140.196 29.2075L196.747 29.2075"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_14"
                  d="M194.429 32.3673L197.718 29.0168L194.331 26.1091"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 28_11"
                  d="M142.517 32.0691L138.96 29.3077L140.521 27.7979L142.081 26.2881"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_waistband_circumference') && (
              <g id="product_waistband_circumference">
                <path
                  id="Vector 19_12"
                  d="M140.196 29.2075L196.747 29.2075"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_15"
                  d="M194.429 32.3673L197.718 29.0168L194.331 26.1091"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 28_12"
                  d="M142.517 32.0691L138.96 29.3077L140.521 27.7979L142.081 26.2881"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_high_waist_width') && (
              <g id="product_high_waist_width">
                <path
                  id="Vector 19_13"
                  d="M140.196 29.2075L196.747 29.2075"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_16"
                  d="M194.429 32.3673L197.718 29.0168L194.331 26.1091"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 28_13"
                  d="M142.517 32.0691L138.96 29.3077L140.521 27.7979L142.081 26.2881"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_width') && (
              <g id="product_lower_waist_width">
                <path
                  id="Vector 19_14"
                  d="M140.196 29.2075L196.747 29.2075"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_17"
                  d="M194.429 32.3673L197.718 29.0168L194.331 26.1091"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 28_14"
                  d="M142.517 32.0691L138.96 29.3077L140.521 27.7979L142.081 26.2881"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_high_waist_circumference') && (
              <g id="product_high_waist_circumference">
                <path
                  id="Vector 19_15"
                  d="M140.196 29.2075L196.747 29.2075"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_18"
                  d="M194.429 32.3673L197.718 29.0168L194.331 26.1091"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 28_15"
                  d="M142.517 32.0691L138.96 29.3077L140.521 27.7979L142.081 26.2881"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_circumference') && (
              <g id="product_lower_waist_circumference">
                <path
                  id="Vector 19_16"
                  d="M140.196 29.2075L196.747 29.2075"
                  stroke="#E55959"
                  stroke-width="1.9516"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_19"
                  d="M194.429 32.3673L197.718 29.0168L194.331 26.1091"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
                <path
                  id="Vector 28_16"
                  d="M142.517 32.0691L138.96 29.3077L140.521 27.7979L142.081 26.2881"
                  stroke="#E55959"
                  stroke-width="1.56128"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4962"
              x1="149.843"
              y1="57.887"
              x2="148.557"
              y2="69.7346"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D5D5D5" />
              <stop offset="0.69487" stop-color="#C1C1C1" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_207_4962"
              x1="206.084"
              y1="100.041"
              x2="207.371"
              y2="111.889"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D2D2D2" />
              <stop offset="0.69487" stop-color="#CDCDCD" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_207_4962"
              x1="150.388"
              y1="69.5477"
              x2="152.253"
              y2="75.2934"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D5D5D5" />
              <stop offset="0.69487" stop-color="#C1C1C1" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_pants">
            <g id="pants">
              <g id="Group 11">
                <path
                  id="Vector 23"
                  d="M117.106 94.1456C121.959 56.781 129.844 35.2074 132.27 29.2429L144.705 23.4805L220.526 23.7838C220.526 25.1991 220.829 29.2429 220.829 29.2429C220.829 29.2429 231.141 77.1617 233.87 116.892C237.467 169.247 244.485 255.189 244.485 255.189L204.149 262.771C201.217 239.924 193.898 186.95 188.075 157.835C182.252 128.72 174.73 112.949 174.73 112.949C174.73 112.949 162.915 143.317 160.779 153.286C158.959 161.778 154.916 172.292 151.681 187.86L136.213 261.255L97.6961 255.189L100.122 218.795C103.762 192.814 112.254 131.51 117.106 94.1456Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.606567"
                />
                <path
                  id="Vector 20"
                  d="M131.967 29.8495L132.27 22.874L220.526 23.7839L220.829 29.8495C214.056 31.8714 195.129 41.071 175.943 41.071C159.566 41.071 141.066 33.489 131.967 29.8495Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.606567"
                />
                <path
                  id="Vector 19"
                  d="M158.959 32.579C152.749 31.3371 135 24.0871 132.27 23.1772L220.223 23.7838C214.359 26.3112 207.545 30.638 192.017 32.579C181.706 33.868 168.968 34.5807 158.959 32.579Z"
                  fill="white"
                />
                <path
                  id="Vector 24"
                  d="M145.312 51.6861C152.105 52.1713 155.825 42.2843 156.23 38.0383L131.967 29.5464C131.967 29.5464 129.757 34.5417 128.631 38.0383C127.504 41.5386 126.205 47.1368 126.205 47.1368C129.44 48.1478 138.518 51.2008 145.312 51.6861Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.606567"
                />
                <path
                  id="Vector 25"
                  d="M207.182 53.2025C200.631 52.9598 196.061 42.7897 195.05 38.6448C203.846 36.2186 220.829 29.8496 220.829 29.8496C220.829 29.8496 223.862 43.1941 224.772 50.1696C221.436 51.2817 213.733 53.4451 207.182 53.2025Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.606567"
                />
              </g>
              <g id="Group 12">
                <path
                  id="Vector 16"
                  d="M153.748 88.5359C149.454 83.7281 146.035 73.7039 145.626 72.916C145.196 74.3771 144.32 79.4624 144.32 79.4624C144.449 79.9828 148.601 86.1552 155.417 91.5925C160.87 95.9423 166.216 98.7033 168.968 99.3017C166.116 98.0063 157.647 92.9012 153.748 88.5359Z"
                  fill="url(#paint0_linear_207_4963)"
                  fill-opacity="0.87"
                />
                <path
                  id="Vector 41"
                  d="M222.105 155.814C226.399 151.006 233.159 136.18 233.567 135.393C233.998 136.854 234.78 145.098 234.78 145.098C234.651 145.618 231.039 151.665 224.223 157.102C218.77 161.452 209.637 165.982 206.885 166.58C209.737 165.285 218.206 160.18 222.105 155.814Z"
                  fill="url(#paint1_linear_207_4963)"
                  fill-opacity="0.87"
                />
                <path
                  id="Vector 40"
                  d="M154.732 99.6148C151.748 98.2832 145.967 94.3259 145.615 94.0464C145.719 94.8114 146.37 97.3449 146.37 97.3449C146.538 97.5586 149.739 99.551 154.02 100.65C157.445 101.529 162.609 101.728 164.006 101.433C162.418 101.426 157.442 100.824 154.732 99.6148Z"
                  fill="url(#paint2_linear_207_4963)"
                  fill-opacity="0.87"
                />
              </g>
              <g id="Group 13">
                <path
                  id="Vector 21"
                  d="M157.14 42.2844L157.443 31.6694L160.476 31.9727L160.172 42.8909L157.14 42.2844Z"
                  fill="white"
                />
                <path
                  id="Vector 22"
                  d="M192.017 42.2844L191.714 31.6694H194.443L194.747 42.2844H192.017Z"
                  fill="white"
                />
              </g>
              <g id="Group 23">
                <g id="Group 16">
                  <g id="Group 15">
                    <path
                      id="Vector 29"
                      d="M98.3023 250.337C102.852 251.449 109.221 252.763 113.77 253.37C118.926 254.057 129.035 256.403 136.819 257.919"
                      stroke="black"
                      stroke-width="0.606567"
                    />
                    <path
                      id="Vector 30"
                      d="M243.879 251.247C243.879 251.247 229.018 253.976 223.862 255.189C218.799 256.381 211.63 257.615 203.845 259.132"
                      stroke="black"
                      stroke-width="0.606567"
                    />
                  </g>
                  <g id="Group 14">
                    <path
                      id="Vector 31"
                      d="M174.123 100.818C174.123 100.818 155.79 144.87 148.041 174.516C139.719 206.353 134.09 257.616 134.09 257.616"
                      stroke="black"
                      stroke-width="0.606567"
                      stroke-dasharray="1.21 1.21"
                    />
                    <path
                      id="Vector 32"
                      d="M174.123 101.121C174.123 101.121 183.661 118.788 191.41 148.433C199.733 180.27 208.698 258.525 208.698 258.525"
                      stroke="black"
                      stroke-width="0.606567"
                      stroke-dasharray="1.21 1.21"
                    />
                  </g>
                </g>
                <g id="Group 22">
                  <path
                    id="Vector 37"
                    d="M132.877 23.1772L184.738 23.4805L220.526 24.0871"
                    stroke="black"
                    stroke-width="0.606567"
                  />
                  <g id="Group 21">
                    <g id="Vector 28">
                      <path
                        d="M182.919 40.7676L173.213 41.0709C173.289 49.2977 173.068 63.9678 173.213 75.0386C173.213 75.0386 175.64 76.2518 180.492 70.4894C184.618 65.5897 184.233 49.765 182.919 40.7676Z"
                        fill="white"
                      />
                      <path
                        d="M174.73 112.646C174.73 112.646 173.46 93.7612 173.213 75.0386M173.213 75.0386C173.068 63.9678 173.289 49.2977 173.213 41.0709L182.919 40.7676C184.233 49.765 184.618 65.5897 180.492 70.4894C175.64 76.2518 173.213 75.0386 173.213 75.0386Z"
                        stroke="black"
                        stroke-width="0.606567"
                      />
                    </g>
                    <g id="Group 20">
                      <g id="Group 19">
                        <path
                          id="Vector 26"
                          d="M156.23 38.0381C156.23 38.0381 155.274 49.0636 147.738 51.3826C143.795 52.5957 131.259 48.8552 126.205 47.1366"
                          stroke="black"
                          stroke-width="0.606567"
                        />
                        <path
                          id="Vector 27"
                          d="M195.353 38.6445C195.353 38.6445 197.476 49.5627 204.755 52.5956C209.607 54.6175 219.717 51.5846 224.772 49.866"
                          stroke="black"
                          stroke-width="0.606567"
                        />
                      </g>
                      <g id="Group 18">
                        <path
                          id="Vector 33"
                          d="M132.27 23.1772L157.139 32.2758V31.6692L160.476 32.2758V33.1856C166.743 33.7922 176.55 34.0955 182.009 33.7922C185.715 33.5863 191.411 32.579 191.411 32.579V31.3659C191.411 31.3659 193.938 31.3659 194.747 31.3659V31.9725C194.747 31.9725 204.878 30.4345 210.821 28.0298C214.702 26.4591 220.222 24.0871 220.222 24.0871"
                          stroke="black"
                          stroke-width="0.606567"
                        />
                        <path
                          id="Vector 34"
                          d="M131.967 29.5464L157.14 38.3416"
                          stroke="black"
                          stroke-width="0.606567"
                        />
                        <path
                          id="Vector 35"
                          d="M220.829 29.8496C220.829 29.8496 212.972 32.9956 208.091 34.7021C203.141 36.4327 195.353 38.6448 195.353 38.6448"
                          stroke="black"
                          stroke-width="0.606567"
                        />
                        <path
                          id="Vector 36"
                          d="M160.476 39.2515C160.476 39.2515 170.181 41.3745 177.46 41.0712C183.837 40.8055 189.187 39.6558 191.714 39.2515"
                          stroke="black"
                          stroke-width="0.606567"
                        />
                        <g id="Group 17">
                          <path
                            id="Vector 38"
                            d="M157.139 31.9727C157.139 31.9727 156.937 38.9482 156.836 42.2843L160.476 42.8909V32.5792"
                            stroke="black"
                            stroke-width="0.606567"
                          />
                          <path
                            id="Vector 39"
                            d="M191.411 32.2759C191.411 32.2759 191.815 38.9482 191.714 42.2843H195.05L194.747 31.9727"
                            stroke="black"
                            stroke-width="0.606567"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </g>
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 21_2"
                  d="M133.96 255.412L101.332 250.477"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M105.568 245.948L99.8353 250.462L104.481 255.614"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 27_2"
                  d="M130.835 259.721L136.511 255.137L131.802 250.043"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_thigh_width') && (
              <g id="product_thigh_width">
                <path
                  id="Vector 21_3"
                  d="M163.812 127.81L117.713 117.195"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M121.165 112.292L115.433 116.805L120.079 121.957"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 27_3"
                  d="M160.809 132.636L166.485 128.052L161.776 122.958"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_thigh_circumference') && (
              <g id="product_thigh_circumference">
                <path
                  id="Vector 21_4"
                  d="M163.812 127.81L117.713 117.195"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M121.165 112.292L115.433 116.805L120.079 121.957"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 27_4"
                  d="M160.809 132.636L166.485 128.052L161.776 122.958"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_lower_length') && (
              <g id="product_lower_length">
                <path
                  id="Vector 19_2"
                  d="M86.1711 252.156L123.289 24.4396"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_5"
                  d="M127.098 28.4602L123.236 22.2698L117.608 26.326"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 28_2"
                  d="M91.6523 249.602L85.7888 253.423L84.3648 250.363L82.9407 247.304"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19_3"
                  d="M131.128 33.7544L219.009 33.7544"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M215.408 38.6651L220.519 33.4584L215.256 28.9397"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 28_3"
                  d="M134.735 38.2015L129.207 33.9102L131.632 31.5638L134.058 29.2175"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 19_4"
                  d="M125.669 64.083L222.952 64.083"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M220.261 68.9932L225.372 63.7865L220.108 59.2679"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 28_4"
                  d="M129.276 68.5296L123.748 64.2383L126.173 61.892L128.599 59.5457"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_inside_leg') && (
              <g id="product_inside_leg">
                <path
                  id="Vector 19_5"
                  d="M182.963 107.112L208.41 258.535"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_8"
                  d="M202.037 256.243L208.988 260.252L213.404 254.167"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 28_5"
                  d="M178.355 111.549L182.46 105.248L185.613 107.175L188.766 109.102"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_6"
                  d="M176.512 263.075L176.512 25.2057"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_9"
                  d="M181.423 28.8071L176.216 23.6959L171.697 28.9596"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 28_6"
                  d="M180.959 258.861L176.668 264.389L174.321 261.964L171.975 259.538"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_7"
                  d="M131.128 33.7544L219.009 33.7544"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_10"
                  d="M215.408 38.6651L220.519 33.4584L215.256 28.9397"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 28_7"
                  d="M134.735 38.2015L129.207 33.9102L131.632 31.5638L134.058 29.2175"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 19_8"
                  d="M125.669 64.083L222.952 64.083"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_11"
                  d="M220.261 68.9932L225.372 63.7865L220.108 59.2679"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 28_8"
                  d="M129.276 68.5296L123.748 64.2383L126.173 61.892L128.599 59.5457"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_back_hook') && (
              <g id="product_back_hook">
                <path
                  id="Vector 19_9"
                  d="M165.935 36.896L165.935 113.253"
                  stroke="#EDA7A7"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                  stroke-dasharray="6.07 6.07"
                />
                <path
                  id="Vector 27_12"
                  d="M161.024 110.258L166.231 115.369L170.75 110.105"
                  stroke="#EDA7A7"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 28_9"
                  d="M161.488 40.5032L165.779 34.9744L168.125 37.3999L170.472 39.8254"
                  stroke="#EDA7A7"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_hook') && (
              <g id="product_hook">
                <path
                  id="Vector 19_10"
                  d="M165.935 36.896L165.935 113.253"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_13"
                  d="M161.024 110.258L166.231 115.369L170.75 110.105"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 28_10"
                  d="M161.488 40.5032L165.779 34.9744L168.125 37.3999L170.472 39.8254"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_waistband_width') && (
              <g id="product_waistband_width">
                <path
                  id="Vector 19_11"
                  d="M131.128 33.7544L219.009 33.7544"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_14"
                  d="M215.408 38.6651L220.519 33.4584L215.256 28.9397"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 28_11"
                  d="M134.735 38.2015L129.207 33.9102L131.632 31.5638L134.058 29.2175"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_waistband_circumference') && (
              <g id="product_waistband_circumference">
                <path
                  id="Vector 19_12"
                  d="M131.128 33.7544L219.009 33.7544"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_15"
                  d="M215.408 38.6651L220.519 33.4584L215.256 28.9397"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 28_12"
                  d="M134.735 38.2015L129.207 33.9102L131.632 31.5638L134.058 29.2175"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_high_waist_width') && (
              <g id="product_high_waist_width">
                <path
                  id="Vector 19_13"
                  d="M131.128 33.7544L219.009 33.7544"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_16"
                  d="M215.408 38.6651L220.519 33.4584L215.256 28.9397"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 28_13"
                  d="M134.735 38.2015L129.207 33.9102L131.632 31.5638L134.058 29.2175"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_width') && (
              <g id="product_lower_waist_width">
                <path
                  id="Vector 19_14"
                  d="M131.128 33.7544L219.009 33.7544"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_17"
                  d="M215.408 38.6651L220.519 33.4584L215.256 28.9397"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 28_14"
                  d="M134.735 38.2015L129.207 33.9102L131.632 31.5638L134.058 29.2175"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_high_waist_circumference') && (
              <g id="product_high_waist_circumference">
                <path
                  id="Vector 19_15"
                  d="M131.128 33.7544L219.009 33.7544"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_18"
                  d="M215.408 38.6651L220.519 33.4584L215.256 28.9397"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 28_15"
                  d="M134.735 38.2015L129.207 33.9102L131.632 31.5638L134.058 29.2175"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_circumference') && (
              <g id="product_lower_waist_circumference">
                <path
                  id="Vector 19_16"
                  d="M131.128 33.7544L219.009 33.7544"
                  stroke="#E55959"
                  stroke-width="3.03284"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_19"
                  d="M215.408 38.6651L220.519 33.4584L215.256 28.9397"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
                <path
                  id="Vector 28_16"
                  d="M134.735 38.2015L129.207 33.9102L131.632 31.5638L134.058 29.2175"
                  stroke="#E55959"
                  stroke-width="2.42627"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4963"
              x1="146.12"
              y1="78.3237"
              x2="144.121"
              y2="96.7353"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D5D5D5" />
              <stop offset="0.69487" stop-color="#C1C1C1" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_207_4963"
              x1="233.52"
              y1="143.833"
              x2="235.519"
              y2="162.245"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D2D2D2" />
              <stop offset="0.69487" stop-color="#CDCDCD" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_207_4963"
              x1="146.966"
              y1="96.4447"
              x2="149.865"
              y2="105.374"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D5D5D5" />
              <stop offset="0.69487" stop-color="#C1C1C1" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
