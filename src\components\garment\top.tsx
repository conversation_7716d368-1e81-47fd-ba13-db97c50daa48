import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface TopProps {
  measure: GarmentMeasure;
  className?: string;
}

export function Top({ measure, className }: TopProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_top">
            <g id="top">
              <path
                id="Union"
                d="M114.938 34.9467C117.111 32.313 125.644 26.9666 140.814 29.811C155.984 32.6554 163.464 36.5269 165.308 38.1072C167.151 36.5269 174.631 32.6554 189.801 29.811C204.971 26.9666 213.505 32.313 215.677 34.9467C215.677 39.6726 214.2 44.7677 212.635 50.1689C211.91 52.6691 211.167 55.2348 210.542 57.8599C205.556 78.7979 203.835 100.723 205.801 109.415C207.767 118.106 220.616 157.216 220.616 157.216C219.254 158.124 215.223 159.257 212.01 160.159C211.495 160.304 211 160.443 210.542 160.574C207.223 161.522 179.991 163.34 165.9 162.945C151.81 163.34 123.392 161.522 120.074 160.574C119.615 160.443 119.121 160.304 118.605 160.159C115.393 159.257 111.362 158.124 110 157.216C110 157.216 122.849 118.106 124.815 109.415C126.78 100.723 125.059 78.7979 120.074 57.8599C119.449 55.2348 118.705 52.6691 117.981 50.1689C116.415 44.7677 114.938 39.6726 114.938 34.9467Z"
                fill="white"
                stroke="black"
                stroke-width="0.410866"
              />
              <g id="Group 86">
                <g id="Group 84">
                  <path
                    id="Vector 105"
                    d="M180.32 38.5022C177.159 39.0948 166.098 40.4775 166.098 40.4775C171.958 38.7656 183.875 35.3418 184.665 35.3418C185.456 35.3418 186.443 36.2636 186.838 36.7245C185.719 37.1195 182.848 38.0282 180.32 38.5022Z"
                    fill="url(#paint0_linear_207_4956)"
                  />
                  <path
                    id="Vector 153"
                    d="M180.71 43.4762C177.508 43.1769 166.493 41.4646 166.493 41.4646C172.598 41.43 184.997 41.4152 185.757 41.6325C186.517 41.8497 187.213 43.0076 187.466 43.5593C186.281 43.6314 183.271 43.7156 180.71 43.4762Z"
                    fill="url(#paint1_linear_207_4956)"
                  />
                  <path
                    id="Vector 154"
                    d="M179.784 47.0849C176.676 46.2585 166.098 42.7421 166.098 42.7421C172.124 43.7211 184.354 45.764 185.067 46.1043C185.78 46.4446 186.274 47.7019 186.432 48.288C185.252 48.1624 182.27 47.746 179.784 47.0849Z"
                    fill="url(#paint2_linear_207_4956)"
                  />
                  <path
                    id="Vector 155"
                    d="M179.353 51.1062C176.408 49.8138 166.493 44.7192 166.493 44.7192C172.298 46.6092 184.071 50.5006 184.724 50.946C185.376 51.3915 185.672 52.7097 185.739 53.3131C184.591 53.0083 181.708 52.1402 179.353 51.1062Z"
                    fill="url(#paint3_linear_207_4956)"
                  />
                  <path
                    id="Vector 156"
                    d="M178.976 55.8047C176.228 54.1345 167.073 47.7745 167.073 47.7745C172.577 50.4149 183.733 55.8278 184.321 56.3556C184.909 56.8834 185.028 58.2291 185.014 58.836C183.918 58.3823 181.174 57.1408 178.976 55.8047Z"
                    fill="url(#paint4_linear_207_4956)"
                  />
                </g>
                <g id="Group 85">
                  <path
                    id="Vector 105_2"
                    d="M149.526 38.5022C152.686 39.0948 163.748 40.4775 163.748 40.4775C157.888 38.7656 145.97 35.3418 145.18 35.3418C144.39 35.3418 143.402 36.2636 143.007 36.7245C144.127 37.1195 146.997 38.0282 149.526 38.5022Z"
                    fill="url(#paint5_linear_207_4956)"
                  />
                  <path
                    id="Vector 153_2"
                    d="M149.136 43.4762C152.337 43.1769 163.353 41.4646 163.353 41.4646C157.248 41.43 144.848 41.4152 144.089 41.6325C143.329 41.8497 142.633 43.0076 142.38 43.5593C143.565 43.6314 146.574 43.7156 149.136 43.4762Z"
                    fill="url(#paint6_linear_207_4956)"
                  />
                  <path
                    id="Vector 154_2"
                    d="M150.062 47.0849C153.169 46.2585 163.748 42.7421 163.748 42.7421C157.722 43.7211 145.492 45.764 144.779 46.1043C144.065 46.4446 143.571 47.7019 143.413 48.288C144.593 48.1624 147.576 47.746 150.062 47.0849Z"
                    fill="url(#paint7_linear_207_4956)"
                  />
                  <path
                    id="Vector 155_2"
                    d="M150.493 51.1062C153.437 49.8138 163.353 44.7192 163.353 44.7192C157.548 46.6092 145.774 50.5006 145.122 50.946C144.469 51.3915 144.173 52.7097 144.107 53.3131C145.254 53.0083 148.137 52.1402 150.493 51.1062Z"
                    fill="url(#paint8_linear_207_4956)"
                  />
                  <path
                    id="Vector 156_2"
                    d="M150.869 55.8047C153.617 54.1345 162.772 47.7745 162.772 47.7745C157.268 50.4149 146.112 55.8278 145.524 56.3556C144.936 56.8834 144.817 58.2291 144.831 58.836C145.928 58.3823 148.671 57.1408 150.869 55.8047Z"
                    fill="url(#paint9_linear_207_4956)"
                  />
                </g>
              </g>
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19"
                  d="M120.272 49.3379L209.841 49.3379"
                  stroke="#E55959"
                  stroke-width="2.05433"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27"
                  d="M208.223 52.8439L211.685 49.3172L208.12 46.2564"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
                <path
                  id="Vector 28"
                  d="M122.211 52.3576L118.763 49.1043L120.551 47.6803L122.339 46.2563"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
              </g>
            )}
            {findMeasure('product_underbust') && (
              <g id="product_underbust">
                <path
                  id="Vector 19_2"
                  d="M123.559 61.2529L206.759 61.2529"
                  stroke="#E55959"
                  stroke-width="2.05433"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M204.525 64.7592L207.987 61.2324L204.422 58.1717"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
                <path
                  id="Vector 28_2"
                  d="M125.498 64.2729L122.05 61.0196L123.838 59.5956L125.626 58.1716"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19_3"
                  d="M128.489 100.696L202.445 100.696"
                  stroke="#E55959"
                  stroke-width="2.05433"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M200.416 104.202L203.878 100.676L200.313 97.6148"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
                <path
                  id="Vector 28_3"
                  d="M130.429 103.716L126.981 100.463L128.769 99.0387L130.557 97.6147"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
              </g>
            )}
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 19_4"
                  d="M113.698 154.931L217.442 154.931"
                  stroke="#E55959"
                  stroke-width="2.05433"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M215.208 158.437L218.67 154.91L215.104 151.849"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
                <path
                  id="Vector 28_4"
                  d="M115.638 157.95L112.189 154.697L113.977 153.273L115.766 151.849"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_5"
                  d="M131.981 32.6978L131.981 158.834"
                  stroke="#E55959"
                  stroke-width="2.05433"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_5"
                  d="M128.655 156.805L132.182 160.267L135.243 156.702"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
                <path
                  id="Vector 28_5"
                  d="M128.969 34.7782L131.876 31.0333L133.465 32.6762L135.055 34.3192"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_6"
                  d="M131.981 32.6978L131.981 158.834"
                  stroke="#E55959"
                  stroke-width="2.05433"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M128.655 156.805L132.182 160.267L135.243 156.702"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
                <path
                  id="Vector 28_6"
                  d="M128.969 34.7782L131.876 31.0333L133.465 32.6762L135.055 34.3192"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_7"
                  d="M120.272 49.3379L209.841 49.3379"
                  stroke="#E55959"
                  stroke-width="2.05433"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M208.223 52.8439L211.685 49.3172L208.12 46.2564"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
                <path
                  id="Vector 28_7"
                  d="M122.211 52.3576L118.763 49.1043L120.551 47.6803L122.339 46.2563"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_8"
                  d="M128.489 100.696L202.445 100.696"
                  stroke="#E55959"
                  stroke-width="2.05433"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_8"
                  d="M200.416 104.202L203.878 100.676L200.313 97.6148"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
                <path
                  id="Vector 28_8"
                  d="M130.429 103.716L126.981 100.463L128.769 99.0387L130.557 97.6147"
                  stroke="#E55959"
                  stroke-width="1.64347"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_9"
                  d="M131.981 32.6978L131.981 158.834"
                  stroke="#EDA7A7"
                  stroke-width="2.05433"
                  stroke-linecap="square"
                  stroke-dasharray="4.11 4.11"
                />
                <path
                  id="Vector 27_9"
                  d="M128.655 156.805L132.182 160.267L135.243 156.702"
                  stroke="#EDA7A7"
                  stroke-width="1.64347"
                />
                <path
                  id="Vector 28_9"
                  d="M128.969 34.7782L131.876 31.0333L133.465 32.6762L135.055 34.3192"
                  stroke="#EDA7A7"
                  stroke-width="1.64347"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4956"
              x1="159.777"
              y1="42.6503"
              x2="185.258"
              y2="35.1443"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_207_4956"
              x1="159.818"
              y1="41.8156"
              x2="186.381"
              y2="41.6055"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_207_4956"
              x1="159.457"
              y1="41.9807"
              x2="185.687"
              y2="46.1812"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_207_4956"
              x1="160.047"
              y1="42.95"
              x2="185.324"
              y2="51.117"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_207_4956"
              x1="160.918"
              y1="45.1692"
              x2="184.894"
              y2="56.6044"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint5_linear_207_4956"
              x1="170.069"
              y1="42.6503"
              x2="144.587"
              y2="35.1443"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint6_linear_207_4956"
              x1="170.027"
              y1="41.8156"
              x2="143.464"
              y2="41.6055"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint7_linear_207_4956"
              x1="170.388"
              y1="41.9807"
              x2="144.159"
              y2="46.1812"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint8_linear_207_4956"
              x1="169.798"
              y1="42.95"
              x2="144.521"
              y2="51.117"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint9_linear_207_4956"
              x1="168.928"
              y1="45.1692"
              x2="144.951"
              y2="56.6044"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_top">
            <g id="top">
              <path
                id="Union"
                d="M96.3336 54.8313C99.5604 50.9201 112.233 42.9802 134.762 47.2044C157.29 51.4285 168.398 57.178 171.136 59.5248C173.874 57.178 184.982 51.4285 207.511 47.2044C230.04 42.9802 242.712 50.9201 245.939 54.8313C245.939 61.8496 243.746 69.4162 241.421 77.4375C240.344 81.1504 239.24 84.9607 238.312 88.8591C230.908 119.954 228.352 152.515 231.272 165.422C234.191 178.329 253.272 236.411 253.272 236.411C251.25 237.759 245.263 239.441 240.493 240.782C239.727 240.997 238.993 241.203 238.312 241.398C233.384 242.806 192.941 245.504 172.016 244.918C151.091 245.504 108.889 242.806 103.961 241.398C103.279 241.203 102.545 240.997 101.779 240.782C97.009 239.441 91.0222 237.759 89 236.411C89 236.411 108.081 178.329 111.001 165.422C113.92 152.515 111.364 119.954 103.961 88.8591C103.032 84.9607 101.928 81.1504 100.852 77.4375C98.5268 69.4162 96.3336 61.8496 96.3336 54.8313Z"
                fill="white"
                stroke="black"
                stroke-width="0.610167"
              />
              <g id="Group 86">
                <g id="Group 84">
                  <path
                    id="Vector 105"
                    d="M193.43 60.1115C188.737 60.9915 172.31 63.0449 172.31 63.0449C181.012 60.5026 198.71 55.418 199.884 55.418C201.057 55.418 202.524 56.7869 203.111 57.4714C201.448 58.0581 197.185 59.4074 193.43 60.1115Z"
                    fill="url(#paint0_linear_207_4957)"
                  />
                  <path
                    id="Vector 153"
                    d="M194.009 67.4982C189.255 67.0537 172.896 64.5109 172.896 64.5109C181.962 64.4596 200.377 64.4376 201.505 64.7602C202.633 65.0829 203.667 66.8023 204.043 67.6217C202.283 67.7287 197.813 67.8538 194.009 67.4982Z"
                    fill="url(#paint1_linear_207_4957)"
                  />
                  <path
                    id="Vector 154"
                    d="M192.634 72.8572C188.019 71.63 172.31 66.4079 172.31 66.4079C181.259 67.8617 199.421 70.8956 200.48 71.4009C201.539 71.9063 202.273 73.7735 202.508 74.6439C200.755 74.4574 196.326 73.8391 192.634 72.8572Z"
                    fill="url(#paint2_linear_207_4957)"
                  />
                  <path
                    id="Vector 155"
                    d="M191.994 78.829C187.621 76.9096 172.896 69.3438 172.896 69.3438C181.517 72.1506 199.001 77.9296 199.97 78.5912C200.939 79.2527 201.379 81.2103 201.477 82.1064C199.774 81.6537 195.492 80.3646 191.994 78.829Z"
                    fill="url(#paint3_linear_207_4957)"
                  />
                  <path
                    id="Vector 156"
                    d="M191.435 85.8067C187.354 83.3264 173.758 73.8813 173.758 73.8813C181.932 77.8026 198.499 85.8411 199.372 86.6249C200.246 87.4087 200.423 89.4072 200.402 90.3084C198.773 89.6346 194.699 87.791 191.435 85.8067Z"
                    fill="url(#paint4_linear_207_4957)"
                  />
                </g>
                <g id="Group 85">
                  <path
                    id="Vector 105_2"
                    d="M147.699 60.1115C152.392 60.9915 168.819 63.0449 168.819 63.0449C160.117 60.5026 142.418 55.418 141.245 55.418C140.072 55.418 138.605 56.7869 138.018 57.4714C139.68 58.0581 143.944 59.4074 147.699 60.1115Z"
                    fill="url(#paint5_linear_207_4957)"
                  />
                  <path
                    id="Vector 153_2"
                    d="M147.119 67.4982C151.874 67.0537 168.233 64.5109 168.233 64.5109C159.166 64.4596 140.752 64.4376 139.624 64.7602C138.496 65.0829 137.462 66.8023 137.086 67.6217C138.846 67.7287 143.316 67.8538 147.119 67.4982Z"
                    fill="url(#paint6_linear_207_4957)"
                  />
                  <path
                    id="Vector 154_2"
                    d="M148.495 72.8572C153.109 71.63 168.819 66.4079 168.819 66.4079C159.87 67.8617 141.708 70.8956 140.649 71.4009C139.59 71.9063 138.856 73.7735 138.621 74.6439C140.374 74.4574 144.803 73.8391 148.495 72.8572Z"
                    fill="url(#paint7_linear_207_4957)"
                  />
                  <path
                    id="Vector 155_2"
                    d="M149.135 78.829C153.507 76.9096 168.233 69.3438 168.233 69.3438C159.612 72.1506 142.128 77.9296 141.159 78.5912C140.19 79.2527 139.75 81.2103 139.651 82.1064C141.355 81.6537 145.637 80.3646 149.135 78.829Z"
                    fill="url(#paint8_linear_207_4957)"
                  />
                  <path
                    id="Vector 156_2"
                    d="M149.694 85.8067C153.775 83.3264 167.371 73.8813 167.371 73.8813C159.197 77.8026 142.63 85.8411 141.756 86.6249C140.883 87.4087 140.706 89.4072 140.727 90.3084C142.356 89.6346 146.43 87.791 149.694 85.8067Z"
                    fill="url(#paint9_linear_207_4957)"
                  />
                </g>
              </g>
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19"
                  d="M104.254 76.2034L237.271 76.2034"
                  stroke="#E55959"
                  stroke-width="3.05084"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27"
                  d="M234.868 81.4101L240.01 76.1726L234.715 71.6271"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
                <path
                  id="Vector 28"
                  d="M107.135 80.6879L102.014 75.8566L104.67 73.7418L107.325 71.627"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
              </g>
            )}
            {findMeasure('product_under_bust') && (
              <g id="product_underbust">
                <path
                  id="Vector 19_2"
                  d="M109.135 93.8979L232.694 93.8979"
                  stroke="#E55959"
                  stroke-width="3.05084"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M229.377 99.1049L234.518 93.8674L229.223 89.3219"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
                <path
                  id="Vector 28_2"
                  d="M112.016 98.3828L106.895 93.5514L109.551 91.4366L112.206 89.3219"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19_3"
                  d="M116.457 152.474L226.288 152.474"
                  stroke="#E55959"
                  stroke-width="3.05084"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M223.275 157.681L228.417 152.444L223.122 147.898"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
                <path
                  id="Vector 28_3"
                  d="M119.338 156.959L114.217 152.128L116.873 150.013L119.528 147.898"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
              </g>
            )}
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 19_4"
                  d="M94.4915 233.016L248.559 233.016"
                  stroke="#E55959"
                  stroke-width="3.05084"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M245.241 238.223L250.383 232.986L245.088 228.44"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
                <path
                  id="Vector 28_4"
                  d="M97.3723 237.501L92.2514 232.67L94.9068 230.555L97.5623 228.44"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_5"
                  d="M121.644 51.4915L121.644 238.813"
                  stroke="#E55959"
                  stroke-width="3.05084"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_5"
                  d="M116.704 235.8L121.942 240.942L126.488 235.647"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
                <path
                  id="Vector 28_5"
                  d="M117.171 54.5811L121.488 49.0196L123.848 51.4595L126.208 53.8994"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_6"
                  d="M121.644 51.4915L121.644 238.813"
                  stroke="#E55959"
                  stroke-width="3.05084"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M116.704 235.8L121.942 240.942L126.487 235.647"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
                <path
                  id="Vector 28_6"
                  d="M117.171 54.5811L121.487 49.0196L123.848 51.4595L126.208 53.8994"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_7"
                  d="M104.254 76.2034L237.271 76.2034"
                  stroke="#E55959"
                  stroke-width="3.05084"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M234.868 81.4101L240.01 76.1726L234.715 71.6271"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
                <path
                  id="Vector 28_7"
                  d="M107.135 80.6879L102.014 75.8566L104.67 73.7418L107.325 71.627"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_8"
                  d="M116.457 152.474L226.288 152.474"
                  stroke="#E55959"
                  stroke-width="3.05084"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_8"
                  d="M223.275 157.681L228.417 152.444L223.122 147.898"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
                <path
                  id="Vector 28_8"
                  d="M119.338 156.959L114.217 152.128L116.873 150.013L119.528 147.898"
                  stroke="#E55959"
                  stroke-width="2.44067"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_9"
                  d="M121.644 51.4915L121.644 238.813"
                  stroke="#EDA7A7"
                  stroke-width="3.05084"
                  stroke-linecap="square"
                  stroke-dasharray="6.1 6.1"
                />
                <path
                  id="Vector 27_9"
                  d="M116.704 235.8L121.942 240.942L126.488 235.647"
                  stroke="#EDA7A7"
                  stroke-width="2.44067"
                />
                <path
                  id="Vector 28_9"
                  d="M117.171 54.5811L121.488 49.0196L123.848 51.4595L126.208 53.8994"
                  stroke="#EDA7A7"
                  stroke-width="2.44067"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4957"
              x1="162.923"
              y1="66.2717"
              x2="200.764"
              y2="55.1246"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_207_4957"
              x1="162.984"
              y1="65.0321"
              x2="202.432"
              y2="64.7202"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_207_4957"
              x1="162.448"
              y1="65.2771"
              x2="201.401"
              y2="71.5152"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_207_4957"
              x1="163.324"
              y1="66.7164"
              x2="200.862"
              y2="78.8451"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_207_4957"
              x1="164.617"
              y1="70.0122"
              x2="200.223"
              y2="86.9944"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint5_linear_207_4957"
              x1="178.206"
              y1="66.2717"
              x2="140.365"
              y2="55.1246"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint6_linear_207_4957"
              x1="178.145"
              y1="65.0321"
              x2="138.697"
              y2="64.7202"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint7_linear_207_4957"
              x1="178.681"
              y1="65.2771"
              x2="139.728"
              y2="71.5152"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint8_linear_207_4957"
              x1="177.805"
              y1="66.7164"
              x2="140.266"
              y2="78.8451"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint9_linear_207_4957"
              x1="176.512"
              y1="70.0122"
              x2="140.906"
              y2="86.9944"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
