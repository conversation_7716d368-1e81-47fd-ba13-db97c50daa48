import type React from 'react';
import { useTranslation } from 'react-i18next';
import Menu from './menu';
import { Text } from '../atoms/text';
import { Button } from '../atoms/button';
import { Container } from '../atoms/container';
import CloseIcon from '../icons/close';
import { cn } from '@/lib/utils';
import getQueryParams from '@/lib/get-query-params';

interface HeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Function to call when the close button is clicked */
  onClose: () => void;
  /** Additional classes to apply to the header */
  className?: string;
  /** Additional classes to apply to the close button */
  iconClassName?: string;
}

export const Header = ({ onClose, className, iconClassName, ...props }: HeaderProps) => {
  const { t } = useTranslation();

  const { disableCloseApp } = getQueryParams<{ disableCloseApp?: string }>();
  const shouldHideCloseButton = disableCloseApp === 'true';

  return (
    <Container
      maxWidth="full"
      as="header"
      className={cn('flex flex-row items-center justify-center p-3 relative', className)}
      {...props}
    >
      <Menu className="absolute w-full m-auto left-2" />
      <Text variant="h1" className="text-sm size-chart__title">
        {t('generics.chart')}
      </Text>
      {!shouldHideCloseButton && (
        <Button variant="blank" onClick={onClose} className={cn('absolute w-fit m-auto right-2', iconClassName)}>
          <CloseIcon />
        </Button>
      )}
    </Container>
  );
};
