import React, { useState, useCallback } from 'react';
import { Button } from '../atoms/button';
import { Text } from '../atoms/text';
import { Container } from '../atoms/container';
import Arrow from '../icons/arrow';
import Check from '../icons/check';
import { cn } from '@/lib/utils';
import { decodedRecommendedSize } from '@/lib/decode-base64';

export interface CarouselItem {
  id: string | number;
  content: string;
  isSelected?: boolean;
}

interface CarouselProps extends React.HtmlHTMLAttributes<HTMLDivElement> {
  items: CarouselItem[];
  onSelectItem?: (item: CarouselItem) => void;
  selectedItem?: CarouselItem;
  className?: string;
  buttonClassName?: string;
}

export const Carousel = ({
  items,
  onSelectItem,
  selectedItem,
  className,
  buttonClassName,
  ...props
}: CarouselProps) => {
  const [currentIndex, setCurrentIndex] = useState(items.findIndex((i) => i.id === selectedItem?.id) || 0);

  const handlePrevious = useCallback(() => {
    const item = items[currentIndex - 1];
    setCurrentIndex((prevIndex) => (prevIndex > 0 ? prevIndex - 1 : prevIndex));
    onSelectItem?.(item);
  }, [currentIndex, items, onSelectItem]);

  const handleNext = useCallback(() => {
    const item = items[currentIndex + 1];
    setCurrentIndex((prevIndex) => (prevIndex < items.length - 1 ? prevIndex + 1 : prevIndex));
    onSelectItem?.(item);
  }, [currentIndex, items, onSelectItem]);

  const handleSelect = useCallback(
    (item: CarouselItem) => {
      const index = items.findIndex((i) => i.id === item.id);
      if (index !== -1) {
        setCurrentIndex(index);
        onSelectItem?.(item);
      }
    },
    [items, onSelectItem]
  );

  const getVisibleItems = () => {
    if (items.length === 1) {
      return [null, items[0], null];
    }

    const previousItem = currentIndex > 0 ? items[currentIndex - 1] : null;
    const currentItem = items[currentIndex];
    const nextItem = currentIndex < items.length - 1 ? items[currentIndex + 1] : null;

    return [previousItem, currentItem, nextItem];
  };

  const arrowButtonStyle = 'absolute z-10 p-2 disabled:opacity-30 disabled:cursor-not-allowed hover:brightness-50';

  const isRecommendedSize = (item: CarouselItem | null) => {
    return item && item.content === decodedRecommendedSize && selectedItem?.content === item.content;
  };

  return (
    <Container className={cn('relative flex items-center', className)} {...props}>
      <Button
        variant="blank"
        onClick={handlePrevious}
        disabled={currentIndex === 0}
        className={cn(
          `${arrowButtonStyle} left-0 -translate-x-full`,
          currentIndex === 0 ? 'carousel__arrow-disabled' : 'carousel__arrow-enabled'
        )}
        aria-label="Previous items"
      >
        <Arrow className="rotate-90" color="#6D6D6D" />
      </Button>

      <div data-test-id="carousel-items" className="w-full grid grid-cols-[0.8fr_1fr_0.8fr] gap-4 justify-center ">
        {getVisibleItems().map((item, index) => (
          <Button
            data-test-id={`carousel-item-${index}`}
            key={index}
            variant="blank"
            className={cn(
              'w-full h-fit px-6 py-3 text-center border rounded-lg relative',
              index === 1 ? 'carousel__selected-item' : 'carousel__item',
              buttonClassName,
              item ? (index === 1 ? 'border-[#272727]' : 'border-[#E7E7E7]') : 'border-transparent pointer-events-none'
            )}
            onClick={() => item && handleSelect(item)}
            disabled={!item?.content}
          >
            {item?.content && <Text variant="body">{item.content}</Text>}
            {isRecommendedSize(item) && (
              <Check className="absolute top-0 right-0 translate-y-[-40%] translate-x-[40%]" />
            )}
          </Button>
        ))}
      </div>

      <Button
        variant="blank"
        onClick={handleNext}
        disabled={currentIndex >= items.length - 1}
        className={cn(
          `${arrowButtonStyle} right-0 translate-x-full`,
          currentIndex >= items.length - 1 ? 'carousel__arrow-disabled' : 'carousel__arrow-enabled'
        )}
        aria-label="Next items"
      >
        <Arrow className="-rotate-90" color="#6D6D6D" />
      </Button>
    </Container>
  );
};
