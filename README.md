# Measurements Table Widget

A React-based widget for displaying size measurements, built with Vite, TypeScript, and modern tooling.

## 🚀 Features

- 📱 Responsive design with mobile and desktop layouts
- 🌍 Internationalization support (i18n)
- 📊 Interactive measurement tables
- 🎨 Storybook component documentation
- ✅ Comprehensive testing with Cypress
- 📏 Code coverage tracking
- 🎯 Type safety with TypeScript

## 🛠️ Tech Stack

- React 18.3
- TypeScript
- Vite
- TailwindCSS
- React Query
- i18next
- Storybook
- Cypress
- ESLint + Prettier
- Husky + lint-staged

## 📋 Prerequisites

- Node.js v22.13.1
- pnpm v10.x

## 🚀 Getting Started

1. Clone the repository
2. Install dependencies:
```bash
pnpm install
```

3. Copy the environment variables:
```bash
cp .env.example .env
```

4. Start the development server:
```bash
pnpm dev
```

## 📜 Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm preview` - Preview production build
- `pnpm lint` - Run ESLint
- `pnpm lint:fix` - Fix ESLint issues
- `pnpm format` - Format code with Prettier
- `pnpm storybook` - Start Storybook server
- `pnpm cy:open` - Open Cypress test runner
- `pnpm cy:run` - Run Cypress tests headlessly
- `pnpm coverage` - View test coverage report

## 🧪 Testing

The project uses Cypress for end-to-end and component testing. Tests run automatically on pull requests via GitHub Actions.

To run tests locally:
```bash
pnpm cy:run
```

To check coverage:
```bash
pnpm coverage
```

## 📚 Documentation

Component documentation is available through Storybook. To view:
```bash
pnpm storybook
```

## 🔄 Git Workflow

1. Branch naming:
   - Feature: `feature/your-feature-name`
   - Bug fix: `fix/issue-description`
   - Release: `release/version-number`

2. Commit messages follow conventional commits standard

3. Pull requests:
   - Must pass all checks (tests, coverage, lint)
   - Require code review
   - Can only merge to main from release branch

## 🌍 Internationalization

The widget supports multiple languages through i18next. Translations are stored in `src/locales/`.

To add a new language:
1. Create a new translation file in `src/locales/`
2. Add the language to the i18n configuration in `src/hooks/i18n.ts`

## 📦 Deployment

Storybook documentation is automatically deployed to AWS S3 when changes are pushed to the develop branch.

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request