import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface VestProps {
  measure: GarmentMeasure;
  className?: string;
}

export function Vest({ measure, className }: VestProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_vest">
            <g id="vest">
              <path
                id="Vector 141"
                d="M143.861 26.399C143.861 26.399 128.955 30.8078 125.177 31.6476C125.946 38.9955 128.536 57.4703 127.486 69.4369C126.558 80.0106 118.249 84.1327 113 87.4918C116.359 103.447 117.619 115.204 118.668 128.22C119.013 132.488 118.318 149.914 117.619 155.513C120.698 157.542 127.654 161.979 130.845 163.49C134.036 165.002 148.83 169.998 157.088 172.728L168.634 160.971L181.021 172.728C181.021 172.728 194.252 168.162 201.175 163.49C207.686 159.097 217.97 155.513 217.97 155.513C217.97 155.513 215.296 139.471 214.401 128.22C212.932 109.745 216.991 89.3113 217.97 82.6632C215.871 81.4035 206.634 80.1439 204.954 69.4369C203.274 58.7299 206.564 46.2035 206.634 31.6476C201.875 30.178 185.849 25.3493 185.849 25.3493C185.849 25.3493 183.75 22.2002 181.021 20.7307C178.837 19.555 171.387 18.0466 165.065 18.0014C159.859 17.9642 157.046 18.6732 151.839 19.681C145.331 20.9406 143.861 26.399 143.861 26.399Z"
                fill="white"
                stroke="black"
                stroke-width="0.419881"
              />
              <g id="Group 81">
                <g id="Group 80">
                  <path
                    id="Vector 148"
                    d="M139.453 138.067L137.773 61.4385C137.773 61.4385 136.723 68.5972 136.723 74.0557C136.723 97.0775 139.243 137.857 137.773 139.746C136.597 141.258 136.303 156.892 136.303 164.519L139.453 138.067Z"
                    fill="url(#paint0_linear_207_4954)"
                  />
                  <path
                    id="Vector 142"
                    d="M125.806 137.038C125.946 135.149 126.226 131.244 126.226 130.74C126.226 130.74 133.445 133.53 138.193 134.939C142.806 136.307 150.16 137.878 150.16 137.878V144.806L125.806 137.038Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.419881"
                  />
                  <path
                    id="Vector 145"
                    d="M195.927 138.067L197.606 61.4385C197.606 61.4385 198.656 69.437 198.656 74.8955C198.656 97.9173 196.137 137.857 197.606 139.746C198.782 141.258 199.076 156.892 199.076 164.519L195.927 138.067Z"
                    fill="url(#paint1_linear_207_4954)"
                  />
                  <path
                    id="Vector 143"
                    d="M210.325 134.901C209.98 133.038 209.274 129.187 209.219 128.686C209.219 128.686 202.349 132.249 197.783 134.168C193.347 136.033 186.21 138.398 186.21 138.398L186.967 145.284L210.325 134.901Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.419881"
                  />
                </g>
                <g id="Group 79">
                  <g id="Group 78">
                    <g id="Group 64">
                      <path
                        id="Vector 135"
                        d="M168.728 93.7434L170.314 93.7901"
                        stroke="black"
                        stroke-width="0.419881"
                      />
                      <circle
                        id="Ellipse 8"
                        cx="167.585"
                        cy="93.5802"
                        r="1.25964"
                        stroke="black"
                        stroke-width="0.419881"
                      />
                    </g>
                    <g id="Group 65">
                      <path
                        id="Vector 135_2"
                        d="M168.728 108.02L170.314 108.066"
                        stroke="black"
                        stroke-width="0.419881"
                      />
                      <circle
                        id="Ellipse 8_2"
                        cx="167.585"
                        cy="107.856"
                        r="1.25964"
                        stroke="black"
                        stroke-width="0.419881"
                      />
                    </g>
                    <g id="Group 66">
                      <path
                        id="Vector 135_3"
                        d="M169.567 123.135L171.154 123.182"
                        stroke="black"
                        stroke-width="0.419881"
                      />
                      <circle
                        id="Ellipse 8_3"
                        cx="168.424"
                        cy="122.972"
                        r="1.25964"
                        stroke="black"
                        stroke-width="0.419881"
                      />
                    </g>
                    <g id="Group 67">
                      <path
                        id="Vector 135_4"
                        d="M169.567 136.991L171.154 137.038"
                        stroke="black"
                        stroke-width="0.419881"
                      />
                      <circle
                        id="Ellipse 8_4"
                        cx="168.424"
                        cy="136.828"
                        r="1.25964"
                        stroke="black"
                        stroke-width="0.419881"
                      />
                    </g>
                  </g>
                  <path id="Vector 149" d="M168.004 150.894H171.573" stroke="black" stroke-width="0.419881" />
                </g>
              </g>
              <g id="Group 82">
                <path
                  id="Vector 146"
                  d="M148.464 50.3323C152.049 62.7187 164.855 81.1936 166.535 84.5526C166.535 84.5526 175.142 69.6468 178.921 58.31C182.7 46.9732 184.59 30.3039 182.91 25.7692C180.733 19.8908 171.334 19.4709 163.765 19.4709C154.107 19.471 151.224 20.4331 147.809 25.7692C144.45 31.0177 145.854 41.3135 148.464 50.3323Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.419881"
                />
                <path
                  id="Vector 147"
                  d="M166.745 84.3425C166.745 84.3425 163.596 89.3811 162.756 96.729C161.397 108.62 165.065 138.717 164.855 141.656C164.645 144.596 162.966 146.905 164.855 152.153C166.367 156.352 168.144 160.201 168.844 161.601"
                  stroke="black"
                  stroke-width="0.419881"
                />
                <path
                  id="Vector 150"
                  d="M156.458 27.6584L156.038 25.1392H170.734C170.734 25.1392 170.104 27.6584 170.314 27.6584H156.458Z"
                  stroke="black"
                  stroke-width="0.419881"
                />
              </g>
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19"
                  d="M116.989 90.2209L213.562 90.2209"
                  stroke="#E55959"
                  stroke-width="2.09941"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27"
                  d="M211.069 93.8041L214.607 90.2L210.963 87.072"
                  stroke="#E55959"
                  stroke-width="1.67953"
                />
                <path
                  id="Vector 28"
                  d="M118.971 93.3071L115.447 89.9824L117.275 88.5272L119.102 87.0719"
                  stroke="#E55959"
                  stroke-width="1.67953"
                />
              </g>
            )}
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 19_2"
                  d="M120.348 156.142L213.981 156.142"
                  stroke="#E55959"
                  stroke-width="2.09941"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M211.908 159.726L215.446 156.121L211.803 152.993"
                  stroke="#E55959"
                  stroke-width="1.67953"
                />
                <path
                  id="Vector 28_2"
                  d="M122.33 159.228L118.806 155.904L120.634 154.449L122.461 152.993"
                  stroke="#E55959"
                  stroke-width="1.67953"
                />
              </g>
            )}
            <g id="product_waist_width">
              <path
                id="Vector 19_3"
                d="M121.188 122.552L210.622 122.552"
                stroke="#E55959"
                stroke-width="2.09941"
                stroke-linecap="square"
              />
              <path
                id="Vector 27_3"
                d="M208.969 126.135L212.507 122.531L208.864 119.403"
                stroke="#E55959"
                stroke-width="1.67953"
              />
              <path
                id="Vector 28_3"
                d="M123.17 125.638L119.646 122.313L121.473 120.858L123.301 119.403"
                stroke="#E55959"
                stroke-width="1.67953"
              />
            </g>
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_4"
                  d="M166.09 20.7307L166.09 171.888"
                  stroke="#E55959"
                  stroke-width="2.09941"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M162.73 169.393L166.051 173.161L169.388 169.732"
                  stroke="#E55959"
                  stroke-width="1.67953"
                />
                <path
                  id="Vector 28_4"
                  d="M162.724 23.1363L165.984 19.3823L169.262 22.823"
                  stroke="#E55959"
                  stroke-width="1.67953"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_5"
                  d="M136.698 31.6475L136.698 171.888"
                  stroke="#E55959"
                  stroke-width="2.09941"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_5"
                  d="M133.339 169.393L136.66 173.161L139.997 169.732"
                  stroke="#E55959"
                  stroke-width="1.67953"
                />
                <path
                  id="Vector 28_5"
                  d="M133.333 34.0533L136.593 30.2993L139.871 33.74"
                  stroke="#E55959"
                  stroke-width="1.67953"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_6"
                  d="M116.989 90.2209L213.562 90.2209"
                  stroke="#E55959"
                  stroke-width="2.09941"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M211.069 93.8041L214.607 90.2L210.963 87.072"
                  stroke="#E55959"
                  stroke-width="1.67953"
                />
                <path
                  id="Vector 28_6"
                  d="M118.971 93.3071L115.447 89.9824L117.275 88.5272L119.102 87.0719"
                  stroke="#E55959"
                  stroke-width="1.67953"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_7"
                  d="M121.188 122.552L210.622 122.552"
                  stroke="#E55959"
                  stroke-width="2.09941"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M208.969 126.135L212.507 122.531L208.864 119.403"
                  stroke="#E55959"
                  stroke-width="1.67953"
                />
                <path
                  id="Vector 28_7"
                  d="M123.17 125.638L119.646 122.313L121.473 120.858L123.301 119.403"
                  stroke="#E55959"
                  stroke-width="1.67953"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_9"
                  d="M129.585 35.2166L204.744 35.2166"
                  stroke="#EDA7A7"
                  stroke-width="2.09941"
                  stroke-linecap="square"
                  stroke-dasharray="4.2 4.2"
                />
                <path
                  id="Vector 27_9"
                  d="M202.251 38.7998L205.789 35.1956L202.146 32.0676"
                  stroke="#EDA7A7"
                  stroke-width="1.67953"
                />
                <path
                  id="Vector 28_9"
                  d="M131.568 38.3027L128.044 34.978L129.871 33.5228L131.698 32.0675"
                  stroke="#EDA7A7"
                  stroke-width="1.67953"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4954"
              x1="136.315"
              y1="60.6195"
              x2="136.315"
              y2="164.519"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#D9D9D9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_207_4954"
              x1="199.064"
              y1="60.6195"
              x2="199.064"
              y2="164.519"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#D9D9D9" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_vest">
            <g id="vest">
              <path
                id="Vector 141"
                d="M140.661 46.8826C140.661 46.8826 119.573 53.12 114.227 54.3081C115.316 64.7036 118.979 90.8409 117.494 107.771C116.182 122.73 104.425 128.562 97 133.314C101.752 155.887 103.534 172.52 105.019 190.935C105.506 196.974 104.524 221.627 103.534 229.547C107.891 232.418 117.732 238.695 122.246 240.834C126.761 242.972 147.691 250.041 159.373 253.903L175.709 237.27L193.233 253.903C193.233 253.903 211.951 247.443 221.747 240.834C230.958 234.618 245.508 229.547 245.508 229.547C245.508 229.547 241.725 206.852 240.459 190.935C238.379 164.798 244.122 135.888 245.508 126.483C242.538 124.701 229.469 122.919 227.093 107.771C224.717 92.623 229.37 74.9011 229.469 54.3081C222.737 52.229 200.064 45.3975 200.064 45.3975C200.064 45.3975 197.094 40.9423 193.233 38.8632C190.144 37.1999 179.603 35.0659 170.66 35.002C163.294 34.9493 159.314 35.9525 151.948 37.3782C142.74 39.1602 140.661 46.8826 140.661 46.8826Z"
                fill="white"
                stroke="black"
                stroke-width="0.594031"
              />
              <g id="Group 81">
                <g id="Group 80">
                  <path
                    id="Vector 148"
                    d="M134.424 204.866L132.048 96.4548C132.048 96.4548 130.563 106.583 130.563 114.305C130.563 146.875 134.127 204.569 132.048 207.242C130.385 209.38 129.969 231.498 129.969 242.29L134.424 204.866Z"
                    fill="url(#paint0_linear_207_4955)"
                  />
                  <path
                    id="Vector 142"
                    d="M115.118 203.41C115.316 200.737 115.712 195.212 115.712 194.499C115.712 194.499 125.925 198.446 132.642 200.44C139.169 202.376 149.572 204.598 149.572 204.598V214.399L115.118 203.41Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.594031"
                  />
                  <path
                    id="Vector 145"
                    d="M214.321 204.866L216.697 96.4548C216.697 96.4548 218.182 107.771 218.182 115.493C218.182 148.064 214.618 204.569 216.697 207.242C218.361 209.38 218.776 231.498 218.776 242.29L214.321 204.866Z"
                    fill="url(#paint1_linear_207_4955)"
                  />
                  <path
                    id="Vector 143"
                    d="M234.692 200.386C234.203 197.751 233.205 192.303 233.127 191.594C233.127 191.594 223.407 196.634 216.948 199.35C210.672 201.988 200.574 205.334 200.574 205.334L201.646 215.077L234.692 200.386Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.594031"
                  />
                </g>
                <g id="Group 79">
                  <g id="Group 78">
                    <g id="Group 64">
                      <path
                        id="Vector 135"
                        d="M175.841 142.159L178.085 142.225"
                        stroke="black"
                        stroke-width="0.594031"
                      />
                      <circle
                        id="Ellipse 8"
                        cx="174.224"
                        cy="141.928"
                        r="1.78209"
                        stroke="black"
                        stroke-width="0.594031"
                      />
                    </g>
                    <g id="Group 65">
                      <path
                        id="Vector 135_2"
                        d="M175.841 162.356L178.085 162.422"
                        stroke="black"
                        stroke-width="0.594031"
                      />
                      <circle
                        id="Ellipse 8_2"
                        cx="174.224"
                        cy="162.125"
                        r="1.78209"
                        stroke="black"
                        stroke-width="0.594031"
                      />
                    </g>
                    <g id="Group 66">
                      <path
                        id="Vector 135_3"
                        d="M177.029 183.741L179.273 183.807"
                        stroke="black"
                        stroke-width="0.594031"
                      />
                      <circle
                        id="Ellipse 8_3"
                        cx="175.412"
                        cy="183.51"
                        r="1.78209"
                        stroke="black"
                        stroke-width="0.594031"
                      />
                    </g>
                    <g id="Group 67">
                      <path
                        id="Vector 135_4"
                        d="M177.029 203.344L179.273 203.41"
                        stroke="black"
                        stroke-width="0.594031"
                      />
                      <circle
                        id="Ellipse 8_4"
                        cx="175.412"
                        cy="203.113"
                        r="1.78209"
                        stroke="black"
                        stroke-width="0.594031"
                      />
                    </g>
                  </g>
                  <path id="Vector 149" d="M174.818 223.013H179.867" stroke="black" stroke-width="0.594031" />
                </g>
              </g>
              <g id="Group 82">
                <path
                  id="Vector 146"
                  d="M147.173 80.7427C152.245 98.2665 170.363 124.404 172.739 129.156C172.739 129.156 184.917 108.068 190.263 92.0292C195.609 75.9904 198.282 52.4073 195.906 45.9918C192.826 37.6754 179.529 37.0813 168.82 37.0813C155.157 37.0813 151.078 38.4425 146.246 45.9918C141.494 53.4171 143.481 67.9833 147.173 80.7427Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.594031"
                />
                <path
                  id="Vector 147"
                  d="M173.036 128.859C173.036 128.859 168.581 135.988 167.393 146.383C165.47 163.206 170.66 205.786 170.363 209.944C170.066 214.103 167.69 217.37 170.363 224.795C172.501 230.736 175.016 236.181 176.006 238.161"
                  stroke="black"
                  stroke-width="0.594031"
                />
                <path
                  id="Vector 150"
                  d="M158.482 48.6648L157.888 45.1006H178.679C178.679 45.1006 177.788 48.6648 178.085 48.6648H158.482Z"
                  stroke="black"
                  stroke-width="0.594031"
                />
              </g>
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 19"
                  d="M102.643 137.175L239.27 137.175"
                  stroke="#E55959"
                  stroke-width="2.97016"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27"
                  d="M235.744 142.245L240.749 137.146L235.594 132.72"
                  stroke="#E55959"
                  stroke-width="2.37613"
                />
                <path
                  id="Vector 28"
                  d="M105.448 141.541L100.462 136.838L103.048 134.779L105.633 132.72"
                  stroke="#E55959"
                  stroke-width="2.37613"
                />
              </g>
            )}
            {findMeasure('product_hem') && (
              <g id="product_hem">
                <path
                  id="Vector 19_2"
                  d="M107.395 230.438L239.864 230.438"
                  stroke="#E55959"
                  stroke-width="2.97016"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M236.932 235.508L241.937 230.409L236.782 225.983"
                  stroke="#E55959"
                  stroke-width="2.37613"
                />
                <path
                  id="Vector 28_2"
                  d="M110.2 234.804L105.215 230.101L107.8 228.042L110.385 225.983"
                  stroke="#E55959"
                  stroke-width="2.37613"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19_3"
                  d="M108.584 182.916L235.112 182.916"
                  stroke="#E55959"
                  stroke-width="2.97016"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M232.773 187.985L237.779 182.886L232.624 178.461"
                  stroke="#E55959"
                  stroke-width="2.37613"
                />
                <path
                  id="Vector 28_3"
                  d="M111.388 187.282L106.403 182.578L108.988 180.52L111.573 178.461"
                  stroke="#E55959"
                  stroke-width="2.37613"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_4"
                  d="M172.109 38.8633L172.109 252.715"
                  stroke="#E55959"
                  stroke-width="2.97016"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M167.356 249.185L172.055 254.515L176.776 249.665"
                  stroke="#E55959"
                  stroke-width="2.37613"
                />
                <path
                  id="Vector 28_4"
                  d="M167.348 42.2667L171.96 36.9557L176.598 41.8235"
                  stroke="#E55959"
                  stroke-width="2.37613"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_5"
                  d="M130.527 54.3081L130.527 252.715"
                  stroke="#E55959"
                  stroke-width="2.97016"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_5"
                  d="M125.775 249.185L130.473 254.515L135.194 249.665"
                  stroke="#E55959"
                  stroke-width="2.37613"
                />
                <path
                  id="Vector 28_5"
                  d="M125.767 57.7115L130.379 52.4005L135.016 57.2683"
                  stroke="#E55959"
                  stroke-width="2.37613"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 19_6"
                  d="M102.643 137.175L239.27 137.175"
                  stroke="#E55959"
                  stroke-width="2.97016"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M235.744 142.245L240.749 137.146L235.594 132.72"
                  stroke="#E55959"
                  stroke-width="2.37613"
                />
                <path
                  id="Vector 28_6"
                  d="M105.448 141.541L100.462 136.838L103.048 134.779L105.633 132.72"
                  stroke="#E55959"
                  stroke-width="2.37613"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_7"
                  d="M108.584 182.916L235.112 182.916"
                  stroke="#E55959"
                  stroke-width="2.97016"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M232.773 187.985L237.779 182.886L232.624 178.461"
                  stroke="#E55959"
                  stroke-width="2.37613"
                />
                <path
                  id="Vector 28_7"
                  d="M111.388 187.282L106.403 182.578L108.988 180.52L111.573 178.461"
                  stroke="#E55959"
                  stroke-width="2.37613"
                />
              </g>
            )}
            {findMeasure('product_back_length') && (
              <g id="product_back_length">
                <path
                  id="Vector 19_8"
                  d="M172.109 38.8633L172.109 252.715"
                  stroke="#EDA7A7"
                  stroke-width="2.97016"
                  stroke-linecap="square"
                  stroke-dasharray="5.94 5.94"
                />
                <path
                  id="Vector 27_8"
                  d="M167.356 249.185L172.055 254.515L176.776 249.665"
                  stroke="#EDA7A7"
                  stroke-width="2.37613"
                />
                <path
                  id="Vector 28_8"
                  d="M167.348 42.2667L171.96 36.9557L176.598 41.8235"
                  stroke="#EDA7A7"
                  stroke-width="2.37613"
                />
              </g>
            )}
            {findMeasure('product_shoulder_length') && (
              <g id="product_shoulder_length">
                <path
                  id="Vector 19_9"
                  d="M120.464 59.3572L226.796 59.3572"
                  stroke="#EDA7A7"
                  stroke-width="2.97016"
                  stroke-linecap="square"
                  stroke-dasharray="5.94 5.94"
                />
                <path
                  id="Vector 27_9"
                  d="M223.269 64.4266L228.274 59.3275L223.12 54.9023"
                  stroke="#EDA7A7"
                  stroke-width="2.37613"
                />
                <path
                  id="Vector 28_9"
                  d="M123.269 63.7235L118.283 59.0199L120.869 56.961L123.454 54.9022"
                  stroke="#EDA7A7"
                  stroke-width="2.37613"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_207_4955"
              x1="129.985"
              y1="95.2962"
              x2="129.985"
              y2="242.29"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#D9D9D9" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_207_4955"
              x1="218.76"
              y1="95.2962"
              x2="218.76"
              y2="242.29"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#D9D9D9" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
