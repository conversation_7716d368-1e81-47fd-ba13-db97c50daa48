import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppContext } from '@/store';
import { getMappedLanguages } from '@/lib/get-mapped-languages';

export type ObservationTranslations = {
  id: string;
  modelingId: string;
  language: string;
  observation: string;
}[];

type Translation = {
  [key: string]: string;
};

/**
 * Parses the language from different formats to a standardized format
 * Similar to the parseLanguage function in the original Observation component
 */
const parseLanguage = (language: string): string => {
  if (['esAR', 'esAR'].includes(language)) return 'esAR';
  if (['pt-BR', 'ptBR', 'ptBr', 'br'].includes(language)) return 'ptBR';
  if (['pt', 'pt-PT', 'ptPT', 'PT'].includes(language)) return 'pt';
  if (['cs', 'cz'].includes(language)) return 'cs';
  if (['gr', 'el'].includes(language)) return 'el';
  if (['dk', 'da'].includes(language)) return 'da';

  return language;
};

/**
 * Parses translation text and extracts language-specific content
 * Similar to the parseTranslation function in the original Observation component
 */
const parseTranslation = (text: string, lang: string, languages: string[]): string => {
  const matchContent = text.matchAll(/\[([^\]]+)\]([^[\]]+)/g);
  const translations: Translation = {};

  for (const match of matchContent) {
    const langCode = match[1].match(/\w+/)?.[0];
    const content = match[2];

    if (langCode) {
      translations[langCode] = content;
    }
  }

  const translationsKey = Object.keys(translations);

  if (!translationsKey.length || languages.length === 1) return text;

  return translations[lang] || translations[translationsKey[0]];
};

/**
 * Hook for managing observation text with internationalization support
 * Replicates the behavior of the original Observation component
 */
export function useObservation(
  observationText: string,
  observationTranslations?: ObservationTranslations
) {
  const {
    app: { config },
  } = useAppContext();
  const { i18n } = useTranslation();

  const processedObservation = useMemo(() => {
    const { language: innerLanguage } = getMappedLanguages(i18n.language);
    const sanitizedLanguage = parseLanguage(innerLanguage.split('-')[0]);

    // Find the appropriate translation from observationTranslations array
    const matchingTranslation = observationTranslations?.find(
      ({ language }) => language === parseLanguage(sanitizedLanguage)
    ) || observationTranslations?.[0];

    // Parse the observation text for embedded translations
    const parsedTranslation = parseTranslation(
      observationText,
      sanitizedLanguage,
      config.general.language
    );

    // Return the appropriate observation text
    if (observationTranslations?.length) {
      return matchingTranslation?.observation || parsedTranslation;
    }

    return parsedTranslation;
  }, [observationText, observationTranslations, i18n.language, config.general.language]);

  const hasObservation = Boolean(observationText || observationTranslations?.length);

  return {
    /** The processed observation text based on current language */
    observationText: processedObservation,
    /** Whether there is any observation content to display */
    hasObservation,
    /** The current language being used */
    currentLanguage: i18n.language,
  };
}

/**
 * Hook specifically for working with product modeling observations
 * This is a convenience hook for the common use case with product data
 */
export function useProductObservation() {
  const { product } = useAppContext();
  
  const modelingObservationTranslations = product?.modelingInfo?.modelingObservationTranslations || [];
  const observationText = product?.modelingInfo?.observation || '';

  return useObservation(observationText, modelingObservationTranslations);
}
