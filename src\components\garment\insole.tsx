import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface InsoleProps {
  measure: GarmentMeasure;
  className?: string;
}

export function Insole({ measure, className }: InsoleProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width={isMobile ? '331' : '342'}
          height={isMobile ? '193' : '291'}
          viewBox={isMobile ? '0 0 331 193' : '0 0 342 291'}
          fill="none"
        >
          <g id="group_insole">
            <g id="insole">
              <path
                id="Vector 420"
                d="M147.101 35.4102C150.864 25.4508 159.274 27.4426 161.709 27C163.996 27 169.455 28.0623 172.996 32.3117C177.422 37.6234 187.382 55.5503 189.374 66.395C191.366 77.2397 189.374 86.0925 188.046 92.5108C186.983 97.6454 186.201 120.618 185.168 132.127C184.209 140.39 186.275 148.947 179.635 159.792C172.885 170.818 160.381 166.874 154.848 163.112C149.315 159.35 146.88 146.513 148.429 137.217C149.831 128.807 147.544 104.019 147.544 98.0438C147.544 92.0682 142.454 76.3544 140.462 70.1575C138.47 63.9605 143.339 45.3696 147.101 35.4102Z"
                fill="white"
                stroke="black"
                stroke-width="0.387507"
              />
              <path
                id="Vector 421"
                d="M166.578 90.9613C153.298 92.7318 147.765 81.4445 145.773 76.1328C145.773 76.1328 149.189 87.6022 150.2 95.1664C150.808 99.7197 151.085 106.896 151.085 106.896C151.085 106.896 160.107 104.904 164.364 104.904C172.111 104.904 182.291 106.896 182.291 106.896L186.718 83.4364C184.431 85.3545 177.201 89.5448 166.578 90.9613Z"
                fill="url(#paint0_linear_208_4995)"
              />
              <path
                id="Vector 422"
                d="M152.856 147.059V131.25C152.856 131.25 159.986 127.408 164.613 127.48C169.026 127.548 179.631 131.25 179.631 131.25L179.857 148.283C179.857 148.283 170.028 143.567 164.613 143.388C159.404 143.216 152.856 147.059 152.856 147.059Z"
                fill="url(#paint1_linear_208_4995)"
              />
            </g>
            {findMeasure('insole_width') && (
              <g id="insole_width">
                <path
                  id="Vector 21"
                  d="M186.501 68.7266L143.282 68.7266"
                  stroke="#E55959"
                  stroke-width="1.93754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M145.475 65.75L141.938 68.7858L145.042 71.9487"
                  stroke="#E55959"
                  stroke-width="1.55003"
                />
                <path
                  id="Vector 27"
                  d="M184.936 71.574L188.148 68.1962L184.74 65.3633"
                  stroke="#E55959"
                  stroke-width="1.55003"
                />
              </g>
            )}
            {findMeasure('insole_length') && (
              <g id="insole_length">
                <path
                  id="Vector 21_2"
                  d="M164.923 163.396L164.923 30.4688"
                  stroke="#E55959"
                  stroke-width="1.93754"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M168.002 32.4678L164.754 29.1244L161.788 32.4175"
                  stroke="#E55959"
                  stroke-width="1.55003"
                />
                <path
                  id="Vector 27_2"
                  d="M161.577 162.024L164.955 165.236L167.788 161.828"
                  stroke="#E55959"
                  stroke-width="1.55003"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_208_4995"
              x1="166.135"
              y1="91.6252"
              x2="166.135"
              y2="125.266"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_208_4995"
              x1="165.756"
              y1="127.48"
              x2="165.756"
              y2="159.676"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="1" stop-color="#FDFDFD" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_insole">
            <g id="insole">
              <path
                id="Vector 420"
                d="M142.21 48.2761C148.149 32.5544 161.425 35.6987 165.269 35C168.879 35 177.496 36.677 183.086 43.3849C190.074 51.7698 205.795 80.0687 208.94 97.1878C212.084 114.307 208.94 128.282 206.844 138.413C205.167 146.519 203.932 182.783 202.302 200.951C200.788 213.994 204.048 227.503 193.567 244.622C182.912 262.026 163.172 255.802 154.438 249.862C145.704 243.923 141.861 223.66 144.306 208.986C146.519 195.71 142.909 156.581 142.909 147.148C142.909 137.715 134.873 112.909 131.729 103.127C128.585 93.3448 136.271 63.9977 142.21 48.2761Z"
                fill="white"
                stroke="black"
                stroke-width="0.611708"
              />
              <path
                id="Vector 421"
                d="M172.955 135.968C151.992 138.763 143.258 120.945 140.114 112.561C140.114 112.561 145.505 130.666 147.101 142.606C148.062 149.794 148.499 161.123 148.499 161.123C148.499 161.123 162.741 157.979 169.461 157.979C181.689 157.979 197.76 161.123 197.76 161.123L204.747 124.09C201.137 127.118 189.724 133.732 172.955 135.968Z"
                fill="url(#paint0_linear_208_4996)"
              />
              <path
                id="Vector 422"
                d="M151.294 224.522V199.567C151.294 199.567 162.55 193.501 169.853 193.615C176.82 193.723 193.56 199.567 193.56 199.567L193.917 226.454C193.917 226.454 178.402 219.01 169.853 218.727C161.631 218.456 151.294 224.522 151.294 224.522Z"
                fill="url(#paint1_linear_208_4996)"
              />
            </g>
            {findMeasure('insole_width') && (
              <g id="insole_width">
                <path
                  id="Vector 21"
                  d="M204.405 100.867L136.181 100.867"
                  stroke="#E55959"
                  stroke-width="3.05854"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M139.642 96.1699L134.058 100.962L138.959 105.955"
                  stroke="#E55959"
                  stroke-width="2.44683"
                />
                <path
                  id="Vector 27"
                  d="M201.935 105.363L207.005 100.031L201.625 95.5586"
                  stroke="#E55959"
                  stroke-width="2.44683"
                />
              </g>
            )}
            {findMeasure('insole_length') && (
              <g id="insole_length">
                <path
                  id="Vector 21_2"
                  d="M170.343 250.311L170.343 40.4766"
                  stroke="#E55959"
                  stroke-width="3.05854"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M175.203 43.6314L170.076 38.3536L165.395 43.5521"
                  stroke="#E55959"
                  stroke-width="2.44683"
                />
                <path
                  id="Vector 27_2"
                  d="M165.061 248.148L170.393 253.218L174.865 247.838"
                  stroke="#E55959"
                  stroke-width="2.44683"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_208_4996"
              x1="172.256"
              y1="137.016"
              x2="172.256"
              y2="190.121"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_208_4996"
              x1="171.658"
              y1="193.615"
              x2="171.658"
              y2="244.439"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="1" stop-color="#FDFDFD" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
