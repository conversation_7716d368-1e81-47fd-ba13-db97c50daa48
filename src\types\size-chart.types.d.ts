import { filterByClotheTypes } from '@/components/body_measures';

export type ProductInfo = {
  isShoe: boolean;
  clothesType: keyof typeof filterByClotheTypes;
  accessory: boolean;
  ageGroup: string;
  gender: string;
  coverImage: string;
  name: string;
};

export type measure = {
  text: string;
  type: string;
  finalValue: number;
  initialValue: number;
};

type Sizes = {
  [key: string]: {
    sizeName: string;
    composedMeasureValue: string;
    garmentMeasurements: {
      [key: string]: measure;
    };
    sizeSystemsGrid: {
      [key: string]: string;
    };
  };
};

type ModelingObservationTranslations = {
  id: string;
  modelingId: string;
  language: string;
  observation: string;
};

export type Display = {
  bodyMeasurement: {
    [key: string]: {
      position: number;
      hidden: boolean;
    };
  };
  garmentMeasurement: {
    [key: string]: {
      position: number;
      hidden: boolean;
    };
  };
};

export type ModelingInfo = {
  composedMeasure: string;
  composedMeasureOrder: string;
  modelingGender: string;
  modelingId: number;
  modelingSizeSystem: string;
  showSizeSystem: string[];
  sizes: Sizes;
  modelingObservationTranslations: ModelingObservationTranslations[];
  template: string;
  display: Display;
  observation: string;
};

export type Product = {
  productInfo: ProductInfo;
  modelingInfo: ModelingInfo;
};
