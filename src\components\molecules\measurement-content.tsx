import { Container } from '../atoms/container';
import { Text } from '../atoms/text';
import { cn } from '@/lib/utils';

export interface MeasurementContentProps {
  image: React.ReactNode;
  value: string;
  className?: string;
}

export function MeasurementContent({ image, value, className }: MeasurementContentProps) {
  return (
    <Container className={cn('w-full flex flex-col gap-3 p-0', className)}>
      <div className="w-full bg-[#E7E7E7] rounded-lg overflow-hidden flex justify-center items-center">{image}</div>
      <Text variant="label" className="text-[#272727] inline-block text-[12px]">
        {value}
      </Text>
    </Container>
  );
}
