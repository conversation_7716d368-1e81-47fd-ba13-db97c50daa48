import type { <PERSON>a, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Switch } from './switch';
import { Text } from './text';

const meta = {
  title: 'Atoms/Switch',
  component: Switch,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    checked: { control: 'boolean', description: 'The checked state of the switch' },
    onChange: { action: 'changed', description: 'The onChange event handler' },
  },
} satisfies Meta<typeof Switch>;

export default meta;
type Story = StoryObj<typeof meta>;

const InteractiveSwitch = ({ checked = false }: { checked?: boolean }) => {
  const [isChecked, setIsChecked] = useState(checked);
  const setTextColor = (checked: boolean) => (checked ? 'text-[#262626]' : 'text-[#5D5D5D]');

  const handleSwitchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsChecked(event.target.checked);
  };

  return (
    <div className="flex gap-2 justify-center items-center">
      <Text variant="label" className={setTextColor(!isChecked)}>
        cm
      </Text>
      <Switch checked={isChecked} onChange={handleSwitchChange} />
      <Text variant="label" className={setTextColor(isChecked)}>
        in
      </Text>
    </div>
  );
};

export const Default: Story = {
  render: () => <InteractiveSwitch />,
};

export const Checked: Story = {
  render: () => <InteractiveSwitch checked />,
};
