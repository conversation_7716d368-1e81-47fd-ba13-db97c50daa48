import { CentralSeam as ChildCentralSeam } from './child/central_seam';
import { LowerBody as ChildLowerBody } from './child/lower_body';
import { MidBody as ChildMidBody } from './child/mid_body';
import { FullBody as ChildFullBody } from './child/fullbody';
import { UpperBody as ChildUpperBody } from './child/upper_body';
import { Sleeve as ChildSleeve } from './child/sleeve';
import { FootLength as ChildFootLength } from './child/foot_length';
import { FootWidth as ChildFootWidth } from './child/foot_width';

import { CentralSeam as FemaleCentralSeam } from './female/central_seam';
import { LowerBody as FemaleLowerBody } from './female/lower_body';
import { MidBody as FemaleMidBody } from './female/mid_body';
import { Fullbody as FemaleFullBody } from './female/fullbody';
import { UpperBody as FemaleUpperBody } from './female/upper_body';
import { Sleeve as FemaleSleeve } from './female/sleeve';
import { FootLength as FemaleFootLength } from './female/foot_length';
import { FootWidth as FemaleFootWidth } from './female/foot_width';
import { ShoulderLength as FemaleShoulderLength } from './female/shoulder_length';
import { Ring as FemaleRing } from './female/ring';
import { Hand as FemaleHand } from './female/hand';

import { CentralSeam as MaleCentralSeam } from './male/central_seam';
import { LowerBody as MaleLowerBody } from './male/lower_body';
import { MidBody as MaleMidBody } from './male/mid_body';
import { Fullbody as MaleFullBody } from './male/fullbody';
import { UpperBody as MaleUpperBody } from './male/upper_body';
import { Sleeve as MaleSleeve } from './male/sleeve';
import { FootLength as MaleFootLength } from './male/foot_length';
import { FootWidth as MaleFootWidth } from './male/foot_width';
import { ShoulderLength as MaleShoulderLength } from './male/shoulder_length';
import { Ring as MaleRing } from './male/ring';
import { Hand as MaleHand } from './male/hand';

export const bodyMeasures = {
  child: {
    fullbody: ['height', 'fullbodyLength'],
    lower_body: ['hip', 'thigh', 'insideLeg', 'lowerLength'],
    mid_body: ['body_length', 'upperWaist', 'waist', 'waistLower', 'chest'],
    upper_body: ['headCircumference', 'neck', 'collar'],
    sleeve: ['biceps', 'sleeve'],
    insoleLength: ['insoleLength'],
    insoleWidth: ['insoleWidth'],
    central_seam: ['centralSeam'],
  },
  male: {
    fullbody: ['height', 'fullbodyLength'],
    lower_body: ['hip', 'thigh', 'insideLeg', 'lowerLength'],
    mid_body: ['body_length', 'upperWaist', 'waist', 'waistLower', 'chest'],
    upper_body: ['headCircumference', 'neck', 'collar'],
    sleeve: ['wrist', 'fist', 'biceps', 'sleeve'],
    shoulder_length: ['shoulder_length'],
    hand: ['palm', 'finger_ring_circumference'],
    ring: ['finger_ring_diameter'],
    insoleLength: ['insoleLength'],
    insoleWidth: ['insoleWidth'],
    central_seam: ['centralSeam'],
  },
  female: {
    fullbody: ['height', 'fullbodyLength'],
    lower_body: ['hip', 'thigh', 'insideLeg', 'lowerLength'],
    mid_body: ['body_length', 'upperWaist', 'waist', 'waistLower', 'chest', 'underBust'],
    upper_body: ['headCircumference', 'neck', 'collar'],
    sleeve: ['wrist', 'fist', 'biceps', 'sleeve'],
    shoulder_length: ['shoulder_length'],
    hand: ['palm', 'finger_ring_circumference'],
    ring: ['finger_ring_diameter'],
    insoleLength: ['insoleLength'],
    insoleWidth: ['insoleWidth'],
    central_seam: ['centralSeam'],
  },
};

export const filterByClotheTypes = {
  FULL_BODY: ['chest', 'waist', 'hip', 'insoleLength'],
  BOTTOM: ['waist', 'hip'],
  TOP: ['chest', 'waist'],
  UNDERWEAR_TOP: ['chest', 'underBust'],
  UNDERWEAR_FULL_BODY: ['chest', 'waist', 'hip', 'underBust'],
  UNDERWEAR_BOTTOM: ['waist', 'hip'],
  WETSUIT_FULL_BODY: ['chest', 'waist', 'hip', 'height'],
  WETSUIT_BOTTOM: ['waist', 'hip', 'height'],
  WETSUIT_TOP: ['chest', 'waist', 'height'],
  HEAD_ACCESSORY: ['headCircumference'],
  HAND_WRIST_ACCESSORY: ['wrist'],
  HAND_PALM_ACCESSORY: ['palm'],
  LEG_ACCESSORY: ['thigh', 'insideLeg'],
  ARM_ACCESSORY: ['biceps', 'wrist'],
  BICYCLE_ACCESSORY: ['height'],
  SHOE_ACCESSORY: ['insoleLength'],
  SHOE_ACCESSORY_INSOLE_WIDTH: ['insoleLength', 'insoleWidth'],
  BOTTOM_WITH_HEIGHT: ['waist', 'hip', 'height'],
  BOTTOM_WITH_INSIDE_LEG: ['waist', 'hip', 'insideLeg'],
};

export const dict = {
  child: {
    CentralSeam: ChildCentralSeam,
    MidBody: ChildMidBody,
    LowerBody: ChildLowerBody,
    Fullbody: ChildFullBody,
    UpperBody: ChildUpperBody,
    Sleeve: ChildSleeve,
    InsoleLength: ChildFootLength,
    InsoleWidth: ChildFootWidth,
  },
  female: {
    CentralSeam: FemaleCentralSeam,
    MidBody: FemaleMidBody,
    LowerBody: FemaleLowerBody,
    Fullbody: FemaleFullBody,
    UpperBody: FemaleUpperBody,
    Sleeve: FemaleSleeve,
    InsoleLength: FemaleFootLength,
    InsoleWidth: FemaleFootWidth,
    ShoulderLength: FemaleShoulderLength,
    Ring: FemaleRing,
    Hand: FemaleHand,
  },
  male: {
    CentralSeam: MaleCentralSeam,
    MidBody: MaleMidBody,
    LowerBody: MaleLowerBody,
    Fullbody: MaleFullBody,
    UpperBody: MaleUpperBody,
    Sleeve: MaleSleeve,
    InsoleLength: MaleFootLength,
    InsoleWidth: MaleFootWidth,
    ShoulderLength: MaleShoulderLength,
    Ring: MaleRing,
    Hand: MaleHand,
  },
};
