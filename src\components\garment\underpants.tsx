import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface UnderpantsProps {
  measure: GarmentMeasure;
  className?: string;
}

export function Underpants({ measure, className }: UnderpantsProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width={331}
          height={193}
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_underpants">
            <g id="underpants">
              <path
                id="Vector 328"
                d="M226.04 155.665C237.838 152.377 265.77 143.899 278.504 140.675L243.205 113.355L177.445 138.499L176.719 164.852C188.163 163.563 214.242 158.953 226.04 155.665Z"
                fill="white"
                stroke="black"
                stroke-width="0.483536"
              />
              <g id="Group 199">
                <path
                  id="Vector 326"
                  d="M261.338 26.5605C147.465 19.5492 76.2405 24.9649 70.8249 26.3188L87.5069 44.4513C97.7417 50.0926 118.888 61.9554 121.596 64.2763C124.981 67.1775 219.512 62.1004 222.413 61.3751C224.734 60.7949 249.088 37.9236 261.338 26.5605Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.483536"
                />
                <g id="Group 197">
                  <path
                    id="Vector 327"
                    d="M106.365 155.665C94.5665 152.377 66.3924 143.899 53.6593 140.675L89.1992 113.355L154.96 138.499L155.444 164.852C144 163.563 118.163 158.953 106.365 155.665Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.483536"
                  />
                  <g id="Vector">
                    <path
                      d="M118.042 38.166C134.869 41.0672 147.14 42.5093 165.912 42.5178C184.777 42.5263 197.196 41.0672 214.023 38.166C230.85 35.2648 252.787 29.0594 261.652 26.3193C267.212 44.2102 276.168 67.4692 278.817 94.9814C280.527 112.744 278.978 130.441 278.817 140.676C265.198 141.965 249.355 142.696 230.947 146.72C209.071 151.502 192.345 159.533 176.549 164.852L166.103 148.73L155.516 164.852C139.72 159.533 122.994 151.502 101.118 146.72C82.7096 142.696 66.8673 141.965 53.2477 140.676C53.0865 130.441 51.5375 112.744 53.2477 94.9814C55.8966 67.4692 64.8526 44.2102 70.4132 26.3193C79.2781 29.0594 101.214 35.2648 118.042 38.166Z"
                      fill="white"
                    />
                    <path
                      d="M166.153 148.654L155.516 164.852C139.72 159.533 122.994 151.502 101.118 146.72C82.7096 142.696 66.8673 141.965 53.2477 140.676C53.0865 130.441 51.5375 112.744 53.2477 94.9814C55.8966 67.4692 64.8526 44.2102 70.4132 26.3193C79.2781 29.0594 101.214 35.2648 118.042 38.166C134.869 41.0672 147.14 42.5093 165.912 42.5178C184.777 42.5263 197.196 41.0672 214.023 38.166C230.85 35.2648 252.787 29.0594 261.652 26.3193C267.212 44.2102 276.168 67.4692 278.817 94.9814C280.527 112.744 278.978 130.441 278.817 140.676C265.198 141.965 249.355 142.696 230.947 146.72C209.071 151.502 192.345 159.533 176.549 164.852L166.103 148.73"
                      stroke="black"
                      stroke-width="0.483536"
                    />
                  </g>
                  <g id="Group 193">
                    <path
                      id="Vector 331"
                      d="M53.1758 137.533C53.1758 137.533 85.8929 139.275 106.123 144.303C126.596 149.39 156.653 163.402 156.653 163.402"
                      stroke="black"
                      stroke-width="0.483536"
                      stroke-dasharray="0.97 0.97"
                    />
                    <path
                      id="Vector 332"
                      d="M53.1758 134.632C53.1758 134.632 85.8928 136.374 106.123 141.401C126.596 146.489 157.861 161.226 157.861 161.226"
                      stroke="black"
                      stroke-width="0.483536"
                      stroke-dasharray="0.97 0.97"
                    />
                  </g>
                  <g id="Group 194">
                    <path
                      id="Vector 331_2"
                      d="M278.987 137.533C278.987 137.533 246.27 139.275 226.04 144.303C205.567 149.39 175.51 163.402 175.51 163.402"
                      stroke="black"
                      stroke-width="0.483536"
                      stroke-dasharray="0.97 0.97"
                    />
                    <path
                      id="Vector 332_2"
                      d="M278.987 134.632C278.987 134.632 246.27 136.374 226.04 141.401C205.567 146.489 174.302 161.226 174.302 161.226"
                      stroke="black"
                      stroke-width="0.483536"
                      stroke-dasharray="0.97 0.97"
                    />
                  </g>
                  <g id="Group 195">
                    <path
                      id="Vector 334"
                      d="M134.652 55.8154C134.652 55.8154 125.706 115.29 138.762 136.082C148.662 151.85 162.697 153.973 162.697 153.973"
                      stroke="black"
                      stroke-width="0.483536"
                    />
                    <path
                      id="Vector 336"
                      d="M131.75 55.8154C131.75 55.8154 122.805 115.29 135.86 136.082C145.761 151.85 161.246 155.907 161.246 155.907"
                      stroke="black"
                      stroke-width="0.483536"
                      stroke-dasharray="0.97 0.97"
                    />
                  </g>
                  <g id="Group 196">
                    <path
                      id="Vector 334_2"
                      d="M197.995 55.8154C197.995 55.8154 206.94 115.29 193.885 136.082C183.984 151.85 169.708 154.215 169.708 154.215"
                      stroke="black"
                      stroke-width="0.483536"
                    />
                    <path
                      id="Vector 336_2"
                      d="M200.896 55.8154C200.896 55.8154 209.841 115.29 196.786 136.082C186.885 151.85 171.4 155.907 171.4 155.907"
                      stroke="black"
                      stroke-width="0.483536"
                      stroke-dasharray="0.97 0.97"
                    />
                  </g>
                </g>
                <g id="Group 198">
                  <path
                    id="Vector 329"
                    d="M65.2642 43.4844C65.2642 43.4844 126.776 57.8776 166.807 57.9905C206.613 58.1027 267.382 44.4514 267.382 44.4514"
                    stroke="black"
                    stroke-width="0.483536"
                  />
                  <path
                    id="Vector 330"
                    d="M65.7477 41.3086C65.7477 41.3086 126.776 55.46 166.807 55.5729C206.613 55.6852 266.899 42.0339 266.899 42.0339"
                    stroke="black"
                    stroke-width="0.483536"
                    stroke-dasharray="0.97 0.97"
                  />
                </g>
                <path
                  id="Vector 333"
                  d="M166.323 58.2324C166.323 58.2324 166.446 119.16 166.081 148.654"
                  stroke="black"
                  stroke-width="0.483536"
                />
              </g>
            </g>
            {findMeasure('product_thigh_circumference') && (
              <g id="product_thigh_circumference">
                <path
                  id="Vector 21"
                  d="M149.635 168.155L53.889 145.459"
                  stroke="#E55959"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M57.158 142.418L51.9898 145.086L54.8487 149.82"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27"
                  d="M147.04 171.689L151.912 168.512L148.589 164.092"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
              </g>
            )}
            {findMeasure('product_thigh_width') && (
              <g id="product_thigh_width">
                <path
                  id="Vector 21_2"
                  d="M149.635 168.155L53.889 145.459"
                  stroke="#E55959"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M57.158 142.418L51.9898 145.086L54.8487 149.82"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27_2"
                  d="M147.04 171.689L151.912 168.512L148.589 164.092"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
              </g>
            )}
            {findMeasure('product_waistband_width') && (
              <g id="product_waistband_width">
                <path
                  id="Vector 21_3"
                  d="M265.69 49.8574L66.9566 49.8574"
                  stroke="#E55959"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M69.436 46.1445L65.0225 49.9326L68.8961 53.8794"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27_3"
                  d="M263.254 53.8934L267.262 49.6785L263.009 46.1436"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 21_4"
                  d="M194.141 159.05L194.141 27.2861"
                  stroke="#E55959"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M197.854 29.7651L194.066 25.3516L190.12 29.2252"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27_4"
                  d="M190.105 156.372L194.32 160.38L197.854 156.127"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 21_5"
                  d="M270.042 68.2314L61.1542 68.2315"
                  stroke="#E55959"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_5"
                  d="M63.6335 64.5176L59.22 68.3056L63.0936 72.2524"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27_5"
                  d="M268.089 72.2684L272.097 68.0535L267.845 64.5186"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 21_6"
                  d="M275.844 99.1777L56.3188 99.1777"
                  stroke="#E55959"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_6"
                  d="M58.7982 95.4648L54.3846 99.2529L58.2582 103.2"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27_6"
                  d="M273.408 103.215L277.416 98.9998L273.163 95.4648"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
              </g>
            )}
            {findMeasure('product_inside_leg') && (
              <g id="product_inside_leg">
                <path
                  id="Vector 21_7"
                  d="M179.862 160.742L172.302 148.888"
                  stroke="#E55959"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_7"
                  d="M176.696 148.863L171.087 147.324L169.982 152.743"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27_7"
                  d="M175.044 159.879L180.415 162.11L182.191 156.873"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
              </g>
            )}
            {findMeasure('product_lower_length') && (
              <g id="product_lower_length">
                <path
                  id="Vector 21_8"
                  d="M293.843 139.466L270.768 23.874"
                  stroke="#E55959"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_8"
                  d="M275.017 25.246L270.168 22.0338L267.429 26.8374"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27_8"
                  d="M289.492 138.568L294.386 141.711L297.057 136.869"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 21_9"
                  d="M230.89 143.335L230.89 37.9238"
                  stroke="#E55959"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_9"
                  d="M234.742 40.8862L230.954 36.4727L227.007 40.3463"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27_9"
                  d="M226.853 140.899L231.068 144.907L234.603 140.654"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
              </g>
            )}
            {findMeasure('product_back_hook') && (
              <g id="product_back_hook">
                <path
                  id="Vector 21_10"
                  d="M155.839 149.379L155.839 26.5605"
                  stroke="#EDA7A7"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                  stroke-dasharray="4.84 4.84"
                />
                <path
                  id="Vector 26_10"
                  d="M159.725 29.119L155.577 25.0422L151.973 29.2362"
                  stroke="#EDA7A7"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27_10"
                  d="M151.986 147.717L156.084 151.844L159.739 147.694"
                  stroke="#EDA7A7"
                  stroke-width="1.93414"
                />
              </g>
            )}
            {findMeasure('product_hook') && (
              <g id="product_hook">
                <path
                  id="Vector 21_11"
                  d="M155.839 149.379L155.839 26.5605"
                  stroke="#E55959"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_11"
                  d="M159.725 29.119L155.577 25.0422L151.973 29.2362"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27_11"
                  d="M151.986 147.717L156.084 151.844L159.739 147.694"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
              </g>
            )}
            {findMeasure('product_waistband_circumference') && (
              <g id="product_waistband_circumference">
                <path
                  id="Vector 21_12"
                  d="M265.69 49.8574L66.9566 49.8574"
                  stroke="#E55959"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_12"
                  d="M69.436 46.1445L65.0225 49.9326L68.8961 53.8794"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27_12"
                  d="M263.254 53.8934L267.262 49.6785L263.009 46.1436"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 21_13"
                  d="M270.042 68.2314L61.1542 68.2315"
                  stroke="#E55959"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_13"
                  d="M63.6335 64.5176L59.22 68.3056L63.0936 72.2524"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27_13"
                  d="M268.089 72.2684L272.097 68.0535L267.845 64.5186"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 21_14"
                  d="M275.844 99.1777L56.3188 99.1777"
                  stroke="#E55959"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_14"
                  d="M58.7982 95.4648L54.3846 99.2529L58.2582 103.2"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27_14"
                  d="M273.408 103.215L277.416 98.9998L273.163 95.4648"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_circumference') && (
              <g id="product_lower_waist_circumference">
                <path
                  id="Vector 21_15"
                  d="M270.042 68.2314L61.1542 68.2315"
                  stroke="#E55959"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_15"
                  d="M63.6335 64.5176L59.22 68.3056L63.0936 72.2524"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27_15"
                  d="M268.089 72.2684L272.097 68.0535L267.845 64.5186"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_width') && (
              <g id="product_lower_waist_width">
                <path
                  id="Vector 21_16"
                  d="M270.042 68.2314L61.1542 68.2315"
                  stroke="#E55959"
                  stroke-width="2.41768"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_16"
                  d="M63.6335 64.5176L59.22 68.3056L63.0936 72.2524"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
                <path
                  id="Vector 27_16"
                  d="M268.089 72.2684L272.097 68.0535L267.845 64.5186"
                  stroke="#E55959"
                  stroke-width="1.93414"
                />
              </g>
            )}
          </g>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_underpants">
            <g id="underpants">
              <path
                id="Vector 328"
                d="M237.465 210.84C250.505 207.206 281.378 197.836 295.451 194.273L256.438 164.077L183.755 191.868L182.953 220.994C195.601 219.569 224.425 214.474 237.465 210.84Z"
                fill="white"
                stroke="black"
                stroke-width="0.534435"
              />
              <g id="Group 199">
                <path
                  id="Vector 326"
                  d="M276.479 68.1463C150.62 60.397 71.8974 66.3827 65.9117 67.8791L84.3497 87.9204C95.6619 94.1555 119.035 107.267 122.027 109.832C125.768 113.039 230.25 107.427 233.457 106.626C236.022 105.984 262.94 80.7055 276.479 68.1463Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.534435"
                />
                <g id="Group 197">
                  <path
                    id="Vector 327"
                    d="M105.193 210.84C92.1524 207.206 61.0127 197.836 46.9393 194.273L86.2202 164.077L158.903 191.868L159.438 220.994C146.789 219.569 118.233 214.474 105.193 210.84Z"
                    fill="white"
                    stroke="black"
                    stroke-width="0.534435"
                  />
                  <g id="Vector">
                    <path
                      d="M118.099 80.9726C136.697 84.1792 150.26 85.7731 171.008 85.7825C191.859 85.7919 205.586 84.1792 224.184 80.9726C242.782 77.7659 267.028 70.9074 276.826 67.8789C282.972 87.653 292.87 113.36 295.798 143.769C297.688 163.401 295.976 182.961 295.798 194.273C280.745 195.698 263.235 196.506 242.889 200.953C218.71 206.238 200.223 215.116 182.765 220.994L171.219 203.175L159.517 220.994C142.059 215.116 123.572 206.238 99.3933 200.953C79.0474 196.506 61.5376 195.698 46.4843 194.273C46.3062 182.961 44.5941 163.401 46.4843 143.769C49.4121 113.36 59.3107 87.653 65.4567 67.8789C75.2547 70.9074 99.5002 77.7659 118.099 80.9726Z"
                      fill="white"
                    />
                    <path
                      d="M171.275 203.091L159.517 220.994C142.059 215.116 123.572 206.238 99.3933 200.953C79.0474 196.506 61.5376 195.698 46.4843 194.273C46.3062 182.961 44.5941 163.401 46.4843 143.769C49.4121 113.36 59.3107 87.653 65.4567 67.8789C75.2547 70.9074 99.5002 77.7659 118.099 80.9726C136.697 84.1792 150.26 85.7731 171.008 85.7825C191.859 85.7919 205.586 84.1792 224.184 80.9726C242.782 77.7659 267.028 70.9074 276.826 67.8789C282.972 87.653 292.87 113.36 295.798 143.769C297.688 163.401 295.976 182.961 295.798 194.273C280.745 195.698 263.235 196.506 242.889 200.953C218.71 206.238 200.223 215.116 182.765 220.994L171.219 203.175"
                      stroke="black"
                      stroke-width="0.534435"
                    />
                  </g>
                  <g id="Group 193">
                    <path
                      id="Vector 331"
                      d="M46.4047 190.799C46.4047 190.799 82.5657 192.724 104.925 198.281C127.553 203.904 160.774 219.391 160.774 219.391"
                      stroke="black"
                      stroke-width="0.534435"
                      stroke-dasharray="1.07 1.07"
                    />
                    <path
                      id="Vector 332"
                      d="M46.4047 187.592C46.4047 187.592 82.5657 189.517 104.925 195.074C127.553 200.697 162.11 216.986 162.11 216.986"
                      stroke="black"
                      stroke-width="0.534435"
                      stroke-dasharray="1.07 1.07"
                    />
                  </g>
                  <g id="Group 194">
                    <path
                      id="Vector 331_2"
                      d="M295.986 190.799C295.986 190.799 259.825 192.724 237.465 198.281C214.837 203.904 181.617 219.391 181.617 219.391"
                      stroke="black"
                      stroke-width="0.534435"
                      stroke-dasharray="1.07 1.07"
                    />
                    <path
                      id="Vector 332_2"
                      d="M295.986 187.592C295.986 187.592 259.825 189.517 237.465 195.074C214.837 200.697 180.281 216.986 180.281 216.986"
                      stroke="black"
                      stroke-width="0.534435"
                      stroke-dasharray="1.07 1.07"
                    />
                  </g>
                  <g id="Group 195">
                    <path
                      id="Vector 334"
                      d="M136.457 100.479C136.457 100.479 126.57 166.215 141 189.196C151.942 206.623 167.454 208.97 167.454 208.97"
                      stroke="black"
                      stroke-width="0.534435"
                    />
                    <path
                      id="Vector 336"
                      d="M133.25 100.479C133.25 100.479 123.363 166.215 137.793 189.196C148.736 206.623 165.851 211.107 165.851 211.107"
                      stroke="black"
                      stroke-width="0.534435"
                      stroke-dasharray="1.07 1.07"
                    />
                  </g>
                  <g id="Group 196">
                    <path
                      id="Vector 334_2"
                      d="M206.468 100.479C206.468 100.479 216.355 166.215 201.925 189.196C190.983 206.623 175.203 209.237 175.203 209.237"
                      stroke="black"
                      stroke-width="0.534435"
                    />
                    <path
                      id="Vector 336_2"
                      d="M209.675 100.479C209.675 100.479 219.562 166.215 205.132 189.196C194.189 206.623 177.074 211.107 177.074 211.107"
                      stroke="black"
                      stroke-width="0.534435"
                      stroke-dasharray="1.07 1.07"
                    />
                  </g>
                </g>
                <g id="Group 198">
                  <path
                    id="Vector 329"
                    d="M59.7657 86.8525C59.7657 86.8525 127.752 102.761 171.997 102.886C215.993 103.01 283.159 87.9214 283.159 87.9214"
                    stroke="black"
                    stroke-width="0.534435"
                  />
                  <path
                    id="Vector 330"
                    d="M60.3002 84.4473C60.3002 84.4473 127.752 100.088 171.997 100.213C215.993 100.337 282.625 85.2489 282.625 85.2489"
                    stroke="black"
                    stroke-width="0.534435"
                    stroke-dasharray="1.07 1.07"
                  />
                </g>
                <path
                  id="Vector 333"
                  d="M171.463 103.152C171.463 103.152 171.599 170.493 171.195 203.092"
                  stroke="black"
                  stroke-width="0.534435"
                />
              </g>
            </g>
            {findMeasure('product_thigh_circumference') && (
              <g id="product_thigh_circumference">
                <path
                  id="Vector 21"
                  d="M153.018 224.645L47.1931 199.561"
                  stroke="#E55959"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M50.8063 196.199L45.094 199.148L48.2538 204.38"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27"
                  d="M150.15 228.551L155.534 225.04L151.862 220.154"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
              </g>
            )}
            {findMeasure('product_thigh_width') && (
              <g id="product_thigh_width">
                <path
                  id="Vector 21_2"
                  d="M153.018 224.645L47.1931 199.561"
                  stroke="#E55959"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M50.8063 196.199L45.094 199.148L48.2538 204.38"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27_2"
                  d="M150.15 228.551L155.534 225.04L151.862 220.154"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
              </g>
            )}
            {findMeasure('product_waistband_width') && (
              <g id="product_waistband_width">
                <path
                  id="Vector 21_3"
                  d="M281.289 93.8945L61.6363 93.8945"
                  stroke="#E55959"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M64.3766 89.791L59.4985 93.9778L63.7799 98.3401"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27_3"
                  d="M278.597 98.3566L283.026 93.698L278.326 89.791"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 21_4"
                  d="M202.208 214.582L202.208 68.9482"
                  stroke="#E55959"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M206.313 71.6886L202.126 66.8105L197.764 71.0919"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27_4"
                  d="M197.747 211.621L202.406 216.051L206.313 211.351"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 21_5"
                  d="M286.099 114.203L55.223 114.203"
                  stroke="#E55959"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_5"
                  d="M57.9633 110.1L53.0852 114.286L57.3666 118.649"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27_5"
                  d="M283.941 118.664L288.371 114.006L283.67 110.099"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 21_6"
                  d="M292.512 148.407L49.8787 148.407"
                  stroke="#E55959"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_6"
                  d="M52.6189 144.303L47.7408 148.49L52.0222 152.852"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27_6"
                  d="M289.82 152.868L294.25 148.21L289.549 144.303"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
              </g>
            )}
            {findMeasure('product_inside_leg') && (
              <g id="product_inside_leg">
                <path
                  id="Vector 21_7"
                  d="M186.427 216.452L178.07 203.351"
                  stroke="#E55959"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_7"
                  d="M182.927 203.323L176.728 201.621L175.507 207.611"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27_7"
                  d="M181.101 215.498L187.038 217.964L189.001 212.176"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
              </g>
            )}
            {findMeasure('product_lower_length') && (
              <g id="product_lower_length">
                <path
                  id="Vector 21_8"
                  d="M312.406 192.937L286.902 65.1768"
                  stroke="#E55959"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_8"
                  d="M291.598 66.6929L286.239 63.1426L283.211 68.4519"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27_8"
                  d="M307.596 191.943L313.005 195.417L315.958 190.065"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 21_9"
                  d="M242.826 197.212L242.826 80.7051"
                  stroke="#E55959"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_9"
                  d="M247.083 83.9806L242.896 79.1025L238.534 83.3839"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27_9"
                  d="M238.364 194.52L243.023 198.949L246.93 194.249"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
              </g>
            )}
            {findMeasure('product_back_hook') && (
              <g id="product_back_hook">
                <path
                  id="Vector 21_10"
                  d="M159.875 203.893L159.875 68.1465"
                  stroke="#EDA7A7"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                  stroke-dasharray="5.34 5.34"
                />
                <path
                  id="Vector 26_10"
                  d="M164.17 70.9731L159.585 66.4672L155.601 71.1026"
                  stroke="#EDA7A7"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27_10"
                  d="M155.616 202.055L160.145 206.616L164.185 202.03"
                  stroke="#EDA7A7"
                  stroke-width="2.13774"
                />
              </g>
            )}
            {findMeasure('product_hook') && (
              <g id="product_hook">
                <path
                  id="Vector 21_11"
                  d="M159.875 203.893L159.875 68.1465"
                  stroke="#E55959"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_11"
                  d="M164.17 70.9731L159.585 66.4672L155.601 71.1026"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27_11"
                  d="M155.616 202.055L160.145 206.616L164.185 202.03"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
              </g>
            )}
            {findMeasure('product_waistband_circumference') && (
              <g id="product_waistband_circumference">
                <path
                  id="Vector 21_12"
                  d="M281.289 93.8945L61.6363 93.8945"
                  stroke="#E55959"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_12"
                  d="M64.3766 89.791L59.4985 93.9778L63.7799 98.3401"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27_12"
                  d="M278.597 98.3566L283.026 93.698L278.326 89.791"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 21_13"
                  d="M286.099 114.203L55.223 114.203"
                  stroke="#E55959"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_13"
                  d="M57.9633 110.1L53.0852 114.286L57.3666 118.649"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27_13"
                  d="M283.941 118.664L288.371 114.006L283.67 110.099"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 21_14"
                  d="M292.512 148.407L49.8787 148.407"
                  stroke="#E55959"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_14"
                  d="M52.6189 144.303L47.7408 148.49L52.0222 152.852"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27_14"
                  d="M289.82 152.868L294.25 148.21L289.549 144.303"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_circumference') && (
              <g id="product_lower_waist_circumference">
                <path
                  id="Vector 21_15"
                  d="M286.099 114.203L55.223 114.203"
                  stroke="#E55959"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_15"
                  d="M57.9633 110.1L53.0852 114.286L57.3666 118.649"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27_15"
                  d="M283.941 118.664L288.371 114.006L283.67 110.099"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_width') && (
              <g id="product_lower_waist_width">
                <path
                  id="Vector 21_16"
                  d="M286.099 114.203L55.223 114.203"
                  stroke="#E55959"
                  stroke-width="2.67217"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_16"
                  d="M57.9633 110.1L53.0852 114.286L57.3666 118.649"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
                <path
                  id="Vector 27_16"
                  d="M283.941 118.664L288.371 114.006L283.67 110.099"
                  stroke="#E55959"
                  stroke-width="2.13774"
                />
              </g>
            )}
          </g>
        </svg>
      )}
    </div>
  );
}
