import { useDevice } from '@/hooks/use-device';
import { BodyMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface MidBodyProps {
  measure: BodyMeasure;
  className?: string;
}

export function MidBody({ measure, className }: MidBodyProps) {
  const { measures } = measure;
  const mappedMeasures = measures.map((item) => item.measure);
  const { isMobile } = useDevice();

  const findMeasure = (measure: string) => {
    const foundMeasure = mappedMeasures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full flex justify-center', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="193"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_mid_body">
            <mask id="mask0_128_1439" maskUnits="userSpaceOnUse" x="0" y="0" width="331" height="193">
              <rect id="rect" width="331" height="193" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_1439)">
              <g id="group_mid_body_mask">
                <g id="female">
                  <g id="female_2">
                    <path
                      id="Union"
                      d="M153.448 -15.6596L153.24 -3.80466L137.018 5.13856L130.778 6.59443L125.787 8.46627L122.875 10.3381L120.795 12.8339L116.843 21.3612L115.388 26.9767L113.932 35.088L113.1 45.0711L111.02 63.5815L109.98 71.4848L108.94 78.3482L105.197 93.3229L102.285 107.05L99.997 120.777L98.5411 133.879L97.7092 143.031L96.8772 154.47L95.2134 164.245C95.6987 167.988 96.6693 175.559 96.6693 175.892C96.6693 176.224 99.165 179.635 100.413 181.299L102.701 184.211L103.741 184.003C103.879 183.656 103.741 183.296 103.741 182.963C103.741 182.657 103.593 180.695 103.213 179.651C103.076 179.277 102.964 178.968 102.909 178.803C102.742 178.304 101.453 176.1 100.829 175.06C100.69 173.673 100.413 170.692 100.413 169.86C100.413 169.028 101.522 165.354 102.077 163.621L103.325 160.085C103.671 160.085 104.365 160.127 104.365 160.293C104.365 160.379 104.65 161.714 104.985 163.278C105.457 165.491 106.028 168.161 106.028 168.404C106.028 168.737 106.583 170.207 106.86 170.9C107.068 171.039 107.526 171.233 107.692 170.9C107.859 170.567 108.594 169.375 108.94 168.82L108.316 161.541V157.589L108.732 153.846C108.663 153.083 108.524 151.475 108.524 151.142C108.524 150.898 108.167 149.513 107.872 148.365C107.663 147.553 107.484 146.86 107.484 146.774C107.484 146.67 107.172 145.838 106.86 145.006C106.548 144.174 106.236 143.343 106.236 143.239V141.783L108.316 132.631C109.287 129.581 111.228 123.439 111.228 123.272C111.228 123.119 112.699 120.363 113.555 118.76C113.859 118.19 114.085 117.766 114.14 117.657C114.306 117.324 116.428 112.388 117.467 109.961L119.963 102.89L121.419 97.6905L122.875 86.2515L123.707 80.22L129.114 56.9261L130.986 48.3988C131.888 54.4996 133.69 66.9092 133.69 67.7411C133.69 68.2611 134.158 72.3687 134.626 76.4764C135.094 80.584 135.562 84.6916 135.562 85.2116C135.562 86.0435 135.839 90.1338 135.978 92.075L129.114 114.745L125.371 125.144L122.251 139.287C121.904 141.505 121.211 146.067 121.211 146.566C121.211 147.065 120.934 152.875 120.795 155.717L122.251 164.869L125.912 190.658H163.847L163.639 174.852L163.223 166.117L163.431 160.709C163.501 160.293 163.681 159.42 163.847 159.253C164.014 159.087 164.887 158.629 165.303 158.421C165.719 158.629 166.593 159.087 166.759 159.253C166.926 159.42 167.106 160.293 167.175 160.709L167.383 166.117L166.967 174.852L166.995 190.698L204.301 190.934L208.356 164.869L209.811 155.717C209.673 152.875 209.395 147.065 209.395 146.566C209.395 146.067 208.702 141.505 208.356 139.287L205.236 125.144L201.492 114.745L194.629 92.075C194.767 90.1338 195.045 86.0435 195.045 85.2116C195.045 84.6916 195.513 80.584 195.981 76.4764C196.449 72.3687 196.917 68.2611 196.917 67.7411C196.917 66.9092 198.719 54.4996 199.62 48.3988L201.492 56.9261L206.9 80.22L207.732 86.2515L209.187 97.6905L210.643 102.89L213.139 109.961C214.179 112.388 216.3 117.324 216.467 117.657C216.521 117.766 216.748 118.19 217.052 118.76C217.907 120.363 219.379 123.119 219.379 123.272C219.379 123.439 221.32 129.581 222.29 132.631L224.37 141.783V143.239C224.37 143.343 224.058 144.174 223.746 145.006C223.434 145.838 223.122 146.67 223.122 146.774C223.122 146.86 222.944 147.553 222.735 148.365C222.439 149.513 222.082 150.898 222.082 151.142C222.082 151.475 221.944 153.083 221.874 153.846L222.29 157.589V161.541L221.666 168.82C222.013 169.375 222.748 170.567 222.914 170.9C223.081 171.233 223.538 171.039 223.746 170.9C224.024 170.207 224.578 168.737 224.578 168.404C224.578 168.161 225.149 165.491 225.622 163.278C225.957 161.714 226.242 160.379 226.242 160.293C226.242 160.127 226.935 160.085 227.282 160.085L228.53 163.621C229.084 165.354 230.194 169.028 230.194 169.86C230.194 170.692 229.916 173.673 229.778 175.06C229.154 176.1 227.864 178.304 227.698 178.803C227.643 178.968 227.53 179.277 227.394 179.651C227.014 180.695 226.45 182.241 226.45 182.547C226.45 182.88 226.727 183.656 226.866 184.003H227.698L230.194 181.299C231.442 179.635 233.937 176.224 233.937 175.892C233.937 175.559 234.908 167.988 235.393 164.245L233.729 154.47L232.897 143.031L232.065 133.879L230.61 120.777L228.322 107.05L225.41 93.3229L221.666 78.3482L220.626 71.4848L219.587 63.5815L217.507 45.0711L216.675 35.088L215.219 26.9767L213.763 21.3612L209.811 12.8339L207.732 10.3381L204.82 8.46627L199.828 6.59443L193.589 5.13856L177.366 -3.80466L177.158 -15.6596L166.958 -19.8968L166.967 -21.2751L165.303 -20.584L163.639 -21.2751L163.649 -19.8968L153.448 -15.6596Z"
                      fill="white"
                    />
                    <g id="Group 3">
                      <path
                        id="Vector 16"
                        d="M154.486 64.4128C161.557 59.8372 162.597 48.8142 162.597 48.3982L161.349 50.478C160.309 51.7259 158.105 54.3049 157.606 54.6377C157.106 54.9704 152.683 56.8561 150.534 57.7574C148.662 58.104 144.752 58.7973 144.087 58.7973C143.421 58.7973 140.759 58.104 139.511 57.7574L134.104 54.2217L131.192 49.0221C132.093 54.7763 133.854 66.451 133.688 67.1166C133.48 67.9485 147.414 68.9884 154.486 64.4128Z"
                        fill="url(#paint0_linear_128_1439)"
                      />
                      <path
                        id="Vector 19"
                        d="M168.213 48.3982C168.213 48.8142 167.072 54.296 170.709 57.7574C174.432 61.3011 179.292 62.7275 184.228 61.2931C187.585 60.3173 191.091 57.7574 191.091 57.7574C191.091 57.7574 198.717 54.7763 199.618 49.0221L196.498 53.8057L191.091 57.7574C191.091 57.7574 187.389 58.7973 186.723 58.7973C186.058 58.7973 182.148 58.104 180.276 57.7574C178.127 56.8561 173.704 54.9704 173.204 54.6377C172.705 54.3049 170.501 51.7259 169.461 50.478L168.213 48.3982Z"
                        fill="url(#paint1_linear_128_1439)"
                      />
                      <path
                        id="Vector 17"
                        d="M150.742 148.23C155.674 153.726 163.221 160.501 163.221 160.501V164.869L163.845 181.923V190.242L125.992 192.114L123.08 173.188L121 157.173L121.416 145.942L123.496 132.839L129.32 114.745L136.391 129.72C138.679 134.85 143.462 140.119 150.742 148.23Z"
                        fill="url(#paint2_linear_128_1439)"
                      />
                      <path
                        id="Vector 20"
                        d="M119.129 84.3794C120.793 85.3777 122.734 82.2995 123.497 80.6357H123.705L122.249 91.4507C122.041 93.4612 121.625 97.607 121.625 98.1062C121.625 98.6053 120.238 103.167 119.545 105.386L115.593 115.577C114.415 117.795 111.642 122.648 111.642 122.856C111.642 123.444 110.186 126.669 109.562 128.264L106.442 140.119V143.03C106.65 143.724 107.066 145.193 107.066 145.526C107.066 145.942 108.73 150.934 108.73 151.35V154.677L108.314 158.629C108.314 162.165 109.271 167.946 108.938 168.612C108.522 169.444 108.591 169.999 108.314 170.068L107.274 171.108L106.65 170.692L106.026 169.236C105.472 166.394 104.57 160.626 104.57 160.293C104.57 159.96 103.738 160.154 103.322 160.293L100.203 170.276L101.035 175.475C102.005 177.07 103.738 180.966 103.738 181.299C103.738 181.632 103.946 182.616 103.946 183.379L103.322 184.419L102.074 183.379L96.8749 176.307L95.0031 164.66L96.8749 154.677L98.3308 139.079L100.827 116.201L102.906 104.138C103.946 99.7007 106.026 90.702 106.026 90.2029C106.026 89.7037 107.967 82.5075 108.938 78.9718C109.354 77.3773 110.144 74.0219 109.978 73.3563C109.811 72.6908 111.156 63.2345 111.85 58.5896C113.444 65.453 116.675 79.3878 116.841 80.2197C117.049 81.2596 117.049 83.1315 119.129 84.3794Z"
                        fill="url(#paint3_linear_128_1439)"
                      />
                      <path
                        id="Vector 39"
                        d="M135.591 247.128C135.78 244.861 127.799 209.822 125.674 189.989L135.827 190.225H163.689V225.406C163.531 229.105 163.216 237.306 163.216 240.517C163.216 244.531 160.855 252.323 160.855 254.92C160.855 257.517 159.911 262.239 160.855 269.323C161.8 276.406 161.8 290.573 162.036 293.643C162.272 296.712 159.911 314.421 159.911 317.018V332.602C160.226 333.782 160.903 336.615 161.091 338.504C161.328 340.865 159.675 345.116 159.675 345.588C159.675 345.966 159.203 352.671 158.966 355.977L158.022 359.046V363.06C158.022 363.249 156.92 364.713 156.369 365.421L155.897 367.31C155.661 368.255 152.827 369.199 150.702 368.963C149.002 368.774 149.05 367.625 149.286 367.074C148.577 367.389 146.925 368.066 145.98 368.255C145.036 368.444 144.17 367.704 143.855 367.31C143.383 367.546 142.25 368.019 141.494 368.019C140.738 368.019 140.235 367.546 140.077 367.31H138.188C137.055 367.31 136.929 366.523 137.008 366.13C136.929 366.208 136.677 366.413 136.299 366.602C135.827 366.838 135.355 366.366 135.119 364.713C134.93 363.391 136.142 361.486 136.772 360.699L145.98 347.241C145.901 346.375 145.791 344.454 145.98 343.699C146.216 342.754 147.397 330.949 147.869 328.115C148.341 325.282 139.605 294.587 136.536 286.559C133.466 278.531 135.355 249.962 135.591 247.128Z"
                        fill="#F4F3F3"
                        stroke="black"
                        stroke-width="0.472228"
                      />
                      <path
                        id="Vector 40"
                        d="M194.855 247.837C194.667 245.57 202.411 210.767 204.536 189.989L194.619 190.225L166.758 190.461V225.406C166.915 229.105 167.23 237.306 167.23 240.517C167.23 244.531 169.591 252.323 169.591 254.92C169.591 257.517 170.536 262.239 169.591 269.323C168.647 276.406 168.647 290.573 168.411 293.643C168.175 296.712 170.536 314.421 170.536 317.018V332.602C170.221 333.782 169.544 336.615 169.355 338.504C169.119 340.865 170.772 345.116 170.772 345.588C170.772 345.966 171.244 352.671 171.48 355.977L172.425 359.046V363.06C172.425 363.249 173.526 364.713 174.077 365.421L174.55 367.31C174.786 368.255 177.619 369.199 179.744 368.963C181.444 368.774 181.397 367.625 181.161 367.074C181.869 367.389 183.522 368.066 184.466 368.255C185.411 368.444 186.277 367.704 186.591 367.31C187.064 367.546 188.197 368.019 188.953 368.019C189.708 368.019 190.212 367.546 190.369 367.31H192.258C193.392 367.31 193.517 366.523 193.439 366.13C193.517 366.208 193.769 366.413 194.147 366.602C194.619 366.838 195.092 366.366 195.328 364.713C195.517 363.391 194.305 361.486 193.675 360.699L184.466 347.241C184.545 346.375 184.655 344.454 184.466 343.699C184.23 342.754 183.05 330.949 182.578 328.115C182.105 325.282 190.842 294.587 193.911 286.559C196.98 278.531 195.092 250.67 194.855 247.837Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.472228"
                      />
                    </g>
                    <path
                      id="Union_2"
                      d="M153.501 -16.0032C153.501 -16.1181 153.408 -16.2112 153.293 -16.2112C153.178 -16.2112 153.085 -16.1181 153.085 -16.0032V-3.64994C148.949 -1.47632 140.234 3.12787 137.592 4.61372C136.792 5.06393 135.537 5.38332 134.012 5.72957C133.784 5.78138 133.55 5.83372 133.311 5.88712C131.968 6.18762 130.474 6.522 128.975 6.98819C125.446 8.08609 121.84 9.93079 120.035 13.8594C119.912 14.127 119.79 14.3915 119.67 14.6533C118.06 18.1504 116.674 21.1604 115.605 24.7803C114.455 28.6738 113.673 33.2665 113.361 39.9342C112.862 50.5653 110.107 69.8433 108.789 78.1692C107.054 84.6243 103.131 100.178 101.3 110.831C99.4689 121.487 97.3475 144.115 96.5153 154.101L94.8477 164.315L96.5235 176.255L101.961 183.366L101.963 183.368C102.107 183.566 102.292 183.784 102.492 183.967C102.689 184.147 102.92 184.311 103.157 184.375C103.278 184.408 103.409 184.416 103.538 184.38C103.67 184.343 103.784 184.264 103.878 184.149C104.058 183.926 104.166 183.564 104.208 183.053C104.253 182.516 104.14 181.857 103.94 181.155C103.738 180.449 103.443 179.683 103.11 178.927C102.452 177.435 101.638 175.968 101.08 175.057C100.942 174.261 100.749 173.049 100.609 171.95C100.539 171.391 100.482 170.864 100.453 170.437C100.439 170.223 100.432 170.037 100.433 169.885C100.435 169.73 100.445 169.625 100.459 169.567C100.621 168.919 102.42 163.345 103.334 160.53C103.53 160.468 103.781 160.396 103.993 160.35C104.087 160.33 104.167 160.317 104.227 160.312C104.233 160.35 104.24 160.396 104.248 160.448C104.272 160.596 104.306 160.8 104.349 161.046C104.433 161.539 104.55 162.203 104.68 162.934C104.94 164.394 105.252 166.119 105.46 167.263C105.565 167.963 105.775 168.995 106.067 169.815C106.212 170.223 106.384 170.597 106.584 170.852C106.684 170.979 106.802 171.09 106.941 171.153C107.086 171.22 107.246 171.23 107.406 171.166C107.885 170.974 108.279 170.51 108.571 170.049C108.867 169.58 109.082 169.076 109.19 168.751L109.205 168.707L109.199 168.661C109.026 167.171 108.777 164.865 108.592 162.786C108.499 161.746 108.423 160.765 108.38 159.971C108.336 159.17 108.329 158.582 108.367 158.315C108.399 158.089 108.453 157.813 108.515 157.493C108.847 155.793 109.424 152.83 108.36 149.283C107.378 146.009 106.717 143.941 106.497 143.277V141.876C107.017 139.278 107.939 135.145 109.022 131.162C110.11 127.16 111.351 123.339 112.5 121.37C113.964 118.86 116.049 114.272 117.87 109.589C119.69 104.909 121.258 100.106 121.678 97.1684C122.034 94.6766 122.428 91.3106 122.794 88.1785C122.856 87.6465 122.918 87.1213 122.978 86.6082C123.186 84.8402 123.381 83.2169 123.55 81.9579C123.634 81.3283 123.712 80.7913 123.781 80.3736C123.851 79.9505 123.911 79.6643 123.957 79.5263C124.043 79.2663 124.357 78.0286 124.804 76.2098C125.253 74.3838 125.84 71.9578 126.478 69.3077C127.754 64.0073 129.231 57.8092 130.201 53.7188L130.202 53.7144L131.011 49.5108C131.935 55.8823 133.195 65.0837 134.176 73.4118C134.698 77.8493 135.141 82.0373 135.412 85.4163C135.683 88.8051 135.779 91.3555 135.616 92.5349C135.287 94.9247 133.527 100.779 131.449 107.151C129.376 113.513 126.995 120.364 125.435 124.731L125.434 124.735L125.433 124.739C124.355 128.215 122.855 134.163 121.854 140.721C120.852 147.277 120.346 154.46 121.266 160.397C123.096 172.208 124.815 184.929 125.439 189.989L163.926 190.225C163.926 185.029 164.024 174.073 163.691 169.916C163.483 167.315 163.316 164.734 163.413 162.699C163.461 161.68 163.575 160.811 163.778 160.148C163.983 159.479 164.266 159.063 164.617 158.888C164.801 158.796 164.988 158.749 165.13 158.725C165.201 158.713 165.259 158.707 165.299 158.704C165.319 158.703 165.334 158.702 165.344 158.702L165.354 158.702L165.356 158.702L165.357 158.702L165.368 158.702C165.377 158.702 165.393 158.703 165.413 158.704C165.453 158.707 165.511 158.713 165.582 158.725C165.724 158.749 165.911 158.796 166.095 158.888C166.446 159.063 166.729 159.479 166.933 160.148C167.136 160.811 167.251 161.68 167.299 162.699C167.396 164.734 167.229 167.315 167.02 169.916C166.688 174.073 166.523 185.029 166.523 190.225L204.774 189.989C205.397 184.929 207.616 172.208 209.446 160.397C210.366 154.46 209.859 147.277 208.858 140.721C207.856 134.163 206.357 128.215 205.279 124.739L205.278 124.735L205.276 124.731C203.717 120.364 201.336 113.513 199.262 107.151C197.185 100.779 195.425 94.9247 195.095 92.5349C194.933 91.3555 195.029 88.8051 195.3 85.4163C195.571 82.0373 196.014 77.8493 196.536 73.4118C197.517 65.0837 198.776 55.8823 199.701 49.5108L200.509 53.71L200.509 53.7144L200.51 53.7188C201.481 57.8092 202.958 64.0073 204.234 69.3077C204.871 71.9578 205.459 74.3838 205.908 76.2098C206.355 78.0286 206.668 79.2663 206.755 79.5263C206.801 79.6643 206.86 79.9505 206.931 80.3736C207 80.7913 207.078 81.3283 207.162 81.9579C207.331 83.2169 207.526 84.8402 207.734 86.6082C207.794 87.1213 207.856 87.6465 207.918 88.1785C208.284 91.3106 208.678 94.6766 209.034 97.1684C209.454 100.106 211.022 104.909 212.842 109.589C214.663 114.272 216.748 118.86 218.212 121.37C219.36 123.339 220.602 127.16 221.69 131.162C222.772 135.145 223.695 139.278 224.215 141.876V143.277C223.995 143.941 223.334 146.009 222.352 149.283C221.288 152.83 221.865 155.793 222.197 157.493C222.259 157.813 222.313 158.089 222.345 158.315C222.383 158.582 222.376 159.17 222.332 159.971C222.289 160.765 222.213 161.746 222.12 162.786C221.935 164.865 221.686 167.171 221.512 168.661L221.507 168.707L221.522 168.751C221.63 169.076 221.845 169.58 222.141 170.049C222.433 170.51 222.826 170.974 223.306 171.166C223.465 171.23 223.625 171.22 223.771 171.153C223.91 171.09 224.028 170.979 224.128 170.852C224.328 170.597 224.5 170.223 224.645 169.815C224.937 168.995 225.147 167.963 225.252 167.263C225.46 166.119 225.772 164.394 226.031 162.934C226.161 162.203 226.279 161.539 226.363 161.046C226.405 160.8 226.44 160.596 226.463 160.448C226.472 160.396 226.479 160.35 226.485 160.312C226.545 160.317 226.625 160.33 226.719 160.35C226.931 160.396 227.182 160.468 227.377 160.53C228.292 163.345 230.09 168.919 230.252 169.567C230.267 169.625 230.277 169.73 230.279 169.885C230.28 170.037 230.273 170.223 230.258 170.437C230.23 170.864 230.173 171.391 230.102 171.95C229.963 173.049 229.77 174.261 229.632 175.057C229.074 175.968 228.26 177.435 227.602 178.927C227.268 179.683 226.973 180.449 226.772 181.155C226.571 181.857 226.459 182.516 226.503 183.053C226.546 183.564 226.653 183.926 226.834 184.149C226.927 184.264 227.042 184.343 227.173 184.38C227.303 184.416 227.434 184.408 227.555 184.375C227.792 184.311 228.023 184.147 228.22 183.967C228.42 183.784 228.605 183.566 228.749 183.368L228.751 183.366L234.188 176.255L235.864 164.315L234.197 154.101C233.364 144.115 231.243 121.487 229.411 110.831C227.58 100.178 223.658 84.6243 221.923 78.1692C220.605 69.8433 217.85 50.5653 217.351 39.9342C217.039 33.2665 216.257 28.6738 215.107 24.7803C214.038 21.1604 212.652 18.1504 211.042 14.6533L210.88 14.3018C210.813 14.1553 210.745 14.0078 210.677 13.8594C208.872 9.93079 205.265 8.08609 201.737 6.98819C200.238 6.522 198.744 6.18763 197.401 5.88713L197.399 5.88681C197.161 5.83351 196.928 5.78129 196.7 5.72957C195.175 5.38332 193.92 5.06393 193.119 4.61372C190.478 3.12787 181.763 -1.47632 177.627 -3.64994V-16.0032C177.627 -16.1181 177.534 -16.2112 177.419 -16.2112C177.304 -16.2112 177.211 -16.1181 177.211 -16.0032V-3.39862L177.322 -3.34017C181.414 -1.1903 190.257 3.48063 192.916 4.97627C193.779 5.46198 195.098 5.79253 196.608 6.13522C196.838 6.18751 197.074 6.24017 197.313 6.29377C198.656 6.59441 200.132 6.92466 201.613 7.38538C205.103 8.47129 208.568 10.2663 210.299 14.033C210.419 14.2934 210.537 14.5506 210.654 14.8051L210.662 14.8218C212.274 18.3241 213.648 21.3082 214.708 24.8982C215.846 28.7521 216.624 33.3105 216.936 39.9537C217.435 50.6153 220.197 69.9266 221.514 78.2451L221.515 78.256L221.518 78.2666C223.251 84.7129 227.173 100.262 229.001 110.901C230.83 121.539 232.951 144.158 233.783 154.143L233.783 154.151L235.443 164.32L233.792 176.089L228.417 183.117L228.414 183.121C228.282 183.303 228.115 183.499 227.939 183.66C227.759 183.824 227.588 183.935 227.447 183.973C227.379 183.992 227.327 183.991 227.286 183.98C227.247 183.969 227.203 183.944 227.157 183.887C227.057 183.764 226.958 183.506 226.918 183.018C226.879 182.556 226.976 181.954 227.172 181.269C227.366 180.587 227.654 179.84 227.982 179.095C228.64 177.604 229.457 176.136 230.007 175.241L230.028 175.207L230.035 175.168C230.174 174.368 230.372 173.128 230.515 172.002C230.586 171.44 230.644 170.904 230.673 170.465C230.688 170.245 230.696 170.048 230.695 169.882C230.693 169.72 230.683 169.574 230.656 169.466C230.487 168.791 228.639 163.067 227.74 160.301L227.708 160.201L227.608 160.168C227.397 160.098 227.077 160.001 226.806 159.944C226.673 159.915 226.539 159.893 226.433 159.892C226.383 159.892 226.317 159.895 226.255 159.919C226.223 159.932 226.18 159.955 226.145 159.997C226.106 160.043 226.087 160.1 226.087 160.158C226.087 160.154 226.087 160.154 226.086 160.162C226.085 160.169 226.083 160.184 226.079 160.21C226.073 160.252 226.064 160.309 226.053 160.382C226.029 160.528 225.995 160.73 225.953 160.976C225.869 161.467 225.752 162.131 225.622 162.861C225.362 164.322 225.05 166.048 224.842 167.192L224.842 167.195L224.841 167.198C224.738 167.885 224.533 168.889 224.253 169.676C224.112 170.072 223.959 170.394 223.801 170.594C223.723 170.694 223.654 170.75 223.598 170.775C223.55 170.797 223.508 170.799 223.46 170.779C223.107 170.638 222.773 170.27 222.493 169.827C222.23 169.411 222.035 168.962 221.931 168.662C222.104 167.169 222.351 164.885 222.535 162.823C222.627 161.781 222.704 160.794 222.748 159.994C222.79 159.202 222.802 158.572 222.757 158.256C222.722 158.015 222.667 157.727 222.602 157.397C222.271 155.689 221.719 152.84 222.75 149.402C223.748 146.075 224.413 143.998 224.62 143.377L224.631 143.345V141.835L224.627 141.814C224.106 139.211 223.179 135.057 222.091 131.053C221.006 127.058 219.752 123.184 218.571 121.16C217.123 118.678 215.048 114.114 213.23 109.439C211.41 104.759 209.858 99.9954 209.446 97.1096C209.091 94.6234 208.698 91.2638 208.331 88.1311C208.269 87.5989 208.207 87.0733 208.147 86.5596C207.939 84.7919 207.744 83.1654 207.574 81.9027C207.49 81.2714 207.411 80.7295 207.341 80.3055C207.272 79.887 207.208 79.5687 207.15 79.3947C207.07 79.1555 206.762 77.9426 206.312 76.1104C205.863 74.2855 205.276 71.8603 204.638 69.2103C203.363 63.9124 201.887 57.7174 200.916 53.6271L199.877 48.2239L199.467 48.233C198.531 54.6124 197.168 64.4832 196.123 73.3631C195.6 77.8029 195.157 81.9966 194.885 85.3831C194.615 88.7597 194.513 91.3586 194.683 92.5918C195.019 95.0272 196.795 100.924 198.867 107.28C200.942 113.645 203.323 120.497 204.883 124.866C205.955 128.324 207.449 134.25 208.447 140.784C209.446 147.324 209.945 154.458 209.035 160.334C207.225 172.01 204.943 185.273 204.301 190.461L167.048 190.562C167.05 185.279 167.109 174.031 167.435 169.949C167.643 167.351 167.813 164.745 167.715 162.679C167.666 161.647 167.549 160.737 167.331 160.026C167.115 159.322 166.787 158.769 166.281 158.516C166.049 158.4 165.82 158.343 165.65 158.314C165.565 158.3 165.493 158.293 165.442 158.289C165.417 158.288 165.396 158.287 165.382 158.286C165.374 158.286 165.368 158.286 165.364 158.286L165.359 158.286H165.357H165.356H165.356H165.355H165.353L165.348 158.286C165.343 158.286 165.338 158.286 165.33 158.286C165.316 158.287 165.295 158.288 165.27 158.289C165.219 158.293 165.147 158.3 165.062 158.314C164.892 158.343 164.663 158.4 164.431 158.516C163.925 158.769 163.596 159.322 163.381 160.026C163.163 160.737 163.046 161.647 162.997 162.679C162.899 164.745 163.069 167.351 163.277 169.949C163.603 174.031 163.451 184.939 163.454 190.222L125.838 189.991C125.196 184.803 123.486 172.01 121.677 160.334C120.767 154.458 121.266 147.324 122.265 140.784C123.263 134.25 124.757 128.324 125.829 124.866C127.389 120.497 129.77 113.645 131.845 107.28C133.917 100.924 135.693 95.0272 136.029 92.5918C136.199 91.3586 136.097 88.7597 135.826 85.3831C135.555 81.9966 135.111 77.8029 134.589 73.3631C133.543 64.4832 132.181 54.6124 131.245 48.233L130.835 48.2239L129.796 53.6271C128.825 57.7174 127.349 63.9124 126.074 69.2103C125.436 71.8603 124.849 74.2855 124.4 76.1104C123.949 77.9426 123.642 79.1555 123.562 79.3947C123.504 79.5687 123.44 79.887 123.371 80.3055C123.3 80.7295 123.222 81.2714 123.137 81.9027C122.968 83.1654 122.773 84.7919 122.565 86.5596C122.505 87.0733 122.443 87.5989 122.381 88.1311C122.014 91.2637 121.621 94.6235 121.266 97.1096C120.854 99.9954 119.302 104.759 117.482 109.439C115.664 114.114 113.589 118.678 112.141 121.16C110.96 123.184 109.706 127.058 108.621 131.053C107.532 135.057 106.606 139.211 106.085 141.814L106.081 141.835V143.345L106.092 143.377C106.299 143.998 106.964 146.075 107.962 149.402C108.993 152.84 108.441 155.689 108.109 157.397C108.045 157.727 107.989 158.015 107.955 158.256C107.91 158.572 107.921 159.202 107.964 159.994C108.008 160.794 108.084 161.781 108.177 162.823C108.361 164.885 108.608 167.169 108.781 168.662C108.677 168.962 108.482 169.411 108.219 169.827C107.939 170.27 107.604 170.638 107.252 170.779C107.204 170.799 107.162 170.797 107.114 170.775C107.058 170.75 106.989 170.694 106.911 170.594C106.753 170.394 106.6 170.072 106.459 169.676C106.179 168.889 105.974 167.885 105.871 167.198L105.87 167.195L105.87 167.192C105.662 166.048 105.35 164.322 105.09 162.861C104.96 162.131 104.843 161.467 104.759 160.976C104.716 160.73 104.682 160.528 104.659 160.382C104.647 160.309 104.638 160.252 104.633 160.21C104.629 160.184 104.627 160.169 104.626 160.162C104.625 160.154 104.625 160.154 104.625 160.158C104.625 160.1 104.606 160.043 104.567 159.997C104.532 159.955 104.489 159.932 104.456 159.919C104.395 159.895 104.329 159.892 104.279 159.892C104.172 159.893 104.039 159.915 103.906 159.944C103.635 160.001 103.315 160.098 103.104 160.168L103.004 160.201L102.972 160.301C102.072 163.067 100.224 168.791 100.056 169.466C100.029 169.574 100.019 169.72 100.017 169.882C100.016 170.048 100.024 170.245 100.038 170.465C100.068 170.904 100.126 171.44 100.197 172.002C100.339 173.128 100.538 174.368 100.677 175.168L100.683 175.207L100.704 175.241C101.255 176.136 102.072 177.604 102.729 179.095C103.058 179.84 103.345 180.587 103.54 181.269C103.736 181.954 103.832 182.556 103.794 183.018C103.753 183.506 103.655 183.764 103.555 183.887C103.509 183.944 103.465 183.969 103.426 183.98C103.385 183.991 103.333 183.992 103.265 183.973C103.124 183.935 102.953 183.824 102.773 183.66C102.597 183.499 102.43 183.303 102.298 183.121L102.295 183.117L96.9202 176.089L95.2684 164.32L96.9284 154.151L96.9291 154.143C97.7612 144.158 99.882 121.539 101.71 110.901C103.539 100.262 107.461 84.7129 109.194 78.2666L109.197 78.256L109.198 78.2451C110.515 69.9266 113.276 50.6153 113.776 39.9537C114.088 33.3105 114.866 28.7521 116.004 24.8982C117.064 21.3082 118.438 18.3241 120.05 14.8218C120.17 14.5619 120.291 14.2991 120.413 14.033C122.143 10.2663 125.608 8.47129 129.099 7.38538C130.58 6.92466 132.055 6.59441 133.399 6.29377C133.638 6.24017 133.874 6.18751 134.104 6.13522C135.614 5.79253 136.933 5.46198 137.796 4.97627C140.455 3.48063 149.298 -1.1903 153.39 -3.34017L153.501 -3.39862V-16.0032Z"
                      fill="black"
                    />
                    <path
                      id="Vector 15"
                      d="M118.297 80.4282C118.297 80.4282 118.505 82.3001 119.129 83.132C119.753 83.9639 120.585 84.1719 120.585 84.1719"
                      stroke="black"
                      stroke-width="0.419068"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 14"
                      d="M162.598 9.71463C162.598 9.71463 161.933 8.00918 160.102 7.01087C158.272 6.01255 152.407 5.97096 149.495 4.09912"
                      stroke="black"
                      stroke-width="0.419068"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 14_2"
                      d="M167.176 9.71463C167.176 9.71463 167.842 8.00918 169.672 7.01087C171.502 6.01255 177.367 5.97096 180.279 4.09912"
                      stroke="black"
                      stroke-width="0.419068"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 15_2"
                      d="M213.763 80.4282C213.763 80.4282 213.555 82.3001 212.931 83.132C212.307 83.9639 211.475 84.1719 211.475 84.1719"
                      stroke="black"
                      stroke-width="0.419068"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 11"
                      d="M165.483 103.987C165.514 103.876 165.45 103.76 165.338 103.729C165.227 103.697 165.111 103.762 165.08 103.873L165.483 103.987ZM165.08 103.873C165.002 104.148 165.01 104.548 165.056 104.98C165.102 105.422 165.19 105.928 165.292 106.433C165.395 106.938 165.512 107.446 165.616 107.89C165.721 108.336 165.812 108.713 165.863 108.964L166.274 108.88C166.22 108.621 166.127 108.234 166.024 107.794C165.92 107.351 165.804 106.849 165.703 106.35C165.602 105.85 165.517 105.359 165.472 104.937C165.427 104.506 165.429 104.177 165.483 103.987L165.08 103.873ZM165.863 108.964C166.068 109.962 166.119 111.809 165.864 112.936L166.273 113.028C166.543 111.834 166.489 109.925 166.274 108.88L165.863 108.964ZM165.864 112.936C165.804 113.203 165.72 113.266 165.689 113.28C165.656 113.294 165.593 113.295 165.481 113.234C165.375 113.175 165.268 113.082 165.184 112.997C165.143 112.956 165.109 112.919 165.086 112.892C165.074 112.879 165.065 112.868 165.06 112.861C165.057 112.858 165.055 112.855 165.053 112.853C165.053 112.853 165.052 112.852 165.052 112.852C165.052 112.852 165.052 112.852 165.052 112.852C165.052 112.852 165.052 112.852 165.052 112.852C165.052 112.852 165.052 112.852 165.052 112.852C165.052 112.852 165.052 112.852 164.888 112.982C164.723 113.112 164.723 113.112 164.723 113.112C164.724 113.112 164.724 113.112 164.724 113.112C164.724 113.112 164.724 113.112 164.724 113.112C164.724 113.113 164.724 113.113 164.725 113.114C164.725 113.114 164.726 113.115 164.727 113.117C164.73 113.12 164.733 113.123 164.737 113.128C164.744 113.138 164.756 113.151 164.77 113.167C164.798 113.199 164.838 113.244 164.887 113.293C164.983 113.389 165.122 113.514 165.278 113.6C165.429 113.684 165.644 113.758 165.858 113.663C166.073 113.568 166.202 113.34 166.273 113.028L165.864 112.936Z"
                      fill="black"
                    />
                    <path
                      id="Vector 12"
                      d="M147.206 144.279C147.206 144.279 149.494 147.398 151.574 149.478C153.392 151.296 155.525 153.222 155.525 153.222"
                      stroke="black"
                      stroke-width="0.419068"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 13"
                      d="M148.04 58.3813L148.073 58.5882L148.04 58.3813ZM162.381 48.259C162.419 48.1498 162.361 48.0303 162.252 47.9923C162.142 47.9543 162.023 48.0121 161.985 48.1214L162.381 48.259ZM129.736 36.5271C129.005 46.0288 131.35 51.9172 135.027 55.2467C138.701 58.573 143.66 59.3038 148.073 58.5882L148.006 58.1745C143.676 58.8767 138.86 58.1516 135.309 54.936C131.76 51.7236 129.429 45.984 130.154 36.5593L129.736 36.5271ZM148.073 58.5882C153.74 57.6692 157.317 55.0954 159.477 52.7446C160.556 51.5702 161.281 50.4529 161.736 49.6276C161.963 49.215 162.124 48.8751 162.228 48.6373C162.28 48.5184 162.317 48.425 162.342 48.3607C162.355 48.3286 162.364 48.3038 162.371 48.2867C162.374 48.2781 162.376 48.2715 162.378 48.2669C162.379 48.2646 162.379 48.2628 162.38 48.2615C162.38 48.2608 162.38 48.2603 162.38 48.2599C162.38 48.2597 162.38 48.2595 162.38 48.2594C162.381 48.2592 162.381 48.259 162.183 48.1902C161.985 48.1214 161.985 48.1213 161.985 48.1212C161.985 48.1213 161.985 48.1212 161.985 48.1213C161.985 48.1214 161.985 48.1216 161.985 48.122C161.984 48.1227 161.984 48.1239 161.983 48.1257C161.982 48.1291 161.98 48.1345 161.977 48.1419C161.972 48.1565 161.964 48.1789 161.952 48.2085C161.929 48.2678 161.893 48.356 161.844 48.4695C161.744 48.6966 161.59 49.0248 161.369 49.4253C160.927 50.2263 160.221 51.3152 159.169 52.461C157.065 54.7505 153.57 57.2722 148.006 58.1745L148.073 58.5882Z"
                      fill="black"
                    />
                    <path
                      id="Vector 14_3"
                      d="M182.773 58.3813L182.739 58.5881L182.773 58.3813ZM168.432 48.259C168.394 48.1498 168.452 48.0303 168.561 47.9923C168.67 47.9543 168.79 48.0121 168.828 48.1214L168.432 48.259ZM201.076 36.5312C201.599 45.618 199.252 51.5009 195.633 54.9314C192.015 58.3606 187.161 59.3052 182.739 58.5881L182.806 58.1745C187.128 58.8753 191.841 57.948 195.344 54.6273C198.846 51.3079 201.176 45.5629 200.658 36.5552L201.076 36.5312ZM182.739 58.5881C177.072 57.6692 173.495 55.0954 171.335 52.7446C170.256 51.5702 169.532 50.4529 169.077 49.6276C168.849 49.215 168.689 48.8751 168.585 48.6373C168.533 48.5184 168.495 48.425 168.47 48.3607C168.458 48.3286 168.448 48.3038 168.442 48.2867C168.439 48.2781 168.436 48.2715 168.435 48.2669C168.434 48.2646 168.433 48.2628 168.433 48.2615C168.433 48.2608 168.432 48.2603 168.432 48.2599C168.432 48.2597 168.432 48.2595 168.432 48.2594C168.432 48.2592 168.432 48.259 168.63 48.1902C168.828 48.1214 168.828 48.1213 168.828 48.1212C168.828 48.1213 168.828 48.1212 168.828 48.1213C168.828 48.1214 168.828 48.1216 168.828 48.122C168.828 48.1227 168.829 48.1239 168.829 48.1257C168.831 48.1291 168.832 48.1345 168.835 48.1419C168.841 48.1565 168.849 48.1789 168.861 48.2085C168.884 48.2678 168.919 48.356 168.969 48.4695C169.068 48.6966 169.223 49.0248 169.444 49.4253C169.886 50.2263 170.591 51.3152 171.644 52.461C173.748 54.7505 177.242 57.2722 182.806 58.1745L182.739 58.5881Z"
                      fill="black"
                    />
                  </g>
                  <path
                    id="Vector 41"
                    d="M204.064 192.114L167.23 190.934V190.462V189.753L169.78 186.684L204.537 188.573L204.3 190.462L204.064 192.114Z"
                    fill="white"
                  />
                  <path
                    id="Rectangle 1"
                    d="M167.23 186.684L167.466 189.753V190.698H166.994L167.23 186.684Z"
                    fill="white"
                  />
                  <path
                    id="Vector 42"
                    d="M163.455 190.934V189.99L156.607 187.628L125.676 188.809L126.148 191.878L126.385 191.17L163.455 190.934Z"
                    fill="#F4F3F3"
                  />
                </g>
                {findMeasure('chest') && (
                  <g id="chest">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M200.634 44C200.634 44 179.11 48.3886 156.958 47.1347M130 44C130 44 137.941 47.1347 150.479 47.1347"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 27"
                            d="M159.763 44.2411L155.694 46.9719L159.19 49.5935"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 28"
                            d="M146.941 44.2411L151.01 46.9719L147.514 49.5935"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('waistLower') && (
                  <g id="waistLower">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M205.907 127.503C205.907 127.503 186.301 135.215 156.683 133.552M125 126.85C125 126.85 135.718 132.339 150.124 133.552"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 27_2"
                            d="M159.524 130.482L155.404 133.247L158.943 135.901"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 28_2"
                            d="M146.542 130.482L150.662 133.247L147.122 135.901"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('waist') && (
                  <g id="waist">
                    <g id="Group 221_3">
                      <g id="Group 217_3">
                        <g id="Group 220_3">
                          <path
                            id="Ellipse 23_3"
                            d="M196.5 98C196.5 98 179.129 102.186 156.941 100.93M134.001 99.0835C134.001 99.0835 137.893 100.93 150.452 100.93"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 27_3"
                            d="M159.752 97.9445L155.676 100.68L159.178 103.306"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 28_3"
                            d="M146.908 97.9445L150.984 100.68L147.482 103.306"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('upper_waist') && (
                  <g id="upper_waist">
                    <g id="Group 221_4">
                      <g id="Group 217_4">
                        <g id="Group 220_4">
                          <path
                            id="Ellipse 23_4"
                            d="M197.5 66C197.5 66 178.964 70.1136 157.018 68.8713M133.5 66.5C133.5 66.5 139.061 68.068 150.6 68.8713"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 27_4"
                            d="M159.798 65.9691L155.766 68.6745L159.23 71.2718"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 28_4"
                            d="M147.094 65.9691L151.126 68.6745L147.662 71.2718"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('underbust') && (
                  <g id="underbust">
                    <g id="Group 221_5">
                      <g id="Group 217_5">
                        <g id="Group 220_5">
                          <path
                            id="Ellipse 23_5"
                            d="M198 59.176C198 59.176 178.754 63.1262 156.885 61.8883M133 59.5584C133 59.5584 138.736 61.0878 150.489 61.8883"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 27_5"
                            d="M159.655 59.0075L155.637 61.7035L159.089 64.2916"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                          <path
                            id="Vector 28_5"
                            d="M146.996 59.0075L151.013 61.7035L147.562 64.2916"
                            stroke="#E55959"
                            stroke-width="2"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('body_length') && (
                  <g id="body_length">
                    <path
                      id="Vector 19_2"
                      d="M189.527 117.26L189.527 27.9999"
                      stroke="#E55959"
                      stroke-width="2"
                      stroke-linecap="square"
                    />
                    <path
                      id="Vector 29"
                      d="M124 121L206.553 121"
                      stroke="#E55959"
                      stroke-linecap="square"
                      stroke-dasharray="2.58 2.58"
                    />
                    <path
                      id="Vector 28_6"
                      d="M192.762 115.411L189.444 118.668L186.564 115.314"
                      stroke="#E55959"
                      stroke-width="2"
                    />
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_1439"
              x1="146.791"
              y1="10.9614"
              x2="148.038"
              y2="75.8518"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_1439"
              x1="184.02"
              y1="10.9614"
              x2="182.772"
              y2="75.8518"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_1439"
              x1="145.958"
              y1="131.799"
              x2="129.944"
              y2="224.143"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_128_1439"
              x1="114.553"
              y1="61.2934"
              x2="99.7867"
              y2="180.259"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className=" h-full m-auto "
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_mid_body">
            <mask id="mask0_128_2817" maskUnits="userSpaceOnUse" x="0" y="0" width="342" height="291">
              <rect id="rect" width="342" height="291" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_128_2817)">
              <g id="group_mid_body_mask">
                <g id="female">
                  <g id="female_2">
                    <path
                      id="Union"
                      d="M152.947 -18.7272L152.638 -1.13774L128.569 12.1315L119.311 14.2916L111.905 17.0689L107.585 19.8462L104.499 23.5492L98.6356 36.2013L96.4755 44.5332L94.3153 56.5681L93.081 71.3803L89.9951 98.8446L88.4522 110.571L86.9092 120.754L81.3547 142.973L77.0344 163.339L73.64 183.706L71.4799 203.147L70.2455 216.725L69.0112 233.697L66.5425 248.201C67.2625 253.755 68.7026 264.988 68.7026 265.482C68.7026 265.975 72.4056 271.036 74.2572 273.505L77.6516 277.825L79.1946 277.517C79.4003 277.002 79.1946 276.467 79.1946 275.974C79.1946 275.519 78.9754 272.608 78.4111 271.06C78.2089 270.505 78.0416 270.046 77.9602 269.802C77.7133 269.061 75.8001 265.79 74.8743 264.247C74.6686 262.19 74.2572 257.767 74.2572 256.533C74.2572 255.298 75.903 249.847 76.7259 247.275L78.5774 242.029C79.0917 242.029 80.1203 242.091 80.1203 242.338C80.1203 242.465 80.5439 244.446 81.0401 246.767C81.7419 250.049 82.589 254.011 82.589 254.373C82.589 254.866 83.4119 257.047 83.8234 258.076C84.132 258.281 84.8108 258.569 85.0577 258.076C85.3046 257.582 86.3949 255.813 86.9092 254.99L85.9835 244.189V238.326L86.6007 232.771C86.4978 231.64 86.2921 229.254 86.2921 228.76C86.2921 228.398 85.7626 226.343 85.324 224.639C85.0139 223.435 84.7491 222.407 84.7491 222.28C84.7491 222.125 84.2863 220.891 83.8234 219.657C83.3605 218.422 82.8976 217.188 82.8976 217.034V214.873L85.9835 201.296C87.4236 196.77 90.3037 187.656 90.3037 187.409C90.3037 187.182 92.4866 183.092 93.7558 180.714C94.2071 179.868 94.543 179.239 94.6239 179.077C94.8708 178.584 98.0184 171.26 99.5613 167.66L103.264 157.168L105.424 149.453L107.585 132.481L108.819 123.532L116.842 88.9698L119.619 76.3177C120.957 85.3696 123.631 103.782 123.631 105.016C123.631 105.788 124.325 111.882 125.02 117.977C125.714 124.072 126.408 130.166 126.408 130.938C126.408 132.172 126.82 138.241 127.026 141.121L116.842 174.757L111.288 190.186L106.659 211.17C106.145 214.462 105.116 221.23 105.116 221.971C105.116 222.712 104.704 231.331 104.499 235.549L106.659 249.127L112.09 287.391H168.376L168.068 263.939L167.451 250.978L167.759 242.955C167.862 242.338 168.129 241.042 168.376 240.795C168.623 240.548 169.919 239.869 170.536 239.56C171.154 239.869 172.45 240.548 172.697 240.795C172.943 241.042 173.211 242.338 173.314 242.955L173.622 250.978L173.005 263.939L173.047 287.45L228.399 287.8L234.414 249.127L236.574 235.549C236.368 231.331 235.957 222.712 235.957 221.971C235.957 221.23 234.928 214.462 234.414 211.17L229.785 190.186L224.231 174.757L214.047 141.121C214.253 138.241 214.664 132.172 214.664 130.938C214.664 130.166 215.359 124.072 216.053 117.977C216.747 111.882 217.442 105.788 217.442 105.016C217.442 103.782 220.116 85.3696 221.453 76.3177L224.231 88.9698L232.254 123.532L233.488 132.481L235.648 149.453L237.808 157.168L241.511 167.66C243.054 171.26 246.202 178.584 246.449 179.077C246.53 179.239 246.866 179.868 247.317 180.714C248.586 183.092 250.769 187.182 250.769 187.409C250.769 187.656 253.649 196.77 255.089 201.296L258.175 214.873V217.034C258.175 217.188 257.712 218.422 257.249 219.657C256.787 220.891 256.324 222.125 256.324 222.28C256.324 222.407 256.059 223.435 255.749 224.639C255.31 226.343 254.781 228.398 254.781 228.76C254.781 229.254 254.575 231.64 254.472 232.771L255.089 238.326V244.189L254.164 254.99C254.678 255.813 255.768 257.582 256.015 258.076C256.262 258.569 256.941 258.281 257.249 258.076C257.661 257.047 258.484 254.866 258.484 254.373C258.484 254.011 259.331 250.049 260.033 246.767C260.529 244.446 260.952 242.465 260.952 242.338C260.952 242.091 261.981 242.029 262.495 242.029L264.347 247.275C265.17 249.847 266.816 255.298 266.816 256.533C266.816 257.767 266.404 262.19 266.198 264.247C265.273 265.79 263.359 269.061 263.113 269.802C263.031 270.046 262.864 270.505 262.662 271.06C262.097 272.608 261.261 274.902 261.261 275.357C261.261 275.85 261.673 277.002 261.878 277.517H263.113L266.816 273.505C268.667 271.036 272.37 265.975 272.37 265.482C272.37 264.988 273.81 253.755 274.53 248.201L272.062 233.697L270.827 216.725L269.593 203.147L267.433 183.706L264.038 163.339L259.718 142.973L254.164 120.754L252.621 110.571L251.078 98.8446L247.992 71.3803L246.757 56.5681L244.597 44.5332L242.437 36.2013L236.574 23.5492L233.488 19.8462L229.168 17.0689L221.762 14.2916L212.504 12.1315L188.434 -1.13774L188.126 -18.7272L172.991 -25.0141L173.005 -27.0591L170.536 -26.0336L168.068 -27.0591L168.082 -25.0141L152.947 -18.7272Z"
                      fill="white"
                    />
                    <g id="Group 3">
                      <path
                        id="Vector 16"
                        d="M154.487 100.078C164.979 93.289 166.522 76.9338 166.522 76.3167L164.67 79.4025C163.127 81.254 159.856 85.0805 159.115 85.5743C158.375 86.068 151.812 88.8659 148.623 90.2031C145.846 90.7174 140.045 91.746 139.057 91.746C138.07 91.746 134.12 90.7174 132.268 90.2031L124.245 84.9571L119.925 77.2424C121.262 85.78 123.875 103.102 123.628 104.09C123.319 105.324 143.995 106.867 154.487 100.078Z"
                        fill="url(#paint0_linear_128_2817)"
                      />
                      <path
                        id="Vector 19"
                        d="M174.853 76.3167C174.853 76.9338 173.161 85.0674 178.557 90.2031C184.081 95.461 191.291 97.5774 198.615 95.4491C203.596 94.0014 208.798 90.2031 208.798 90.2031C208.798 90.2031 220.113 85.78 221.45 77.2424L216.821 84.3399L208.798 90.2031C208.798 90.2031 203.305 91.746 202.318 91.746C201.33 91.746 195.529 90.7174 192.752 90.2031C189.563 88.8659 183 86.068 182.26 85.5743C181.519 85.0805 178.248 81.254 176.705 79.4025L174.853 76.3167Z"
                        fill="url(#paint1_linear_128_2817)"
                      />
                      <path
                        id="Vector 17"
                        d="M148.932 224.439C156.25 232.593 167.447 242.646 167.447 242.646V249.126L168.373 274.43V286.774L112.21 289.551L107.889 261.47L104.804 237.708L105.421 221.045L108.507 201.604L117.147 174.757L127.639 196.975C131.033 204.587 138.131 212.404 148.932 224.439Z"
                        fill="url(#paint2_linear_128_2817)"
                      />
                      <path
                        id="Vector 18"
                        d="M173.314 -2.37257C182.336 -6.19914 186.892 -14.4075 188.435 -16.8762V-19.0363C184.732 -21.505 177.202 -26.4424 176.709 -26.4424C176.215 -26.4424 160.662 -22.1222 152.948 -19.962V-4.53268C159.119 -0.521044 167.587 0.0566101 173.314 -2.37257Z"
                        fill="url(#paint3_linear_128_2817)"
                      />
                      <path
                        id="Vector 20"
                        d="M102.027 129.703C104.496 131.184 107.376 126.617 108.507 124.148H108.816L106.656 140.195C106.347 143.178 105.73 149.329 105.73 150.069C105.73 150.81 103.673 157.578 102.644 160.87L96.781 175.991C95.0323 179.282 90.9178 186.483 90.9178 186.791C90.9178 187.664 88.7577 192.449 87.832 194.815L83.2032 212.404V216.724C83.5118 217.753 84.1289 219.934 84.1289 220.427C84.1289 221.044 86.5976 228.451 86.5976 229.068V234.005L85.9804 239.868C85.9804 245.114 87.4 253.693 86.9062 254.68C86.289 255.915 86.3919 256.738 85.9804 256.841L84.4375 258.383L83.5118 257.766L82.586 255.606C81.7631 251.389 80.4259 242.831 80.4259 242.337C80.4259 241.843 79.1915 242.131 78.5744 242.337L73.9455 257.149L75.1799 264.864C76.62 267.23 79.1915 273.011 79.1915 273.504C79.1915 273.998 79.5001 275.459 79.5001 276.59L78.5744 278.133L76.7228 276.59L69.0081 266.098L66.2309 248.817L69.0081 234.005L71.1683 210.861L74.8713 176.916L77.9572 159.018C79.5001 152.435 82.586 139.084 82.586 138.343C82.586 137.602 85.4661 126.925 86.9062 121.679C87.5234 119.313 88.696 114.335 88.4492 113.347C88.2023 112.36 90.1978 98.3295 91.2264 91.4377C93.5923 101.621 98.3857 122.296 98.6325 123.531C98.9411 125.074 98.9411 127.851 102.027 129.703Z"
                        fill="url(#paint4_linear_128_2817)"
                      />
                      <path
                        id="Vector 39"
                        d="M126.452 371.177C126.732 367.813 114.891 315.825 111.738 286.397L126.802 286.748H168.141V338.946C167.908 344.435 167.44 356.603 167.44 361.367C167.44 367.323 163.937 378.884 163.937 382.737C163.937 386.591 162.536 393.598 163.937 404.107C165.338 414.617 165.338 435.637 165.689 440.191C166.039 444.745 162.536 471.02 162.536 474.874V497.995C163.003 499.747 164.007 503.951 164.287 506.753C164.638 510.257 162.185 516.563 162.185 517.263C162.185 517.824 161.485 527.773 161.134 532.678L159.733 537.232V543.188C159.733 543.468 158.098 545.64 157.281 546.691L156.58 549.493C156.23 550.895 152.026 552.296 148.873 551.946C146.351 551.665 146.421 549.96 146.771 549.143C145.72 549.61 143.268 550.614 141.866 550.895C140.465 551.175 139.181 550.077 138.714 549.493C138.013 549.844 136.331 550.544 135.21 550.544C134.089 550.544 133.342 549.844 133.108 549.493H130.306C128.624 549.493 128.437 548.326 128.554 547.742C128.437 547.859 128.064 548.162 127.503 548.442C126.802 548.793 126.102 548.092 125.751 545.64C125.471 543.678 127.269 540.852 128.204 539.684L141.866 519.716C141.75 518.431 141.586 515.582 141.866 514.461C142.217 513.059 143.968 495.543 144.669 491.339C145.37 487.135 132.408 441.592 127.853 429.681C123.299 417.77 126.102 375.38 126.452 371.177Z"
                        fill="#F4F3F3"
                        stroke="black"
                        stroke-width="0.700655"
                      />
                      <path
                        id="Vector 40"
                        d="M214.384 372.228C214.103 368.864 225.594 317.226 228.747 286.397L214.033 286.748L172.695 287.098V338.946C172.928 344.435 173.395 356.603 173.395 361.367C173.395 367.323 176.899 378.884 176.899 382.737C176.899 386.591 178.3 393.598 176.899 404.107C175.497 414.617 175.497 435.637 175.147 440.191C174.797 444.745 178.3 471.02 178.3 474.874V497.995C177.833 499.747 176.828 503.951 176.548 506.753C176.198 510.257 178.65 516.563 178.65 517.263C178.65 517.824 179.351 527.773 179.701 532.678L181.102 537.232V543.188C181.102 543.468 182.737 545.64 183.555 546.691L184.255 549.493C184.606 550.895 188.81 552.296 191.963 551.946C194.485 551.665 194.415 549.96 194.065 549.143C195.116 549.61 197.568 550.614 198.969 550.895C200.371 551.175 201.655 550.077 202.122 549.493C202.823 549.844 204.504 550.544 205.625 550.544C206.746 550.544 207.494 549.844 207.727 549.493H210.53C212.212 549.493 212.398 548.326 212.282 547.742C212.398 547.859 212.772 548.162 213.333 548.442C214.033 548.793 214.734 548.092 215.084 545.64C215.365 543.678 213.566 540.852 212.632 539.684L198.969 519.716C199.086 518.431 199.249 515.582 198.969 514.461C198.619 513.059 196.867 495.543 196.167 491.339C195.466 487.135 208.428 441.592 212.982 429.681C217.537 417.77 214.734 376.431 214.384 372.228Z"
                        fill="white"
                        stroke="black"
                        stroke-width="0.700655"
                      />
                    </g>
                    <path
                      id="Union_2"
                      d="M153.025 -19.2373C153.025 -19.4077 152.887 -19.5459 152.717 -19.5459C152.546 -19.5459 152.408 -19.4077 152.408 -19.2373V-0.90851C146.271 2.31654 133.34 9.14788 129.421 11.3525C128.234 12.0205 126.372 12.4943 124.109 13.0081C123.77 13.085 123.423 13.1626 123.069 13.2418C121.076 13.6877 118.859 14.1838 116.636 14.8755C111.4 16.5045 106.049 19.2415 103.371 25.0704C103.188 25.4676 103.008 25.86 102.829 26.2484C100.44 31.4371 98.3838 35.9031 96.7977 41.2741C95.0919 47.0509 93.9319 53.8653 93.4681 63.7582C92.7287 79.5318 88.6408 108.135 86.6852 120.488C84.111 130.066 78.2908 153.143 75.5742 168.949C72.8567 184.76 69.7091 218.334 68.4743 233.149L66 248.304L68.4865 266.021L76.5545 276.571L76.5565 276.574C76.7712 276.868 77.0449 277.191 77.3423 277.463C77.6348 277.73 77.977 277.973 78.3281 278.068C78.5084 278.117 78.7022 278.13 78.8946 278.076C79.0896 278.021 79.2595 277.904 79.3987 277.732C79.6663 277.402 79.8257 276.864 79.8889 276.106C79.9552 275.311 79.7878 274.332 79.4904 273.29C79.1914 272.243 78.7536 271.106 78.259 269.985C77.2823 267.771 76.0745 265.594 75.2465 264.243C75.0419 263.062 74.7556 261.264 74.5489 259.633C74.4438 258.804 74.3598 258.022 74.3173 257.388C74.2961 257.071 74.2855 256.794 74.2874 256.57C74.2894 256.339 74.3046 256.184 74.3262 256.097C74.5667 255.135 77.2352 246.865 78.592 242.69C78.8826 242.597 79.2547 242.49 79.5686 242.423C79.7087 242.393 79.8267 242.373 79.9164 242.365C79.9249 242.422 79.9356 242.49 79.9481 242.568C79.9833 242.787 80.0342 243.089 80.0969 243.455C80.2224 244.186 80.3961 245.172 80.589 246.256C80.9745 248.422 81.437 250.981 81.7456 252.679C81.9017 253.717 82.2136 255.248 82.6465 256.465C82.8617 257.071 83.1163 257.626 83.4132 258.003C83.5621 258.192 83.737 258.357 83.9426 258.451C84.159 258.55 84.3963 258.564 84.6334 258.469C85.3444 258.185 85.9284 257.497 86.3611 256.812C86.8007 256.117 87.1193 255.369 87.2802 254.886L87.3019 254.821L87.294 254.753C87.037 252.543 86.6671 249.12 86.3922 246.036C86.2548 244.493 86.1413 243.037 86.0776 241.86C86.0133 240.671 86.002 239.799 86.0586 239.403C86.1065 239.067 86.1862 238.658 86.2789 238.183C86.7705 235.661 87.6276 231.264 86.0487 226.001C84.5914 221.143 83.6112 218.075 83.2844 217.091V215.011C84.0559 211.157 85.4246 205.024 87.0307 199.115C88.6444 193.178 90.487 187.508 92.1914 184.586C94.3636 180.862 97.4564 174.056 100.159 167.107C102.859 160.163 105.186 153.037 105.808 148.678C106.336 144.981 106.921 139.987 107.465 135.339C107.557 134.55 107.648 133.771 107.738 133.009C108.046 130.386 108.335 127.978 108.586 126.11C108.711 125.176 108.826 124.379 108.929 123.759C109.033 123.131 109.122 122.707 109.19 122.502C109.318 122.116 109.783 120.28 110.447 117.581C111.113 114.872 111.985 111.272 112.931 107.34C114.824 99.476 117.015 90.2798 118.455 84.2107L118.457 84.2043L119.656 77.9673C121.028 87.4209 122.897 101.073 124.352 113.43C125.127 120.014 125.784 126.227 126.186 131.241C126.589 136.269 126.731 140.053 126.49 141.803C126 145.349 123.389 154.036 120.307 163.49C117.23 172.929 113.698 183.094 111.384 189.573L111.382 189.579L111.38 189.585C109.781 194.742 107.556 203.567 106.069 213.298C104.584 223.025 103.833 233.682 105.198 242.492C107.913 260.016 110.464 278.891 111.389 286.398L168.493 286.748C168.493 279.038 168.638 262.782 168.145 256.615C167.836 252.756 167.588 248.927 167.731 245.907C167.803 244.396 167.973 243.105 168.274 242.122C168.578 241.129 168.997 240.513 169.518 240.252C169.791 240.115 170.068 240.046 170.28 240.01C170.385 239.993 170.471 239.984 170.531 239.98C170.56 239.978 170.583 239.977 170.597 239.977L170.613 239.976L170.614 239.976L170.617 239.976L170.632 239.977C170.647 239.977 170.669 239.978 170.699 239.98C170.758 239.984 170.845 239.993 170.95 240.01C171.161 240.046 171.438 240.115 171.711 240.252C172.232 240.513 172.651 241.129 172.955 242.122C173.256 243.105 173.426 244.396 173.498 245.907C173.642 248.927 173.393 252.756 173.084 256.615C172.591 262.782 172.346 279.038 172.346 286.748L229.099 286.398C230.025 278.891 233.317 260.016 236.032 242.492C237.396 233.682 236.645 223.025 235.16 213.298C233.674 203.567 231.448 194.742 229.85 189.585L229.848 189.579L229.845 189.573C227.532 183.094 223.999 172.929 220.922 163.49C217.841 154.036 215.229 145.349 214.74 141.803C214.498 140.053 214.641 136.269 215.043 131.241C215.445 126.227 216.102 120.014 216.878 113.43C218.332 101.073 220.201 87.4209 221.573 77.9673L222.771 84.1977L222.773 84.2043L222.774 84.2107C224.214 90.2798 226.405 99.476 228.298 107.34C229.245 111.272 230.116 114.872 230.783 117.581C231.446 120.28 231.911 122.116 232.039 122.502C232.108 122.707 232.196 123.131 232.3 123.759C232.403 124.379 232.518 125.176 232.643 126.11C232.894 127.978 233.183 130.386 233.491 133.009C233.581 133.771 233.672 134.55 233.765 135.339C234.308 139.987 234.893 144.981 235.421 148.678C236.044 153.037 238.37 160.163 241.071 167.107C243.773 174.056 246.866 180.862 249.038 184.586C250.742 187.508 252.585 193.178 254.199 199.115C255.805 205.024 257.173 211.157 257.945 215.011V217.091C257.618 218.075 256.638 221.143 255.181 226.001C253.602 231.264 254.459 235.661 254.95 238.183C255.043 238.658 255.123 239.067 255.171 239.403C255.227 239.799 255.216 240.671 255.152 241.86C255.088 243.037 254.975 244.493 254.837 246.036C254.562 249.12 254.192 252.543 253.935 254.753L253.927 254.821L253.949 254.886C254.11 255.369 254.429 256.117 254.868 256.812C255.301 257.497 255.885 258.185 256.596 258.469C256.833 258.564 257.07 258.55 257.287 258.451C257.492 258.357 257.667 258.192 257.816 258.003C258.113 257.626 258.368 257.071 258.583 256.465C259.016 255.248 259.328 253.717 259.484 252.679C259.792 250.981 260.255 248.422 260.64 246.256C260.833 245.172 261.007 244.186 261.132 243.455C261.195 243.089 261.246 242.787 261.281 242.568C261.294 242.49 261.304 242.422 261.313 242.365C261.403 242.373 261.521 242.393 261.661 242.423C261.975 242.49 262.347 242.597 262.637 242.69C263.994 246.865 266.663 255.135 266.903 256.097C266.925 256.184 266.94 256.339 266.942 256.57C266.944 256.794 266.933 257.071 266.912 257.388C266.869 258.022 266.785 258.804 266.68 259.633C266.474 261.264 266.187 263.062 265.983 264.243C265.155 265.594 263.947 267.771 262.97 269.985C262.476 271.106 262.038 272.243 261.739 273.29C261.441 274.332 261.274 275.311 261.34 276.106C261.404 276.864 261.563 277.402 261.831 277.732C261.97 277.904 262.14 278.021 262.335 278.076C262.527 278.13 262.721 278.117 262.901 278.068C263.252 277.973 263.595 277.73 263.887 277.463C264.184 277.191 264.458 276.868 264.673 276.574L264.675 276.571L272.743 266.021L275.229 248.304L272.755 233.149C271.52 218.334 268.373 184.76 265.655 168.949C262.939 153.143 257.118 130.066 254.544 120.488C252.588 108.135 248.501 79.5318 247.761 63.7582C247.297 53.8653 246.137 47.0509 244.432 41.2741C242.845 35.9031 240.789 31.4371 238.4 26.2484L238.16 25.7269C238.06 25.5095 237.96 25.2907 237.859 25.0704C235.18 19.2415 229.829 16.5045 224.593 14.8755C222.37 14.1838 220.153 13.6877 218.16 13.2419L218.158 13.2414C217.805 13.1623 217.459 13.0848 217.121 13.0081C214.857 12.4943 212.996 12.0205 211.808 11.3525C207.889 9.14788 194.958 2.31654 188.821 -0.90851V-19.2373C188.821 -19.4077 188.683 -19.5459 188.513 -19.5459C188.342 -19.5459 188.204 -19.4077 188.204 -19.2373V-0.535618L188.369 -0.448895C194.44 2.74092 207.56 9.67128 211.505 11.8904C212.787 12.611 214.744 13.1015 216.984 13.6099C217.326 13.6875 217.675 13.7657 218.03 13.8452C220.023 14.2913 222.213 14.7813 224.41 15.4648C229.589 17.076 234.73 19.7393 237.298 25.3281C237.475 25.7145 237.651 26.0961 237.825 26.4737L237.836 26.4984C240.228 31.6948 242.267 36.1224 243.84 41.4489C245.528 47.1671 246.683 53.9305 247.145 63.7871C247.886 79.606 251.983 108.259 253.937 120.601L253.94 120.617L253.944 120.633C256.515 130.197 262.334 153.268 265.047 169.053C267.76 184.837 270.906 218.398 272.141 233.213L272.142 233.225L274.605 248.311L272.154 265.774L264.18 276.202L264.176 276.208C263.979 276.478 263.732 276.769 263.471 277.007C263.204 277.251 262.949 277.416 262.74 277.472C262.64 277.5 262.562 277.499 262.502 277.482C262.444 277.465 262.378 277.428 262.31 277.344C262.162 277.161 262.016 276.779 261.955 276.055C261.898 275.37 262.042 274.476 262.332 273.46C262.621 272.448 263.047 271.34 263.535 270.234C264.511 268.023 265.722 265.844 266.54 264.516L266.571 264.466L266.581 264.407C266.787 263.221 267.081 261.38 267.293 259.71C267.398 258.876 267.484 258.08 267.528 257.429C267.55 257.104 267.561 256.81 267.559 256.564C267.557 256.325 267.542 256.108 267.502 255.948C267.252 254.947 264.51 246.454 263.176 242.349L263.128 242.201L262.98 242.152C262.666 242.047 262.191 241.905 261.789 241.819C261.592 241.777 261.394 241.744 261.236 241.743C261.162 241.742 261.064 241.747 260.973 241.783C260.924 241.802 260.861 241.836 260.808 241.898C260.751 241.967 260.722 242.05 260.722 242.136C260.722 242.13 260.722 242.131 260.721 242.142C260.719 242.154 260.717 242.176 260.711 242.214C260.702 242.276 260.689 242.362 260.672 242.47C260.637 242.686 260.587 242.985 260.524 243.35C260.399 244.079 260.225 245.064 260.033 246.148C259.647 248.315 259.184 250.876 258.876 252.573L258.875 252.578L258.874 252.582C258.721 253.601 258.416 255.092 258.001 256.259C257.792 256.847 257.565 257.325 257.331 257.622C257.215 257.769 257.113 257.852 257.03 257.89C256.958 257.922 256.897 257.925 256.825 257.896C256.302 257.687 255.806 257.14 255.39 256.483C255 255.866 254.711 255.2 254.556 254.755C254.813 252.54 255.179 249.15 255.452 246.09C255.59 244.545 255.704 243.081 255.768 241.893C255.831 240.719 255.848 239.783 255.782 239.315C255.731 238.958 255.648 238.53 255.553 238.04C255.061 235.506 254.242 231.279 255.772 226.178C257.253 221.242 258.239 218.16 258.546 217.238L258.562 217.191V214.95L258.556 214.92C257.784 211.057 256.409 204.894 254.794 198.953C253.183 193.026 251.323 187.278 249.571 184.275C247.423 180.593 244.344 173.821 241.646 166.883C238.946 159.941 236.644 152.872 236.032 148.591C235.505 144.902 234.922 139.917 234.378 135.269C234.285 134.479 234.194 133.7 234.104 132.937C233.796 130.315 233.506 127.901 233.255 126.028C233.13 125.091 233.013 124.287 232.909 123.658C232.806 123.037 232.711 122.565 232.625 122.307C232.507 121.952 232.05 120.152 231.382 117.434C230.716 114.726 229.845 111.128 228.898 107.196C227.006 99.3353 224.816 90.1435 223.376 84.0747L221.835 76.0579L221.226 76.0714C219.837 85.5366 217.816 100.182 216.265 113.357C215.489 119.945 214.831 126.167 214.428 131.192C214.027 136.202 213.876 140.058 214.128 141.887C214.627 145.501 217.261 154.249 220.336 163.681C223.414 173.124 226.947 183.292 229.262 189.774C230.852 194.904 233.069 203.696 234.55 213.392C236.032 223.095 236.772 233.68 235.422 242.398C232.737 259.723 229.351 279.401 228.399 287.098L173.125 287.248C173.128 279.409 173.215 262.721 173.7 256.664C174.008 252.809 174.26 248.942 174.114 245.877C174.042 244.346 173.869 242.996 173.545 241.941C173.225 240.896 172.738 240.076 171.987 239.7C171.643 239.528 171.303 239.444 171.051 239.402C170.925 239.381 170.818 239.37 170.743 239.365C170.705 239.362 170.674 239.36 170.653 239.36C170.642 239.359 170.633 239.359 170.627 239.359L170.619 239.359H170.616H170.615H170.615H170.613H170.61L170.603 239.359C170.596 239.359 170.588 239.359 170.577 239.36C170.555 239.36 170.524 239.362 170.487 239.365C170.411 239.37 170.305 239.381 170.178 239.402C169.927 239.444 169.586 239.528 169.242 239.7C168.491 240.076 168.004 240.896 167.684 241.941C167.361 242.996 167.188 244.346 167.115 245.877C166.969 248.942 167.221 252.809 167.53 256.664C168.014 262.721 167.789 278.905 167.792 286.744L111.981 286.401C111.029 278.704 108.492 259.723 105.808 242.398C104.457 233.68 105.197 223.095 106.68 213.392C108.16 203.696 110.377 194.904 111.967 189.774C114.282 183.292 117.815 173.124 120.894 163.681C123.968 154.249 126.603 145.501 127.101 141.887C127.353 140.058 127.202 136.202 126.801 131.192C126.398 126.167 125.74 119.945 124.965 113.357C123.414 100.182 121.392 85.5366 120.003 76.0714L119.395 76.0579L117.853 84.0747C116.413 90.1435 114.223 99.3353 112.331 107.196C111.385 111.128 110.513 114.726 109.847 117.434C109.179 120.152 108.723 121.952 108.604 122.307C108.518 122.565 108.423 123.037 108.32 123.658C108.216 124.287 108.1 125.091 107.974 126.028C107.723 127.901 107.433 130.315 107.125 132.937C107.035 133.7 106.944 134.479 106.852 135.269C106.308 139.917 105.724 144.902 105.197 148.591C104.586 152.872 102.283 159.941 99.5834 166.883C96.8853 173.821 93.8063 180.593 91.6583 184.275C89.9066 187.278 88.0461 193.026 86.4351 198.953C84.8205 204.894 83.4458 211.057 82.6732 214.92L82.6672 214.95V217.191L82.6831 217.238C82.9903 218.16 83.9768 221.242 85.4575 226.178C86.9878 231.279 86.1681 235.506 85.6767 238.04C85.5816 238.53 85.4987 238.958 85.4476 239.315C85.3808 239.783 85.3978 240.719 85.4614 241.893C85.5256 243.081 85.6398 244.545 85.7775 246.09C86.0501 249.15 86.4159 252.54 86.6729 254.755C86.5187 255.2 86.2292 255.866 85.8394 256.483C85.4236 257.14 84.9274 257.687 84.4042 257.896C84.3326 257.925 84.271 257.922 84.1993 257.89C84.1168 257.852 84.0145 257.769 83.8981 257.622C83.6647 257.325 83.4371 256.847 83.228 256.259C82.813 255.092 82.508 253.601 82.3552 252.582L82.3545 252.578L82.3537 252.573C82.0451 250.876 81.5823 248.315 81.1967 246.148C81.0038 245.064 80.8304 244.079 80.7052 243.35C80.6426 242.985 80.5922 242.686 80.5575 242.47C80.5401 242.362 80.5269 242.276 80.5181 242.214C80.5127 242.176 80.5099 242.154 80.5085 242.142C80.5072 242.131 80.5071 242.13 80.5071 242.136C80.5071 242.05 80.4782 241.967 80.4209 241.898C80.3682 241.836 80.3048 241.802 80.2566 241.783C80.1656 241.747 80.067 241.742 79.9929 241.743C79.8353 241.744 79.6369 241.777 79.4398 241.819C79.0384 241.905 78.5636 242.047 78.2494 242.152L78.1017 242.201L78.0535 242.349C76.7196 246.454 73.9777 254.947 73.7274 255.948C73.6873 256.108 73.6723 256.325 73.6702 256.564C73.6681 256.81 73.6797 257.104 73.7015 257.429C73.7452 258.08 73.8309 258.876 73.9367 259.71C74.1483 261.38 74.4423 263.221 74.6485 264.407L74.6587 264.466L74.6897 264.516C75.5069 265.844 76.7188 268.023 77.6944 270.234C78.1821 271.34 78.6084 272.448 78.897 273.46C79.1872 274.476 79.3309 275.37 79.2738 276.055C79.2136 276.779 79.067 277.161 78.9193 277.344C78.8508 277.428 78.7857 277.465 78.7276 277.482C78.6668 277.499 78.5894 277.5 78.4893 277.472C78.2799 277.416 78.0255 277.251 77.7586 277.007C77.4976 276.769 77.2501 276.478 77.0536 276.208L77.0492 276.202L69.0751 265.774L66.6242 248.311L69.0873 233.225L69.0883 233.213C70.3229 218.398 73.4695 184.837 76.1824 169.053C78.8955 153.268 84.7144 130.197 87.2855 120.633L87.2897 120.617L87.2922 120.601C89.2465 108.259 93.3431 79.606 94.0846 63.7871C94.5467 53.9305 95.7011 47.1671 97.3897 41.4489C98.9626 36.1224 101.001 31.6949 103.393 26.4984C103.571 26.1128 103.75 25.7229 103.932 25.3281C106.499 19.7393 111.64 17.076 116.819 15.4648C119.016 14.7813 121.206 14.2913 123.199 13.8452C123.554 13.7657 123.904 13.6875 124.245 13.6099C126.485 13.1015 128.443 12.611 129.724 11.8904C133.669 9.67128 146.789 2.74092 152.86 -0.448895L153.025 -0.535618V-19.2373Z"
                      fill="black"
                    />
                    <path
                      id="Vector 15"
                      d="M100.792 123.84C100.792 123.84 101.101 126.617 102.027 127.852C102.953 129.086 104.187 129.395 104.187 129.395"
                      stroke="black"
                      stroke-width="0.62178"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 14"
                      d="M166.523 18.9207C166.523 18.9207 165.535 16.3903 162.82 14.9091C160.104 13.4279 151.402 13.3662 147.082 10.5889"
                      stroke="black"
                      stroke-width="0.62178"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 14_2"
                      d="M173.315 18.9207C173.315 18.9207 174.303 16.3903 177.018 14.9091C179.734 13.4279 188.436 13.3662 192.756 10.5889"
                      stroke="black"
                      stroke-width="0.62178"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 15_2"
                      d="M242.437 123.84C242.437 123.84 242.128 126.617 241.203 127.852C240.277 129.086 239.043 129.395 239.043 129.395"
                      stroke="black"
                      stroke-width="0.62178"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 11"
                      d="M170.803 158.795C170.85 158.63 170.754 158.458 170.588 158.412C170.423 158.365 170.251 158.461 170.205 158.626L170.803 158.795ZM170.205 158.626C170.09 159.033 170.102 159.626 170.169 160.268C170.238 160.923 170.369 161.675 170.52 162.424C170.672 163.174 170.846 163.927 171.001 164.585C171.156 165.247 171.291 165.806 171.367 166.179L171.976 166.054C171.897 165.67 171.759 165.097 171.606 164.443C171.451 163.786 171.28 163.04 171.13 162.3C170.98 161.559 170.853 160.83 170.788 160.204C170.721 159.564 170.723 159.077 170.803 158.795L170.205 158.626ZM171.367 166.179C171.672 167.659 171.747 170.401 171.369 172.072L171.975 172.209C172.376 170.437 172.295 167.605 171.976 166.054L171.367 166.179ZM171.369 172.072C171.279 172.469 171.155 172.562 171.108 172.583C171.06 172.604 170.966 172.606 170.8 172.514C170.643 172.427 170.485 172.289 170.359 172.163C170.298 172.102 170.248 172.047 170.214 172.007C170.197 171.987 170.184 171.972 170.175 171.961C170.171 171.956 170.168 171.952 170.166 171.95C170.165 171.949 170.164 171.948 170.164 171.948C170.164 171.947 170.163 171.947 170.163 171.947C170.163 171.947 170.163 171.947 170.164 171.947C170.164 171.947 170.164 171.947 170.164 171.947C170.164 171.947 170.164 171.948 169.92 172.14C169.676 172.333 169.676 172.333 169.676 172.334C169.676 172.334 169.676 172.334 169.676 172.334C169.677 172.334 169.677 172.334 169.677 172.334C169.677 172.335 169.678 172.335 169.678 172.336C169.679 172.337 169.68 172.339 169.682 172.341C169.685 172.345 169.69 172.35 169.696 172.357C169.707 172.371 169.724 172.391 169.745 172.415C169.786 172.463 169.846 172.529 169.919 172.602C170.061 172.745 170.267 172.93 170.499 173.058C170.723 173.182 171.042 173.291 171.36 173.151C171.678 173.01 171.87 172.673 171.975 172.209L171.369 172.072Z"
                      fill="black"
                    />
                    <path
                      id="Vector 12"
                      d="M143.686 218.576C143.686 218.576 147.08 223.205 150.166 226.291C152.864 228.989 156.029 231.846 156.029 231.846"
                      stroke="black"
                      stroke-width="0.62178"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      id="Vector 13"
                      d="M144.922 91.1287L144.972 91.4356L144.922 91.1287ZM166.2 76.1101C166.256 75.9479 166.171 75.7707 166.008 75.7143C165.846 75.6579 165.669 75.7436 165.613 75.9058L166.2 76.1101ZM117.765 58.7032C116.681 72.801 120.159 81.5378 125.615 86.4778C131.066 91.4131 138.424 92.4975 144.972 91.4356L144.873 90.8218C138.448 91.8637 131.302 90.7879 126.032 86.0169C120.768 81.2505 117.309 72.7346 118.385 58.7509L117.765 58.7032ZM144.972 91.4356C153.38 90.0721 158.687 86.2533 161.892 82.7653C163.493 81.023 164.568 79.3651 165.243 78.1407C165.581 77.5284 165.819 77.0241 165.973 76.6713C166.05 76.4949 166.106 76.3563 166.143 76.261C166.162 76.2133 166.176 76.1765 166.185 76.1511C166.19 76.1384 166.193 76.1286 166.196 76.1217C166.197 76.1183 166.198 76.1156 166.199 76.1137C166.199 76.1127 166.199 76.1119 166.199 76.1113C166.2 76.111 166.2 76.1107 166.2 76.1106C166.2 76.1103 166.2 76.1101 165.906 76.0079C165.613 75.9058 165.613 75.9057 165.613 75.9056C165.613 75.9057 165.613 75.9056 165.613 75.9057C165.613 75.9059 165.612 75.9062 165.612 75.9068C165.612 75.9078 165.611 75.9096 165.61 75.9122C165.609 75.9173 165.606 75.9253 165.602 75.9362C165.594 75.958 165.581 75.9912 165.564 76.0352C165.53 76.1231 165.477 76.2539 165.403 76.4224C165.256 76.7593 165.026 77.2463 164.699 77.8404C164.043 79.0289 162.996 80.6446 161.434 82.3447C158.313 85.7416 153.128 89.4831 144.873 90.8218L144.972 91.4356Z"
                      fill="black"
                    />
                    <path
                      id="Vector 14_3"
                      d="M196.456 91.1287L196.406 91.4356L196.456 91.1287ZM175.179 76.1101C175.122 75.9479 175.208 75.7707 175.37 75.7143C175.532 75.6579 175.709 75.7436 175.766 75.9058L175.179 76.1101ZM223.614 58.7092C224.389 72.1915 220.907 80.9201 215.537 86.0101C210.169 91.098 202.967 92.4995 196.406 91.4356L196.506 90.8218C202.918 91.8617 209.911 90.4858 215.109 85.5588C220.305 80.6338 223.761 72.1097 222.993 58.7449L223.614 58.7092ZM196.406 91.4356C187.998 90.0721 182.691 86.2533 179.486 82.7653C177.885 81.023 176.811 79.3651 176.135 78.1407C175.798 77.5284 175.56 77.0241 175.405 76.6713C175.328 76.4949 175.272 76.3563 175.235 76.261C175.217 76.2133 175.203 76.1765 175.193 76.1511C175.189 76.1384 175.185 76.1286 175.183 76.1217C175.181 76.1183 175.18 76.1156 175.18 76.1137C175.179 76.1127 175.179 76.1119 175.179 76.1113C175.179 76.111 175.179 76.1107 175.179 76.1106C175.179 76.1103 175.179 76.1101 175.472 76.0079C175.766 75.9058 175.766 75.9057 175.766 75.9056C175.766 75.9057 175.766 75.9056 175.766 75.9057C175.766 75.9059 175.766 75.9062 175.766 75.9068C175.767 75.9078 175.767 75.9096 175.768 75.9122C175.77 75.9173 175.773 75.9253 175.777 75.9362C175.785 75.958 175.797 75.9912 175.814 76.0352C175.849 76.1231 175.902 76.2539 175.975 76.4224C176.122 76.7592 176.352 77.2463 176.68 77.8404C177.335 79.0289 178.382 80.6446 179.944 82.3447C183.065 85.7416 188.25 89.4831 196.506 90.8218L196.406 91.4356Z"
                      fill="black"
                    />
                  </g>
                  <path
                    id="Vector 41"
                    d="M228.047 289.551L173.396 287.8V287.099V286.048L177.18 281.494L228.748 284.297L228.397 287.099L228.047 289.551Z"
                    fill="white"
                  />
                  <path
                    id="Rectangle 1"
                    d="M173.396 281.494L173.746 286.048V287.449H173.045L173.396 281.494Z"
                    fill="white"
                  />
                  <path
                    id="Vector 42"
                    d="M167.794 287.8V286.399L157.634 282.896L111.741 284.647L112.442 289.201L112.792 288.15L167.794 287.8Z"
                    fill="#F4F3F3"
                  />
                </g>
                {findMeasure('chest') && (
                  <g id="chest">
                    <g id="Group 221">
                      <g id="Group 217">
                        <g id="Group 220">
                          <path
                            id="Ellipse 23"
                            d="M222.959 69.7908C222.959 69.7908 191.022 76.3022 158.155 74.4418M118.156 69.7908C118.156 69.7908 129.939 74.4418 148.543 74.4418"
                            stroke="#E55959"
                            stroke-width="2.96744"
                          />
                          <path
                            id="Vector 27"
                            d="M162.318 70.1484L156.28 74.2001L161.467 78.0899"
                            stroke="#E55959"
                            stroke-width="2.96744"
                          />
                          <path
                            id="Vector 28"
                            d="M143.293 70.1484L149.331 74.2001L144.144 78.0899"
                            stroke="#E55959"
                            stroke-width="2.96744"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('waistLower') && (
                  <g id="lower_waist">
                    <g id="Group 221_2">
                      <g id="Group 217_2">
                        <g id="Group 220_2">
                          <path
                            id="Ellipse 23_2"
                            d="M230.781 193.686C230.781 193.686 201.691 205.128 157.746 202.662M110.737 192.717C110.737 192.717 126.64 200.862 148.015 202.662"
                            stroke="#E55959"
                            stroke-width="2.96744"
                          />
                          <path
                            id="Vector 27_2"
                            d="M161.961 198.106L155.848 202.208L161.1 206.146"
                            stroke="#E55959"
                            stroke-width="2.96744"
                          />
                          <path
                            id="Vector 28_2"
                            d="M142.7 198.106L148.812 202.208L143.561 206.146"
                            stroke="#E55959"
                            stroke-width="2.96744"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('waist') && (
                  <g id="waist">
                    <g id="Group 221_3">
                      <g id="Group 217_3">
                        <g id="Group 220_3">
                          <path
                            id="Ellipse 23_3"
                            d="M216.824 149.912C216.824 149.912 191.05 156.123 158.129 154.259M124.093 151.519C124.093 151.519 129.867 154.259 148.501 154.259"
                            stroke="#E55959"
                            stroke-width="2.96744"
                          />
                          <path
                            id="Vector 27_3"
                            d="M162.3 149.829L156.253 153.888L161.448 157.784"
                            stroke="#E55959"
                            stroke-width="2.96744"
                          />
                          <path
                            id="Vector 28_3"
                            d="M143.243 149.829L149.29 153.888L144.095 157.784"
                            stroke="#E55959"
                            stroke-width="2.96744"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('upper_waist') && (
                  <g id="upper_waist">
                    <g id="Group 221_4">
                      <g id="Group 217_4">
                        <g id="Group 220_4">
                          <path
                            id="Ellipse 23_4"
                            d="M218.307 102.433C218.307 102.433 190.805 108.536 158.243 106.693M123.349 103.174C123.349 103.174 131.599 105.501 148.72 106.693"
                            stroke="#E55959"
                            stroke-width="2.96744"
                          />
                          <path
                            id="Vector 27_4"
                            d="M162.367 102.387L156.385 106.401L161.524 110.255"
                            stroke="#E55959"
                            stroke-width="2.96744"
                          />
                          <path
                            id="Vector 28_4"
                            d="M143.518 102.387L149.5 106.401L144.361 110.255"
                            stroke="#E55959"
                            stroke-width="2.96744"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('underBust') && (
                  <g id="underbust">
                    <g id="Group 221_5">
                      <g id="Group 217_5">
                        <g id="Group 220_5">
                          <path
                            id="Ellipse 23_5"
                            d="M219.05 92.3079C219.05 92.3079 190.493 98.1688 158.046 96.3321M122.608 92.8752C122.608 92.8752 131.117 95.1444 148.556 96.3321"
                            stroke="#E55959"
                            stroke-width="2.96744"
                          />
                          <path
                            id="Vector 27_5"
                            d="M162.155 92.0579L156.194 96.0579L161.315 99.8979"
                            stroke="#E55959"
                            stroke-width="2.96744"
                          />
                          <path
                            id="Vector 28_5"
                            d="M143.372 92.0579L149.333 96.0579L144.212 99.8979"
                            stroke="#E55959"
                            stroke-width="2.96744"
                          />
                        </g>
                      </g>
                    </g>
                  </g>
                )}
                {findMeasure('body_length') && (
                  <g id="body_length">
                    <path
                      id="Vector 19_2"
                      d="M206.478 178.489L206.478 46.0509"
                      stroke="#E55959"
                      stroke-width="2.96744"
                      stroke-linecap="square"
                    />
                    <path
                      id="Vector 29"
                      d="M109.254 184.037L231.74 184.037"
                      stroke="#E55959"
                      stroke-width="1.48372"
                      stroke-linecap="square"
                      stroke-dasharray="3.83 3.83"
                    />
                    <path
                      id="Vector 28_6"
                      d="M211.279 175.744L206.356 180.577L202.083 175.6"
                      stroke="#E55959"
                      stroke-width="2.96744"
                    />
                  </g>
                )}
              </g>
            </g>
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_128_2817"
              x1="143.069"
              y1="20.7709"
              x2="144.92"
              y2="117.05"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_128_2817"
              x1="198.306"
              y1="20.7709"
              x2="196.455"
              y2="117.05"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECE9E9" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_128_2817"
              x1="141.834"
              y1="200.061"
              x2="118.073"
              y2="337.074"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_128_2817"
              x1="164.982"
              y1="-26.4424"
              x2="164.982"
              y2="10.0142"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_128_2817"
              x1="95.2381"
              y1="95.4494"
              x2="73.3284"
              y2="271.961"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="white" />
              <stop offset="1" stop-color="#ECE9E9" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
