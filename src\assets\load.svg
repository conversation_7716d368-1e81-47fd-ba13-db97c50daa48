<?xml version="1.0" encoding="utf-8"?>

<svg class="circular" version="1.1" id="Ebene_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 22 22" style="enable-background:new 0 0 22 22;" xml:space="preserve">

<style type="text/css">

.circular {
  animation: rotate 2s linear infinite;
  height: 22px;
  transform-origin: center center;
  width: 22px;
}

.path {
  stroke-dasharray: 1, 200;
  stroke-dashoffset: 0;
  animation: dash 2s ease-in-out infinite, color 8s ease-in-out infinite;
  stroke-linecap: round;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -16px;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -60px;
  }
}

@keyframes color {
  100%,
  0% {
    stroke: #7C89A5;
  }
  40% {
    stroke: #7C89A5;
  }
  66% {
    stroke: #7C89A5;
  }
  80%,
  90% {
    stroke: #7C89A5;
  }
}
</style>

<circle class="path" cx="11" cy="11" r="10" fill="none" stroke-width="2" stroke-miterlimit="10"/>
	
</svg>