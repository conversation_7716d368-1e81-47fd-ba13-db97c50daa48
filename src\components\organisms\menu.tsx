import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Container, Link, Text } from '../atoms';
import Dots from '../icons/dots';
import Arrow from '../icons/arrow';
import { cn } from '@/lib/utils';
import { useAppContext } from '@/store';
import { AppStateActions } from '@/store/types';
import { getMappedLanguages } from '@/lib/get-mapped-languages';

export const LanguageSwitcher = ({ className, onclick }: { className?: string; onclick?: () => void }) => {
  const {
    setState,
    app: { config },
  } = useAppContext();
  const { i18n } = useTranslation();
  const changeLanguage = (lang: string) => {
    setState({ action: AppStateActions.TOGGLE_MENU_OPEN, payload: null });
    i18n.changeLanguage(getMappedLanguages(lang).value);
  };

  const availableLanguages = config.general.language;

  return (
    <div className={cn('flex flex-col max-h-[50vh] overflow-hidden overflow-y-scroll custom-scroll', className)}>
      {availableLanguages.map((lang) => (
        <Button
          variant="blank"
          className="p-3 pr-9 text-left rounded-none border-b-[1px] border-[#E7E7E7]"
          onClick={() => {
            changeLanguage(lang);
            onclick?.();
          }}
        >
          <Text
            className={cn('text-[12px] text-[#272727] font-normal', lang === i18n.language ? 'font-medium' : '')}
            variant="label"
          >
            {i18n.t(`languages.all_langs.${lang}`)} - {lang.toUpperCase()}
          </Text>
        </Button>
      ))}
    </div>
  );
};

function Menu({ className }: { className?: string }) {
  const { app, setState } = useAppContext();
  const { menuOpen: isOpen } = app;
  const [isLanguageOpen, setIsLanguageOpen] = useState(false);
  const { t } = useTranslation();

  const toggleMenu = () => {
    setState({ action: AppStateActions.TOGGLE_MENU_OPEN, payload: null });
    setIsLanguageOpen(false);
  };

  const toggleLanguage = () => {
    setIsLanguageOpen(!isLanguageOpen);
  };

  return (
    <Container className={cn('p-0 menu-container', className)}>
      <Button variant="blank" className="w-fit" onClick={toggleMenu}>
        <Dots />
      </Button>
      {isOpen && (
        <div className="absolute flex top-full w-fit left-0  m-auto">
          <div className="flex flex-col h-fit bg-white border border-[#E7E7E7] rounded-lg shadow-lg z-50">
            <Button
              variant="blank"
              className="text-nowrap rounded-none text-[#272727] text-[12px] p-3 flex justify-between items-center gap-10"
              onClick={toggleLanguage}
            >
              {t('generics.language')} <Arrow className="-rotate-90" color="#272727" />
            </Button>
            <Button
              variant="blank"
              className="text-nowrap rounded-none text-left text-[#272727] text-[12px] p-3 border-b-[1px] border-t-[1px] border-gray-300"
              onClick={toggleMenu}
            >
              <Link external href="https://sizebay.com/pt/" className="no-underline">
                {t('generics.about')}
              </Link>
            </Button>
          </div>
          <aside className="bg-white border border-[#E7E7E7] rounded-lg shadow-lg z-50 h-fit w-fit">
            {isLanguageOpen && <LanguageSwitcher />}
          </aside>
        </div>
      )}
    </Container>
  );
}

export default Menu;
