import { CarouselItem } from '@/components/organisms';
import { Config } from '@/types/config';
import { Product } from '@/types/size-chart.types';

export type UnitSystem = 'metric' | 'imperial';

export type selectedImage = {
  image: JSX.Element | null;
  content: string;
};

export interface AppState {
  app: {
    lang: string;
    unitSystem: UnitSystem;
    loading: boolean;
    menuOpen: boolean;
    config: Config;
    selectedImage: selectedImage;
  };
  carousel: {
    selected: CarouselItem;
  };
  product: Product;
}

export enum AppStateActions {
  SET_LANG = 'SET_LANG',
  SET_UNIT_SYSTEM = 'SET_UNIT_SYSTEM',
  SET_PRODUCT = 'SET_PRODUCT',
  SET_CAROUSEL_SELECTED = 'SET_CAROUSEL_SELECTED',
  TOGGLE_MENU_OPEN = 'TOGGLE_MENU_OPEN',
  SET_CONFIG = 'SET_CONFIG',
  SET_SELECTED_IMAGE = 'SET_SELECTED_IMAGE',
}

export type SetState = ({ action, payload }: { action: AppStateActions; payload: unknown }) => void;

export interface AppContextType extends AppState {
  setState: SetState;
}
