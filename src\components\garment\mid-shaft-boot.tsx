import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface MidShaftBootProps {
  measure: GarmentMeasure;
  className?: string;
}

export function MidShaftBoot({ measure, className }: MidShaftBootProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width={isMobile ? '331' : '342'}
          height={isMobile ? '193' : '291'}
          viewBox={isMobile ? '0 0 331 193' : '0 0 342 291'}
          fill="none"
        >
          <g id="group_mid_shaft_boot">
            <g id="mid_shaft_boot">
              <path
                id="Vector 355"
                d="M145.933 99.8924C143.275 102.808 136.879 107.712 136.673 108.124V115.583C144.047 113.611 159.103 109.564 160.338 109.153C161.572 108.741 183.317 96.6342 194.034 90.6322L211.011 51.5336L184.517 34.5566L175 36.3572C173.456 48.0182 172.509 72.8352 167.797 79.3142C163.682 84.9732 151.506 95.0051 145.933 99.8924Z"
                fill="white"
                stroke="black"
                stroke-width="0.460034"
              />
              <path
                id="Vector 356"
                d="M193.006 27.0974C195.269 27.0974 205.953 32.0704 211.011 34.557L216.67 35.586L218.728 56.4214C215.642 65.5958 209.314 84.2534 208.696 85.4881C208.079 86.7227 193.863 97.6635 186.832 102.98L159.309 107.352L146.705 105.037L153.393 99.1211C156.479 94.5768 162.738 88.832 164.968 85.4881C165.997 83.9447 172.627 75.7341 176.8 69.2828C185.256 56.2102 186.575 35.3287 186.832 30.6986C187.089 26.0686 190.176 27.0974 193.006 27.0974Z"
                fill="white"
                stroke="black"
                stroke-width="0.460034"
              />
              <path
                id="Vector 348"
                d="M78.7966 142.85H79.3111L162.653 122.271L212.04 120.985L248.824 129.217C249.253 134.104 250.11 144.033 250.11 144.65C250.11 145.268 247.366 145.936 245.994 146.194L243.937 144.65V146.965C243.937 147.583 239.992 148.423 238.02 148.766L236.734 146.194C236.391 147.137 235.654 149.126 235.448 149.538C235.242 149.949 231.247 151.424 229.275 152.11L228.76 148.766L226.96 152.624L220.272 154.939L212.04 150.824C205.524 152.11 192.491 154.734 192.491 154.939V159.827L186.06 160.598L184.517 159.827H182.459V162.656L173.971 163.685L172.17 162.656H171.141V164.457C168.569 164.971 163.27 166 162.653 166C162.035 166 160.852 163.771 160.338 162.656H158.28L156.736 166H152.878L146.962 163.685L145.161 162.656V164.457C143.961 165.143 136.879 165.331 118.152 160.598C99.4262 155.865 84.4556 150.738 79.3111 148.766L78.7966 142.85Z"
                fill="white"
                stroke="black"
                stroke-width="0.460034"
              />
              <path
                id="Vector 347"
                d="M79.5683 140.793C80.0827 139.764 80.7687 139.935 81.3689 139.764L100.661 128.446C120.467 128.103 161.058 127.417 164.968 127.417C168.878 127.417 218.385 128.103 242.65 128.446C244.537 128.103 248.412 127.417 248.824 127.417C249.338 127.417 250.624 128.446 249.338 130.761C248.083 133.021 228.246 143.365 198.15 151.854C168.054 160.342 157.922 160.133 136.301 157.88C122.951 156.49 117.156 154.769 102.719 150.567C93.5684 147.905 79.5683 142.851 79.5683 142.851C79.5683 142.851 79.1541 141.621 79.5683 140.793Z"
                fill="white"
                stroke="black"
                stroke-width="0.460034"
              />
              <path
                id="Vector 346"
                d="M81.1116 140.02C79.9467 138.558 80.0828 136.419 82.1406 136.162L133.586 129.731L201.751 117.642L245.737 125.616C245.737 125.616 247.709 125.082 248.309 125.616C248.985 126.216 249.004 127.095 248.309 127.673C246.766 128.96 244.208 130.568 241.364 132.046C228.503 138.734 222.526 141.641 206.381 146.451C181.519 153.858 171.913 157.255 140.531 155.197C125.03 154.18 97.8314 147.48 81.1116 140.02Z"
                fill="white"
                stroke="black"
                stroke-width="0.460034"
              />
              <path
                id="Vector 345"
                d="M82.655 134.104C82.8608 133.898 83.5982 133.504 83.9411 133.332H96.2881C117.809 133.59 161.367 134.104 163.424 134.104C165.482 134.104 218.3 126.216 244.451 122.272C244.451 122.272 244.965 122.271 246.252 122.272C246.252 122.272 247.185 122.583 247.28 123.301C247.424 124.378 247.083 124.784 246.252 125.615C244.965 126.902 242.197 128.366 239.564 129.731C224.442 137.567 215.082 141.148 198.665 145.679C180.095 150.805 167.058 151.981 147.802 151.439C129.309 150.919 114.679 147.345 103.233 143.622C96.1166 141.306 81.8319 136.522 81.6261 135.905C81.3689 135.133 82.3978 134.361 82.655 134.104Z"
                fill="white"
                stroke="black"
                stroke-width="0.460034"
              />
              <path
                id="Vector 344"
                d="M240.078 85.7452C241.146 100.669 244.451 119.699 244.965 123.043C229.275 131.789 194.806 143.107 182.459 145.936C169.323 148.947 137.187 148.251 119.953 144.65C107.528 142.054 93.973 138.477 83.9411 133.59C84.3699 132.389 85.999 129.268 89.0857 126.387C92.9441 122.786 108.121 121.5 109.149 121.5C110.178 121.5 116.095 119.699 125.098 116.612C132.3 114.143 149.02 102.036 156.479 96.2915L165.997 97.8349C170.541 93.6335 180.916 83.1215 186.06 74.6844C192.491 64.1381 193.006 39.9588 195.578 35.8432C198.15 31.7276 198.407 34.557 203.552 34.2998C207.667 34.094 229.961 37.1293 240.593 38.6726C239.992 51.4482 238.78 67.5959 240.078 85.7452Z"
                fill="white"
                stroke="black"
                stroke-width="0.460034"
              />
              <path
                id="Vector 343"
                d="M238.02 33.7852V38.158L228.503 36.8904V33.7852H238.02Z"
                fill="white"
                stroke="black"
                stroke-width="0.460034"
              />
              <path
                id="Vector 353"
                d="M203.809 79.3144C207.738 70.8663 209.897 46.9895 209.983 35.8429C209.983 35.8429 199.693 33.7853 195.835 35.0713C192.552 36.1655 192.737 46.5861 191.462 54.1061C188.89 69.2826 188.633 73.9127 180.144 86.0024C177.296 90.0583 162.481 102.465 155.708 108.124C155.45 110.01 154.473 113.731 155.708 117.641C156.942 121.551 161.195 127.673 163.167 130.245L180.659 122.529L180.144 114.554C186.06 106.152 198.665 90.3753 203.809 79.3144Z"
                fill="white"
                stroke="black"
                stroke-width="0.460034"
              />
              <path
                id="Vector 352"
                d="M181.945 115.326C183.488 115.069 221.043 100.407 225.159 98.6065C228.451 97.166 231.59 96.0342 231.59 96.0342"
                stroke="black"
                stroke-width="0.460034"
                stroke-dasharray="0.92 0.92"
              />
              <path
                id="Vector 351"
                d="M231.59 96.8057C229.943 97.8346 196.864 110.867 180.401 117.384"
                stroke="black"
                stroke-width="0.460034"
              />
              <path
                id="Vector 349"
                d="M240.335 90.6328C239.718 91.2502 233.819 94.7484 231.59 96.0346V129.358L238.792 126.13L245.223 123.043"
                stroke="black"
                stroke-width="0.460034"
              />
              <path
                id="Vector 350"
                d="M240.593 92.1758C240.078 92.433 232.876 96.8059 232.876 96.8059C232.876 96.8059 232.79 118.156 232.876 128.702"
                stroke="black"
                stroke-width="0.460034"
                stroke-dasharray="0.92 0.92"
              />
              <path
                id="Vector 354"
                d="M202.78 78.2853C207.38 66.2748 208.439 45.1032 208.954 36.8715C205.438 36.8715 200.98 34.8139 196.092 36.8715C194.292 37.6296 194.034 43.0452 193.263 48.1897C192.491 53.3343 191.977 72.6263 183.488 84.4588C175.362 95.7858 163.596 104.094 156.994 108.381C156.822 109.581 156.582 112.908 156.994 116.612C157.508 121.242 162.653 126.13 163.167 126.901C163.682 127.673 178.344 122.014 178.344 121.499V113.783C183.831 106.58 198.15 90.3752 202.78 78.2853Z"
                fill="white"
                stroke="black"
                stroke-width="0.460034"
                stroke-dasharray="0.92 0.92"
              />
              <g id="Group 202">
                <path
                  id="Ellipse 15"
                  d="M204.865 42.5305C204.865 44.2679 203.569 45.6444 202.008 45.6444C200.448 45.6444 199.152 44.2679 199.152 42.5305C199.152 40.793 200.448 39.4165 202.008 39.4165C203.569 39.4165 204.865 40.793 204.865 42.5305Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Ellipse 14"
                  d="M203.836 42.5309C203.836 43.7086 202.993 44.6159 202.009 44.6159C201.024 44.6159 200.181 43.7086 200.181 42.5309C200.181 41.3531 201.024 40.4458 202.009 40.4458C202.993 40.4458 203.836 41.3531 203.836 42.5309Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Ellipse 16"
                  d="M200.75 59.5074C200.75 60.6852 199.907 61.5925 198.922 61.5925C197.937 61.5925 197.094 60.6852 197.094 59.5074C197.094 58.3297 197.937 57.4224 198.922 57.4224C199.907 57.4224 200.75 58.3297 200.75 59.5074Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Ellipse 17"
                  d="M196.634 75.9703C196.634 77.1481 195.791 78.0553 194.806 78.0553C193.821 78.0553 192.978 77.1481 192.978 75.9703C192.978 74.7926 193.821 73.8853 194.806 73.8853C195.791 73.8853 196.634 74.7926 196.634 75.9703Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Ellipse 18"
                  d="M196.634 75.9703C196.634 77.1481 195.791 78.0553 194.806 78.0553C193.821 78.0553 192.978 77.1481 192.978 75.9703C192.978 74.7926 193.821 73.8853 194.806 73.8853C195.791 73.8853 196.634 74.7926 196.634 75.9703Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Ellipse 19"
                  d="M188.917 89.86C188.917 91.0377 188.074 91.945 187.089 91.945C186.105 91.945 185.262 91.0377 185.262 89.86C185.262 88.6822 186.105 87.7749 187.089 87.7749C188.074 87.7749 188.917 88.6822 188.917 89.86Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Ellipse 20"
                  d="M179.657 102.208C179.657 103.385 178.814 104.293 177.829 104.293C176.844 104.293 176.001 103.385 176.001 102.208C176.001 101.03 176.844 100.123 177.829 100.123C178.814 100.123 179.657 101.03 179.657 102.208Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Ellipse 21"
                  d="M165.252 110.438C165.252 111.616 164.409 112.523 163.424 112.523C162.44 112.523 161.597 111.616 161.597 110.438C161.597 109.26 162.44 108.353 163.424 108.353C164.409 108.353 165.252 109.26 165.252 110.438Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Ellipse 22"
                  d="M165.252 110.438C165.252 111.616 164.409 112.523 163.424 112.523C162.44 112.523 161.597 111.616 161.597 110.438C161.597 109.26 162.44 108.353 163.424 108.353C164.409 108.353 165.252 109.26 165.252 110.438Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
              </g>
              <g id="Group 203">
                <path
                  id="Ellipse 15_2"
                  d="M181.715 41.5031C181.715 42.391 181.493 43.1838 181.146 43.7471C180.797 44.3142 180.346 44.6171 179.887 44.6171C179.428 44.6171 178.977 44.3142 178.628 43.7471C178.281 43.1838 178.059 42.391 178.059 41.5031C178.059 40.6153 178.281 39.8225 178.628 39.2592C178.977 38.6921 179.428 38.3892 179.887 38.3892C180.346 38.3892 180.797 38.6921 181.146 39.2592C181.493 39.8225 181.715 40.6153 181.715 41.5031Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Ellipse 14_2"
                  d="M181.029 41.5025C181.029 42.1078 180.883 42.6446 180.659 43.0223C180.432 43.405 180.152 43.5876 179.887 43.5876C179.622 43.5876 179.342 43.405 179.115 43.0223C178.891 42.6446 178.745 42.1078 178.745 41.5025C178.745 40.8973 178.891 40.3605 179.115 39.9828C179.342 39.6001 179.622 39.4175 179.887 39.4175C180.152 39.4175 180.432 39.6001 180.659 39.9828C180.883 40.3605 181.029 40.8973 181.029 41.5025Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
              </g>
              <g id="Group 204">
                <path
                  id="Vector 367"
                  d="M175.514 55.6496L170.627 57.9647C170.325 57.135 169.705 55.4144 169.423 54.6021L172.942 53.3346C174.228 52.6486 176.852 51.2253 177.057 51.0195C177.263 50.8138 180.591 50.4618 183.403 49.7717C186.214 49.0817 188.628 48.503 191.735 47.4184C195.236 46.1958 195.875 45.8615 198.865 43.5613C199.791 42.8493 200.778 41.9512 201.395 41.9512C202.013 41.9512 202.659 42.4148 202.316 43.1013C200.935 45.8615 198.449 47.0753 195.415 48.8517C193.108 50.203 191.791 50.7305 189.205 51.6119C186.256 52.6168 181.945 53.0774 181.173 53.3346C180.556 53.5404 177.143 54.9637 175.514 55.6496Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Vector 366"
                  d="M171.656 44.5892C171.656 43.9718 173.31 42.5246 174.254 42.1816C174.254 42.1816 174.74 43.754 175.174 44.7118C176.268 47.1297 177.284 48.4535 179.314 50.0022C180.861 51.1823 183.745 52.0488 183.745 52.0488L189.662 54.8783C189.662 54.8783 195.645 57.3627 196.565 57.5928C197.485 57.8228 199.555 57.8315 199.555 59.6629C199.555 61.4943 193.006 58.9939 193.006 58.9939C189.747 57.965 183.025 55.8043 182.202 55.3927C181.379 54.9812 178.601 52.9919 177.315 52.0488C175.428 49.8195 171.656 45.2065 171.656 44.5892Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Vector 365"
                  d="M168.826 68.768C168.826 68.5622 169.512 67.6533 169.855 67.2246C171.055 66.7959 173.559 65.9385 173.971 65.9385C174.382 65.9385 179.115 63.7092 181.43 62.5945L186.832 60.7939H190.355L190.125 62.1925L189.404 62.5945C187.261 63.5377 182.665 65.5269 181.43 65.9385C180.196 66.3501 176.629 67.4819 175 67.9963L169.598 70.5686C169.341 70.0541 168.826 68.9738 168.826 68.768Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Vector 364"
                  d="M173.713 61.3086C173.456 61.0514 174.485 59.2508 175.257 58.7363L177.829 60.2797C180.144 62.3375 184.929 66.6589 185.546 67.4821C186.318 68.511 192.776 72.9492 193.805 73.4637C194.628 73.8753 196.023 74.7505 195.645 75.7639L195.644 75.7668C195.467 76.2404 195.42 76.367 194.725 76.4539C192.885 76.6839 188.118 73.9127 188.118 73.9127L177.829 65.4242L173.713 61.3086Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Vector 363"
                  d="M179.115 75.9707H183.745L185.294 77.6042L184.517 79.0574L180.916 78.543C179.115 78.7145 175.257 79.0574 174.228 79.0574C172.942 79.0574 166.254 80.6008 165.739 80.858C165.328 81.0638 166.254 79.7434 166.768 79.0574C168.397 78.4572 171.759 77.2054 172.17 76.9996C172.685 76.7424 178.601 75.9707 179.115 75.9707Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Vector 362"
                  d="M177.572 81.1149C177.16 80.7033 173.285 78.1996 171.398 76.9992L170.627 76.742L171.398 74.9414H173.199C174.399 75.6273 176.69 77.2362 177.057 77.5137C178.394 78.5237 179.747 79.3894 180.004 79.9038C180.261 80.4183 182.94 83.6008 183.488 84.2016C184.604 85.4242 186.047 86.8398 186.904 87.9544C187.824 88.6445 187.824 89.3345 187.824 89.3345C187.824 89.3345 187.594 90.7146 186.444 90.0246C185.294 89.3345 177.983 81.5264 177.572 81.1149Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Vector 361"
                  d="M168.054 76.742C168.312 75.9703 170.37 73.9125 170.884 73.6553H174.228L175.257 75.7131H173.456L172.685 79.057C171.656 79.3143 169.392 79.8287 168.569 79.8287C167.54 79.8287 167.797 77.5137 168.054 76.742Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Vector 360"
                  d="M156.479 90.1185C156.273 90.1185 155.022 90.29 154.421 90.3757L155.965 87.546H159.309H159.309C159.581 87.546 167.541 87.546 168.054 87.289C168.569 87.0318 175 88.0607 175 88.3179V90.3757H170.112C165.654 90.29 156.685 90.1185 156.479 90.1185Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Vector 359"
                  d="M168.054 88.8316C166.511 87.717 162.753 85.2304 162.753 85.2304L164.593 83.124L168.054 85.2304C169.512 86.1736 172.53 88.2143 172.942 88.8316C173.456 89.6033 175.799 94.0301 176.029 94.4906C176.554 95.5449 178.624 100.239 178.624 101.525C178.624 102.812 178.229 102.905 177.704 102.905C177.053 102.905 176.029 101.295 176.029 101.295L168.054 88.8316Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Vector 358"
                  d="M152.364 102.721C152.106 102.721 145.418 104.008 145.161 104.265L148.248 100.921C149.363 100.664 151.901 100.046 153.135 99.6348C154.37 99.2232 157.765 98.6059 159.309 98.3486L163.682 99.892L162.395 101.693L158.794 101.95C156.736 102.207 152.569 102.721 152.364 102.721Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
                <path
                  id="Vector 357"
                  d="M154.164 107.867C156.542 110.496 158.077 112.331 161.367 112.239C162.391 112.211 163.321 112.137 163.939 111.725C164.711 111.211 164.363 109.806 163.939 109.41C163.187 108.707 162.029 109.671 160.852 109.41C159.536 109.118 158.906 108.584 157.765 107.867C155.672 106.551 155.115 105.186 153.092 103.596C151.399 102.265 150.314 101.027 148.262 100.375C146.326 99.7605 145.271 99.9153 143.063 100.496C140.854 101.077 141.046 104.266 141.046 104.266C141.046 104.266 141.361 103.596 142.971 102.675C144.581 101.755 145.501 101.526 146.882 101.986C150.294 103.123 151.712 105.155 154.164 107.867Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.460034"
                />
              </g>
            </g>
            {findMeasure('platform_height') && (
              <g id="platform_height">
                <path
                  id="Vector 21"
                  d="M73.2762 146.609L75.2282 128.668"
                  stroke="#E55959"
                  stroke-width="2.30017"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M78.7581 131.281L75.592 126.742L71.4874 130.034"
                  stroke="#E55959"
                  stroke-width="1.84013"
                />
                <path
                  id="Vector 27"
                  d="M69.8725 144.089L73.1992 148.511L77.1832 145.074"
                  stroke="#E55959"
                  stroke-width="1.84013"
                />
              </g>
            )}
            {findMeasure('heel_height') && (
              <g id="heel_height">
                <path
                  id="Vector 21_2"
                  d="M257.312 141.921L254.695 124.064"
                  stroke="#E55959"
                  stroke-width="2.30017"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M258.768 125.708L254.564 122.111L251.418 126.328"
                  stroke="#E55959"
                  stroke-width="1.84013"
                />
                <path
                  id="Vector 27_2"
                  d="M253.385 140.337L257.715 143.781L260.709 139.454"
                  stroke="#E55959"
                  stroke-width="1.84013"
                />
              </g>
            )}
            {findMeasure('shaft_height') && (
              <g id="shaft_height">
                <path
                  id="Vector 21_3"
                  d="M249.023 118.777L249.023 40.3408"
                  stroke="#E55959"
                  stroke-width="2.30017"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M252.556 43.1599L248.952 38.9609L245.197 42.6463"
                  stroke="#E55959"
                  stroke-width="1.84013"
                />
                <path
                  id="Vector 27_3"
                  d="M245.333 116.619L249.389 120.382L252.703 116.295"
                  stroke="#E55959"
                  stroke-width="1.84013"
                />
              </g>
            )}
            {findMeasure('calf_width') && (
              <g id="calf_width">
                <path
                  id="Vector 21_4"
                  d="M236.588 52.3018L174.484 52.3018"
                  stroke="#E55959"
                  stroke-width="2.30017"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M177.302 48.7686L173.103 52.3725L176.789 56.1274"
                  stroke="#E55959"
                  stroke-width="1.84013"
                />
                <path
                  id="Vector 27_4"
                  d="M234.272 55.9918L238.035 51.9352L233.948 48.6221"
                  stroke="#E55959"
                  stroke-width="1.84013"
                />
              </g>
            )}
            {findMeasure('calf_circumference') && (
              <g id="calf_circumference">
                <g id="Group 256">
                  <g id="Group 258">
                    <g id="Group 220">
                      <path
                        id="Ellipse 23"
                        d="M199.372 54.8794C180.526 54.5612 173.769 50.9346 173.769 50.9346M238.753 51.431C238.753 51.431 232.342 55.0255 213.243 54.9883"
                        stroke="#E55959"
                        stroke-width="2.30017"
                      />
                      <path
                        id="Vector 27_5"
                        d="M216.209 51.8753L211.921 54.8788L215.596 57.7751"
                        stroke="#E55959"
                        stroke-width="1.84013"
                      />
                      <path
                        id="Vector 28"
                        d="M195.819 51.8805L200.098 54.8982L196.413 57.7823"
                        stroke="#E55959"
                        stroke-width="1.84013"
                      />
                    </g>
                  </g>
                </g>
              </g>
            )}
          </g>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_mid_shaft_boot">
            <g id="mid_shaft_boot">
              <path
                id="Vector 355"
                d="M142.374 150.735C138.664 154.804 129.737 161.649 129.45 162.223V172.634C139.741 169.882 160.755 164.234 162.478 163.659C164.202 163.085 194.55 146.187 209.509 137.811L233.203 83.2414L196.225 59.5469L182.942 62.0599C180.788 78.335 179.466 112.972 172.89 122.014C167.146 129.912 150.153 143.914 142.374 150.735Z"
                fill="white"
                stroke="black"
                stroke-width="0.642061"
              />
              <path
                id="Vector 356"
                d="M208.072 49.1359C211.232 49.1359 226.143 56.0767 233.203 59.5471L241.101 60.9833L243.973 90.0629C239.665 102.868 230.834 128.908 229.972 130.631C229.11 132.354 209.269 147.624 199.456 155.043L161.042 161.146L143.451 157.915L152.785 149.658C157.093 143.316 165.829 135.298 168.941 130.631C170.377 128.477 179.631 117.017 185.455 108.013C197.257 89.7682 199.097 60.6243 199.456 54.1621C199.815 47.7 204.123 49.1359 208.072 49.1359Z"
                fill="white"
                stroke="black"
                stroke-width="0.642061"
              />
              <path
                id="Vector 348"
                d="M48.673 210.69H49.391L165.71 181.969L234.639 180.174L285.977 191.662C286.576 198.483 287.772 212.341 287.772 213.203C287.772 214.064 283.943 214.998 282.028 215.357L279.156 213.203V216.434C279.156 217.295 273.651 218.468 270.899 218.947L269.104 215.357C268.625 216.673 267.596 219.449 267.309 220.024C267.022 220.598 261.445 222.656 258.693 223.614L257.975 218.947L255.462 224.332L246.127 227.563L234.639 221.819C225.544 223.614 207.354 227.276 207.354 227.563V234.384L198.379 235.461L196.225 234.384H193.353V238.333L181.506 239.769L178.993 238.333H177.557V240.846C173.967 241.564 166.571 243 165.71 243C164.848 243 163.197 239.889 162.478 238.333H159.606L157.452 243H152.067L143.81 239.769L141.297 238.333V240.846C139.622 241.804 129.737 242.067 103.601 235.461C77.4654 228.855 56.5711 221.699 49.391 218.947L48.673 210.69Z"
                fill="white"
                stroke="black"
                stroke-width="0.642061"
              />
              <path
                id="Vector 347"
                d="M49.75 207.819C50.4679 206.383 51.4254 206.622 52.263 206.383L79.1886 190.586C106.832 190.108 163.484 189.15 168.941 189.15C174.397 189.15 243.495 190.108 277.361 190.586C279.994 190.108 285.403 189.15 285.977 189.15C286.695 189.15 288.49 190.586 286.695 193.817C284.943 196.971 257.257 211.409 215.253 223.256C173.249 235.103 159.107 234.811 128.931 231.668C110.299 229.727 102.211 227.325 82.0607 221.461C69.2897 217.745 49.7499 210.691 49.7499 210.691C49.7499 210.691 49.1719 208.975 49.75 207.819Z"
                fill="white"
                stroke="black"
                stroke-width="0.642061"
              />
              <path
                id="Vector 346"
                d="M51.904 206.741C50.2781 204.7 50.468 201.714 53.3401 201.355L125.142 192.38L220.279 175.507L281.669 186.636C281.669 186.636 284.421 185.891 285.259 186.636C286.202 187.474 286.229 188.7 285.259 189.508C283.105 191.303 279.535 193.548 275.566 195.611C257.616 204.945 249.273 209.003 226.741 215.716C192.041 226.053 178.634 230.794 134.835 227.922C113.2 226.503 75.2395 217.152 51.904 206.741Z"
                fill="white"
                stroke="black"
                stroke-width="0.642061"
              />
              <path
                id="Vector 345"
                d="M54.0581 198.483C54.3453 198.196 55.3745 197.646 55.8531 197.406H73.0855C103.123 197.765 163.915 198.483 166.787 198.483C169.659 198.483 243.375 187.474 279.874 181.969C279.874 181.969 280.592 181.969 282.387 181.969C282.387 181.969 283.69 182.403 283.823 183.405C284.024 184.908 283.547 185.476 282.387 186.636C280.592 188.431 276.729 190.475 273.053 192.38C251.948 203.316 238.884 208.314 215.971 214.639C190.053 221.792 171.858 223.434 144.982 222.678C119.172 221.951 98.7538 216.963 82.7788 211.767C72.8462 208.536 52.9093 201.858 52.6221 200.996C52.2631 199.919 53.6991 198.842 54.0581 198.483Z"
                fill="white"
                stroke="black"
                stroke-width="0.642061"
              />
              <path
                id="Vector 344"
                d="M273.771 130.99C275.261 151.819 279.874 178.379 280.592 183.046C258.693 195.252 210.586 211.049 193.353 214.998C175.019 219.199 130.168 218.229 106.114 213.203C88.7725 209.579 69.8544 204.587 55.8531 197.766C56.4515 196.09 58.7252 191.734 63.0333 187.713C68.4184 182.687 89.5999 180.892 91.0359 180.892C92.4719 180.892 100.729 178.379 113.294 174.071C123.347 170.625 146.682 153.727 157.093 145.709L170.377 147.863C176.719 142 191.199 127.328 198.379 115.553C207.354 100.833 208.072 67.0868 211.663 61.3426C215.253 55.5985 215.612 59.5475 222.792 59.1885C228.536 58.9013 259.65 63.1376 274.489 65.2916C273.651 83.1223 271.959 105.659 273.771 130.99Z"
                fill="white"
                stroke="black"
                stroke-width="0.642061"
              />
              <path
                id="Vector 343"
                d="M270.899 58.4697V64.5729L257.616 62.8036V58.4697H270.899Z"
                fill="white"
                stroke="black"
                stroke-width="0.642061"
              />
              <path
                id="Vector 353"
                d="M223.151 122.014C228.635 110.223 231.647 76.899 231.767 61.342C231.767 61.342 217.407 58.4701 212.022 60.2649C207.44 61.7921 207.697 76.336 205.918 86.8315C202.328 108.013 201.969 114.475 190.122 131.349C186.147 137.009 165.47 154.325 156.016 162.223C155.657 164.856 154.293 170.05 156.016 175.506C157.74 180.963 163.675 189.508 166.428 193.098L190.84 182.328L190.122 171.198C198.379 159.471 215.971 137.452 223.151 122.014Z"
                fill="white"
                stroke="black"
                stroke-width="0.642061"
              />
              <path
                id="Vector 352"
                d="M192.635 172.275C194.789 171.916 247.204 151.453 252.948 148.94C257.544 146.929 261.924 145.35 261.924 145.35"
                stroke="black"
                stroke-width="0.642061"
                stroke-dasharray="1.28 1.28"
              />
              <path
                id="Vector 351"
                d="M261.924 146.427C259.626 147.863 213.458 166.053 190.481 175.147"
                stroke="black"
                stroke-width="0.642061"
              />
              <path
                id="Vector 349"
                d="M274.13 137.812C273.268 138.673 265.035 143.556 261.924 145.351V191.859L271.976 187.355L280.951 183.047"
                stroke="black"
                stroke-width="0.642061"
              />
              <path
                id="Vector 350"
                d="M274.489 139.965C273.771 140.324 263.719 146.427 263.719 146.427C263.719 146.427 263.599 176.225 263.719 190.944"
                stroke="black"
                stroke-width="0.642061"
                stroke-dasharray="1.28 1.28"
              />
              <path
                id="Vector 354"
                d="M221.715 120.578C228.135 103.815 229.613 74.2661 230.331 62.7773C225.425 62.7773 219.202 59.9056 212.381 62.7773C209.868 63.8353 209.509 71.3938 208.432 78.5739C207.355 85.7541 206.637 112.68 194.789 129.194C183.448 145.003 167.026 156.598 157.811 162.582C157.572 164.257 157.237 168.9 157.811 174.07C158.529 180.532 165.71 187.353 166.428 188.43C167.146 189.507 187.609 181.609 187.609 180.891V170.121C195.268 160.069 215.253 137.451 221.715 120.578Z"
                fill="white"
                stroke="black"
                stroke-width="0.642061"
                stroke-dasharray="1.28 1.28"
              />
              <g id="Group 202">
                <path
                  id="Ellipse 15"
                  d="M224.625 70.6749C224.625 73.0999 222.816 75.021 220.638 75.021C218.46 75.021 216.651 73.0999 216.651 70.6749C216.651 68.25 218.46 66.3288 220.638 66.3288C222.816 66.3288 224.625 68.25 224.625 70.6749Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Ellipse 14"
                  d="M223.189 70.6754C223.189 72.3192 222.012 73.5854 220.638 73.5854C219.263 73.5854 218.087 72.3192 218.087 70.6754C218.087 69.0316 219.263 67.7654 220.638 67.7654C222.012 67.7654 223.189 69.0316 223.189 70.6754Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Ellipse 16"
                  d="M218.881 94.3697C218.881 96.0135 217.704 97.2798 216.33 97.2798C214.955 97.2798 213.779 96.0135 213.779 94.3697C213.779 92.726 214.955 91.4597 216.33 91.4597C217.704 91.4597 218.881 92.726 218.881 94.3697Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Ellipse 17"
                  d="M213.137 117.346C213.137 118.99 211.96 120.256 210.586 120.256C209.211 120.256 208.035 118.99 208.035 117.346C208.035 115.703 209.211 114.436 210.586 114.436C211.96 114.436 213.137 115.703 213.137 117.346Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Ellipse 18"
                  d="M213.137 117.346C213.137 118.99 211.96 120.256 210.586 120.256C209.211 120.256 208.035 118.99 208.035 117.346C208.035 115.703 209.211 114.436 210.586 114.436C211.96 114.436 213.137 115.703 213.137 117.346Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Ellipse 19"
                  d="M202.366 136.732C202.366 138.376 201.19 139.642 199.815 139.642C198.441 139.642 197.264 138.376 197.264 136.732C197.264 135.088 198.441 133.822 199.815 133.822C201.19 133.822 202.366 135.088 202.366 136.732Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Ellipse 20"
                  d="M189.442 153.965C189.442 155.609 188.265 156.875 186.891 156.875C185.517 156.875 184.34 155.609 184.34 153.965C184.34 152.322 185.517 151.055 186.891 151.055C188.265 151.055 189.442 152.322 189.442 153.965Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Ellipse 21"
                  d="M169.338 165.453C169.338 167.097 168.161 168.363 166.787 168.363C165.412 168.363 164.236 167.097 164.236 165.453C164.236 163.809 165.412 162.543 166.787 162.543C168.161 162.543 169.338 163.809 169.338 165.453Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Ellipse 22"
                  d="M169.338 165.453C169.338 167.097 168.161 168.363 166.787 168.363C165.412 168.363 164.236 167.097 164.236 165.453C164.236 163.809 165.412 162.543 166.787 162.543C168.161 162.543 169.338 163.809 169.338 165.453Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
              </g>
              <g id="Group 203">
                <path
                  id="Ellipse 15_2"
                  d="M192.314 69.2423C192.314 70.4815 192.004 71.5879 191.521 72.3742C191.034 73.1656 190.403 73.5884 189.763 73.5884C189.123 73.5884 188.493 73.1656 188.006 72.3742C187.522 71.5879 187.212 70.4815 187.212 69.2423C187.212 68.0031 187.522 66.8967 188.006 66.1104C188.493 65.319 189.123 64.8962 189.763 64.8962C190.403 64.8962 191.034 65.319 191.521 66.1104C192.004 66.8967 192.314 68.0031 192.314 69.2423Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Ellipse 14_2"
                  d="M191.357 69.2418C191.357 70.0866 191.153 70.8357 190.841 71.3629C190.524 71.897 190.133 72.1519 189.763 72.1519C189.393 72.1519 189.002 71.897 188.685 71.3629C188.373 70.8357 188.169 70.0866 188.169 69.2418C188.169 68.3971 188.373 67.6479 188.685 67.1208C189.002 66.5866 189.393 66.3318 189.763 66.3318C190.133 66.3318 190.524 66.5866 190.841 67.1208C191.153 67.6479 191.357 68.3971 191.357 69.2418Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
              </g>
              <g id="Group 204">
                <path
                  id="Vector 367"
                  d="M183.66 88.9859L176.839 92.217C176.418 91.0591 175.552 88.6576 175.159 87.5239L180.07 85.7548C181.865 84.7975 185.527 82.811 185.814 82.5238C186.101 82.2365 190.746 81.7453 194.67 80.7822C198.594 79.8191 201.963 79.0115 206.299 77.4976C211.186 75.7913 212.078 75.3247 216.251 72.1144C217.543 71.1207 218.921 69.8672 219.782 69.8672C220.644 69.8672 221.545 70.5143 221.066 71.4723C219.14 75.3247 215.669 77.0188 211.436 79.4981C208.215 81.3841 206.378 82.1203 202.768 83.3505C198.652 84.7531 192.635 85.3958 191.558 85.7548C190.697 86.042 185.934 88.0285 183.66 88.9859Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Vector 366"
                  d="M178.275 73.5486C178.275 72.687 180.584 70.6672 181.901 70.1885C181.901 70.1885 182.58 72.383 183.185 73.7198C184.712 77.0944 186.13 78.942 188.963 81.1035C191.122 82.7505 195.148 83.9599 195.148 83.9599L203.405 87.9089C203.405 87.9089 211.756 91.3765 213.041 91.6975C214.325 92.0186 217.214 92.0307 217.214 94.5868C217.214 97.1429 208.072 93.6531 208.072 93.6531C203.525 92.217 194.143 89.2014 192.994 88.627C191.845 88.0525 187.968 85.2762 186.173 83.9599C183.54 80.8485 178.275 74.4102 178.275 73.5486Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Vector 365"
                  d="M174.326 107.295C174.326 107.008 175.283 105.74 175.762 105.141C177.437 104.543 180.931 103.346 181.506 103.346C182.08 103.346 188.686 100.235 191.917 98.6791L199.456 96.166H204.373L204.052 98.1179L203.046 98.6791C200.055 99.9954 193.64 102.772 191.917 103.346C190.194 103.921 185.216 105.5 182.942 106.218L175.403 109.808C175.044 109.09 174.326 107.582 174.326 107.295Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Vector 364"
                  d="M181.147 96.884C180.788 96.525 182.224 94.012 183.301 93.2939L186.891 95.448C190.122 98.3201 196.8 104.351 197.661 105.5C198.738 106.936 207.752 113.131 209.188 113.849C210.337 114.423 212.284 115.645 211.757 117.059L211.755 117.063C211.508 117.724 211.442 117.901 210.472 118.022C207.904 118.343 201.251 114.475 201.251 114.475L186.891 102.628L181.147 96.884Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Vector 363"
                  d="M188.686 117.348H195.148L197.31 119.627L196.225 121.656L191.199 120.938C188.686 121.177 183.301 121.656 181.865 121.656C180.07 121.656 170.736 123.81 170.018 124.169C169.443 124.456 170.736 122.613 171.454 121.656C173.727 120.818 178.418 119.071 178.993 118.784C179.711 118.425 187.968 117.348 188.686 117.348Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Vector 362"
                  d="M186.532 124.527C185.958 123.953 180.548 120.459 177.916 118.783L176.839 118.424L177.916 115.911H180.429C182.104 116.868 185.302 119.114 185.814 119.501C187.679 120.911 189.567 122.119 189.926 122.837C190.285 123.555 194.024 127.997 194.789 128.835C196.347 130.542 198.361 132.517 199.557 134.073C200.841 135.036 200.841 135.999 200.841 135.999C200.841 135.999 200.52 137.926 198.915 136.962C197.31 135.999 187.106 125.102 186.532 124.527Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Vector 361"
                  d="M173.249 118.424C173.608 117.347 176.48 114.475 177.198 114.116H181.865L183.301 116.988H180.788L179.711 121.655C178.275 122.014 175.115 122.732 173.967 122.732C172.531 122.732 172.89 119.501 173.249 118.424Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Vector 360"
                  d="M157.093 137.093C156.806 137.093 155.059 137.332 154.221 137.452L156.375 133.503H161.042H161.043C161.422 133.503 172.531 133.503 173.249 133.144C173.967 132.785 182.942 134.221 182.942 134.58V137.452H176.121C169.898 137.332 157.381 137.093 157.093 137.093Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Vector 359"
                  d="M173.249 135.297C171.095 133.741 165.849 130.271 165.849 130.271L168.417 127.331L173.249 130.271C175.283 131.587 179.495 134.435 180.07 135.297C180.788 136.374 184.058 142.553 184.378 143.195C185.111 144.667 188 151.218 188 153.014C188 154.809 187.449 154.94 186.716 154.94C185.808 154.94 184.378 152.692 184.378 152.692L173.249 135.297Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Vector 358"
                  d="M151.349 154.683C150.99 154.683 141.656 156.478 141.297 156.837L145.605 152.17C147.161 151.811 150.703 150.95 152.426 150.375C154.149 149.801 158.888 148.939 161.042 148.58L167.146 150.734L165.351 153.247L160.324 153.606C157.452 153.965 151.636 154.683 151.349 154.683Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
                <path
                  id="Vector 357"
                  d="M153.862 161.865C157.181 165.535 159.324 168.095 163.915 167.968C165.344 167.928 166.643 167.824 167.505 167.25C168.582 166.532 168.096 164.571 167.505 164.019C166.455 163.038 164.839 164.384 163.196 164.019C161.36 163.611 160.481 162.865 158.888 161.865C155.967 160.028 155.189 158.123 152.366 155.904C150.003 154.047 148.488 152.318 145.624 151.409C142.923 150.551 141.451 150.767 138.368 151.578C135.285 152.389 135.553 156.84 135.553 156.84C135.553 156.84 135.993 155.904 138.241 154.619C140.488 153.335 141.772 153.014 143.698 153.656C148.46 155.244 150.44 158.08 153.862 161.865Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.642061"
                />
              </g>
            </g>
            {findMeasure('platform_height') && (
              <g id="platform_height">
                <path
                  id="Vector 21"
                  d="M40.9682 215.937L43.6927 190.896"
                  stroke="#E55959"
                  stroke-width="3.21031"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M48.6193 194.542L44.2003 188.208L38.4717 192.802"
                  stroke="#E55959"
                  stroke-width="2.56825"
                />
                <path
                  id="Vector 27"
                  d="M36.2178 212.419L40.8608 218.59L46.4212 213.794"
                  stroke="#E55959"
                  stroke-width="2.56825"
                />
              </g>
            )}
            {findMeasure('heel_height') && (
              <g id="heel_height">
                <path
                  id="Vector 21_2"
                  d="M297.824 209.393L294.172 184.471"
                  stroke="#E55959"
                  stroke-width="3.21031"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M299.857 186.765L293.988 181.744L289.597 187.63"
                  stroke="#E55959"
                  stroke-width="2.56824"
                />
                <path
                  id="Vector 27_2"
                  d="M292.343 207.181L298.387 211.988L302.564 205.949"
                  stroke="#E55959"
                  stroke-width="2.56824"
                />
              </g>
            )}
            {findMeasure('shaft_height') && (
              <g id="shaft_height">
                <path
                  id="Vector 21_3"
                  d="M286.255 177.092L286.255 67.6201"
                  stroke="#E55959"
                  stroke-width="3.21031"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M291.186 71.5548L286.156 65.6943L280.915 70.8379"
                  stroke="#E55959"
                  stroke-width="2.56824"
                />
                <path
                  id="Vector 27_3"
                  d="M281.105 174.08L286.766 179.332L291.391 173.628"
                  stroke="#E55959"
                  stroke-width="2.56825"
                />
              </g>
            )}
            {findMeasure('calf_width') && (
              <g id="calf_width">
                <path
                  id="Vector 21_4"
                  d="M268.9 84.3135L182.222 84.3135"
                  stroke="#E55959"
                  stroke-width="3.21031"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M186.156 79.3818L180.296 84.4118L185.439 89.6525"
                  stroke="#E55959"
                  stroke-width="2.56824"
                />
                <path
                  id="Vector 27_4"
                  d="M265.667 89.4635L270.92 83.8018L265.215 79.1777"
                  stroke="#E55959"
                  stroke-width="2.56824"
                />
              </g>
            )}
            {findMeasure('calf_circumference') && (
              <g id="calf_circumference">
                <g id="Group 256">
                  <g id="Group 258">
                    <g id="Group 220">
                      <path
                        id="Ellipse 23"
                        d="M216.959 87.9119C190.656 87.4679 181.225 82.4062 181.225 82.4062M271.922 83.0992C271.922 83.0992 262.974 88.1159 236.317 88.064"
                        stroke="#E55959"
                        stroke-width="3.21031"
                      />
                      <path
                        id="Vector 27_5"
                        d="M240.458 83.7193L234.473 87.9112L239.602 91.9535"
                        stroke="#E55959"
                        stroke-width="2.56825"
                      />
                      <path
                        id="Vector 28"
                        d="M212 83.7265L217.971 87.9383L212.828 91.9635"
                        stroke="#E55959"
                        stroke-width="2.56824"
                      />
                    </g>
                  </g>
                </g>
              </g>
            )}
          </g>
        </svg>
      )}
    </div>
  );
}
