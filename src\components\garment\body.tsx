import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface BodyProps {
  measure: GarmentMeasure;
  className?: string;
}

export function Body({ measure, className }: BodyProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width={isMobile ? '331' : '342'}
          height={isMobile ? '193' : '291'}
          viewBox={isMobile ? '0 0 331 193' : '0 0 342 291'}
          fill="none"
        >
          <g id="group_body">
            <g id="body">
              <path
                id="Vector 323"
                d="M164.5 171.66C153.432 162.236 128.667 139.937 128.667 139.937L129.16 138.786C147.077 135.827 183.83 130.37 184.225 130.239C184.619 130.107 198.58 135.553 204.114 139.936L168.281 171.66L164.5 171.66Z"
                fill="white"
                stroke="black"
                stroke-width="0.328744"
              />
              <g id="Vector">
                <path
                  d="M137.05 102.953C137.05 90.3288 136.721 75.1189 136.721 69.0919L165.322 60.4153L165.979 60.2158L195.238 69.0919C195.238 75.1189 195.731 90.3288 195.731 102.953C195.731 115.576 201.319 132.868 204.114 139.936C202.251 140.429 196.914 142.862 190.471 148.648C184.027 154.434 172.993 166.291 168.281 171.496H164.5C159.788 166.291 148.753 154.434 142.31 148.648C135.866 142.862 130.53 140.429 128.667 139.936C131.461 132.868 137.05 115.576 137.05 102.953Z"
                  fill="white"
                />
                <path
                  d="M165.979 60.2158L136.721 69.0919C136.721 75.1189 137.05 90.3288 137.05 102.953C137.05 115.576 131.461 132.868 128.667 139.936C130.53 140.429 135.866 142.862 142.31 148.648C148.753 154.434 159.788 166.291 164.5 171.496H168.281C172.993 166.291 184.027 154.434 190.471 148.648C196.914 142.862 202.251 140.429 204.114 139.936C201.319 132.868 195.731 115.576 195.731 102.953C195.731 90.3288 195.238 75.1189 195.238 69.0919L165.979 60.2158ZM165.979 60.2158L176.664 63.5033M165.979 60.2158L165.322 60.4153"
                  stroke="black"
                  stroke-width="0.328744"
                />
              </g>
              <g id="Group 190">
                <path
                  id="Vector 320"
                  d="M150.693 39.9987L143.789 16.3291L142.967 22.4109L143.789 23.3971L147.898 37.2043L150.693 39.9987Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.328744"
                />
                <path
                  id="Vector 319"
                  d="M143.789 16L141.981 16.8219L143.789 37.0396L146.255 36.0534L143.789 16Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.328744"
                />
              </g>
              <g id="Group 191">
                <path
                  id="Vector 320_2"
                  d="M181.759 39.9987L188.663 16.3291L189.485 22.4109L188.663 23.3971L184.553 37.2043L181.759 39.9987Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.328744"
                />
                <path
                  id="Vector 319_2"
                  d="M188.663 16L190.471 16.8219L188.663 37.0396L186.197 36.0534L188.663 16Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.328744"
                />
              </g>
              <path
                id="Vector 321"
                d="M145.104 33.917L165.815 54.7922V60.2165L136.721 69.0926C135.844 65.0929 133.508 56.8284 134.42 50.8473C135.899 41.1494 141.269 36.7113 145.104 33.917Z"
                fill="white"
                stroke="black"
                stroke-width="0.328744"
              />
              <path
                id="Vector 322"
                d="M186.855 33.917L165.979 54.7922V60.2165L195.238 69.0926C196.114 65.0929 198.451 56.8284 197.539 50.8473C196.059 41.1494 190.69 36.7113 186.855 33.917Z"
                fill="white"
                stroke="black"
                stroke-width="0.328744"
              />
              <path
                id="Vector 324"
                d="M138.694 145.525C138.694 144.045 143.562 118.164 141.817 100.487C141.074 92.9674 137.817 84.1045 136.886 79.4473"
                stroke="black"
                stroke-width="0.328744"
              />
              <path
                id="Vector 325"
                d="M193.758 145.854C193.758 144.374 188.61 118.164 190.355 100.487C191.098 92.9677 194.47 83.9404 195.402 79.2832"
                stroke="black"
                stroke-width="0.328744"
              />
              <g id="Group 86">
                <g id="Group 84">
                  <path
                    id="Vector 105"
                    d="M174.564 53.9004C172.825 54.2263 166.741 54.9868 166.741 54.9868C169.964 54.0452 176.519 52.1621 176.954 52.1621C177.388 52.1621 177.931 52.6691 178.149 52.9226C177.533 53.1399 175.954 53.6396 174.564 53.9004Z"
                    fill="url(#paint0_linear_208_4979)"
                  />
                  <path
                    id="Vector 153"
                    d="M174.778 56.6366C173.017 56.472 166.959 55.5302 166.959 55.5302C170.316 55.5112 177.136 55.5031 177.554 55.6226C177.972 55.7421 178.355 56.3789 178.494 56.6824C177.842 56.722 176.187 56.7683 174.778 56.6366Z"
                    fill="url(#paint1_linear_208_4979)"
                  />
                  <path
                    id="Vector 154"
                    d="M174.269 58.6214C172.56 58.1669 166.741 56.2328 166.741 56.2328C170.056 56.7713 176.782 57.8949 177.174 58.082C177.567 58.2692 177.839 58.9607 177.925 59.2831C177.276 59.214 175.636 58.985 174.269 58.6214Z"
                    fill="url(#paint2_linear_208_4979)"
                  />
                  <path
                    id="Vector 155"
                    d="M174.032 60.8331C172.412 60.1223 166.959 57.3202 166.959 57.3202C170.151 58.3597 176.627 60.5 176.986 60.745C177.345 60.99 177.507 61.715 177.544 62.0469C176.913 61.8793 175.327 61.4018 174.032 60.8331Z"
                    fill="url(#paint3_linear_208_4979)"
                  />
                </g>
                <g id="Group 85">
                  <path
                    id="Vector 105_2"
                    d="M157.627 53.9004C159.365 54.2263 165.449 54.9868 165.449 54.9868C162.226 54.0452 155.671 52.1621 155.236 52.1621C154.802 52.1621 154.259 52.6691 154.041 52.9226C154.657 53.1399 156.236 53.6396 157.627 53.9004Z"
                    fill="url(#paint4_linear_208_4979)"
                  />
                  <path
                    id="Vector 153_2"
                    d="M157.412 56.6366C159.173 56.472 165.231 55.5302 165.231 55.5302C161.874 55.5112 155.054 55.5031 154.636 55.6226C154.218 55.7421 153.835 56.3789 153.696 56.6824C154.348 56.722 156.003 56.7683 157.412 56.6366Z"
                    fill="url(#paint5_linear_208_4979)"
                  />
                  <path
                    id="Vector 154_2"
                    d="M157.921 58.6214C159.63 58.1669 165.449 56.2328 165.449 56.2328C162.134 56.7713 155.408 57.8949 155.016 58.082C154.623 58.2692 154.351 58.9607 154.265 59.2831C154.914 59.214 156.554 58.985 157.921 58.6214Z"
                    fill="url(#paint6_linear_208_4979)"
                  />
                  <path
                    id="Vector 155_2"
                    d="M158.159 60.8331C159.778 60.1223 165.231 57.3202 165.231 57.3202C162.039 58.3597 155.563 60.5 155.204 60.745C154.846 60.99 154.683 61.715 154.646 62.0469C155.277 61.8793 156.863 61.4018 158.159 60.8331Z"
                    fill="url(#paint7_linear_208_4979)"
                  />
                </g>
              </g>
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 21"
                  d="M194.909 52.7139L136.547 52.7139"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M138.407 50.1895L135.406 52.7649L138.04 55.4482"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 27"
                  d="M193.253 55.4583L195.978 52.5928L193.086 50.1895"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 21_2"
                  d="M193.758 109.258L139.022 109.258"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M140.708 106.733L137.707 109.309L140.341 111.992"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 27_2"
                  d="M192.267 112.002L194.991 109.137L192.1 106.733"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
            {findMeasure('product_thigh_circumference') && (
              <g id="product_thigh_circumference">
                <path
                  id="Vector 27_3"
                  d="M159.07 175.835L163.018 175.618L162.562 171.886"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 21_3"
                  d="M162.254 175.198L127.812 142.746"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M130.77 142.066L126.82 141.882L126.897 145.641"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
            {findMeasure('product_thigh_width') && (
              <g id="product_thigh_width">
                <path
                  id="Vector 27_4"
                  d="M159.07 175.835L163.018 175.618L162.562 171.886"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 21_4"
                  d="M162.254 175.198L127.812 142.746"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M130.77 142.066L126.82 141.882L126.897 145.641"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
            {findMeasure('product_waistband_width') && (
              <g id="product_waistband_width">
                <path
                  id="Vector 21_5"
                  d="M197.868 129.969L134.749 129.969"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_5"
                  d="M136.434 127.444L133.434 130.02L136.067 132.703"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 27_5"
                  d="M196.54 132.713L199.265 129.848L196.374 127.444"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 21_6"
                  d="M199.676 135.557L132.776 135.557"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_6"
                  d="M134.462 133.033L131.461 135.609L134.095 138.292"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 27_6"
                  d="M198.513 138.301L201.238 135.436L198.346 133.032"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 21_7"
                  d="M189.001 170.017L189.001 18.127"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_7"
                  d="M191.526 19.987L188.95 16.9863L186.267 19.6199"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 27_7"
                  d="M186.434 168.19L189.094 171.116L191.701 168.407"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 21_8"
                  d="M189.001 170.017L189.001 18.127"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_8"
                  d="M191.526 19.987L188.95 16.9863L186.267 19.6199"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 27_8"
                  d="M186.434 168.19L189.094 171.116L191.701 168.407"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 21_9"
                  d="M194.909 52.7139L136.547 52.7139"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_9"
                  d="M138.407 50.1895L135.406 52.7649L138.04 55.4482"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 27_9"
                  d="M193.253 55.4583L195.978 52.5928L193.086 50.1895"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 21_10"
                  d="M193.758 109.258L139.022 109.258"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_10"
                  d="M140.708 106.733L137.707 109.309L140.341 111.992"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 27_10"
                  d="M192.267 112.002L194.991 109.137L192.1 106.733"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
            {findMeasure('product_waistband_circumference') && (
              <g id="product_waistband_circumference">
                <path
                  id="Vector 21_11"
                  d="M197.868 129.969L134.749 129.969"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_11"
                  d="M136.434 127.444L133.434 130.02L136.067 132.703"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 27_11"
                  d="M196.54 132.713L199.265 129.848L196.374 127.444"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 21_12"
                  d="M199.676 135.557L132.776 135.557"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_12"
                  d="M134.462 133.033L131.461 135.609L134.095 138.292"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 27_12"
                  d="M198.513 138.301L201.238 135.436L198.346 133.032"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
            {findMeasure('product_high_waist_circumference') && (
              <g id="product_high_waist_circumference">
                <path
                  id="Vector 21_13"
                  d="M193.758 109.258L139.022 109.258"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_13"
                  d="M140.708 106.733L137.707 109.309L140.341 111.992"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 27_13"
                  d="M192.267 112.002L194.991 109.137L192.1 106.733"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
            {findMeasure('product_high_waist_width') && (
              <g id="product_high_waist_width">
                <path
                  id="Vector 21_14"
                  d="M193.758 109.258L139.022 109.258"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_14"
                  d="M140.708 106.733L137.707 109.309L140.341 111.992"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 27_14"
                  d="M192.267 112.002L194.991 109.137L192.1 106.733"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_circumference') && (
              <g id="product_lower_waist_circumference">
                <path
                  id="Vector 21_15"
                  d="M193.758 109.258L139.022 109.258"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_15"
                  d="M140.708 106.733L137.707 109.309L140.341 111.992"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 27_15"
                  d="M192.267 112.002L194.991 109.137L192.1 106.733"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_width') && (
              <g id="product_lower_waist_width">
                <path
                  id="Vector 21_16"
                  d="M193.758 109.258L139.022 109.258"
                  stroke="#E55959"
                  stroke-width="1.64372"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_16"
                  d="M140.708 106.733L137.707 109.309L140.341 111.992"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
                <path
                  id="Vector 27_16"
                  d="M192.267 112.002L194.991 109.137L192.1 106.733"
                  stroke="#E55959"
                  stroke-width="1.31498"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_208_4979"
              x1="163.265"
              y1="56.1818"
              x2="177.28"
              y2="52.0535"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_208_4979"
              x1="163.288"
              y1="55.7233"
              x2="177.897"
              y2="55.6077"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_208_4979"
              x1="163.089"
              y1="55.814"
              x2="177.515"
              y2="58.1244"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_208_4979"
              x1="163.414"
              y1="56.3472"
              x2="177.316"
              y2="60.8391"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_208_4979"
              x1="168.925"
              y1="56.1818"
              x2="154.91"
              y2="52.0535"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint5_linear_208_4979"
              x1="168.902"
              y1="55.7233"
              x2="154.293"
              y2="55.6077"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint6_linear_208_4979"
              x1="169.101"
              y1="55.814"
              x2="154.675"
              y2="58.1244"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint7_linear_208_4979"
              x1="168.776"
              y1="56.3472"
              x2="154.874"
              y2="60.8391"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_body">
            <g id="body">
              <path
                id="Vector 323"
                d="M170.975 253.881C155.057 240.327 119.437 208.254 119.437 208.254L120.147 206.599C145.916 202.344 198.777 194.495 199.344 194.306C199.912 194.117 219.991 201.95 227.95 208.254L176.413 253.881L170.975 253.881Z"
                fill="white"
                stroke="black"
                stroke-width="0.472823"
              />
              <g id="Vector">
                <path
                  d="M131.494 155.062C131.494 136.905 131.022 115.029 131.022 106.361L172.157 93.8816L173.103 93.5947L215.184 106.361C215.184 115.029 215.893 136.905 215.893 155.062C215.893 173.218 223.931 198.089 227.95 208.254C225.271 208.963 217.595 212.462 208.328 220.784C199.061 229.106 183.19 246.159 176.413 253.645H170.975C164.198 246.159 148.327 229.106 139.06 220.784C129.792 212.462 122.117 208.963 119.437 208.254C123.456 198.089 131.494 173.218 131.494 155.062Z"
                  fill="white"
                />
                <path
                  d="M173.103 93.5947L131.022 106.361C131.022 115.029 131.494 136.905 131.494 155.062C131.494 173.218 123.456 198.089 119.437 208.254C122.117 208.963 129.792 212.462 139.06 220.784C148.327 229.106 164.198 246.159 170.975 253.645H176.413C183.19 246.159 199.061 229.106 208.328 220.784C217.595 212.462 225.271 208.963 227.95 208.254C223.931 198.089 215.893 173.218 215.893 155.062C215.893 136.905 215.184 115.029 215.184 106.361L173.103 93.5947ZM173.103 93.5947L188.47 98.323M173.103 93.5947L172.157 93.8816"
                  stroke="black"
                  stroke-width="0.472823"
                />
              </g>
              <g id="Group 190">
                <path
                  id="Vector 320"
                  d="M151.116 64.5159L141.187 30.4727L140.005 39.2199L141.187 40.6383L147.097 60.4969L151.116 64.5159Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.472823"
                />
                <path
                  id="Vector 319"
                  d="M141.187 30L138.587 31.1821L141.187 60.2607L144.733 58.8422L141.187 30Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.472823"
                />
              </g>
              <g id="Group 191">
                <path
                  id="Vector 320_2"
                  d="M195.798 64.5159L205.728 30.4727L206.91 39.2199L205.728 40.6383L199.817 60.4969L195.798 64.5159Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.472823"
                />
                <path
                  id="Vector 319_2"
                  d="M205.728 30L208.328 31.1821L205.728 60.2607L202.181 58.8422L205.728 30Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.472823"
                />
              </g>
              <path
                id="Vector 321"
                d="M143.079 55.7686L172.866 85.7928V93.5944L131.022 106.361C129.761 100.608 126.4 88.7214 127.712 80.1189C129.84 66.1707 137.562 59.7875 143.079 55.7686Z"
                fill="white"
                stroke="black"
                stroke-width="0.472823"
              />
              <path
                id="Vector 322"
                d="M203.127 55.7686L173.103 85.7928V93.5944L215.184 106.361C216.445 100.608 219.806 88.7214 218.494 80.1189C216.366 66.1707 208.643 59.7875 203.127 55.7686Z"
                fill="white"
                stroke="black"
                stroke-width="0.472823"
              />
              <path
                id="Vector 324"
                d="M133.859 216.292C133.859 214.165 140.86 176.94 138.35 151.516C137.283 140.7 132.598 127.953 131.258 121.255"
                stroke="black"
                stroke-width="0.472823"
              />
              <path
                id="Vector 325"
                d="M213.056 216.765C213.056 214.637 205.652 176.94 208.162 151.516C209.23 140.701 214.081 127.717 215.42 121.019"
                stroke="black"
                stroke-width="0.472823"
              />
              <g id="Group 86">
                <g id="Group 84">
                  <path
                    id="Vector 105"
                    d="M185.449 84.5108C182.949 84.9796 174.199 86.0734 174.199 86.0734C178.834 84.7192 188.262 82.0107 188.887 82.0107C189.512 82.0107 190.293 82.7399 190.606 83.1045C189.72 83.417 187.449 84.1358 185.449 84.5108Z"
                    fill="url(#paint0_linear_208_4980)"
                  />
                  <path
                    id="Vector 153"
                    d="M185.758 88.4455C183.225 88.2088 174.511 86.8543 174.511 86.8543C179.34 86.8269 189.149 86.8152 189.75 86.9871C190.351 87.1589 190.902 88.0749 191.102 88.5113C190.165 88.5683 187.784 88.6349 185.758 88.4455Z"
                    fill="url(#paint1_linear_208_4980)"
                  />
                  <path
                    id="Vector 154"
                    d="M185.025 91.3001C182.567 90.6464 174.199 87.8648 174.199 87.8648C178.966 88.6391 188.64 90.2552 189.204 90.5244C189.768 90.7936 190.159 91.7882 190.284 92.2518C189.351 92.1525 186.992 91.8231 185.025 91.3001Z"
                    fill="url(#paint2_linear_208_4980)"
                  />
                  <path
                    id="Vector 155"
                    d="M184.684 94.4811C182.355 93.4587 174.511 89.4286 174.511 89.4286C179.103 90.9237 188.417 94.002 188.933 94.3544C189.449 94.7068 189.683 95.7495 189.736 96.2269C188.828 95.9857 186.547 95.299 184.684 94.4811Z"
                    fill="url(#paint3_linear_208_4980)"
                  />
                </g>
                <g id="Group 85">
                  <path
                    id="Vector 105_2"
                    d="M161.089 84.5108C163.589 84.9796 172.34 86.0734 172.34 86.0734C167.704 84.7192 158.277 82.0107 157.652 82.0107C157.027 82.0107 156.245 82.7399 155.933 83.1045C156.818 83.417 159.089 84.1358 161.089 84.5108Z"
                    fill="url(#paint4_linear_208_4980)"
                  />
                  <path
                    id="Vector 153_2"
                    d="M160.781 88.4455C163.313 88.2088 172.027 86.8543 172.027 86.8543C167.198 86.8269 157.389 86.8152 156.788 86.9871C156.187 87.1589 155.637 88.0749 155.436 88.5113C156.374 88.5683 158.755 88.6349 160.781 88.4455Z"
                    fill="url(#paint5_linear_208_4980)"
                  />
                  <path
                    id="Vector 154_2"
                    d="M161.513 91.3001C163.971 90.6464 172.34 87.8648 172.34 87.8648C167.573 88.6391 157.898 90.2552 157.334 90.5244C156.77 90.7936 156.379 91.7882 156.254 92.2518C157.188 92.1525 159.547 91.8231 161.513 91.3001Z"
                    fill="url(#paint6_linear_208_4980)"
                  />
                  <path
                    id="Vector 155_2"
                    d="M161.854 94.4811C164.183 93.4587 172.027 89.4286 172.027 89.4286C167.435 90.9237 158.122 94.002 157.606 94.3544C157.089 94.7068 156.855 95.7495 156.803 96.2269C157.71 95.9857 159.991 95.299 161.854 94.4811Z"
                    fill="url(#paint7_linear_208_4980)"
                  />
                </g>
              </g>
            </g>
            {findMeasure('product_chest_width') && (
              <g id="product_chest_width">
                <path
                  id="Vector 21"
                  d="M214.711 82.8047L130.771 82.8047"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M133.446 79.1729L129.13 82.877L132.918 86.7363"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 27"
                  d="M212.329 86.7519L216.248 82.6304L212.09 79.1738"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 21_2"
                  d="M213.056 164.13L134.331 164.13"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M136.756 160.499L132.44 164.203L136.228 168.062"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 27_2"
                  d="M210.911 168.077L214.83 163.956L210.671 160.499"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
            {findMeasure('product_thigh_circumference') && (
              <g id="product_thigh_circumference">
                <path
                  id="Vector 27_3"
                  d="M163.165 259.886L168.844 259.574L168.187 254.206"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 21_3"
                  d="M167.745 258.97L118.208 212.296"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M122.463 211.315L116.782 211.051L116.892 216.458"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
            {findMeasure('product_thigh_width') && (
              <g id="product_thigh_width">
                <path
                  id="Vector 27_4"
                  d="M163.165 259.886L168.844 259.574L168.187 254.206"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 21_4"
                  d="M167.745 258.97L118.208 212.296"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M122.463 211.315L116.782 211.051L116.892 216.458"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
            {findMeasure('product_waistband_width') && (
              <g id="product_waistband_width">
                <path
                  id="Vector 21_5"
                  d="M218.967 193.918L128.185 193.918"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_5"
                  d="M130.609 190.287L126.293 193.991L130.081 197.851"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 27_5"
                  d="M217.058 197.865L220.977 193.744L216.818 190.287"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 21_6"
                  d="M221.567 201.956L125.348 201.956"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_6"
                  d="M127.772 198.324L123.456 202.028L127.244 205.888"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 27_6"
                  d="M219.895 205.903L223.814 201.782L219.655 198.325"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 21_7"
                  d="M206.215 251.517L206.214 33.0586"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_7"
                  d="M209.846 35.7347L206.141 31.4189L202.282 35.2067"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 27_7"
                  d="M202.522 248.891L206.348 253.099L210.098 249.203"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 21_8"
                  d="M206.215 251.517L206.214 33.0586"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_8"
                  d="M209.846 35.7347L206.141 31.4189L202.282 35.2067"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 27_8"
                  d="M202.522 248.891L206.348 253.099L210.098 249.203"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
            {findMeasure('product_chest_circumference') && (
              <g id="product_chest_circumference">
                <path
                  id="Vector 21_9"
                  d="M214.711 82.8047L130.771 82.8047"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_9"
                  d="M133.446 79.1729L129.13 82.877L132.918 86.7363"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 27_9"
                  d="M212.329 86.7519L216.248 82.6304L212.09 79.1738"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 21_10"
                  d="M213.056 164.13L134.331 164.13"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_10"
                  d="M136.756 160.499L132.44 164.203L136.228 168.062"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 27_10"
                  d="M210.911 168.077L214.83 163.956L210.671 160.499"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
            {findMeasure('product_waistband_circumference') && (
              <g id="product_waistband_circumference">
                <path
                  id="Vector 21_11"
                  d="M218.967 193.918L128.185 193.918"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_11"
                  d="M130.609 190.287L126.293 193.991L130.081 197.851"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 27_11"
                  d="M217.058 197.865L220.977 193.744L216.818 190.287"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 21_12"
                  d="M221.567 201.956L125.348 201.956"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_12"
                  d="M127.772 198.324L123.456 202.028L127.244 205.888"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 27_12"
                  d="M219.895 205.903L223.814 201.782L219.655 198.325"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
            {findMeasure('product_high_waist_circumference') && (
              <g id="product_high_waist_circumference">
                <path
                  id="Vector 21_13"
                  d="M213.056 164.13L134.331 164.13"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_13"
                  d="M136.756 160.499L132.44 164.203L136.228 168.062"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 27_13"
                  d="M210.911 168.077L214.83 163.956L210.671 160.499"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
            {findMeasure('product_high_waist_width') && (
              <g id="product_high_waist_width">
                <path
                  id="Vector 21_14"
                  d="M213.056 164.13L134.331 164.13"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_14"
                  d="M136.756 160.499L132.44 164.203L136.228 168.062"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 27_14"
                  d="M210.911 168.077L214.83 163.956L210.671 160.499"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_circumference') && (
              <g id="product_lower_waist_circumference">
                <path
                  id="Vector 21_15"
                  d="M213.056 164.13L134.331 164.13"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_15"
                  d="M136.756 160.499L132.44 164.203L136.228 168.062"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 27_15"
                  d="M210.911 168.077L214.83 163.956L210.671 160.499"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_width') && (
              <g id="product_lower_waist_width">
                <path
                  id="Vector 21_16"
                  d="M213.056 164.13L134.331 164.13"
                  stroke="#E55959"
                  stroke-width="2.36411"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_16"
                  d="M136.756 160.499L132.44 164.203L136.228 168.062"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
                <path
                  id="Vector 27_16"
                  d="M210.911 168.077L214.83 163.956L210.671 160.499"
                  stroke="#E55959"
                  stroke-width="1.89129"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_208_4980"
              x1="169.199"
              y1="87.7922"
              x2="189.356"
              y2="81.8545"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_208_4980"
              x1="169.231"
              y1="87.1319"
              x2="190.244"
              y2="86.9657"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_208_4980"
              x1="168.946"
              y1="87.2624"
              x2="189.695"
              y2="90.5853"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_208_4980"
              x1="169.412"
              y1="88.0291"
              x2="189.408"
              y2="94.4897"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_208_4980"
              x1="177.34"
              y1="87.7922"
              x2="157.183"
              y2="81.8545"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint5_linear_208_4980"
              x1="177.307"
              y1="87.1319"
              x2="156.294"
              y2="86.9657"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint6_linear_208_4980"
              x1="177.592"
              y1="87.2624"
              x2="156.844"
              y2="90.5853"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint7_linear_208_4980"
              x1="177.126"
              y1="88.0291"
              x2="157.13"
              y2="94.4897"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
