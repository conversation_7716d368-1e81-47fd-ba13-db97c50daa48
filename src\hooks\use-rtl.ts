import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

const rtlLanguages = ['ar', 'he', 'ar_modifier', 'he_modifier'];

const useRTL = () => {
  const { i18n } = useTranslation();

  const [isRTL, setIsRTL] = useState(false);

  useEffect(() => {
    if (rtlLanguages.includes(i18n.language)) {
      setIsRTL(true);
      document.documentElement.dir = 'rtl';
    } else {
      setIsRTL(false);
      document.documentElement.dir = '';
    }
  }, [i18n.language]);

  return isRTL;
};

export const getDocumentIsRTL = () => {
  return document.documentElement.dir === 'rtl';
};

export default useRTL;
