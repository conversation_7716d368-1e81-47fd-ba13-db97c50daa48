import { createContext, useContext } from 'react';
import { AppContextType, AppState } from './types';

const initialState: AppState = {
  app: {
    lang: 'en',
    unitSystem: 'metric',
    loading: true,
    menuOpen: false,
    config: {
      general: {
        language: [],
        theme: {
          name: '',
          css: '',
          logo: '',
        },
        isMetric: true,
        measurementSwitcher: true,
        filterChartByClothesType: false,
      },
    },
    selectedImage: {
      image: null,
      content: '',
    },
  },
  carousel: {
    selected: {
      id: '',
      content: '',
    },
  },
  product: null!,
};

export const appInitialState = initialState;

export const AppContext = createContext<AppContextType>(null!);

export function useAppContext() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within a AppProvider');
  }
  return context;
}
