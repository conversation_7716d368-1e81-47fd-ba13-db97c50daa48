import { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { t } from 'i18next';
import { Container, Image, Link, Loading, Text } from '@/components/atoms';
import { Carousel, TabContainer, Footer, CarouselItem } from '@/components/organisms';
import { BodyMeasure, GarmentMeasure, SizeSystemResponse, useSizeChart } from '@/hooks/use-size-chart';
import { useAppContext } from '@/store';
import { AppStateActions, selectedImage } from '@/store/types';
import { unitConverter } from '@/lib/unit-converter';
import { handleLinkUrl } from '@/lib/handle-measurement-tape';
import { cn } from '@/lib/utils';
import { getBodyMeasureTemplate } from '@/hooks/use-body-measures';
import useGarment from '@/hooks/use-garment';
import { MeasurementRow, MeasurementContent, Notification } from '@/components/molecules';
import getQueryParams from '@/lib/get-query-params';
import { filterByClotheTypes } from '@/components/body_measures';
import { getTablePriority } from '@/lib/get-table-priority';
import { decodedRecommendedSize } from '@/lib/decode-base64';

const qp = getQueryParams<{ recommendedSize: string; pageProductImg: string }>();

const GarmentMeasurements = ({ garmentMeasure }: { garmentMeasure: GarmentMeasure }) => {
  const { t } = useTranslation();
  const {
    app: { unitSystem },
    setState,
  } = useAppContext();

  const unit = unitSystem === 'metric' ? 'cm' : 'in';

  const template = garmentMeasure?.template ?? '';

  const convert = (value: number) => unitConverter({ value, unit });

  const measures = garmentMeasure?.garmentMeasurements ?? ([] as GarmentMeasure[]);
  const Garment = useGarment({ template });
  const composedMeasures = measures.map((item) => ({
    ...garmentMeasure,
    garmentMeasurements: [item],
  })) as GarmentMeasure[];

  const handleSelectedImage = ({ image, content }: selectedImage) => {
    setState({ action: AppStateActions.SET_SELECTED_IMAGE, payload: { image, content } });
  };

  return (
    <>
      {composedMeasures.map((item) => {
        const size = item.garmentMeasurements[0];
        const { initialValue, finalValue } = size.label;
        const measure = size.measure;
        const label =
          initialValue === finalValue
            ? `${convert(initialValue)} ${unit}`
            : `${convert(initialValue)} - ${convert(finalValue)} ${unit}`;

        const translationKey = measure.replace('product_', '');

        const title = t(`garment_measurements.${translationKey}`);
        const content = t(`garment_measures_info.${translationKey}`);

        const selectedItem = {
          image: Garment ? <Garment measure={item} className="translate-x-[-12px]" /> : null,
          content,
        };

        return (
          <div
            key={size.measure}
            className="border-b-2 border-[#E7E7E7] py-4"
            onMouseEnter={() => handleSelectedImage(selectedItem)}
            onMouseLeave={() => handleSelectedImage({ image: null, content: '' })}
          >
            <MeasurementRow key={size.measure} className="p-0 measurement-row" label={title} value={label} />
          </div>
        );
      })}
    </>
  );
};

const BodyMeasurements = ({ bodyMeasure }: { bodyMeasure: BodyMeasure }) => {
  const { product, app, setState } = useAppContext();
  const { productInfo } = product;
  const { gender, ageGroup, clothesType } = productInfo;
  const {
    config: {
      general: { filterChartByClothesType },
    },
  } = app;
  const unit = app.unitSystem === 'metric' ? 'cm' : 'in';
  const weightUnit = app.unitSystem === 'metric' ? 'kg' : 'lb';

  const measures = bodyMeasure?.measures ?? ([] as BodyMeasure[]);

  const convert = (value: number) => unitConverter({ value, unit });

  const composedMeasures = measures
    .filter((item) => !item.hidden)
    .sort((a, b) => (a.position ?? 0) - (b.position ?? 0))
    .filter(({ measure }) => !filterChartByClothesType || filterByClotheTypes[clothesType]?.includes(measure))
    .map((item) => ({ ...bodyMeasure, measures: [item] }));

  const handleSelectedImage = ({ image, content }: selectedImage) => {
    setState({ action: AppStateActions.SET_SELECTED_IMAGE, payload: { image, content } });
  };

  return (
    <>
      {composedMeasures
        .sort((a, b) => {
          const typeA = getTablePriority(a.measures[0]);
          const typeB = getTablePriority(b.measures[0]);

          return typeA - typeB;
        })
        .map((item) => {
          const size = item.measures[0];
          const { initialValue, finalValue, textValue } = size.label;
          const key = size.measure;
          const isLbs = unit === 'in';

          const getTranslationKey = () => {
            if (ageGroup === 'child') return key;

            if (key === 'chest') {
              if (gender === 'M') return 'chest_m';
              if (gender === 'F') return 'chest_f';
              return 'chest';
            }

            return key;
          };

          const translationKey = getTranslationKey();
          const title = t(`body_measurements.${translationKey}`);
          const content = t(`measures_info.${translationKey}`);
          const label =
            size.measure === 'weight' || size.measure === 'weightChart'
              ? `${unitConverter({ value: initialValue, unit, isLbs })} - ${unitConverter({ value: finalValue, unit, isLbs })} ${weightUnit}`
              : textValue
                ? textValue
                : initialValue === finalValue
                  ? `${convert(initialValue)} ${unit}`
                  : `${convert(initialValue)} - ${convert(finalValue)} ${unit}`;

          const Template = getBodyMeasureTemplate({
            gender,
            ageGroup,
            measure: size,
          });

          const selectedItem = {
            image: Template ? <Template measure={item} /> : null,
            content,
          };

          return (
            <div
              key={size.measure}
              className="border-b-2 border-[#E7E7E7] py-4"
              onMouseEnter={() => handleSelectedImage(selectedItem)}
              onMouseLeave={() => handleSelectedImage({ image: null, content: '' })}
            >
              <MeasurementRow className="p-0 measurement-row" label={title} value={label} />
            </div>
          );
        })}
    </>
  );
};

const SizeSystem = ({ sizeSystem }: { sizeSystem: SizeSystemResponse }) => {
  const { sizeSystem: sizeSystemData } = sizeSystem;
  const { t } = useTranslation();

  return (
    <div className="overflow-y-auto max-h-full custom-scroll">
      <table className="w-full border-collapse">
        <thead className="sticky top-0 bg-white border-b border-[#E7E7E7]">
          <tr className="tab__content__size-system-title">
            <th className="text-[12px] text-center font-medium py-3 w-[50%]">{t('regions.region')}</th>
            <th className="text-[12px] text-center font-medium py-3 w-[50%]">
              {t('product_comparison.elements.size')}
            </th>
          </tr>
        </thead>
        <tbody>
          {Object.entries(sizeSystemData).map(([key, value]) => (
            <tr key={key} className="border-b border-[#E7E7E7] tab__content__size-system-item">
              <td className="text-[12px] text-center font-normal py-3">{t(`regions.code.${key}`)}</td>
              <td className="text-[12px] text-center font-normal py-3">{value}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

interface SelectedImageProps {
  className?: string;
}

const SelectedImage = ({ className }: SelectedImageProps) => {
  const {
    product,
    app: {
      selectedImage: { image, content },
    },
  } = useAppContext();
  const {
    productInfo: { coverImage },
    modelingInfo: { modelingObservationTranslations },
  } = product;

  const observationAdvice = modelingObservationTranslations[0]?.observation;
  const productImage = qp.pageProductImg !== 'false' ? qp.pageProductImg : coverImage;

  return (
    <div
      className={cn(
        'flex flex-col h-full w-full rounded-lg overflow-hidden transition-all relative bg-[#E7E7E7]',
        className
      )}
    >
      <Image
        src={coverImage}
        alt="product"
        loading="lazy"
        className={cn('h-full w-full object-cover', { 'h-[75%]': !image })}
      />

      {image && (
        <div className={'absolute top-0 left-0 right-0 m-auto w-full h-full bg-amber-100'}>
          <MeasurementContent
            image={image}
            value={content}
            className="h-full w-full sm:px-0 bg-white measurement-content"
          />
        </div>
      )}
      {!image && observationAdvice && (
        <Notification content={observationAdvice}/>
      )}
    </div>
  );
};

interface TabProps {
  garmentMeasure: GarmentMeasure | null;
  bodyMeasure: BodyMeasure | null;
  sizeSystem: SizeSystemResponse | null;
}

const useTabs = ({ garmentMeasure, bodyMeasure, sizeSystem }: TabProps) => {
  const [activeTab, setActiveTab] = useState(0);
  const { t } = useTranslation();

  const tabs = [];

  if (bodyMeasure && bodyMeasure.measures.length > 0) {
    tabs.push({
      id: 1,
      label: t('drawers.knobs.product_info'),
      content: <BodyMeasurements bodyMeasure={bodyMeasure!} />,
      customClass: 'body-measures',
    });
  }

  if (garmentMeasure && garmentMeasure.garmentMeasurements.length > 0) {
    tabs.push({
      id: 2,
      label: t('drawers.knobs.garment_info'),
      content: <GarmentMeasurements garmentMeasure={garmentMeasure!} />,
      customClass: 'garment-measures',
    });
  }

  if (sizeSystem && Object.keys(sizeSystem.sizeSystem).length > 0) {
    tabs.push({
      id: 3,
      label: t('drawers.knobs.sizes_conversions'),
      content: <SizeSystem sizeSystem={sizeSystem!} />,
      customClass: 'size-system',
    });
  }

  const handleTabChange = (index: number) => {
    setActiveTab(index);
  };

  return { tabs, activeTab, handleTabChange };
};

export function Desktop() {
  const { app, setState, carousel: carouselState, product } = useAppContext();
  const { carousel, selectedGarmentMeasure, selectedBodyMeasure, selectedSizeSystem } = useSizeChart();
  const { t } = useTranslation();
  const unitType = app.unitSystem === 'metric' ? 'cm' : 'in';
  const { tabs, activeTab, handleTabChange } = useTabs({
    garmentMeasure: selectedGarmentMeasure,
    bodyMeasure: selectedBodyMeasure,
    sizeSystem: selectedSizeSystem,
  });

  const shouldHaveBorder = app.config.general?.measurementSwitcher && product?.productInfo?.accessory;
  const shouldCenterMeasures = useMemo(() => {
    if (tabs.length === 1) return true;
    return false;
  }, [tabs.length]);

  const toggleUnit = () => {
    setState({ action: AppStateActions.SET_UNIT_SYSTEM, payload: app.unitSystem === 'metric' ? 'imperial' : 'metric' });
  };

  const handleCloseMenu = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!app.menuOpen) return;

    event.stopPropagation();
    const element = event.target as HTMLElement;

    const isClickInsideMenu = element instanceof Node && Boolean(element.closest('.menu-container'));

    if (!isClickInsideMenu) {
      setState({ action: AppStateActions.TOGGLE_MENU_OPEN, payload: null });
    }
  };

  const handleCarouselChange = (item: CarouselItem) => {
    setState({ action: AppStateActions.SET_CAROUSEL_SELECTED, payload: item });
  };

  if (app.loading) return <Loading className="w-full h-full absolute flex justify-center items-center opacity-80" />;

  return (
    <Container
      className="w-full h-full p-0 sm:px-0 grid grid-cols-[1.9fr_3fr] relative overflow-hidden"
      onClick={handleCloseMenu}
    >
      <div className="flex flex-col gap-3 relative overflow-hidden border-r-[1px] border-[#E1E1E1] p-6">
        <Text variant="h1" className="text-center text-[1rem] size-chart__title">
          {t('generics.size_chart')}
        </Text>
        <SelectedImage />
        <Link external href={handleLinkUrl({ unit: unitType })} className="button__measuring-tape">
          {t('footer.print_measurement_tape')}
        </Link>
      </div>
      <aside className="w-full h-full max-h-full p-0 py-4 flex flex-col gap-3 relative items-center justify-center overflow-hidden">
        <div
          className={cn('h-[83%] w-full flex flex-col gap-3 items-center', { 'justify-center': shouldCenterMeasures })}
        >
          <div className="p-3 w-full flex flex-col items-center gap-5 px-4">
            <Text variant="label" className="text-center carousel__title">
              {t('size_chart.title')}
            </Text>
            <Carousel
              data-test-id="carousel"
              className="max-w-[90%] w-full"
              buttonClassName="p-2"
              items={carousel.content}
              selectedItem={carouselState.selected}
              onSelectItem={handleCarouselChange}
              defaultValue={decodedRecommendedSize || undefined}
            />
          </div>
          <TabContainer
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={handleTabChange}
            className={cn('overflow-hidden', { 'flex-1': !shouldCenterMeasures })}
          />
        </div>
        <div className={shouldHaveBorder ? 'w-full border-t-[1px] border-[#E1E1E1] px-2' : 'w-full px-2'}>
          <Footer
            className="w-full h-fit mt-4 p-0 flex-none"
            unit={app.unitSystem === 'metric' ? 'cm' : 'in'}
            onUnitChange={toggleUnit}
            isAccessory={product?.productInfo?.accessory}
          />
        </div>
      </aside>
    </Container>
  );
}
