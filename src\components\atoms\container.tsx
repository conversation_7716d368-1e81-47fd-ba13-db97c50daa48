import type React from 'react';
import { cn } from '@/lib/utils';

type ContainerElement = 'div' | 'section' | 'main' | 'article' | 'aside' | 'header' | 'footer' | 'nav';

interface ContainerProps extends React.HTMLAttributes<HTMLElement> {
  /** The HTML element to render the container as */
  as?: ContainerElement;
  /** The content of the container */
  children: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** Maximum width of the container */
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
}

export const Container = ({
  as: Element = 'div',
  children,
  className,
  maxWidth = 'full',
  ...props
}: ContainerProps) => {
  return (
    <Element
      className={cn(
        'w-full mx-auto px-4 sm:px-6 lg:px-8',
        {
          'max-w-screen-sm': maxWidth === 'sm',
          'max-w-screen-md': maxWidth === 'md',
          'max-w-screen-lg': maxWidth === 'lg',
          'max-w-screen-xl': maxWidth === 'xl',
          'max-w-screen-2xl': maxWidth === '2xl',
          'max-w-full': maxWidth === 'full',
        },
        className
      )}
      {...props}
    >
      {children}
    </Element>
  );
};
