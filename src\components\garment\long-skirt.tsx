import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface LongSkirtProps {
  measure: GarmentMeasure;
  className?: string;
}

export function LongSkirt({ measure, className }: LongSkirtProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_long_skirt">
            <g id="long_skirt">
              <path
                id="Vector"
                d="M143.449 52.6466C145.829 44.0923 149.694 34.6393 151.452 31.3652C151.452 31.3652 159.987 31.7309 165.458 31.729C170.859 31.7272 179.282 31.3652 179.282 31.3652C181.04 34.6393 184.905 44.0923 187.285 52.6466C194.015 76.8382 194.869 89.2069 198.199 113.762C200.722 132.372 202.564 150.868 203.11 161.6C202.807 162.631 201.764 164.692 200.018 164.692C197.835 164.692 195.47 163.237 194.015 163.964C192.56 164.692 191.469 166.511 188.74 165.965C186.012 165.42 185.648 165.238 182.92 165.965C180.191 166.693 179.464 165.056 176.917 165.42C174.371 165.783 175.28 166.875 172.37 166.329C168.978 165.693 169.136 166.875 167.459 166.875C166.301 166.875 165.604 166.529 165.367 166.388C165.131 166.529 164.434 166.875 163.275 166.875C161.599 166.875 161.756 165.693 158.364 166.329C155.454 166.875 156.364 165.783 153.817 165.42C151.271 165.056 150.543 166.693 147.815 165.965C145.086 165.238 144.722 165.42 141.994 165.965C139.266 166.511 138.174 164.692 136.719 163.964C135.264 163.237 132.9 164.692 130.717 164.692C128.971 164.692 127.928 162.631 127.625 161.6C128.17 150.868 130.012 132.372 132.536 113.762C135.865 89.2069 136.719 76.8382 143.449 52.6466Z"
                fill="white"
              />
              <g id="Group 132">
                <g id="Group 125">
                  <path
                    id="Vector 16"
                    d="M145.848 45.189C145.826 45.25 145.774 45.397 145.702 45.6127C145.773 45.6572 145.848 45.6815 145.848 45.6815C145.818 45.7279 145.669 45.9644 145.463 46.3489C145.038 47.7036 144.399 49.9808 144.205 51.9346C143.949 54.5179 144.823 58.8161 145.255 60.1361C144.625 59.0578 143.923 56.483 143.708 53.3968C143.502 50.4429 144.789 47.6054 145.463 46.3489C145.555 46.0563 145.636 45.8067 145.702 45.6127C145.571 45.5312 145.456 45.3816 145.848 45.189Z"
                    fill="url(#paint0_linear_208_4966)"
                    fill-opacity="0.87"
                  />
                  <path
                    id="Vector 210"
                    d="M184.899 45.189C184.921 45.25 184.973 45.397 185.045 45.6127C184.974 45.6572 184.899 45.6815 184.899 45.6815C184.929 45.7279 185.078 45.9644 185.284 46.3489C185.709 47.7036 186.348 49.9808 186.542 51.9346C186.798 54.5179 185.924 58.8161 185.492 60.1361C186.122 59.0578 186.824 56.483 187.039 53.3968C187.245 50.4429 185.958 47.6054 185.284 46.3489C185.192 46.0563 185.11 45.8067 185.045 45.6127C185.176 45.5312 185.291 45.3816 184.899 45.189Z"
                    fill="url(#paint1_linear_208_4966)"
                    fill-opacity="0.87"
                  />
                </g>
                <g id="Group 131">
                  <g id="Group 126">
                    <path
                      id="Vector 16_2"
                      d="M135.446 92.6626C135.423 92.7513 135.397 92.881 135.37 93.0456C135.342 93.3311 135.291 93.7443 135.198 94.3882C134.971 96.5374 134.763 100.037 134.967 102.079C135.605 108.476 136.402 111.949 137.209 115.332C136.033 112.569 133.161 106.558 134.537 98.4832C134.869 96.5348 135.072 95.2532 135.198 94.3882C135.256 93.8354 135.316 93.3719 135.37 93.0456C135.43 92.4267 135.385 92.4072 135.446 91.9351L135.446 92.6626Z"
                      fill="url(#paint2_linear_208_4966)"
                      fill-opacity="0.87"
                    />
                  </g>
                  <g id="Group 127">
                    <path
                      id="Vector 16_3"
                      d="M195.634 102.079C195.917 99.2467 195.722 93.9739 195.47 93.0262L195.515 92.4805C195.642 93.463 195.447 93.0311 196.562 99.5743C197.938 107.649 194.568 112.568 193.391 115.332C194.198 111.949 194.996 108.476 195.634 102.079Z"
                      fill="url(#paint3_linear_208_4966)"
                      fill-opacity="0.87"
                    />
                  </g>
                </g>
                <g id="Group 130">
                  <g id="Group 124">
                    <path
                      id="Vector 210_2"
                      d="M137.629 144.32L134.9 163.6L136.174 163.782L137.265 164.146L137.629 144.32Z"
                      fill="url(#paint4_linear_208_4966)"
                    />
                    <path
                      id="Vector 215"
                      d="M176.822 146.321L175.826 165.63L178.008 165.419L179.1 165.63L176.822 146.321Z"
                      fill="url(#paint5_linear_208_4966)"
                    />
                    <path
                      id="Vector 211"
                      d="M144.177 149.776V165.601L145.45 165.237L147.087 165.601L144.177 149.776Z"
                      fill="url(#paint6_linear_208_4966)"
                    />
                    <path
                      id="Vector 214"
                      d="M171.029 151.777L169.278 166.329L170.551 165.965L172.188 166.329L171.029 151.777Z"
                      fill="url(#paint7_linear_208_4966)"
                    />
                    <path
                      id="Vector 212"
                      d="M153.089 137.59L152.18 165.419H153.453L155.09 165.783L153.089 137.59Z"
                      fill="url(#paint8_linear_208_4966)"
                    />
                    <path
                      id="Vector 216"
                      d="M186.557 146.139L184.557 165.601H185.83L187.467 165.965L186.557 146.139Z"
                      fill="url(#paint9_linear_208_4966)"
                    />
                    <path
                      id="Vector 213"
                      d="M159.456 158.507V166.329L160.729 166.217H161.093L159.456 158.507Z"
                      fill="url(#paint10_linear_208_4966)"
                    />
                    <path
                      id="Vector 217"
                      d="M193.106 156.143L193.651 163.853H195.652L193.106 156.143Z"
                      fill="url(#paint11_linear_208_4966)"
                    />
                  </g>
                  <g id="Group 128">
                    <path
                      id="Vector 218"
                      d="M131.626 164.51C131.626 164.073 131.626 156.507 131.626 152.505"
                      stroke="black"
                      stroke-width="0.363784"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 220"
                      d="M144.541 165.419C144.515 164.735 144.052 152.592 143.813 146.321"
                      stroke="black"
                      stroke-width="0.363784"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 221"
                      d="M153.999 165.419C153.96 164.331 153.266 145.381 152.908 135.407"
                      stroke="black"
                      stroke-width="0.363784"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 222"
                      d="M159.456 165.965C159.456 165.157 159.456 150.817 159.456 143.411"
                      stroke="black"
                      stroke-width="0.363784"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 219"
                      d="M136.537 163.782C136.616 162.512 138.003 140.142 138.72 128.495"
                      stroke="black"
                      stroke-width="0.363784"
                      stroke-linecap="round"
                    />
                  </g>
                  <g id="Group 129">
                    <path
                      id="Vector 218_2"
                      d="M198.562 164.328C198.562 163.891 198.562 156.507 198.562 152.505"
                      stroke="black"
                      stroke-width="0.363784"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 220_2"
                      d="M185.648 165.419C185.674 164.735 186.137 152.592 186.376 146.321"
                      stroke="black"
                      stroke-width="0.363784"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 221_2"
                      d="M175.826 165.601C175.878 164.52 176.803 145.322 177.281 135.407"
                      stroke="black"
                      stroke-width="0.363784"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 222_2"
                      d="M170.733 165.965C170.733 165.157 170.733 150.817 170.733 143.411"
                      stroke="black"
                      stroke-width="0.363784"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 219_2"
                      d="M193.651 163.965C193.573 162.694 192.185 140.142 191.468 128.496"
                      stroke="black"
                      stroke-width="0.363784"
                      stroke-linecap="round"
                    />
                  </g>
                </g>
              </g>
              <path
                id="Vector_2"
                d="M165.458 166.329C165.458 166.329 164.731 166.875 163.275 166.875C161.599 166.875 161.756 165.693 158.364 166.329C155.454 166.875 156.364 165.783 153.817 165.42C151.271 165.056 150.543 166.693 147.815 165.965C145.086 165.238 144.722 165.42 141.994 165.965C139.266 166.511 138.174 164.692 136.719 163.964C135.264 163.237 132.9 164.692 130.717 164.692C128.971 164.692 127.928 162.631 127.625 161.6C128.17 150.868 130.012 132.372 132.536 113.762C135.865 89.2069 136.719 76.8382 143.449 52.6466C145.829 44.0923 149.694 34.6393 151.452 31.3652C151.452 31.3652 159.987 31.7309 165.458 31.729C170.859 31.7272 179.282 31.3652 179.282 31.3652C181.04 34.6393 184.905 44.0923 187.285 52.6466C194.015 76.8382 194.869 89.2069 198.199 113.762C200.722 132.372 202.564 150.868 203.11 161.6C202.807 162.631 201.764 164.692 200.018 164.692C197.835 164.692 195.47 163.237 194.015 163.964C192.56 164.692 191.469 166.511 188.74 165.965C186.012 165.42 185.648 165.238 182.92 165.965C180.191 166.693 179.464 165.056 176.917 165.42C174.371 165.783 175.28 166.875 172.37 166.329C168.978 165.693 169.136 166.875 167.459 166.875C166.004 166.875 165.276 166.329 165.276 166.329"
                stroke="black"
                stroke-width="0.363784"
              />
              <g id="Group 133">
                <path
                  id="Vector 208"
                  d="M179.464 28.4551H151.27V31.3653C151.27 31.3653 159.916 31.731 165.458 31.7291C170.929 31.7273 179.464 31.3653 179.464 31.3653V28.4551Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.363784"
                />
                <path
                  id="Vector 209"
                  d="M165.64 27.1816C157.273 27.1816 151.27 28.4549 151.27 28.4549H179.646C179.646 28.4549 173.279 27.1816 165.64 27.1816Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.363784"
                />
              </g>
            </g>
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 21"
                  d="M177.463 30.2188H152.804"
                  stroke="#E55959"
                  stroke-width="1.81892"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M154.978 27.5153L151.54 30.2223L154.327 33.3122"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
                <path
                  id="Vector 27"
                  d="M175.934 33.3069L178.789 29.991L175.462 27.4926"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 21_2"
                  d="M184.011 52.7734L145.528 52.7734"
                  stroke="#E55959"
                  stroke-width="1.81892"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M147.703 50.0695L144.265 52.7765L147.051 55.8664"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
                <path
                  id="Vector 27_2"
                  d="M182.482 55.8616L185.337 52.5457L182.01 50.0473"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
              </g>
            )}
            {findMeasure('product_lower_length') && (
              <g id="product_lower_length">
                <path
                  id="Vector 19"
                  d="M143.49 29.0093L120.713 161.418"
                  stroke="#E55959"
                  stroke-width="1.81892"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M117.903 159.067L120.449 162.626L123.663 159.984"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
                <path
                  id="Vector 28"
                  d="M140.49 30.6795L143.597 27.8581L144.732 29.534L145.867 31.2099"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_2"
                  d="M165.64 30.2739L165.64 165.965"
                  stroke="#E55959"
                  stroke-width="1.81892"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M162.513 163.806L165.636 166.871L168.346 163.714"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
                <path
                  id="Vector 28_2"
                  d="M162.791 31.7523L165.365 28.4365L166.772 29.8912L168.179 31.3459"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 21_3"
                  d="M177.463 30.2188H152.804"
                  stroke="#E55959"
                  stroke-width="1.81892"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M154.978 27.5153L151.54 30.2223L154.327 33.3122"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
                <path
                  id="Vector 27_5"
                  d="M175.934 33.3069L178.789 29.991L175.462 27.4926"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 21_4"
                  d="M184.011 52.7734L145.528 52.7734"
                  stroke="#E55959"
                  stroke-width="1.81892"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M147.703 50.0695L144.265 52.7765L147.051 55.8664"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
                <path
                  id="Vector 27_6"
                  d="M182.482 55.8616L185.337 52.5457L182.01 50.0473"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_3"
                  d="M165.64 31.729L165.64 164.146"
                  stroke="#E55959"
                  stroke-width="1.81892"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M162.513 162.35L165.636 165.415L168.346 162.259"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
                <path
                  id="Vector 28_3"
                  d="M162.791 33.2074L165.365 29.8916L166.772 31.3463L168.179 32.801"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
              </g>
            )}
            {findMeasure('product_waistband_width') && (
              <g id="product_waistband_width">
                <path
                  id="Vector 21_5"
                  d="M177.463 30.2188H152.804"
                  stroke="#E55959"
                  stroke-width="1.81892"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_5"
                  d="M154.978 27.5153L151.54 30.2223L154.327 33.3122"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
                <path
                  id="Vector 27_8"
                  d="M175.934 33.3069L178.789 29.991L175.462 27.4926"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
              </g>
            )}
            {findMeasure('product_waistband_circumference') && (
              <g id="product_waistband_circumference">
                <path
                  id="Vector 21_6"
                  d="M177.463 30.2188H152.804"
                  stroke="#E55959"
                  stroke-width="1.81892"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_6"
                  d="M154.978 27.5153L151.54 30.2223L154.327 33.3122"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
                <path
                  id="Vector 27_9"
                  d="M175.934 33.3069L178.789 29.991L175.462 27.4926"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
              </g>
            )}
            {findMeasure('product_high_waist_width') && (
              <g id="product_high_waist_width">
                <path
                  id="Vector 21_7"
                  d="M177.463 30.2188H152.804"
                  stroke="#E55959"
                  stroke-width="1.81892"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_7"
                  d="M154.978 27.5153L151.54 30.2223L154.327 33.3122"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
                <path
                  id="Vector 27_10"
                  d="M175.934 33.3069L178.789 29.991L175.462 27.4926"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_width') && (
              <g id="product_lower_waist_width">
                <path
                  id="Vector 21_8"
                  d="M177.463 30.2188H152.804"
                  stroke="#E55959"
                  stroke-width="1.81892"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_8"
                  d="M154.978 27.5153L151.54 30.2223L154.327 33.3122"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
                <path
                  id="Vector 27_11"
                  d="M175.934 33.3069L178.789 29.991L175.462 27.4926"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
              </g>
            )}
            {findMeasure('product_high_waist_circumference') && (
              <g id="product_high_waist_circumference">
                <path
                  id="Vector 21_9"
                  d="M177.463 30.2188H152.804"
                  stroke="#E55959"
                  stroke-width="1.81892"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_9"
                  d="M154.978 27.5153L151.54 30.2223L154.327 33.3122"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
                <path
                  id="Vector 27_12"
                  d="M175.934 33.3069L178.789 29.991L175.462 27.4926"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_circumference') && (
              <g id="product_lower_waist_circumference">
                <path
                  id="Vector 21_10"
                  d="M177.463 30.2188H152.804"
                  stroke="#E55959"
                  stroke-width="1.81892"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_10"
                  d="M154.978 27.5153L151.54 30.2223L154.327 33.3122"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
                <path
                  id="Vector 27_13"
                  d="M175.934 33.3069L178.789 29.991L175.462 27.4926"
                  stroke="#E55959"
                  stroke-width="1.45514"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_208_4966"
              x1="145.252"
              y1="46.3788"
              x2="138.638"
              y2="51.2501"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#CDCDCD" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_208_4966"
              x1="185.495"
              y1="46.3788"
              x2="192.108"
              y2="51.2501"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#CDCDCD" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_208_4966"
              x1="137.203"
              y1="80.0736"
              x2="122.417"
              y2="88.0055"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_208_4966"
              x1="193.397"
              y1="80.0734"
              x2="208.184"
              y2="88.0053"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_208_4966"
              x1="136.083"
              y1="144.502"
              x2="136.083"
              y2="164.146"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint5_linear_208_4966"
              x1="176.641"
              y1="146.405"
              x2="178.04"
              y2="165.999"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint6_linear_208_4966"
              x1="145.359"
              y1="145.957"
              x2="145.359"
              y2="165.601"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint7_linear_208_4966"
              x1="170.46"
              y1="146.684"
              x2="170.46"
              y2="166.329"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint8_linear_208_4966"
              x1="153.362"
              y1="146.321"
              x2="153.362"
              y2="165.965"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint9_linear_208_4966"
              x1="185.739"
              y1="146.502"
              x2="185.739"
              y2="166.147"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint10_linear_208_4966"
              x1="160.638"
              y1="160.323"
              x2="160.638"
              y2="166.329"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint11_linear_208_4966"
              x1="195.561"
              y1="157.959"
              x2="195.561"
              y2="163.964"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_long_skirt">
            <g id="long_skirt">
              <path
                id="Vector"
                d="M133.342 70.9657C137.422 56.3012 144.048 40.0961 147.062 34.4834C147.062 34.4834 161.692 35.1102 171.072 35.107C180.33 35.1039 194.77 34.4834 194.77 34.4834C197.784 40.0961 204.41 56.3012 208.49 70.9657C220.027 112.437 221.49 133.64 227.198 175.735C231.524 207.638 234.682 239.346 235.617 257.743C235.098 259.51 233.31 263.044 230.317 263.044C226.575 263.044 222.521 260.549 220.027 261.796C217.532 263.044 215.661 266.162 210.984 265.226C206.307 264.291 205.683 263.979 201.006 265.226C196.329 266.474 195.081 263.667 190.716 264.291C186.351 264.915 187.91 266.785 182.921 265.85C177.106 264.76 177.376 266.785 174.502 266.785C172.516 266.785 171.321 266.193 170.916 265.951C170.511 266.193 169.315 266.785 167.33 266.785C164.455 266.785 164.726 264.76 158.911 265.85C153.922 266.785 155.481 264.915 151.116 264.291C146.75 263.667 145.503 266.474 140.826 265.226C136.148 263.979 135.525 264.291 130.848 265.226C126.17 266.162 124.3 263.044 121.805 261.796C119.31 260.549 115.257 263.044 111.515 263.044C108.522 263.044 106.734 259.51 106.214 257.743C107.15 239.346 110.307 207.638 114.633 175.735C120.341 133.64 121.805 112.437 133.342 70.9657Z"
                fill="white"
              />
              <g id="Group 132">
                <g id="Group 125">
                  <path
                    id="Vector 16"
                    d="M137.454 58.1816C137.416 58.2862 137.327 58.5382 137.203 58.9081C137.325 58.9843 137.454 59.0259 137.454 59.0259C137.402 59.1056 137.147 59.511 136.793 60.1701C136.065 62.4925 134.969 66.3962 134.638 69.7456C134.199 74.1741 135.696 81.5425 136.438 83.8054C135.357 81.9567 134.153 77.5429 133.784 72.2522C133.431 67.1883 135.639 62.3241 136.793 60.1701C136.951 59.6685 137.091 59.2406 137.203 58.9081C136.979 58.7682 136.782 58.5119 137.454 58.1816Z"
                    fill="url(#paint0_linear_208_4967)"
                    fill-opacity="0.87"
                  />
                  <path
                    id="Vector 210"
                    d="M204.398 58.1816C204.436 58.2862 204.525 58.5382 204.649 58.9081C204.527 58.9843 204.398 59.0259 204.398 59.0259C204.45 59.1056 204.705 59.511 205.058 60.1701C205.787 62.4925 206.882 66.3962 207.214 69.7456C207.653 74.1741 206.155 81.5425 205.414 83.8054C206.494 81.9567 207.698 77.5429 208.067 72.2522C208.421 67.1883 206.213 62.3241 205.058 60.1701C204.901 59.6685 204.761 59.2406 204.649 58.9081C204.872 58.7682 205.07 58.5119 204.398 58.1816Z"
                    fill="url(#paint1_linear_208_4967)"
                    fill-opacity="0.87"
                  />
                </g>
                <g id="Group 131">
                  <g id="Group 126">
                    <path
                      id="Vector 16_2"
                      d="M119.622 139.566C119.581 139.718 119.537 139.94 119.49 140.222C119.443 140.712 119.356 141.42 119.196 142.524C118.808 146.208 118.451 152.208 118.8 155.709C119.894 166.674 121.261 172.628 122.645 178.428C120.628 173.69 115.704 163.386 118.063 149.544C118.632 146.204 118.981 144.007 119.196 142.524C119.296 141.576 119.398 140.782 119.49 140.222C119.594 139.161 119.517 139.128 119.622 138.318L119.622 139.566Z"
                      fill="url(#paint2_linear_208_4967)"
                      fill-opacity="0.87"
                    />
                  </g>
                  <g id="Group 127">
                    <path
                      id="Vector 16_3"
                      d="M222.801 155.708C223.286 150.853 222.952 141.814 222.52 140.189L222.597 139.253C222.816 140.938 222.48 140.197 224.391 151.414C226.75 165.256 220.973 173.69 218.956 178.428C220.34 172.628 221.707 166.674 222.801 155.708Z"
                      fill="url(#paint3_linear_208_4967)"
                      fill-opacity="0.87"
                    />
                  </g>
                </g>
                <g id="Group 130">
                  <g id="Group 124">
                    <path
                      id="Vector 210_2"
                      d="M123.364 228.12L118.687 261.172L120.869 261.484L122.74 262.108L123.364 228.12Z"
                      fill="url(#paint4_linear_208_4967)"
                    />
                    <path
                      id="Vector 215"
                      d="M190.552 231.55L188.844 264.652L192.586 264.291L194.457 264.652L190.552 231.55Z"
                      fill="url(#paint5_linear_208_4967)"
                    />
                    <path
                      id="Vector 211"
                      d="M134.589 237.475V264.602L136.772 263.979L139.578 264.602L134.589 237.475Z"
                      fill="url(#paint6_linear_208_4967)"
                    />
                    <path
                      id="Vector 214"
                      d="M180.622 240.905L177.619 265.85L179.802 265.226L182.608 265.85L180.622 240.905Z"
                      fill="url(#paint7_linear_208_4967)"
                    />
                    <path
                      id="Vector 212"
                      d="M149.868 216.583L148.309 264.291H150.491L153.298 264.915L149.868 216.583Z"
                      fill="url(#paint8_linear_208_4967)"
                    />
                    <path
                      id="Vector 216"
                      d="M207.242 231.239L203.812 264.603H205.994L208.801 265.227L207.242 231.239Z"
                      fill="url(#paint9_linear_208_4967)"
                    />
                    <path
                      id="Vector 213"
                      d="M160.782 252.442V265.85L162.964 265.659H163.588L160.782 252.442Z"
                      fill="url(#paint10_linear_208_4967)"
                    />
                    <path
                      id="Vector 217"
                      d="M218.467 248.389L219.403 261.606H222.833L218.467 248.389Z"
                      fill="url(#paint11_linear_208_4967)"
                    />
                  </g>
                  <g id="Group 128">
                    <path
                      id="Vector 218"
                      d="M113.074 262.732C113.074 261.983 113.074 249.012 113.074 242.152"
                      stroke="black"
                      stroke-width="0.623629"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 220"
                      d="M135.213 264.291C135.168 263.118 134.375 242.301 133.965 231.55"
                      stroke="black"
                      stroke-width="0.623629"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 221"
                      d="M151.427 264.291C151.36 262.426 150.17 229.94 149.556 212.842"
                      stroke="black"
                      stroke-width="0.623629"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 222"
                      d="M160.782 265.227C160.782 263.842 160.782 239.258 160.782 226.562"
                      stroke="black"
                      stroke-width="0.623629"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 219"
                      d="M121.493 261.484C121.627 259.306 124.006 220.958 125.235 200.992"
                      stroke="black"
                      stroke-width="0.623629"
                      stroke-linecap="round"
                    />
                  </g>
                  <g id="Group 129">
                    <path
                      id="Vector 218_2"
                      d="M227.822 262.42C227.822 261.671 227.822 249.012 227.822 242.152"
                      stroke="black"
                      stroke-width="0.623629"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 220_2"
                      d="M205.683 264.291C205.728 263.118 206.521 242.301 206.93 231.55"
                      stroke="black"
                      stroke-width="0.623629"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 221_2"
                      d="M188.845 264.603C188.934 262.749 190.52 229.838 191.339 212.842"
                      stroke="black"
                      stroke-width="0.623629"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 222_2"
                      d="M180.114 265.227C180.114 263.842 180.114 239.258 180.114 226.562"
                      stroke="black"
                      stroke-width="0.623629"
                      stroke-linecap="round"
                    />
                    <path
                      id="Vector 219_2"
                      d="M219.402 261.797C219.268 259.618 216.889 220.958 215.661 200.993"
                      stroke="black"
                      stroke-width="0.623629"
                      stroke-linecap="round"
                    />
                  </g>
                </g>
              </g>
              <path
                id="Vector_2"
                d="M171.072 265.85C171.072 265.85 169.824 266.785 167.33 266.785C164.455 266.785 164.726 264.76 158.911 265.85C153.922 266.785 155.481 264.915 151.116 264.291C146.75 263.667 145.503 266.474 140.826 265.226C136.148 263.979 135.525 264.291 130.848 265.226C126.17 266.162 124.3 263.044 121.805 261.796C119.31 260.549 115.257 263.044 111.515 263.044C108.522 263.044 106.734 259.51 106.214 257.743C107.15 239.346 110.307 207.638 114.633 175.735C120.341 133.64 121.805 112.437 133.342 70.9657C137.422 56.3012 144.048 40.0961 147.062 34.4834C147.062 34.4834 161.692 35.1102 171.072 35.107C180.33 35.1039 194.77 34.4834 194.77 34.4834C197.784 40.0961 204.41 56.3012 208.49 70.9657C220.027 112.437 221.49 133.64 227.198 175.735C231.524 207.638 234.682 239.346 235.617 257.743C235.098 259.51 233.31 263.044 230.317 263.044C226.575 263.044 222.521 260.549 220.027 261.796C217.532 263.044 215.661 266.162 210.984 265.226C206.307 264.291 205.683 263.979 201.006 265.226C196.329 266.474 195.081 263.667 190.716 264.291C186.351 264.915 187.91 266.785 182.921 265.85C177.106 264.76 177.376 266.785 174.502 266.785C172.007 266.785 170.76 265.85 170.76 265.85"
                stroke="black"
                stroke-width="0.623629"
              />
              <g id="Group 133">
                <path
                  id="Vector 208"
                  d="M195.081 29.4941H146.75V34.4832C146.75 34.4832 161.57 35.11 171.071 35.1068C180.451 35.1037 195.081 34.4832 195.081 34.4832V29.4941Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.623629"
                />
                <path
                  id="Vector 209"
                  d="M171.383 27.3115C157.04 27.3115 146.75 29.4943 146.75 29.4943H195.393C195.393 29.4943 184.479 27.3115 171.383 27.3115Z"
                  fill="white"
                  stroke="black"
                  stroke-width="0.623629"
                />
              </g>
            </g>
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 21"
                  d="M191.651 32.5181H149.378"
                  stroke="#E55959"
                  stroke-width="3.11815"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26"
                  d="M153.106 27.8828L147.213 32.5234L151.989 37.8203"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
                <path
                  id="Vector 27"
                  d="M189.03 37.8121L193.924 32.1277L188.221 27.8448"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 21_2"
                  d="M202.877 71.1836L136.906 71.1836"
                  stroke="#E55959"
                  stroke-width="3.11815"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_2"
                  d="M140.634 66.5478L134.74 71.1884L139.516 76.4853"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
                <path
                  id="Vector 27_2"
                  d="M200.255 76.4772L205.15 70.7928L199.447 66.5098"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
              </g>
            )}
            {findMeasure('product_lower_length') && (
              <g id="product_lower_length">
                <path
                  id="Vector 19"
                  d="M133.412 30.4443L94.3646 257.431"
                  stroke="#E55959"
                  stroke-width="3.11815"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M89.5477 253.401L93.9125 259.502L99.4233 254.974"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
                <path
                  id="Vector 28"
                  d="M128.268 33.3074L133.596 28.4707L135.541 31.3437L137.486 34.2167"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_2"
                  d="M171.383 32.6128L171.383 265.227"
                  stroke="#E55959"
                  stroke-width="3.11815"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M166.023 261.524L171.376 266.778L176.022 261.367"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
                <path
                  id="Vector 28_2"
                  d="M166.5 35.1468L170.912 29.4626L173.324 31.9563L175.736 34.45"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 21_3"
                  d="M191.651 32.5181H149.378"
                  stroke="#E55959"
                  stroke-width="3.11815"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_3"
                  d="M153.106 27.8828L147.213 32.5234L151.989 37.8203"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
                <path
                  id="Vector 27_5"
                  d="M189.03 37.8121L193.924 32.1277L188.221 27.8448"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 21_4"
                  d="M202.877 71.1836L136.906 71.1836"
                  stroke="#E55959"
                  stroke-width="3.11815"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_4"
                  d="M140.634 66.5478L134.74 71.1884L139.516 76.4853"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
                <path
                  id="Vector 27_6"
                  d="M200.255 76.4772L205.15 70.7928L199.447 66.5098"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_3"
                  d="M171.383 35.1069L171.383 262.108"
                  stroke="#E55959"
                  stroke-width="3.11815"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M166.023 259.029L171.376 264.284L176.022 258.872"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
                <path
                  id="Vector 28_3"
                  d="M166.5 37.6414L170.912 31.9572L173.324 34.4509L175.736 36.9447"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
              </g>
            )}
            {findMeasure('product_waistband_width') && (
              <g id="product_waistband_width">
                <path
                  id="Vector 21_5"
                  d="M191.651 32.5181H149.378"
                  stroke="#E55959"
                  stroke-width="3.11815"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_5"
                  d="M153.106 27.8828L147.213 32.5234L151.989 37.8203"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
                <path
                  id="Vector 27_8"
                  d="M189.03 37.8121L193.924 32.1277L188.221 27.8448"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
              </g>
            )}
            {findMeasure('product_waistband_circumference') && (
              <g id="product_waistband_circumference">
                <path
                  id="Vector 21_6"
                  d="M191.651 32.5181H149.378"
                  stroke="#E55959"
                  stroke-width="3.11815"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_6"
                  d="M153.106 27.8828L147.213 32.5234L151.989 37.8203"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
                <path
                  id="Vector 27_9"
                  d="M189.03 37.8121L193.924 32.1277L188.221 27.8448"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
              </g>
            )}
            {findMeasure('product_high_waist_width') && (
              <g id="product_high_waist_width">
                <path
                  id="Vector 21_7"
                  d="M191.651 32.5181H149.378"
                  stroke="#E55959"
                  stroke-width="3.11815"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_7"
                  d="M153.106 27.8828L147.213 32.5234L151.989 37.8203"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
                <path
                  id="Vector 27_10"
                  d="M189.03 37.8121L193.924 32.1277L188.221 27.8448"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_width') && (
              <g id="product_lower_waist_width">
                <path
                  id="Vector 21_8"
                  d="M191.651 32.5181H149.378"
                  stroke="#E55959"
                  stroke-width="3.11815"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_8"
                  d="M153.106 27.8828L147.213 32.5234L151.989 37.8203"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
                <path
                  id="Vector 27_11"
                  d="M189.03 37.8121L193.924 32.1277L188.221 27.8448"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
              </g>
            )}
            {findMeasure('product_high_waist_circumference') && (
              <g id="product_high_waist_circumference">
                <path
                  id="Vector 21_9"
                  d="M191.651 32.5181H149.378"
                  stroke="#E55959"
                  stroke-width="3.11815"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_9"
                  d="M153.106 27.8828L147.213 32.5234L151.989 37.8203"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
                <path
                  id="Vector 27_12"
                  d="M189.03 37.8121L193.924 32.1277L188.221 27.8448"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_circumference') && (
              <g id="product_lower_waist_circumference">
                <path
                  id="Vector 21_10"
                  d="M191.651 32.5181H149.378"
                  stroke="#E55959"
                  stroke-width="3.11815"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 26_10"
                  d="M153.106 27.8828L147.213 32.5234L151.989 37.8203"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
                <path
                  id="Vector 27_13"
                  d="M189.03 37.8121L193.924 32.1277L188.221 27.8448"
                  stroke="#E55959"
                  stroke-width="2.49452"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_208_4967"
              x1="136.432"
              y1="60.2214"
              x2="125.094"
              y2="68.5722"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#CDCDCD" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_208_4967"
              x1="205.42"
              y1="60.2214"
              x2="216.757"
              y2="68.5722"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#CDCDCD" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_208_4967"
              x1="122.634"
              y1="117.984"
              x2="97.2854"
              y2="131.582"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint3_linear_208_4967"
              x1="218.967"
              y1="117.984"
              x2="244.315"
              y2="131.582"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
            <linearGradient
              id="paint4_linear_208_4967"
              x1="120.713"
              y1="228.432"
              x2="120.713"
              y2="262.108"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint5_linear_208_4967"
              x1="190.242"
              y1="231.695"
              x2="192.64"
              y2="265.285"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint6_linear_208_4967"
              x1="136.616"
              y1="230.927"
              x2="136.616"
              y2="264.602"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint7_linear_208_4967"
              x1="179.646"
              y1="232.174"
              x2="179.646"
              y2="265.85"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint8_linear_208_4967"
              x1="150.336"
              y1="231.551"
              x2="150.336"
              y2="265.227"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint9_linear_208_4967"
              x1="205.838"
              y1="231.862"
              x2="205.838"
              y2="265.538"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint10_linear_208_4967"
              x1="162.808"
              y1="255.554"
              x2="162.808"
              y2="265.85"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
            <linearGradient
              id="paint11_linear_208_4967"
              x1="222.677"
              y1="251.501"
              x2="222.677"
              y2="261.797"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#D9D9D9" />
              <stop offset="1" stop-color="#F5F5F5" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
