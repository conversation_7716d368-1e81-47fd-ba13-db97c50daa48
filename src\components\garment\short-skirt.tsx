import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface ShortSkirtProps {
  measure: GarmentMeasure;
  className?: string;
}

export function ShortSkirt({ measure, className }: ShortSkirtProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g id="group_short_skirt">
            <g id="short_skirt">
              <g id="Vector">
                <path
                  d="M219.212 33H111.973H111.866C107.552 47.2845 101.527 75.581 99.3494 103.433C97.6124 125.651 99.3494 160.5 99.3494 160.5H199.377L200.438 131.86L204.045 160.5H204.681H228.866H231.836C231.836 160.5 233.573 125.651 231.836 103.433C229.658 75.581 223.633 47.2845 219.319 33H219.212Z"
                  fill="white"
                />
                <path
                  d="M111.973 33H219.319C223.633 47.2845 229.658 75.581 231.836 103.433C233.573 125.651 231.836 160.5 231.836 160.5H204.681M219.212 33H111.866C107.552 47.2845 101.527 75.581 99.3494 103.433C97.6124 125.651 99.3494 160.5 99.3494 160.5H199.377L200.438 131.86L204.045 160.5H228.866"
                  stroke="black"
                  stroke-width="0.424293"
                />
              </g>
              <path
                id="Vector 41"
                d="M177.407 112.995C189.469 105.131 212.687 77.1025 214.027 75.6416C214.104 79.0432 210.986 97.2756 210.986 97.2756C210.403 98.3223 199.091 109.255 181.223 117.006C166.93 123.206 144.596 127.65 138.313 127.336C145.217 126.206 166.455 120.135 177.407 112.995Z"
                fill="url(#paint0_linear_208_4964)"
                fill-opacity="0.87"
              />
              <path id="Vector 202" d="M194.71 33L200.438 132.285" stroke="black" stroke-width="0.424293" />
              <path
                id="Vector 203"
                d="M110.488 37.8794L220.168 37.6672"
                stroke="black"
                stroke-width="0.424293"
                stroke-dasharray="0.85 0.85"
              />
              <path
                id="Vector 204"
                d="M110.064 40.4253H221.228"
                stroke="black"
                stroke-width="0.424293"
                stroke-dasharray="0.85 0.85"
              />
              <path
                id="Vector 205"
                d="M99.8807 155.833H199.59"
                stroke="black"
                stroke-width="0.424293"
                stroke-dasharray="0.85 0.85"
              />
              <path
                id="Vector 207"
                d="M99.8807 153.287H195.347L199.165 116.586L207.227 153.287H232.26"
                stroke="black"
                stroke-width="0.424293"
                stroke-dasharray="0.85 0.85"
              />
              <path
                id="Vector 206"
                d="M203.408 155.833H231.836"
                stroke="black"
                stroke-width="0.424293"
                stroke-dasharray="0.85 0.85"
              />
            </g>
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19"
                  d="M113.053 39.126L217.804 39.126"
                  stroke="#E55959"
                  stroke-width="2.12146"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27"
                  d="M215.709 42.5605L219.284 38.9185L215.602 35.7576"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
                <path
                  id="Vector 28"
                  d="M115.576 42.2365L111.709 39.2348L113.406 37.5935L115.102 35.9523"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_2"
                  d="M165.49 34.959L165.49 157.318"
                  stroke="#E55959"
                  stroke-width="2.12146"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M162.055 155.86L165.697 159.435L168.858 155.753"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
                <path
                  id="Vector 28_2"
                  d="M162.379 37.4821L165.381 33.6148L167.022 35.3114L168.663 37.0081"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
              </g>
            )}
            {findMeasure('product_lower_length') && (
              <g id="product_lower_length">
                <path
                  id="Vector 19_3"
                  d="M102.882 35.3613L88.8428 156.912"
                  stroke="#E55959"
                  stroke-width="2.12146"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M85.5981 155.069L88.8059 159.039L92.3683 155.744"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
                <path
                  id="Vector 28_3"
                  d="M99.5026 37.5107L102.928 34.0133L104.364 35.8871L105.8 37.7608"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 19_4"
                  d="M103.718 94.7085L228.411 94.7085"
                  stroke="#E55959"
                  stroke-width="2.12146"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M225.892 98.143L229.467 94.501L225.785 91.3402"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
                <path
                  id="Vector 28_4"
                  d="M106.242 97.8191L102.374 94.8173L104.071 93.1761L105.768 91.5348"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_5"
                  d="M165.49 34.959L165.49 157.318"
                  stroke="#E55959"
                  stroke-width="2.12146"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_5"
                  d="M162.055 155.86L165.697 159.435L168.858 155.753"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
                <path
                  id="Vector 28_5"
                  d="M162.379 37.4821L165.381 33.6148L167.022 35.3114L168.663 37.0081"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_6"
                  d="M113.053 39.126L217.804 39.126"
                  stroke="#E55959"
                  stroke-width="2.12146"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M215.709 42.5605L219.284 38.9185L215.602 35.7576"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
                <path
                  id="Vector 28_6"
                  d="M115.576 42.2365L111.709 39.2348L113.406 37.5935L115.102 35.9523"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 19_7"
                  d="M103.718 94.7085L228.411 94.7085"
                  stroke="#E55959"
                  stroke-width="2.12146"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M225.892 98.143L229.467 94.501L225.785 91.3402"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
                <path
                  id="Vector 28_7"
                  d="M106.242 97.8191L102.374 94.8173L104.071 93.1761L105.768 91.5348"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
              </g>
            )}
            {findMeasure('product_waistband_circumference') && (
              <g id="product_waistband_circumference">
                <path
                  id="Vector 19_8"
                  d="M113.053 39.126L217.804 39.126"
                  stroke="#E55959"
                  stroke-width="2.12146"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_8"
                  d="M215.709 42.5605L219.284 38.9185L215.602 35.7576"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
                <path
                  id="Vector 28_8"
                  d="M115.576 42.2365L111.709 39.2348L113.406 37.5935L115.102 35.9523"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
              </g>
            )}
            {findMeasure('product_waistband_width') && (
              <g id="product_waistband_width">
                <path
                  id="Vector 19_9"
                  d="M113.053 39.126L217.804 39.126"
                  stroke="#E55959"
                  stroke-width="2.12146"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_9"
                  d="M215.709 42.5605L219.284 38.9185L215.602 35.7576"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
                <path
                  id="Vector 28_9"
                  d="M115.576 42.2365L111.709 39.2348L113.406 37.5935L115.102 35.9523"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
              </g>
            )}
            {findMeasure('product_high_waist_width') && (
              <g id="product_high_waist_width">
                <path
                  id="Vector 19_10"
                  d="M113.053 39.126L217.804 39.126"
                  stroke="#E55959"
                  stroke-width="2.12146"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_10"
                  d="M215.709 42.5605L219.284 38.9185L215.602 35.7576"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
                <path
                  id="Vector 28_10"
                  d="M115.576 42.2365L111.709 39.2348L113.406 37.5935L115.102 35.9523"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_width') && (
              <g id="product_lower_waist_width">
                <path
                  id="Vector 19_11"
                  d="M113.053 39.126L217.804 39.126"
                  stroke="#E55959"
                  stroke-width="2.12146"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_11"
                  d="M215.709 42.5605L219.284 38.9185L215.602 35.7576"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
                <path
                  id="Vector 28_11"
                  d="M115.576 42.2365L111.709 39.2348L113.406 37.5935L115.102 35.9523"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
              </g>
            )}
            {findMeasure('product_high_waist_circumference') && (
              <g id="product_high_waist_circumference">
                <path
                  id="Vector 19_12"
                  d="M113.053 39.126L217.804 39.126"
                  stroke="#E55959"
                  stroke-width="2.12146"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_12"
                  d="M215.709 42.5605L219.284 38.9185L215.602 35.7576"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
                <path
                  id="Vector 28_12"
                  d="M115.576 42.2365L111.709 39.2348L113.406 37.5935L115.102 35.9523"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_circumference') && (
              <g id="product_lower_waist_circumference">
                <path
                  id="Vector 19_13"
                  d="M113.053 39.126L217.804 39.126"
                  stroke="#E55959"
                  stroke-width="2.12146"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_13"
                  d="M215.709 42.5605L219.284 38.9185L215.602 35.7576"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
                <path
                  id="Vector 28_13"
                  d="M115.576 42.2365L111.709 39.2348L113.406 37.5935L115.102 35.9523"
                  stroke="#E55959"
                  stroke-width="1.69717"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_208_4964"
              x1="209.006"
              y1="93.8144"
              x2="202.583"
              y2="134.68"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
          </defs>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_short_skirt">
            <g id="short_skirt">
              <g id="Vector">
                <path
                  d="M255.528 46H87.3102H87.1423C80.3758 68.4071 70.9238 112.794 67.5083 156.483C64.7837 191.335 67.5083 246 67.5083 246H224.415L226.079 201.075L231.736 246H232.735H270.671H275.33C275.33 246 278.055 191.335 275.33 156.483C271.915 112.794 262.463 68.4071 255.696 46H255.528Z"
                  fill="white"
                />
                <path
                  d="M87.3102 46H255.696C262.463 68.4071 271.915 112.794 275.33 156.483C278.055 191.335 275.33 246 275.33 246H232.735M255.528 46H87.1423C80.3758 68.4071 70.9238 112.794 67.5083 156.483C64.7837 191.335 67.5083 246 67.5083 246H224.415L226.079 201.075L231.736 246H270.671"
                  stroke="black"
                  stroke-width="0.665557"
                />
              </g>
              <path
                id="Vector 41"
                d="M189.952 171.482C208.873 159.147 245.293 115.18 247.395 112.889C247.515 118.224 242.624 146.824 242.624 146.824C241.71 148.466 223.965 165.615 195.938 177.774C173.517 187.5 138.482 194.471 128.628 193.979C139.457 192.205 172.772 182.682 189.952 171.482Z"
                fill="url(#paint0_linear_208_4965)"
                fill-opacity="0.87"
              />
              <path id="Vector 202" d="M217.094 46L226.079 201.74" stroke="black" stroke-width="0.665557" />
              <path
                id="Vector 203"
                d="M84.9807 53.6538L257.027 53.321"
                stroke="black"
                stroke-width="0.665557"
                stroke-dasharray="1.33 1.33"
              />
              <path
                id="Vector 204"
                d="M84.3152 57.6475H258.691"
                stroke="black"
                stroke-width="0.665557"
                stroke-dasharray="1.33 1.33"
              />
              <path
                id="Vector 205"
                d="M68.3416 238.679H224.748"
                stroke="black"
                stroke-width="0.665557"
                stroke-dasharray="1.33 1.33"
              />
              <path
                id="Vector 207"
                d="M68.3416 234.685H218.092L224.082 177.115L236.728 234.685H275.996"
                stroke="black"
                stroke-width="0.665557"
                stroke-dasharray="1.33 1.33"
              />
              <path
                id="Vector 206"
                d="M230.737 238.679H275.33"
                stroke="black"
                stroke-width="0.665557"
                stroke-dasharray="1.33 1.33"
              />
            </g>
            {findMeasure('product_waist_width') && (
              <g id="product_waist_width">
                <path
                  id="Vector 19"
                  d="M89.0042 55.6094L253.319 55.6094"
                  stroke="#E55959"
                  stroke-width="3.32779"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27"
                  d="M250.033 60.9973L255.641 55.2843L249.866 50.3262"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
                <path
                  id="Vector 28"
                  d="M92.9628 60.4885L86.8964 55.7798L89.5578 53.2054L92.2192 50.6309"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
              </g>
            )}
            {findMeasure('product_length') && (
              <g id="product_length">
                <path
                  id="Vector 19_2"
                  d="M171.258 49.0728L171.258 241.008"
                  stroke="#E55959"
                  stroke-width="3.32779"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_2"
                  d="M165.87 238.72L171.583 244.329L176.541 238.553"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
                <path
                  id="Vector 28_2"
                  d="M166.378 53.0309L171.087 46.9645L173.661 49.6259L176.236 52.2873"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
              </g>
            )}
            {findMeasure('product_lower_length') && (
              <g id="product_lower_length">
                <path
                  id="Vector 19_3"
                  d="M73.0508 49.7036L51.0284 240.372"
                  stroke="#E55959"
                  stroke-width="3.32779"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_3"
                  d="M45.9388 237.481L50.9706 243.707L56.5587 238.539"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
                <path
                  id="Vector 28_3"
                  d="M67.7493 53.0759L73.1229 47.5898L75.375 50.529L77.6272 53.4682"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
              </g>
            )}
            {findMeasure('product_hip_width') && (
              <g id="product_hip_width">
                <path
                  id="Vector 19_4"
                  d="M74.3618 142.797L269.958 142.797"
                  stroke="#E55959"
                  stroke-width="3.32779"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_4"
                  d="M266.007 148.185L271.615 142.472L265.839 137.514"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
                <path
                  id="Vector 28_4"
                  d="M78.3205 147.677L72.2541 142.968L74.9155 140.394L77.5769 137.819"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
              </g>
            )}
            {findMeasure('product_front_length') && (
              <g id="product_front_length">
                <path
                  id="Vector 19_5"
                  d="M171.258 49.0728L171.258 241.008"
                  stroke="#E55959"
                  stroke-width="3.32779"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_5"
                  d="M165.87 238.72L171.583 244.329L176.541 238.553"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
                <path
                  id="Vector 28_5"
                  d="M166.378 53.0309L171.087 46.9645L173.661 49.6259L176.236 52.2873"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
              </g>
            )}
            {findMeasure('product_waist_circumference') && (
              <g id="product_waist_circumference">
                <path
                  id="Vector 19_6"
                  d="M89.0042 55.6094L253.319 55.6094"
                  stroke="#E55959"
                  stroke-width="3.32779"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_6"
                  d="M250.033 60.9973L255.641 55.2843L249.866 50.3262"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
                <path
                  id="Vector 28_6"
                  d="M92.9628 60.4885L86.8964 55.7798L89.5578 53.2054L92.2192 50.6309"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
              </g>
            )}
            {findMeasure('product_hip_circumference') && (
              <g id="product_hip_circumference">
                <path
                  id="Vector 19_7"
                  d="M74.3618 142.797L269.958 142.797"
                  stroke="#E55959"
                  stroke-width="3.32779"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_7"
                  d="M266.007 148.185L271.615 142.472L265.839 137.514"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
                <path
                  id="Vector 28_7"
                  d="M78.3205 147.677L72.2541 142.968L74.9155 140.394L77.5769 137.819"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
              </g>
            )}
            {findMeasure('product_waistband_circumference') && (
              <g id="product_waistband_circumference">
                <path
                  id="Vector 19_8"
                  d="M89.0042 55.6094L253.319 55.6094"
                  stroke="#E55959"
                  stroke-width="3.32779"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_8"
                  d="M250.033 60.9973L255.641 55.2843L249.866 50.3262"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
                <path
                  id="Vector 28_8"
                  d="M92.9628 60.4885L86.8964 55.7798L89.5578 53.2054L92.2192 50.6309"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
              </g>
            )}
            {findMeasure('product_waistband_width') && (
              <g id="product_waistband_width">
                <path
                  id="Vector 19_9"
                  d="M89.0042 55.6094L253.319 55.6094"
                  stroke="#E55959"
                  stroke-width="3.32779"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_9"
                  d="M250.033 60.9973L255.641 55.2843L249.866 50.3262"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
                <path
                  id="Vector 28_9"
                  d="M92.9628 60.4885L86.8964 55.7798L89.5578 53.2054L92.2192 50.6309"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
              </g>
            )}
            {findMeasure('product_high_waist_width') && (
              <g id="product_high_waist_width">
                <path
                  id="Vector 19_10"
                  d="M89.0042 55.6094L253.319 55.6094"
                  stroke="#E55959"
                  stroke-width="3.32779"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_10"
                  d="M250.033 60.9973L255.641 55.2843L249.866 50.3262"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
                <path
                  id="Vector 28_10"
                  d="M92.9628 60.4885L86.8964 55.7798L89.5578 53.2054L92.2192 50.6309"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_width') && (
              <g id="product_lower_waist_width">
                <path
                  id="Vector 19_11"
                  d="M89.0042 55.6094L253.319 55.6094"
                  stroke="#E55959"
                  stroke-width="3.32779"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_11"
                  d="M250.033 60.9973L255.641 55.2843L249.866 50.3262"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
                <path
                  id="Vector 28_11"
                  d="M92.9628 60.4885L86.8964 55.7798L89.5578 53.2054L92.2192 50.6309"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
              </g>
            )}
            {findMeasure('product_high_waist_circumference') && (
              <g id="product_high_waist_circumference">
                <path
                  id="Vector 19_12"
                  d="M89.0042 55.6094L253.319 55.6094"
                  stroke="#E55959"
                  stroke-width="3.32779"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_12"
                  d="M250.033 60.9973L255.641 55.2843L249.866 50.3262"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
                <path
                  id="Vector 28_12"
                  d="M92.9628 60.4885L86.8964 55.7798L89.5578 53.2054L92.2192 50.6309"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
              </g>
            )}
            {findMeasure('product_lower_waist_circumference') && (
              <g id="product_lower_waist_circumference">
                <path
                  id="Vector 19_13"
                  d="M89.0042 55.6094L253.319 55.6094"
                  stroke="#E55959"
                  stroke-width="3.32779"
                  stroke-linecap="square"
                />
                <path
                  id="Vector 27_13"
                  d="M250.033 60.9973L255.641 55.2843L249.866 50.3262"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
                <path
                  id="Vector 28_13"
                  d="M92.9628 60.4885L86.8964 55.7798L89.5578 53.2054L92.2192 50.6309"
                  stroke="#E55959"
                  stroke-width="2.66223"
                />
              </g>
            )}
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_208_4965"
              x1="239.518"
              y1="141.395"
              x2="229.444"
              y2="205.498"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#F5F5F5" />
              <stop offset="0.69487" stop-color="#E6E6E6" />
            </linearGradient>
          </defs>
        </svg>
      )}
    </div>
  );
}
