import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Link } from './link';

const meta = {
  title: 'Atoms/Link',
  component: Link,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    href: { control: 'text' },
    external: { control: 'boolean' },
    children: { control: 'text' },
  },
} satisfies Meta<typeof Link>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Internal: Story = {
  args: {
    href: '/example',
    children: 'Internal Link',
  },
};

export const External: Story = {
  args: {
    href: 'https://example.com',
    external: true,
    children: 'External Link',
  },
};

export const CustomStyle: Story = {
  args: {
    href: '/example',
    children: 'Custom Styled Link',
    className: 'text-amber-50 py-2 px-4 rounded-sm bg-amber-500 no-underline pointer',
  },
};
