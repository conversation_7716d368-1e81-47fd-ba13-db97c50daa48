import i18next from 'i18next';
import { UnitTypes } from '@/components/organisms';

const mappedLanguages: { [key: string]: string } = {
  ar: 'ar',
  cs_CZ: 'cs',
  de_DE: 'de',
  en_US: 'en',
  es_ES: 'es',
  fi_FI: 'fi',
  fr_FR: 'fr',
  el_GR: 'el',
  hu_HU: 'hu',
  it_IT: 'it',
  ja_JP: 'ja',
  nl_NL: 'nl',
  pl_PL: 'pl',
  pt_BR: 'ptbr',
  pt_PT: 'ptpt',
  ru_RU: 'ru',
  se_SE: 'se',
  sv_SE: 'sv',
  tr_TR: 'tr',
  zh_CN: 'zh',
};

export const handleLinkUrl = ({ unit: unit = 'cm' }: { unit: UnitTypes }) => {
  const language = i18next.language;
  const selectedLanguage = mappedLanguages[language] ?? 'en';

  return `https://static.sizebay.technology/assets/measurement_tape/measurement_tape_${unit}_${selectedLanguage}.pdf`;
};
