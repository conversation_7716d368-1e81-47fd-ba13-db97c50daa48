const mobileBTN = document.getElementById('mobileBTN');
const desktopBTN = document.getElementById('desktopBTN');

const backdrop = document.createElement('div');
backdrop.id = 'backdrop';

const generateIframe = ({ isMobile }) => {
  const iframe = document.createElement('iframe');
  // Anni?
  // const productId = 49560076;
  // Letras
  // const productId = 70409923;
  // fracionado
  // const productId = 60141656;
  // Shoe full body
  const productId = 6834319;
  const tenantId = 6551;
  const sid = '06B0BCBF3E891f67905f3ebd49b1a2f53388d0329f51';
  iframe.src = `http://localhost:3000/?id=${productId}&tenantId=${tenantId}&sid=${sid}`;
  // iframe.src = `http://localhost:3000/?id=59479180&tenantId=82&sid=0680023A00DB81b051599b414a2393dbd3a73e078541&recommendedSize=36&sizes=WyJVSyAxMC41Il0=`;
  iframe.id = 'measurementsTable';
  iframe.classList.add(isMobile ? 'mobile' : 'desktop');

  return iframe;
};

mobileBTN.addEventListener('click', () => {
  const iframe = generateIframe({ isMobile: true });

  backdrop.appendChild(iframe);
  document.body.appendChild(backdrop);
});

desktopBTN.addEventListener('click', () => {
  const iframe = generateIframe({ isMobile: false });

  backdrop.appendChild(iframe);
  document.body.appendChild(backdrop);
});

window.onload = () => {
  // mobileBTN.click();
  // desktopBTN.click();
};

window.addEventListener(
  'message',
  (event) => {
    // console.log(event.data);
    if (event.data === 'close-fitting-room') {
      backdrop.innerHTML = '';
      document.body.removeChild(backdrop);
    }
  },
  false
);
