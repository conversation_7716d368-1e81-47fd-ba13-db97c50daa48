import { useDevice } from '@/hooks/use-device';
import { getDocumentIsRTL } from '@/hooks/use-rtl';
import { GarmentMeasure } from '@/hooks/use-size-chart';
import { cn } from '@/lib/utils';

interface BeanieProps {
  measure: GarmentMeasure;
  className?: string;
}

export function Beanie({ measure, className }: BeanieProps) {
  const { garmentMeasurements } = measure;
  const measures = garmentMeasurements.map((item) => item.measure);
  const { isMobile } = useDevice();
  const isRtl = getDocumentIsRTL();

  const findMeasure = (measure: string) => {
    const foundMeasure = measures.some((item) => item === measure);
    return foundMeasure;
  };

  return (
    <div className={cn('rounded-lg object-fill h-full w-full', className)}>
      {isMobile ? (
        <svg
          className=" h-full m-auto"
          xmlns="http://www.w3.org/2000/svg"
          width="331"
          height="192"
          viewBox="0 0 331 193"
          fill="none"
        >
          <g xmlns="http://www.w3.org/2000/svg" id="group_beanie">
            <g id="beanie">
              <path
                id="Vector 434"
                d="M164.171 26.1213C104.069 29.7273 95.1739 104.013 95.1739 104.013H234.61C234.61 104.013 225.616 22.4347 164.171 26.1213Z"
                fill="white"
                stroke="black"
                stroke-width="0.480815"
              />
              <path
                id="Vector 433"
                d="M87 163.634C87 151.133 87 117.476 88.9233 105.696C89.2803 103.509 93.491 102.33 93.491 102.33H237.736C237.736 102.33 240.749 104.975 240.861 108.34C240.973 111.706 242.544 162.432 242.544 163.634C242.544 164.836 237.736 166.759 237.736 166.759H91.5677C91.5677 166.759 87 165.795 87 163.634Z"
                fill="white"
                stroke="black"
                stroke-width="0.480815"
              />
              <g id="Group 211">
                <path
                  id="Vector 436"
                  d="M95.8951 103.052C95.8951 103.052 92.594 106.973 92.289 110.024C89.6445 136.468 92.289 160.269 92.289 160.269L95.6547 166.879"
                  stroke="black"
                  stroke-width="0.480815"
                  stroke-dasharray="0.96 0.96"
                />
                <path
                  id="Vector 438"
                  d="M106.446 103.052C106.446 103.052 103.145 106.973 102.84 110.024C100.196 136.468 102.84 160.269 102.84 160.269L106.473 166.879"
                  stroke="black"
                  stroke-width="0.480815"
                  stroke-dasharray="0.96 0.96"
                />
                <path
                  id="Vector 441"
                  d="M117.505 103.052C117.505 103.052 113.872 106.973 113.536 110.024C110.626 136.468 113.536 160.269 113.536 160.269L117.291 166.879"
                  stroke="black"
                  stroke-width="0.480815"
                  stroke-dasharray="0.96 0.96"
                />
                <path
                  id="Vector 447"
                  d="M128.564 103.052C128.564 103.052 124.931 106.973 124.595 110.024C121.685 136.468 124.595 160.269 124.595 160.269L128.591 166.879"
                  stroke="black"
                  stroke-width="0.480815"
                  stroke-dasharray="0.96 0.96"
                />
                <path
                  id="Vector 455"
                  d="M182.949 103.052C182.949 103.052 186.582 106.973 186.918 110.024C189.828 136.468 186.932 160.148 186.932 160.148L182.923 166.879"
                  stroke="black"
                  stroke-width="0.480815"
                  stroke-dasharray="0.96 0.96"
                />
                <path
                  id="Vector 456"
                  d="M194.008 103.052C194.008 103.052 197.641 106.973 197.977 110.024C200.887 136.468 197.977 160.269 197.977 160.269L194.222 166.879"
                  stroke="black"
                  stroke-width="0.480815"
                  stroke-dasharray="0.96 0.96"
                />
                <path
                  id="Vector 458"
                  d="M209.875 103.052C209.875 103.052 213.508 106.973 213.844 110.024C216.754 136.468 213.844 160.269 213.844 160.269L209.875 167"
                  stroke="black"
                  stroke-width="0.480815"
                  stroke-dasharray="0.96 0.96"
                />
                <path
                  id="Vector 459"
                  d="M220.934 103.052C220.934 103.052 224.567 106.973 224.902 110.024C227.813 136.468 224.902 160.269 224.902 160.269L221.147 166.639"
                  stroke="black"
                  stroke-width="0.480815"
                  stroke-dasharray="0.96 0.96"
                />
                <path
                  id="Vector 448"
                  d="M137.566 103.052C130.354 129.737 137.486 166.879 137.486 166.879"
                  stroke="black"
                  stroke-width="0.480815"
                  stroke-dasharray="0.96 0.96"
                />
                <path
                  id="Vector 449"
                  d="M149.746 103.052C145.419 130.939 149.746 166.879 149.746 166.879"
                  stroke="black"
                  stroke-width="0.480815"
                  stroke-dasharray="0.96 0.96"
                />
                <path
                  id="Vector 460"
                  d="M172.345 103.052C176.672 130.939 172.345 166.879 172.345 166.879"
                  stroke="black"
                  stroke-width="0.480815"
                  stroke-dasharray="0.96 0.96"
                />
                <path
                  id="Vector 451"
                  d="M160.324 103.052C160.324 130.458 160.324 167 160.324 167"
                  stroke="black"
                  stroke-width="0.480815"
                  stroke-dasharray="0.96 0.96"
                />
                <path
                  id="Vector 437"
                  d="M233.435 103.052C233.435 103.052 236.736 106.973 237.041 110.024C239.686 136.468 237.041 160.269 237.041 160.269L233.649 166.879"
                  stroke="black"
                  stroke-width="0.480815"
                  stroke-dasharray="0.96 0.96"
                />
              </g>
              <path
                id="Vector 435"
                d="M200.371 100.104L198.549 102.33H201.194V113.389L202.155 115.793H219.465C219.465 115.793 220.907 114.484 220.907 113.389V100.104H200.371Z"
                fill="white"
                stroke="black"
                stroke-width="0.480815"
              />
              <path
                id="Vector 461"
                d="M203.838 100.167V113.149"
                stroke="black"
                stroke-width="0.480815"
                stroke-dasharray="0.96 0.96"
              />
              <path
                id="Vector 462"
                d="M218.743 100.167V113.149"
                stroke="black"
                stroke-width="0.480815"
                stroke-dasharray="0.96 0.96"
              />
              <path
                id="Vector 463"
                d="M218.984 113.148L203.598 113.148"
                stroke="black"
                stroke-width="0.480815"
                stroke-dasharray="0.96 0.96"
              />
            </g>
            {findMeasure('head_circumference') && (
              <g id="head_circumference">
                <g id="Group 256">
                  <g id="Group 258">
                    <g id="Group 220">
                      <path
                        id="Ellipse 23"
                        d="M155.812 143.297C110.796 143.698 88.202 137.549 88.202 137.549M240.444 133.259C240.444 133.259 220.389 141.967 174.775 142.928"
                        stroke="#E55959"
                        stroke-width="2.40408"
                      />
                      <path
                        id="Vector 27"
                        d="M178.122 139.695L173.64 142.834L177.481 145.861"
                        stroke="#E55959"
                        stroke-width="1.92326"
                      />
                      <path
                        id="Vector 28"
                        d="M152.333 139.993L156.804 143.147L152.953 146.161"
                        stroke="#E55959"
                        stroke-width="1.92326"
                      />
                    </g>
                  </g>
                </g>
              </g>
            )}
            {findMeasure('circumference') && (
              <g id="circumference">
                <g id="Group 256_2">
                  <g id="Group 258_2">
                    <g id="Group 220_2">
                      <path
                        id="Ellipse 23_2"
                        d="M155.812 143.297C110.796 143.698 88.202 137.549 88.202 137.549M240.444 133.259C240.444 133.259 220.389 141.967 174.775 142.928"
                        stroke="#E55959"
                        stroke-width="2.40408"
                      />
                      <path
                        id="Vector 27_2"
                        d="M178.122 139.695L173.64 142.834L177.481 145.861"
                        stroke="#E55959"
                        stroke-width="1.92326"
                      />
                      <path
                        id="Vector 28_2"
                        d="M152.333 139.993L156.804 143.147L152.953 146.161"
                        stroke="#E55959"
                        stroke-width="1.92326"
                      />
                    </g>
                  </g>
                </g>
              </g>
            )}
          </g>
        </svg>
      ) : (
        <svg
          className={cn('translate-x-[-10px]', {
            'translate-x-[35px]': isRtl,
          })}
          xmlns="http://www.w3.org/2000/svg"
          width="342"
          height="291"
          viewBox="0 0 342 291"
          fill="none"
        >
          <g id="group_beanie">
            <g id="beanie">
              <path
                id="Vector 434"
                d="M169.726 48.1677C86.606 53.1547 74.3043 155.891 74.3043 155.891H267.142C267.142 155.891 254.704 43.0693 169.726 48.1677Z"
                fill="white"
                stroke="black"
                stroke-width="0.664957"
              />
              <path
                id="Vector 433"
                d="M63 238.346C63 221.057 63 174.51 65.6598 158.218C66.1536 155.194 71.9769 153.563 71.9769 153.563H271.464C271.464 153.563 275.632 157.221 275.786 161.875C275.941 166.53 278.114 236.683 278.114 238.346C278.114 240.008 271.464 242.668 271.464 242.668H69.3171C69.3171 242.668 63 241.335 63 238.346Z"
                fill="white"
                stroke="black"
                stroke-width="0.664957"
              />
              <g id="Group 211">
                <path
                  id="Vector 436"
                  d="M75.3017 154.561C75.3017 154.561 70.7364 159.984 70.3145 164.202C66.6573 200.775 70.3145 233.69 70.3145 233.69L74.9692 242.832"
                  stroke="black"
                  stroke-width="0.664957"
                  stroke-dasharray="1.33 1.33"
                />
                <path
                  id="Vector 438"
                  d="M89.8939 154.561C89.8939 154.561 85.3285 159.984 84.9067 164.202C81.2494 200.775 84.9067 233.69 84.9067 233.69L89.9308 242.832"
                  stroke="black"
                  stroke-width="0.664957"
                  stroke-dasharray="1.33 1.33"
                />
                <path
                  id="Vector 441"
                  d="M105.188 154.561C105.188 154.561 100.163 159.984 99.6992 164.202C95.6741 200.775 99.6992 233.69 99.6992 233.69L104.892 242.832"
                  stroke="black"
                  stroke-width="0.664957"
                  stroke-dasharray="1.33 1.33"
                />
                <path
                  id="Vector 447"
                  d="M120.482 154.561C120.482 154.561 115.457 159.984 114.993 164.202C110.968 200.775 114.993 233.69 114.993 233.69L120.519 242.832"
                  stroke="black"
                  stroke-width="0.664957"
                  stroke-dasharray="1.33 1.33"
                />
                <path
                  id="Vector 455"
                  d="M195.696 154.561C195.696 154.561 200.72 159.984 201.185 164.202C205.21 200.775 201.204 233.523 201.204 233.523L195.659 242.832"
                  stroke="black"
                  stroke-width="0.664957"
                  stroke-dasharray="1.33 1.33"
                />
                <path
                  id="Vector 456"
                  d="M210.99 154.561C210.99 154.561 216.014 159.984 216.479 164.202C220.504 200.775 216.479 233.69 216.479 233.69L211.285 242.832"
                  stroke="black"
                  stroke-width="0.664957"
                  stroke-dasharray="1.33 1.33"
                />
                <path
                  id="Vector 458"
                  d="M232.934 154.561C232.934 154.561 237.958 159.984 238.422 164.202C242.447 200.775 238.422 233.69 238.422 233.69L232.934 243"
                  stroke="black"
                  stroke-width="0.664957"
                  stroke-dasharray="1.33 1.33"
                />
                <path
                  id="Vector 459"
                  d="M248.228 154.561C248.228 154.561 253.252 159.984 253.716 164.202C257.741 200.775 253.716 233.69 253.716 233.69L248.523 242.5"
                  stroke="black"
                  stroke-width="0.664957"
                  stroke-dasharray="1.33 1.33"
                />
                <path
                  id="Vector 448"
                  d="M132.931 154.561C122.957 191.466 132.821 242.832 132.821 242.832"
                  stroke="black"
                  stroke-width="0.664957"
                  stroke-dasharray="1.33 1.33"
                />
                <path
                  id="Vector 449"
                  d="M149.777 154.561C143.792 193.128 149.777 242.832 149.777 242.832"
                  stroke="black"
                  stroke-width="0.664957"
                  stroke-dasharray="1.33 1.33"
                />
                <path
                  id="Vector 460"
                  d="M181.03 154.561C187.015 193.128 181.03 242.832 181.03 242.832"
                  stroke="black"
                  stroke-width="0.664957"
                  stroke-dasharray="1.33 1.33"
                />
                <path
                  id="Vector 451"
                  d="M164.406 154.561C164.406 192.463 164.406 243 164.406 243"
                  stroke="black"
                  stroke-width="0.664957"
                  stroke-dasharray="1.33 1.33"
                />
                <path
                  id="Vector 437"
                  d="M265.516 154.561C265.516 154.561 270.082 159.984 270.504 164.202C274.161 200.775 270.504 233.69 270.504 233.69L265.812 242.832"
                  stroke="black"
                  stroke-width="0.664957"
                  stroke-dasharray="1.33 1.33"
                />
              </g>
              <path
                id="Vector 435"
                d="M219.79 150.484L217.27 153.563H220.927V168.857L222.257 172.182H246.196C246.196 172.182 248.191 170.371 248.191 168.857V150.484H219.79Z"
                fill="white"
                stroke="black"
                stroke-width="0.664957"
              />
              <path
                id="Vector 461"
                d="M224.585 150.571V168.525"
                stroke="black"
                stroke-width="0.664957"
                stroke-dasharray="1.33 1.33"
              />
              <path
                id="Vector 462"
                d="M245.198 150.571V168.525"
                stroke="black"
                stroke-width="0.664957"
                stroke-dasharray="1.33 1.33"
              />
              <path
                id="Vector 463"
                d="M245.531 168.524L224.252 168.524"
                stroke="black"
                stroke-width="0.664957"
                stroke-dasharray="1.33 1.33"
              />
            </g>
            {findMeasure('head_circumference') && (
              <g id="head_circumference">
                <g id="Group 256">
                  <g id="Group 258">
                    <g id="Group 220">
                      <path
                        id="Ellipse 23"
                        d="M158.166 210.22C95.9092 210.774 64.6624 202.271 64.6624 202.271M275.209 196.338C275.209 196.338 247.475 208.381 184.391 209.71"
                        stroke="#E55959"
                        stroke-width="3.32479"
                      />
                      <path
                        id="Vector 27"
                        d="M189.02 205.239L182.821 209.58L188.133 213.767"
                        stroke="#E55959"
                        stroke-width="2.65983"
                      />
                      <path
                        id="Vector 28"
                        d="M153.354 205.65L159.537 210.013L154.211 214.181"
                        stroke="#E55959"
                        stroke-width="2.65983"
                      />
                    </g>
                  </g>
                </g>
              </g>
            )}
            {findMeasure('circumference') && (
              <g id="circumference">
                <g id="Group 256_2">
                  <g id="Group 258_2">
                    <g id="Group 220_2">
                      <path
                        id="Ellipse 23_2"
                        d="M158.166 210.22C95.9092 210.774 64.6624 202.271 64.6624 202.271M275.209 196.338C275.209 196.338 247.475 208.381 184.391 209.71"
                        stroke="#E55959"
                        stroke-width="3.32479"
                      />
                      <path
                        id="Vector 27_2"
                        d="M189.02 205.239L182.821 209.58L188.133 213.767"
                        stroke="#E55959"
                        stroke-width="2.65983"
                      />
                      <path
                        id="Vector 28_2"
                        d="M153.354 205.65L159.537 210.013L154.211 214.181"
                        stroke="#E55959"
                        stroke-width="2.65983"
                      />
                    </g>
                  </g>
                </g>
              </g>
            )}
          </g>
        </svg>
      )}
    </div>
  );
}
